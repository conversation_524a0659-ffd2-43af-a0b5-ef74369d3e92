<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jdh.o2oservice</groupId>
        <artifactId>jdh-o2o-service</artifactId>
        <version>${revision}</version>
    </parent>
    <packaging>war</packaging>
    <artifactId>o2o-service-web</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-export</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-application-ext</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-application</artifactId>
            <version>${revision}</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-1.2-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-web</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-angel-care</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-angel-inspect</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-delivery</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>provider-dada</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>provider-shansong</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>provider-shunfeng</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-angel-core</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>provider-autotest</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>provider-jdlogistics</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jd.mlaas.titan</groupId>
            <artifactId>titan-profiler-sdk</artifactId>
            <version>20221231.2</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wangyin.schedule</groupId>
            <artifactId>schedule-client-starter</artifactId>
        </dependency>

    </dependencies>


    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <!-- 预发 -->
            <id>yfb</id>
            <properties>
                <profiles.active>yfb</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>production</id>
            <properties>
                <profiles.active>production</profiles.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- maven打包成war包,如果是老的web项目理论上讲必须有web.xml,如果没有择报错,但是springboot是没有web.xml,所以就不校验了-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.3</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${basedir}/target/classes/com/jdh/o2oservice/groovy</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/java/com/jdh/o2oservice/groovy</directory>
                                </resource>
                            </resources>
                            <encoding>UTF-8</encoding>
                            <!-- 过滤后缀文件 -->
                            <nonFilteredFileExtensions>
                                <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                                <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                            </nonFilteredFileExtensions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>
    
</project>
