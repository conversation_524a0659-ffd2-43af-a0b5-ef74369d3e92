spring:
  ## jmq
  # https://joyspace.jd.com/pages/W1Rw2L8996bu5FsPgUta
  # dev为测试站，可以本地直接链接 http://test.taishan.jd.com/jmq/application
  # 集群接入地址：https://joyspace.jd.com/pages/OCNOfPvDOzYzOzim8HBd
  jmq:
    enabled: true
    producers:
      reachStoreProducer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
      reachStoreMq2Producer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
        enabled: true
      angelSettleProducer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
      pdfToJpgProvider:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
    consumers:
      jdhReachStoreConsumer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
      jdhReachStoreMq2Consumer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
        enabled: true
      jdhNethpSyncMqConsumer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
      jdhRefundMqConsumer:
        password: C4AE77FB
        app: jdho2oservice
        address: jmq-cluster.jd.local:80
        enabled: true
      dtsConsumer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: false
      locCodeConsumer:
        password: d0af9c1e556746d39334e9cfece1e723
        app: jdho2oservice
        address: nameserver.jmq.jd.local:80
        enabled: true
topics:
  jdhReachStoreConsumer:
    locCodeSendTopic: xfyl_loc_code_sync_forward_yfb
    xfylLocCodeSendForwardYfbTopic: xfyl_loc_code_sync_forward_yfb
    providerCallback: provider_callback_topic_yfb
    nethpTriageEvent: xfyl_NethpTriageEvent_forward_yfb
    xfylNethpTriageEventForwardYfbTopic: xfyl_NethpTriageEvent_forward_yfb
    nethpTriageDiagRecycleEvent: xfyl_triage_diag_recycle_event_forward_yfb
    xfylNethpTriageDiagRecycleEventForwardYfbTopic: xfyl_triage_diag_recycle_event_forward_yfb
    refundResult: physicalexammq_orbCancelResult_pre
    delayMessageTopic: delay_task_topic_pre
    o2oServiceReport: o2o_service_report_yfb
    o2oServiceOrderStatus: o2o_service_order_status_yfb
    jdOrderbinlakeTopic: jd_order_binlake_yfb
    jdOrderbinlakeVtpTransferTopic: jd_order_binlake_vtp_transfer_yfb
    jdhPromiseBinlakeTopic: jdh_promise_binlake_yfb
    jdhMedicalPromiseBinlakeTopic: jdh_medical_promise_binlake_yfb
    jdhAngelWorkBinlakeTopic: jdh_angel_work_binlake_yfb
    jdhAngelTaskBinlakeTopic: jdh_angel_task_binlake_yfb
    withdrawChangeEventEventTopic: withdrawChangeEvent_yfb
    withdrawChangeEventEventForwardYfbTopic: withdrawChangeEvent_yfb
    jdhAngelShipBinlakeTopic: jdh_angel_ship_binlake_yfb
    cancelPayTopic: physicalexammq_ODC_CANCEL_v2_PRE
    reachNoticeTopic: o2o_reach_notice_yfb
    jdGmsProductCategoryPropertyTopic: xfyl_gms_product_category_property_yfb
    jdGmsProductCategoryPropertyTopicYF: xfyl_gms_product_category_property_yfb
    examinationSkuBinLakeTopic: examination_man_sku_binlake_pre
    vtpafs: VTP_AFS_PRE
  jdhReachStoreMq2Consumer:
    nethpDoctorAuditEventTopic: xfyl_DoctorAuditEvent_forward_yfb
    xfylNethpDoctorAuditEventForwardYfbTopic: xfyl_DoctorAuditEvent_forward_yfb
    nethpDoctorChangeEventTopic: xfyl_DoctorInfoChangeEvent_forward_yfb
    xfylNethpDoctorChangeEventForwardYfbTopic: xfyl_DoctorInfoChangeEvent_forward_yfb
    vtprefundResult: svp_refund_message
  order:
    middleware-no-split: 0_275_physicalexammq_PRE
    middleware-no-split-pre: 0_275_physicalexammq_PRE_TRANSFER
    pop-middleware-no-split: physicalexamination_0_276_PRE
    pop-middleware-split: physicalexamination_0_275_PRE
    self-middleware-no-split: 0_276_physicalexammq_PRE
    self-middleware-split: 0_275_physicalexammq_PRE
    middleware-pop-complete: physicalexammq_ODC_COMPLETE_PRE
  event:
    consumer.topic: o2o_service_event_topic_yfb
    forward.topic: promise_event_forward_topic_yfb
    medicalPromiseTopic: jdh_o2o_service_promise_msg_yfb
    jdhServiceItemToYfTopic: jdh_o2o_service_item_to_yf
  reach:
    task.topic: o2o_service_reach_task_topic_yfb
    jdApp.push.topic: msg_common_topic
  settlement:
    ebsTopic: healthcare_examin_order_ebs
  delay:
    event.topic: o2o_delay_task_topic_yfb
  dts:
    order: xfyl_appointment_order_info_binlake_o2o
    order-sku: examination_order_sku_info_binlake_o2o
    appointment: xfyl_appointment_info_binlake_o2o
    ship: xfyl_order_ship_info_binlake_o2o
  net:
    nethpRx : NETHP_RX_CREATE
  report:
    deal: xfyl_pdf_to_jpg_yfb