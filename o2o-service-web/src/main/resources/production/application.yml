server:
  port: 80
  servlet:
    context-path: /api
spring:
  profiles:
    active: production
  matrix:
      env: ON_LINE
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  servlet:
    # 应用的访问路径
    context-path: /
  devtools:
    restart:
      enabled: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8

# mybatisPlus
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  typeAliasesPackage: com.jdh.o2oservice.infrastructure.repository.db.po
  mapper-locations: classpath:sqlmap/*.xml

# redis 配置
redis:
  jimUrl: jim://3452526826096240630/28508
  endpoint: http://cfs.jim.jd.local

# forest配置
forest:
  backend: okhttp3
  max-retry-count: 3 # 请求失败后重试次数，默认为0次不重试
  max-retry-interval: 5000 #重试间隔时间
  connect-timeout: 5000 #链接超时时间
  timeout: 5000  # 请求超时时间
  ## 日志总开关，打开/关闭Forest请求/响应日志（默认为 true）
  log-enabled: true
  ## 打开/关闭Forest请求日志（默认为 true）
  log-request: true
  ## 打开/关闭Forest响应状态日志（默认为 true）
  log-response-status: true
  ## 打开/关闭Forest响应内容日志（默认为 false）
  log-response-content: true

# aces
tde:
  enableGM: false
  isProd: true
  rPath: ''
  token: eyJzaWciOiJUYkh6ZHJOWjUyay9Gc0pkSC9hc3UxNmlTY3hZVk9sYjZPNDc4ODVZVUFJZUxjV0hhTVA0OGZ5ZTZtYWdwaGl1cUNIc3pOdnRRcjFHUTdqdUYrRkFiREpWOEF4dk1ORndNNStGanFxVkZZNmlOZlJzNUduSUxFNGZFdUw3eDRTQ2ZjTnpTYUNzSXNEWE5VZnpGYWNvdVJhUnE1dnpZVm54RkhXQmI3RldaN2t1UGovSUcvZWo1VW5WS2g0RlMzQUQ3U0loZUVDQ0ZqN3FTUUJtSFNtRTdONEZkdytTbmJORlNyZTJzbHRTV1poMmgwRTVUMzhkbzI3N0RZWktBZkJuR0dMejlsNXBlS01qZHNBQ0pwQm80b3VwWWlwNzJGM3ZzMHUxL1UwOUp2T3ZuOU13UDc3d1V0ZytMS1pQcDc0eWNEYXVwdExRMHQyTzNJZWtqaGFLQ3c9PSIsImRhdGEiOnsiYWN0IjoiY3JlYXRlIiwiZWZmZWN0aXZlIjoxNjY4OTYwMDAwMDAwLCJleHBpcmVkIjoxNzMyMTE4NDAwMDAwLCJpZCI6Ik1qaGlPR1F6TXpRdFl6YzVZeTAwWkdJeExXRmxaall0WVRNNU0yUTNNMkZqT1dVMSIsImtleSI6ImxGKzNXVE5seTBIK2hNU2JGaGFoN21UVUU2QURKU3NJSXBCQXRTNnI5UVE9Iiwic2VydmljZSI6InBoeXNpY2FsZXhhbWluYXRpb25fRGpXdEp3Ym8iLCJzdHlwZSI6MX0sImV4dGVybmFsRGF0YSI6eyJ6b25lIjoiQ04tMCJ9fQ==

# oss
oss:
  accessKey: JDC_96582803E0CF4A86AE2D5B168D28
  secretKey: BF9DB00B1F82F1F2721F492DE17230DE
  internalEndPoint: s3-internal.cn-north-1.jdcloud-oss.com
  publicEndPoint: s3.cn-north-1.jdcloud-oss.com
  region: cn-north-1
  connectionTimeout: 30000
  defaultBucket: o2o-service

# ES配置类
elasticsearchfactory:
  clusterName: jiesi-jdos-home-service
  nodeConnectionAddress: prod-3-40000-jiesi-jdos-home-service.jd.local:40100;prod-2-40000-jiesi-jdos-home-service.jd.local:40100;prod-1-40000-jiesi-jdos-home-service.jd.local:40100;
  securityUser: jiesi-jdos-home-service
  securityPassword: 80949DF4A91357A8

# ducc
laf:
  config:
    logger:
      enabled: true
      type: logback
      key: logger.level
    manager:
      application: jdos_jdh-o2o-service
      namespace: jdh_o2o_service
      parameters:
        - name: autoListener
          value: true
      profile: production
      resources:
        - name: common
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/common/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: logging
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/logging/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: reach
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/reach/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: jm_via_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/jm_via_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: via_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/via_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: ability_executor_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/ability_executor_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: export_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/export_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: fee_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/fee_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: dispatch
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/dispatch/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: angel_promise
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/angel_promise/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: dict_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/dict_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: feedback_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/feedback_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: angel
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/angel/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: groovy_script
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/groovy_script/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: man_via_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/man_via_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: deploy_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/deploy_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: quick_drug_config
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/quick_drug_config/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: quick_drug_config2
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/quick_drug_config2/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
        - name: adapterFilterConfig
          uri: ucc://${laf.config.manager.application}:${laf.config.manager.token}@ducc.jd.local/v1/namespace/${laf.config.manager.namespace}/config/adapterFilterConfig/profiles/${laf.config.manager.profile}?longPolling=60000&necessary=false
      token: 0d01565c462242809fae00aa61179653
      baseUrl: http://ducc-api.jd.local

# elasticjob
elasticjob:
  enabled: false
  regCenter:
    #zookeeper 的ip:port
    serverLists: jkfwzk.zk.jddb.com:3525
    #名命空间，自己定义就好了
    namespace: jdh-o2o-service/production
    digest: xfyl1024:wBNrsia8BQTD
  jobs:
    # 服务单过期任务 每天凌晨1点
    jdhVoucherExpireJob:
      elasticJobClass: com.jdh.o2oservice.job.promise.voucher.VoucherExpireJob
      cron: 0 10 0 * * ?
      shardingTotalCount: 1
      overwrite: true
    # 商家域自动处理预约中状态任务 每小时01分钟
    providerPromiseAutoProcessJob:
      elasticJobClass: com.jdh.o2oservice.job.provider.ProviderPromiseAutoProcessJob
      cron: 0 1 * * * ?
      shardingTotalCount: 1
      overwrite: true
    # 计算派单因子
    #    jdhDispatchCalculateFactorJob:
    #      elasticJobClass: com.jdh.o2oservice.job.dispatch.DispatchCalculateFactorJob
    #      cron: 0 0 0 * * ?
    #      shardingTotalCount: 1
    #      overwrite: true
    # 定时清理状态为已过期以及逻辑删除排班 每天凌晨1点10分
    detectExpiredScheduleJob:
      elasticJobClass: com.jdh.o2oservice.job.angel.DetectExpiredScheduleJob
      cron: 0 10 1 * * ?
      shardingTotalCount: 1
      overwrite: true
    # 退款任务 每10s执行一次
    orderRefundTaskJob:
      elasticJobClass: com.jdh.o2oservice.job.trade.refund.OrderRefundTaskJob
      cron: 0/10 * * * * ?
      shardingTotalCount: 1
      overwrite: true
    # 重试ebs失败任务 每天凌晨1点20
    settlementEbsFailJob:
      elasticJobClass: com.jdh.o2oservice.job.settlement.SettlementEbsFailJob
      cron: 0 20 1 * * ?
      shardingTotalCount: 1
      overwrite: true
    # 结算单解冻 每天凌晨1点30
    orderSettlementAmountJob:
      elasticJobClass: com.jdh.o2oservice.job.settlement.OrderSettlementAmountJob
      cron: 0 30 1 * * ?
      shardingTotalCount: 1
      overwrite: true
    # 护士结算调账 每天凌晨1点1分
    angelSettlementDailyAdjustNewJob:
      elasticJobClass: com.jdh.o2oservice.job.settlement.AngelSettlementDailyAdjustNewJob
      cron: 0 1 1 * * ?
      shardingTotalCount: 1
      overwrite: true
    angelStationInventoryRollJob:
      elasticJobClass: com.jdh.o2oservice.job.angel.AngelStationInventoryRollJob
      cron: 0 0 23 * * ?
      shardingTotalCount: 1
      overwrite: true
    providerBillCreateJob:
      elasticJobClass: com.jdh.o2oservice.job.provider.ProviderBillCreateJob
      cron: 0 0 2 * * ?
      shardingTotalCount: 1
      overwrite: true
    promiseAppointmentTodayScanJob:
      elasticJobClass: com.jdh.o2oservice.job.support.PromiseAppointmentTodayScanJob
      cron: 0 30 6 * * ?
      shardingTotalCount: 1
      overwrite: true
    promiseAppointmentBeforeScanJob:
      elasticJobClass: com.jdh.o2oservice.job.support.PromiseAppointmentBeforeScanJob
      cron: 0 0 14 * * ?
      shardingTotalCount: 1
      overwrite: true
    popOrderExpireScanJob:
      elasticJobClass: com.jdh.o2oservice.job.support.PopOrderExpireScanJob
      cron: 0 0 20 * * ?
      shardingTotalCount: 1
      overwrite: true
    # 超过一天工单未完成
    angelFinishServedOverOneDayJob:
      elasticJobClass: com.jdh.o2oservice.job.angelpromise.AngelFinishServedOverOneDayJob
      cron: 0 0 9,16 * * ?
      shardingTotalCount: 1
      overwrite: true
    # 超过两天订单未完成
    angelFinishServedOverTwoDayJob:
      elasticJobClass: com.jdh.o2oservice.job.angelpromise.AngelFinishServedOverTwoDayJob
      cron: 2 0 9,16 * * ?
      shardingTotalCount: 1
      overwrite: true

#短链
#生成短连域名
shortUrlService:
  domain: 3.cn
  #生成短链长度
  length: 8
  #生成短链秘钥，登录http://s.3.cn/userIndex.action 线上点击API文档获取，建议产品提供稳定的key 现在为徐兵舰
  key: 4a5873d27bddb4142ed6cb6d70ee0bec

##地图
gis:
  map:
    baseurl: https://lop-proxy.jd.com
    appkey: 110orkkzzywfx9rAnnK66AnnK8K96n797O91
    scene: openapi
    tag:
# sso
# https://cf.jd.com/pages/viewpage.action?pageId=748451623
sso:
  clientId: "jdh-o2o-service"
  clientSecret: "7ec95cfccb0643e79253171e88d24f6f"
  excludePath: "/product/route,/provider/ship,/tools"
#  ajaxCallbackPolicy: "Referer"
#  endpoint: "https://ssa.jd.com"
#  apiEndpoint: "http://ssa.jd.local"


# uim权限系统配置  http://uim2.jd.com/system/info?systemCode=xqCV5Q2ZUaAS4D4S
uim:
  appKey: 78701b16c7bf4eada5aa048649217888
  appToken: 70c5b895e5614178a8e3c79bb208a49c
  tenantCode: CN.JD.GROUP
  dimResCode: belongBizCodeDataSource

#达达配置
dada:
  base:
    url: http://openplatform.imdada.local
  sourceId: 715670
  appKey: dada6b3e2cf3789bdc6
  appSecret: bb78f4c8415e03819fdf98c81d485c8d


#普通配置
o2o:
  angelPromise:
    angelTrack: https://laputa.jd.com/serviceHome/nurse/nurseposition?promiseId=%s
    angelTransferTrack: https://laputa.jd.com/nurse-on-site-service/service/position?promiseId=%s&shipId=%s&angelType=%s
    orderListLink: https://laputa.jd.com/nurse-on-site-service/service/order-list
    orderDetailLink: https://laputa.jd.com/nurse-on-site-service/service/orderDetail?workId=%s

#闪送配置
shansong:
  config:
    url: https://open.ishansong.com
    clientId: ssmYC81DSQxAowEti
    accessToken: 8R27KzL15SJhBwewYojeab6VuPyJYjW8
    shopId: 20000000000507800

#easyJob配置
jdd:
  easyjob:
    enable: true
    host: http://schedule.jdfin.local
    appId: jdh-o2o-service
    secret: 8b805c6c75d34a46cfb8b1c71b510b64

# 统一操作日志系统分配的appName
opLog:
  appName: jdh-o2o-service
  merchantAppName: xfyl-shop

# 调用promiseGoModel的域名
promiseGoModel:
  domain: http://promisego-model.jd.com