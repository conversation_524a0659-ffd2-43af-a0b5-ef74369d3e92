package com.jdh.o2oservice.listener.handler;

import com.alibaba.fastjson.JSON;
import com.jd.fastjson.support.odps.CodecCheck;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.bo.JdLogisticsListenerBo;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelShipCallBackContext;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.JdhAngelShipFeeRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.export.angelpromise.cmd.ShipInfoForCallBackRequest;
import com.jdh.o2oservice.vertical.enums.JdLogisticsShipStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/3/11
 * @description 京东物流handler
 */
@Slf4j
@Component
public class JdLogisticsHandler extends AbstractHandler<JdLogisticsListenerBo> implements MapAutowiredKey {

    @Autowired
    private AngelWorkApplication angelWorkApplication;

    @Value("${topics.jd.jdLogisticsTopic}")
    private String handlerTopic;

    @Value("${topics.jd.jdLogisticsYfbTopic:bd_waybill_state_toc_xfyl_yfb}")
    private String handlerTopicYf;

    @Autowired
    private DuccConfig duccConfig;

    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    /**
     * 运单状态
     */
    @Autowired
    private AngelShipRepository angelShipRepository;

    @Override
    public String getMapKey() {
        return handlerTopic;
    }

    @Override
    public boolean preFilterOfDiscardMessage(Message message) {
        //消息检查
        if(Objects.isNull(message) || StringUtils.isBlank(message.getText())){
            return true;
        }
        return false;
    }

    @Override
    public JdLogisticsListenerBo analysisMessage(Message message) {
        return JSON.parseObject(message.getText(), JdLogisticsListenerBo.class);
    }

    @Override
    public boolean filterOfDiscardMessage(JdLogisticsListenerBo jdLogisticsListenerBo) {
        return false;
    }

    @Override
    public boolean transferToYf(Message message, JdLogisticsListenerBo order) {
        // 判断当前pin是否在转投预发白名单
        if (duccConfig.checkBlankPin(order.getUserPin())) {
            log.info("JdLogisticsHandler -> transferToYf, eventBody转投到预发环境. doctorPin={}", order.getUserPin());
            try {
                message.setTopic(handlerTopicYf);
                log.info("JdLogisticsHandler -> transferToYf, eventBody转投到预发环境. message={}", JSON.toJSONString(message));
                reachStoreProducer.send(message);
            } catch (JMQException e) {
                throw new RuntimeException(e);
            }
            return true;
        }
        return false;
    }

    @Override
    public void dealMessage(JdLogisticsListenerBo jdLogisticsListenerBo) {
        //发送回调
        AngelShipCallBackContext shipInfoForCallBackRequest = AngelShipCallBackContext.builder().build();
        //运单号/取件单号
        shipInfoForCallBackRequest.setClientId(jdLogisticsListenerBo.getWaybillCode());
        //配送员id
        shipInfoForCallBackRequest.setDmId(jdLogisticsListenerBo.getPsyId()+"");
        //配送员名称
        shipInfoForCallBackRequest.setDmName(jdLogisticsListenerBo.getCourier());
        //配送员联系方式
        shipInfoForCallBackRequest.setDmMobile(jdLogisticsListenerBo.getCourierTel());
        //运单状态
        Integer status = JdLogisticsShipStatusEnum.transferToStandShipStatus(jdLogisticsListenerBo.getState());
        shipInfoForCallBackRequest.setOrderStatus(status);
        //物流动态信息
        shipInfoForCallBackRequest.setLogisticsMessage(jdLogisticsListenerBo.getJdStanderdOperateTrace());
        //操作时间
        shipInfoForCallBackRequest.setUpdateTime(jdLogisticsListenerBo.getOperateTime().getTime()/1000);

        AngelShip angelShip  = AngelShip.builder().outShipId(shipInfoForCallBackRequest.getClientId()).build();
        angelShip.setLogisticsMessage(jdLogisticsListenerBo.getJdStanderdOperateTrace());
        angelShipRepository.updateNoNullByOutShipId(angelShip);
        if(status==null){
            log.info("JdLogisticsHandler -> 未找到匹配的运单状态,不进入回调逻辑");
            return;
        }
        shipInfoForCallBackRequest.setDeliveryType(DeliveryTypeEnum.JD_LOGISTICS_DELIVERY.getType());
        angelWorkApplication.shipStatusCallback(shipInfoForCallBackRequest);
    }


}
