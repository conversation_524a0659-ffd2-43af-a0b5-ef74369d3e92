package com.jdh.o2oservice.listener.handler;

import com.alibaba.fastjson.JSON;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jd.purchase.domain.old.bean.Cart;
import com.jd.purchase.domain.old.bean.Order;
import com.jd.purchase.domain.old.bean.SKU;
import com.jdh.o2oservice.application.foward.MapperHandler;
import com.jdh.o2oservice.application.trade.service.JdOrderMsgApplication;
import com.jdh.o2oservice.application.trade.util.OrderEntityBoUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.enums.OrderMsgTypeEnum;
import com.jdh.o2oservice.base.enums.OrderSplitTypeEnum;
import com.jdh.o2oservice.base.enums.OrderTypeEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.trade.bo.OrderEntityBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName:PopOrderNosplitMsgHandler
 * @Description: pop订单不拆单消息handler
 * @Author: yaoqinghai
 * @Date: 2023/12/27 10:09
 * @Vserion: 1.0
 **/
@Slf4j
@Service
public class PopOrderNosplitMsgHandler extends AbstractPopLocHandler<Order> implements MapAutowiredKey {

    @Value("${topics.order.pop-middleware-no-split}")
    private String handlerTopic;

    @Resource
    private JdOrderMsgApplication jdOrderApplication;

    @Resource
    private Cluster jimClient;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private MapperHandler mapperHandler;

    @Value("${topics.dada.pop-middleware-no-split:o2o_reservation_0_276_v2}")
    private String dadaPopMiddlewareNoSplitTopicYf;

    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    /**
     * 在解析消息体前进行消息处理的判断，不需要的消息直接过滤点或者不进行处理（使用场景消息处理限流等）
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param message
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean preFilterOfDiscardMessage(Message message) {
        log.info("[PopOrderNosplitMsgHandler->preFilterOfDiscardMessage],msgText={}", message.getText());
        //判断是否转发给达达
        try{
            Order order = jdOrderApplication.fireSerializersOrderMsg(message, OrderMsgTypeEnum.NO_SPLIT_ORDER);
            OrderEntityBO orderEntityBO = OrderEntityBoUtil.getOrderEntity(order);
            if(duccConfig.getPopForwardMqGlobalSwitch() && mapperHandler.handler(orderEntityBO.getSkuNo(),"skuIdMapper")){
                log.info("[PopOrderNosplitMsgHandler->dealMessage] 达达拆分-pop数据不处理 jdOrder={}", JSON.toJSONString(orderEntityBO));
                message.setTopic(dadaPopMiddlewareNoSplitTopicYf);
                log.info("PopOrderNosplitMsgHandler -> eventBody转投到达达环境. message={}",JSON.toJSONString(message));
                reachStoreProducer.send(message);
                return true;
            }
        }catch (Exception ex){
            log.info("PopOrderNosplitMsgHandler -> eventBody转投到达达环境异常,消费医疗处理",ex);
        }
        return jdOrderApplication.orderCountLimit();
    }

    /**
     * 解析消息
     *
     * @param message
     * @return
     */
    @Override
    public Order analysisMessage(Message message) {
        //1、反序列化mq消息为Order对象
        return jdOrderApplication.fireSerializersOrderMsg(message, OrderMsgTypeEnum.NO_SPLIT_ORDER);
    }

    /**
     * 根据接到的mq消息解析出的对象，丢弃业务不需要的消息
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param order
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean filterOfDiscardMessage(Order order) {
        log.info("[PopOrderNosplitMsgHandler->filterOfDiscardMessage],orderId={}", order.getOrderId());
        // 1.判断订单类型是否为空
        if(Objects.isNull(order)){
            log.info("PopOrderNosplitMsgHandler -> filterOfDiscardMessage, order is null");
            return true;
        }
        // 2.订单类型过滤（200） 检验单是22和75 224
        if (!OrderTypeEnum.checkPopNoSplitHandleOrderType(order.getOrderType())) {
            log.info("PopOrderNosplitMsgHandler -> filterOfDiscardMessage end, 订单类型不是200,跳过该订单,orderId={}, orderType={}", order.getOrderId(), order.getOrderType());
            return true;
        }

        // 3.拆分类型过滤，2,3 已支付 7 待支付
        int splitType = order.getSplitType();
        if (!OrderSplitTypeEnum.checkOrderPaid(splitType)) {
            log.info("PopOrderNosplitMsgHandler -> filterOfDiscardMessage end, 订单未拆分成功，跳过该消息,orderId={}, splitType={}", order.getOrderId(), splitType);
            return true;
        }

        return filterPopLoc(order);
    }

    /**
     * 转投环境
     *
     * @param message
     * @param order
     * @return
     */
    @Override
    public boolean transferToYf(Message message, Order order) {
        return false;
    }

    /**
     * 业务处理消息
     *
     * @param order
     * @return
     */
    @Override
    public void dealMessage(Order order) {
        //这里redisKey与老的不拆单区分【REPEAT_CHECK_${orderId}_${splitType}】
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.REPEAT_CHECK_PRE, order.getOrderId(), order.getSplitType());
        // 5.防重
        if(jimClient.exists(redisKey)){
            log.info("PopOrderNosplitMsgHandler -> filterOfDiscardMessage, businessId is exists,orderId={}",redisKey);
            return;
        }
        jimClient.setEx(redisKey, CommonConstant.REDIS_DEFAULT_VALUE, 1, TimeUnit.HOURS);
        try{
            jdOrderApplication.dealMessage(order);
        }catch (Exception ex){
            log.error("[PopOrderNosplitMsgHandler->dealMessage],处理订单不拆单消息失败!", ex);
            jimClient.del(redisKey);
            throw ex;
        }
    }

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return handlerTopic;
    }
}
