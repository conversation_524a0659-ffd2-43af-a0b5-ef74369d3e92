package com.jdh.o2oservice.listener.handler;

import com.alibaba.fastjson.JSON;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jd.ofc.domain.Order;
import com.jdh.o2oservice.application.foward.MapperHandler;
import com.jdh.o2oservice.application.trade.service.JdOrderMsgApplication;
import com.jdh.o2oservice.application.trade.util.OrderEntityBoUtil;
import com.jdh.o2oservice.base.enums.OrderMsgTypeEnum;
import com.jdh.o2oservice.base.enums.OrderSplitTypeEnum;
import com.jdh.o2oservice.base.enums.OrderTypeEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.trade.bo.OrderEntityBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;

/**
 * @ClassName:PopOrderSplitMsgHandler
 * @Description: pop订单拆单消息handler
 * @Author: yaoqinghai
 * @Date: 2024/1/9 22:32
 * @Vserion: 1.0
 **/
@Slf4j
@Service
public class PopOrderSplitMsgHandler extends AbstractPopLocHandler<List<Order>> implements MapAutowiredKey {


    @Value("${topics.order.pop-middleware-split}")
    private String handlerTopic;

    @Resource
    private JdOrderMsgApplication popOrderSplitMsgApplication;

    @Resource
    private Cluster jimClient;

    @Value("${topics.dada.pop-middleware-split:o2o_reservation_0_275_v2}")
    private String dadaPopMiddlewareSplitTopicYf;

    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Resource
    private MapperHandler mapperHandler;

    /**
     * 在解析消息体前进行消息处理的判断，不需要的消息直接过滤点或者不进行处理（使用场景消息处理限流等）
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param message
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean preFilterOfDiscardMessage(Message message) {
        //判断是否转发给达达
        try{
            com.jd.purchase.domain.old.bean.Order order = popOrderSplitMsgApplication.fireSerializersOrderMsg(message, OrderMsgTypeEnum.SPLIT_ORDER);
            OrderEntityBO orderEntityBO = OrderEntityBoUtil.getOrderEntity(order);
            if(duccConfig.getPopForwardMqGlobalSwitch() && mapperHandler.handler(orderEntityBO.getSkuNo(),"skuIdMapper")){
                log.info("[PopOrderSplitMsgHandler->dealMessage] 达达拆分-pop数据不处理 jdOrder={}", JSON.toJSONString(orderEntityBO));
                message.setTopic(dadaPopMiddlewareSplitTopicYf);
                log.info("PopOrderSplitMsgHandler -> eventBody转投到达达环境. message={}",JSON.toJSONString(message));
                reachStoreProducer.send(message);
                return true;
            }
        }catch (Exception ex){
            log.info("PopOrderSplitMsgHandler -> eventBody转投到达达环境异常 消费医疗处理",ex);
        }

        return popOrderSplitMsgApplication.orderCountLimit();
    }

    /**
     * 解析消息
     *
     * @param message
     * @return
     */
    @Override
    public List<Order> analysisMessage(Message message) {
        //1、反序列化mq消息为Order对象
        return popOrderSplitMsgApplication.fireSerializersChildOrderMsg(message);
    }

    /**
     * 根据接到的mq消息解析出的对象，丢弃业务不需要的消息
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param orders
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean filterOfDiscardMessage(List<Order> orders) {
        Iterator<Order> iterator = orders.iterator();
        while (iterator.hasNext()) {
            Order order = iterator.next();
            long orderId = order.getOrderId();
            try {
                int splitType = order.getSplitType();
                //1、拆分类型过滤，2 已支付 3 不需要拆分
                if (OrderSplitTypeEnum.SPLIT_TYPE_INT_2.getSplitType() != splitType) {
                    log.info("PopOrderSplitMsgHandler -> processMessage end, 订单未拆分成功，跳过该消息 splitType={}, orderId={}", splitType, orderId);
                    iterator.remove();
                    continue;
                }
                //2、订单类型过滤（22和75）
                if (!OrderTypeEnum.checkPopNoSplitHandleOrderType(order.getOrderType())) {
                    log.info("PopOrderSplitMsgHandler -> processMessage end, 订单类型不是22,75，跳过改订单 orderType={}, orderId={}",
                            order.getOrderType(), orderId);
                    iterator.remove();
                    continue;
                }
                // 3、服务类目 需要被过滤
                if (filterPopLoc(order)) {
                    iterator.remove();
                    continue;
                }
            } catch (Exception e) {
                log.error("PopOrderSplitMsgHandler -> dealChildOrder exception, orderId={}", orderId, e);
                throw new RuntimeException("PopOrderSplitMsgHandler dealChildOrder ERROR orderId = " + orderId);
            }
        }
            // 没有符合处理的子订单，返回false 继续走后面的流程
        if (CollectionUtils.isNotEmpty(orders)){
            return Boolean.FALSE;
            // 返回true，没有需要处理的子单，终止流程
        }else{
            return Boolean.TRUE;
        }
    }

    /**
     * 转投环境
     *
     * @param message
     * @param orders
     * @return
     */
    @Override
    public boolean transferToYf(Message message, List<Order> orders) {
        // 判断当前pin是否在转投预发白名单
        if(CollectionUtils.isEmpty(orders)){
            return false;
        }
        for (Order order : orders) {
            if (duccConfig.checkBlankPin(order.getPin())) {
                log.info("PopOrderNosplitMsgHandler -> filterOfDiscardMessage, 订单转投到预发环境. orderId={}", order.getOrderId());
                return true;
            }
        }
        return false;
    }

    /**
     * 业务处理消息
     *
     * @param orders
     * @return
     */
    @Override
    public void dealMessage(List<Order> orders) {
        Order firstOrder = orders.get(0);
        // 1.防重
        if(jimClient.exists(RedisKeyEnum.getRedisKey(RedisKeyEnum.POP_ORDER_SPLIT_KEY, firstOrder.getOrderId()))){
            log.info("PopOrderSplitMsgHandler -> processMessage, businessId is exists,businessId={}",firstOrder.getOrderId());
            return;
        }
        jimClient.setEx(RedisKeyEnum.getRedisKey(RedisKeyEnum.POP_ORDER_SPLIT_KEY, firstOrder.getOrderId()), "1",
                RedisKeyEnum.POP_ORDER_SPLIT_KEY.getExpireTime(), RedisKeyEnum.POP_ORDER_SPLIT_KEY.getExpireTimeUnit());
        try{
            for (Order order : orders) {
                popOrderSplitMsgApplication.dealMessage(order);
            }
        }catch (Exception ex){
            log.error("[PopOrderSplitMsgHandler->dealMessage],处理订单拆单消息失败!", ex);
            jimClient.del(RedisKeyEnum.getRedisKey(RedisKeyEnum.POP_ORDER_SPLIT_KEY, firstOrder.getOrderId()));
            throw ex;
        }
    }

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return handlerTopic;
    }
}
