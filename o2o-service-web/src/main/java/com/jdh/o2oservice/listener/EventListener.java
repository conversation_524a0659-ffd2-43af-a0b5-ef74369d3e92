package com.jdh.o2oservice.listener;


import com.alibaba.fastjson.JSON;
import com.jd.binlog.client.EntryMessage;
import com.jd.binlog.client.MessageDeserialize;
import com.jd.binlog.client.WaveEntry;
import com.jd.binlog.client.impl.JMQMessageDeserialize;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.foward.MapperHandler;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerTask;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 事件MQ监听器
 *
 * @author: yangxiyu
 * @date: 2022/4/20 2:26 下午
 * @version: 1.0
 */
@Service("eventListener")
@Slf4j
public class EventListener {

    @Resource
    private MapperHandler mapperHandler;

    /**
     * 序列化器
     */
    private static final MessageDeserialize<Message> DESERIALIZE = new JMQMessageDeserialize();
    /** eventConsumerTask:{eventId}:{consumerCode} */
    /**
     * 表名称
     */
    private static final String CONSUMER_TABLE_NAME = "jdh_event_consumer";
    private static final String EVENT_TABLE_NAME = "jdh_event_record";

    /**
     *
     */
    @Resource
    @Lazy
    private EventCoordinator eventCoordinator;

    @Value("${topics.dada.consumertask:}")
    private String dadaEventConsumerTopic;

    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Autowired
    private DuccConfig duccConfig;


    /**
     * 处理事件表的binlog
     * 1、处理事件发布表(jdh_event_record)的binlog，解决生产者延迟发布事件的逻辑，通过binlog将事件转投到延迟队列，达到延迟时间之后再真实发布。
     * 2、处理事件消费表(jdh_event_consumer)的binlog，会将消费任务按照随机策略打散到不同的topic，解决某个事件消费有问题导致的单点问题。
     *
     * @param messages
     * @throws Exception
     */
    @JmqListener(id = "jdhReachStoreConsumer", topics = {"${topics.event.consumer.topic}", "${topics.event.publish.topic}"}, delayedStart = 60)
    public void transferBinlogMessage(List<Message> messages) throws Exception {

        if (messages == null || messages.isEmpty()) {
            return;
        }
        List<EntryMessage> entryMessages = DESERIALIZE.deserialize(messages);
        //获取消息类型：TRANSACTIONBEGIN、ROWDATA、TRANSACTIONEND、HEARTBEAT
        for (EntryMessage entryMessage : entryMessages) {
            if (null == entryMessage.getRowChange()) {
                continue;
            }
            if (!entryMessage.getEntryType().equals(WaveEntry.EntryType.ROWDATA)) {
                continue;
            }
            if (CONSUMER_TABLE_NAME.equals(entryMessage.getHeader().getTableName())) {
                processConsumerBinlog(entryMessage);

                // 处理延迟发布事件
            } else if (EVENT_TABLE_NAME.equals(entryMessage.getHeader().getTableName())) {
                processDelayEvent(entryMessage);
            }

        }
    }


    /**
     * 处理消费任务的监听器，听到任务后，调用eventCoordinator.consumerTask 执行任务。
     *
     * @param messages
     * @throws Exception
     */
    @JmqListener(id = "jdhReachStoreConsumer", topics = {"${topics.event.consumer.distribute.topic1}", "${topics.event.consumer.distribute.topic2}", "${topics.event.consumer.distribute.topic3}"}, delayedStart = 60)
    public void consumerTask(List<Message> messages){
        if (messages == null || messages.isEmpty()) {
            return;
        }
        for (Message message : messages) {
            String text = message.getText();
            try {
                EventConsumerTask task = JSON.parseObject(text, EventConsumerTask.class);
                try{
                    if(duccConfig.getPopForwardMqGlobalSwitch() && mapperHandler.isSupportEventId(task.getEventId())){
                        message.setTopic(dadaEventConsumerTopic);
                        reachStoreProducer.send(message);
                        continue;
                    }
                }catch (Exception ex){
                    log.info("EventListener.consumerTask 转发达达异常,消费医疗处理",ex);
                }
                eventCoordinator.consumerTask(task);
            } catch (Exception e) {
                log.error("EventListener->consumerTask text={}", text, e);
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 延迟事件到达延迟时间后开始处理
     *
     * @param messages
     * @throws Exception
     */
    @JmqListener(id = "jdhReachStoreConsumer", topics = {"${topics.delay.event.transfer.topic}"}, delayedStart = 60)
    public void delayEventArrive(List<Message> messages) throws Exception {

        if (messages == null || messages.isEmpty()) {
            return;
        }
        for (Message message : messages) {
            String text = message.getText();
            Event event = JSON.parseObject(text, Event.class);
            eventCoordinator.delayArrive(event);
        }
    }

    /**
     * 延迟事件到达延迟时间后开始处理
     *
     * @param messages
     * @throws Exception
     */
    @JmqListener(id = "jdhReachStoreConsumer", topics = {"${topics.event.failBack.topic}"}, delayedStart = 60)
    public void failBack(List<Message> messages) throws Exception {

        if (messages == null || messages.isEmpty()) {
            return;
        }
        for (Message message : messages) {
            String text = message.getText();
            EventConsumerTask task = JSON.parseObject(text, EventConsumerTask.class);
            eventCoordinator.consumerTaskExecute(task);
        }
    }


    /**
     * @param columns
     * @return
     */
    private Event convertEvent(List<WaveEntry.Column> columns) {
        Event event = new Event();
        for (WaveEntry.Column column : columns) {
            if ("event_id".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setEventId(Long.valueOf(column.getValue()));

            } else if ("domain_code".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setDomainCode(column.getValue());
            } else if ("aggregate_code".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setAggregateCode(column.getValue());
            } else if ("aggregate_id".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setAggregateId(column.getValue());
            } else if ("event_code".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setEventCode(column.getValue());

            } else if ("publish_time".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                String publishTimeStr = column.getValue();
                log.info("convertEvent->publishTimeStr={}", publishTimeStr);
                event.setPublishTime(TimeUtils.timeStrToLocalDate(publishTimeStr, TimeFormat.LONG_PATTERN_LINE));
            } else if ("body".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setBody(column.getValue());
            } else if ("score".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setScore(Integer.valueOf(column.getValue()));
            } else if ("consumer_ids".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setConsumerCodes(column.getValue());
            } else if ("version".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setVersion(Integer.valueOf(column.getValue()));
            } else if ("trace_id".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setTraceId(column.getValue());
            } else if ("delay".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                event.setDelay(YnStatusEnum.convert(Integer.valueOf(column.getValue())));
            }
        }
        return event;
    }


    private EventConsumerTask convertEventTask(List<WaveEntry.Column> columns) {
        EventConsumerTask task = new EventConsumerTask();
        for (WaveEntry.Column column : columns) {
            if ("event_id".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                task.setEventId(Long.valueOf(column.getValue()));

            } else if ("domain_code".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                task.setDomainCode(column.getValue());
            } else if ("event_code".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                task.setEventCode(column.getValue());
            } else if ("consumer_code".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                task.setConsumerCode(column.getValue());
            } else if ("status".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                task.setStatus(Integer.valueOf(column.getValue()));
            } else if ("async".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                task.setIsAsync(YnStatusEnum.convert(Integer.valueOf(column.getValue())));
            } else if ("num".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                task.setNum(Integer.valueOf(column.getValue()));
            } else if ("branch".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                task.setBranch(column.getValue());
            }
        }
        return task;
    }


    /**
     * 处理延迟事件，事件会被投递到延迟队列，当达到延迟时间时，事件才会发布 去创建消费者任务task
     *
     * @param entryMessage
     */
    private void processDelayEvent(EntryMessage entryMessage) {
        List<WaveEntry.RowData> rowDatas = entryMessage.getRowChange().getRowDatasList();
        log.info("EventListener -> processDelayEvent processEvent");
        for (WaveEntry.RowData rowData : rowDatas) {
            WaveEntry.EventType eventType = entryMessage.getHeader().getEventType();
            Long eventId = null;
            Boolean delay = null;
            try {
                if (WaveEntry.EventType.INSERT == eventType) {
                    List<WaveEntry.Column> columns = rowData.getAfterColumnsList();
                    for (WaveEntry.Column column : columns) {
                        if ("delay".equals(column.getName()) && StringUtils.isNotEmpty(column.getValue())) {
                            delay = YnStatusEnum.convert(Integer.valueOf(column.getValue()));
                            break;
                        }
                    }
                    if (Objects.nonNull(delay) && delay) {
                        Event event = convertEvent(columns);
                        eventCoordinator.transferDelayEvent(event);
                    }
                }
            } catch (Throwable e) {
                log.error("EventListener -> processDelayEvent Throwable eventId={}", eventId, e);
                throw new BusinessException(SystemErrorCode.UNKNOWN_ERROR);
            }
        }
    }


    /**
     * 处理消费者task
     *
     * @param entryMessage
     */
    private void processConsumerBinlog(EntryMessage entryMessage) {
        List<WaveEntry.RowData> rowDatas = entryMessage.getRowChange().getRowDatasList();
        log.info("EventListener -> processConsumerBinlog");
        for (WaveEntry.RowData rowData : rowDatas) {
            WaveEntry.EventType eventType = entryMessage.getHeader().getEventType();
            Long eventId = null;
            String consumerCode = null;
            try {
                if (WaveEntry.EventType.INSERT == eventType) {
                    EventConsumerTask task = convertEventTask(rowData.getAfterColumnsList());
                    // 老的延迟事件会在发布事件时就插入消费任务task，然后直接消费延迟队列处理，这里需要过滤调灰度期间发布的老的延迟事件task binlog
                    if (!StringUtils.equals(task.getBranch(), "isJmq")) {
                        consumerCode = task.getConsumerCode();
                        eventId = task.getEventId();
                        log.info("EventListener -> processConsumerBinlog eventId={}, consumerCode={}", task.getEventId(), task.getConsumerCode());
                        eventCoordinator.distributeEvent(task);
                    }
                }
            } catch (BusinessException e) {
                log.error("EventListener -> processConsumerBinlog business error eventId={}, consumerCode={}, msg={}", eventId, consumerCode, e.getMessage());
                throw e;
            } catch (Exception e) {
                log.error("EventListener -> processConsumerBinlog Exception  eventId={}, consumerCode={}", eventId, consumerCode, e);
                throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
            } catch (Throwable e) {
                log.error("EventListener -> processConsumerBinlog Throwable eventId={}, consumerCode={}", eventId, consumerCode, e);
                throw new BusinessException(SystemErrorCode.UNKNOWN_ERROR);
            }
        }
    }
}
