package com.jdh.o2oservice.listener.afs;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.jmq.common.message.Message;
import com.jd.laf.config.Property;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.SendpayValueEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherExtend;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.VoucherRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.trade.bo.AfsBo;
import com.jdh.o2oservice.core.domain.trade.bo.AfsDuccBo;
import com.jdh.o2oservice.core.domain.trade.bo.AfsRulesDuccBo;
import com.jdh.o2oservice.core.domain.trade.bo.AfsServiceDetail;
import com.jdh.o2oservice.core.domain.trade.context.OrderInfoQueryContext;
import com.jdh.o2oservice.core.domain.trade.enums.OrderExtTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderExtRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.rpc.OrderInfoRpc;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.VoucherPageRequest;
import com.jdh.o2oservice.listener.handler.AbstractHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-07 14:33
 * @Desc : 消费订单表（jd_order）binlake
 */
@Slf4j
@Service("jdAfsListener")
public class JdAfsListener extends AbstractHandler<AfsBo> implements MapAutowiredKey {
    /**
     * 售后消息
     */
    @Value("${topics.afs.afsTopic}")
    private String handlerTopic;

    /**
     * orderInfoRpc
     */
    @Resource
    private OrderInfoRpc orderInfoRpc;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;

    /**
     * jdOrderExtRepository
     */
    @Resource
    private JdOrderExtRepository jdOrderExtRepository;

    /**
     * voucherApplication
     */
    @Resource
    private VoucherApplication voucherApplication;

    /**
     * voucherRepository
     */
    @Resource
    VoucherRepository voucherRepository;

    /**
     * commonDuccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * dongDongRobotRpc
     */
    @Resource
    private DongDongRobotRpc dongDongRobotRpc;

    /**
     * 缓存
     */
    private static final Map<String, List<AfsDuccBo>> CACHE = Maps.newHashMap();

    /**
     * KEY
     */
    private static final String CACHE_KEY = "afsRulesList";

    /**
     * 获取map key
     *
     * @return
     */
    @Override
    public String getMapKey() {
        return handlerTopic;
    }

    /**
     * 在解析消息体前进行消息处理的判断，不需要的消息直接过滤点或者不进行处理（使用场景消息处理限流等）
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param message
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean preFilterOfDiscardMessage(Message message) {
        return false;
    }

    /**
     * 解析消息
     *
     * @param message
     * @return
     */
    @Override
    public AfsBo analysisMessage(Message message) {
        if (message == null || StringUtils.isBlank(message.getText())) {
            return null;
        }
        if (!Boolean.TRUE.equals(duccConfig.getAfsRulesSwitch())) {
            log.info("JdAfsListener afsRulesSwitch is false");
            return null;
        }
        return JSON.parseObject(message.getText(), AfsBo.class);
    }

    /**
     * 根据接到的mq消息解析出的对象，丢弃业务不需要的消息
     * 如不需要这个过滤可给空实现(直接返回false，消息不被丢弃)
     *
     * @param afsBo
     * @return true ：【丢弃】当前消息  false ：【保留】当前消息，后续对消息进行处理
     */
    @Override
    public boolean filterOfDiscardMessage(AfsBo afsBo) {
        if (afsBo == null) {
            return true;
        }
        List<AfsDuccBo> duccBo = CACHE.get(CACHE_KEY);
        if (CollUtil.isEmpty(duccBo)) {
            return false;
        }
        boolean discard = true;
        for (AfsDuccBo afsDuccBo : duccBo) {
            discard = !afsDuccBo.getStepTypeFilterSets().containsKey(afsBo.getStepType()) || (CollUtil.isNotEmpty(afsDuccBo.getStepTypeFilterSets()) && !afsDuccBo.getStepTypeFilterSets().getOrDefault(afsBo.getStepType(), Collections.emptySet()).contains(afsBo.getAfsResultType()));
            log.info("JdAfsListener filterOfDiscardMessage orderId={},orderType={},discard={}", afsBo.getOrderId(), afsBo.getOrderType(), discard);
            if (Boolean.FALSE.equals(discard)) {
                break;
            }
        }
        return discard;
    }

    /**
     * 转投环境
     *
     * @param message
     * @param afsBo
     * @return
     */
    @Override
    public boolean transferToYf(Message message, AfsBo afsBo) {
        return false;
    }

    /**
     * 业务处理消息
     *
     * @param afsBo
     * @return
     */
    @Override
    @LogAndAlarm
    public void dealMessage(AfsBo afsBo) {
        OrderInfoQueryContext context = new OrderInfoQueryContext();
        context.setOrderId(String.valueOf(afsBo.getOrderId()));
        context.setUserPin(afsBo.getCustomerPin());
        JdOrder jdOrder = orderInfoRpc.queryOrderInfo(context);
        if (jdOrder == null) {
            log.error("JdAfsListener dealMessage jdOrder is null orderId={}", afsBo.getOrderId());
            return;
        }
        if (CollUtil.isEmpty(jdOrder.getSendPayMap())) {
            log.error("JdAfsListener dealMessage sendPayMap is null orderId={}", afsBo.getOrderId());
            return;
        }
        log.info("JdAfsListener dealMessage={}", JSON.toJSONString(afsBo));
        List<AfsDuccBo> duccBoList = CACHE.get(CACHE_KEY);
        if (CollUtil.isEmpty(duccBoList)) {
            log.error("JdAfsListener dealMessage rules is null orderId={}", afsBo.getOrderId());
            return;
        }

        Map<String, Object> sceneExpressionParam = new HashMap<>();
        sceneExpressionParam.put("order", jdOrder);
        AfsDuccBo duccBo = null;
        for (AfsDuccBo afsDuccBo : duccBoList) {
            if ((Boolean) AviatorEvaluator.compile(afsDuccBo.getSceneExpression(), Boolean.TRUE).execute(sceneExpressionParam)) {
                duccBo = afsDuccBo;
                break;
            }
        }
        if (duccBo == null) {
            log.info("JdAfsListener dealMessage 非快递检测订单不处理 orderId={}", afsBo.getOrderId());
            return;
        }

        // 保存服务单记录
        this.saveAfsRecord(afsBo);

        Map<String, Object> param = new HashMap<>();
        param.put("afsBo", afsBo);
        for (AfsRulesDuccBo rulesDuccBo : duccBo.getRulesDuccList()) {
            if ((Boolean)AviatorEvaluator.compile(rulesDuccBo.getExpress(), Boolean.TRUE).execute(param)) {
                switch (rulesDuccBo.getOperationType()) {
                    case 1:
                        orderStatusToRefunding(afsBo);
                        break;
                    case 2:
                        orderStatusToAfsSuccess(afsBo);
                        break;
                    case 3:
                        orderStatusToAfsFail(afsBo);
                        break;
                    case 4:
                        orderStatusToAfsFinish(afsBo);
                        break;
                }
                break;
            }
        }

        // 通过订单扩展表售后记录计算voucher冻结数量
        calculateVoucherFreezeNum(afsBo.getOrderId(), duccBo);
    }

    /**
     * 售后成功，扣减promiseNum
     */
    private void orderStatusToAfsSuccess(AfsBo afsBo) {
        Long orderId = afsBo.getOrderId();
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderId).build());
        if (jdOrder == null) {
            log.error("JdAfsListener orderStatusToAfsSuccess jdOrder is null orderId={}", orderId);
            return;
        }

        VoucherPageRequest request = VoucherPageRequest.builder()
                .sourceVoucherId(String.valueOf(orderId)).build();
        List<VoucherDto> voucherList = voucherApplication.queryVoucherList(request);
        if (CollUtil.isEmpty(voucherList)) {
            log.error("JdAfsListener orderStatusToAfsSuccess voucherList is null orderId={}", orderId);
            return;
        }
        if (CollUtil.isEmpty(afsBo.getDetails())) {
            log.error("JdAfsListener orderStatusToAfsSuccess afsBo.getDetails() is empty orderId={}", orderId);
            return;
        }
        // 商品类型过滤主商品 https://joyspace.jd.com/pages/r7SP2jeI3QVrODWP1Njh
        int deductionNum = afsBo.getDetails().stream().filter(s -> s.getWareType().equals(10)).mapToInt(AfsServiceDetail::getWareNum).sum();

        List<VoucherDto> voucherDtos = voucherList.stream().sorted(Comparator.comparing(VoucherDto::getPromiseNum).reversed()).collect(Collectors.toList());
        boolean isSuccess = true;
        for (VoucherDto voucherDto : voucherDtos) {
            int promiseNum;
            if (deductionNum >= voucherDto.getPromiseNum()) {
                promiseNum = 0;
                deductionNum = deductionNum - voucherDto.getPromiseNum();
            } else {
                promiseNum = voucherDto.getPromiseNum() - deductionNum;
            }
            log.info("JdAfsListener orderStatusToAfsSuccess orderId={} voucherId={} promiseNum={}", orderId, voucherDto.getVoucherId(), promiseNum);
            JdhVoucher jdhVoucher = voucherRepository.find(JdhVoucherIdentifier.builder().voucherId(voucherDto.getVoucherId()).build());
            jdhVoucher.setPromiseNum(promiseNum);
            int count = voucherRepository.save(jdhVoucher);
            if (count == 0) {
                isSuccess = false;
                log.error("JdAfsListener orderStatusToAfsSuccess 扣减服务失败 orderId={} voucherId={} promiseNum={}", orderId, voucherDto.getVoucherId(), promiseNum);
                // 如果是一个服务单更新失败，抛异常重试，如果是多个，抛异常重试可能导致重复扣减。
                if (voucherDtos.size() == 1) {
                    throw new BusinessException(TradeErrorCode.VOUCHER_DEDUCTION_FAIL);
                } else {
                    String message = String.format("售后服务扣减失败,orderId=%s", orderId);
                    sendAlarm(message);
                }
            }
        }
        if (isSuccess) {
            rollBackOrderStatus(afsBo, 2);
        }

    }

    /**
     * 发起售后订单设置为退款中
     */
    private void orderStatusToRefunding(AfsBo afsBo) {
        Long orderId = afsBo.getOrderId();
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderId).build());
        if (jdOrder == null) {
            log.error("JdAfsListener orderStatusToRefunding jdOrder is null orderId={}", orderId);
            return;
        }
        Integer currOrderStatus = jdOrder.getOrderStatus();
        JdOrderExt jdOrderExt = jdOrderExtRepository.findJdOrderExtDetail(orderId, OrderExtTypeEnum.AFS_BEFORE_ORDER_STATUS.getType());
        boolean isInsert = jdOrderExt == null;
        JdOrderExt saveJdOrderExt = new JdOrderExt();
        saveJdOrderExt.setExtType(OrderExtTypeEnum.AFS_BEFORE_ORDER_STATUS.getType());
        saveJdOrderExt.setExtContext(String.valueOf(currOrderStatus));
        saveJdOrderExt.setOrderId(orderId);
        if (isInsert) {
            jdOrderExtRepository.save(saveJdOrderExt);
        } else {
            jdOrderExtRepository.updateJdOrderExt(saveJdOrderExt);
        }
        jdOrderRepository.updateOrderStatusByOrderId(JdOrder.builder().orderId(orderId).orderStatus(OrderStatusEnum.ORDER_REFUNDING.getStatus()).build());
    }

    /**
     * 售后服务失败回滚订单状态
     */
    private void orderStatusToAfsFail(AfsBo afsBo) {
        rollBackOrderStatus(afsBo, 3);
    }

    /**
     * 售后服务完成回滚订单状态
     */
    private void orderStatusToAfsFinish(AfsBo afsBo) {
        rollBackOrderStatus(afsBo, 4);
    }

    /**
     * 回滚订单状态
     * @param afsBo
     * @param operationType
     */
    private void rollBackOrderStatus(AfsBo afsBo, Integer operationType) {
        Long orderId = afsBo.getOrderId();
        JdOrder jdOrder = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderId).build());
        if (jdOrder == null) {
            log.error("JdAfsListener orderStatusToRefunding jdOrder is null orderId={}", orderId);
            return;
        }
        // 只处理退款中订单
        if (OrderStatusEnum.ORDER_REFUNDING.getStatus().equals(jdOrder.getOrderStatus())) {
            JdOrderExt jdOrderExt = jdOrderExtRepository.findJdOrderExtDetail(orderId, OrderExtTypeEnum.AFS_BEFORE_ORDER_STATUS.getType());
            boolean notExist = jdOrderExt == null;
            if (notExist) {
                String message = String.format("售后服务回滚订单状态,订单扩展表信息为空,orderId=%s,operationType=%s", orderId, operationType);
                sendAlarm(message);
                return;
            }
            int count = jdOrderRepository.updateOrderStatusByOrderId(JdOrder.builder().orderId(orderId).orderStatus(Integer.parseInt(jdOrderExt.getExtContext())).build());
            if (count == 0) {
                String message = String.format("售后服务回滚订单状态失败,orderId=%s,operationType=%s", orderId, operationType);
                sendAlarm(message);
            }
        } else {
            log.info("JdAfsListener orderStatusToAfsFail 售后失败,订单状态非退款中,不处理 orderId={},operationType={}", orderId, operationType);
        }
    }


    /**
     * 动态变更ducc key value
     * @param property
     */
    @LafValue("afsRules")
    public void onPropertyChange(Property property){
        if (property == null || StringUtils.isBlank(property.getKey()) || property.getValue() == null) {
            log.info("JdAfsListener onPropertyChange property is null");
            return;
        }
        List<AfsDuccBo> afsRulesDuccBo = JSON.parseArray(property.getValue().toString(), AfsDuccBo.class);
        CACHE.remove(CACHE_KEY);
        CACHE.put(CACHE_KEY, afsRulesDuccBo);
    }

    /**
     * 预警
     */
    private void sendAlarm(String message) {
        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get("非快检售后消息处理失败");
        if (Objects.isNull(jsonObject) || StringUtil.isBlank(jsonObject.getString("groupId"))) {
            log.info("JdAfsListener -> alarm disable robotAlarmMap={}", JsonUtil.toJSONString(robotAlarmMap));
            return;
        }
        dongDongRobotRpc.sendDongDongRobotMessage(message,jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
    }

    /**
     * 保存售后服务单记录
     * @param afsBo
     */
    private void saveAfsRecord(AfsBo afsBo) {
        JdOrderExt jdOrderExtQuery = new JdOrderExt();
        jdOrderExtQuery.setOrderId(afsBo.getOrderId());
        jdOrderExtQuery.setExtType(OrderExtTypeEnum.AFS_SERVICE_RECORDS.getType());
        jdOrderExtQuery.setExtId(afsBo.getAfsServiceId());
        List<JdOrderExt> jdOrderExt = jdOrderExtRepository.findJdOrderExtList(jdOrderExtQuery);
        boolean isInsert = CollUtil.isEmpty(jdOrderExt);
        JdOrderExt saveJdOrderExt = new JdOrderExt();
        saveJdOrderExt.setExtType(OrderExtTypeEnum.AFS_SERVICE_RECORDS.getType());
        saveJdOrderExt.setExtContext(JSON.toJSONString(afsBo));
        saveJdOrderExt.setOrderId(afsBo.getOrderId());
        saveJdOrderExt.setExtId(afsBo.getAfsServiceId());
        log.info("JdAfsListener saveAfsRecord={} isInsert={}", JsonUtil.toJSONString(saveJdOrderExt), isInsert);
        if (isInsert) {
            jdOrderExtRepository.save(saveJdOrderExt);
        } else {
            jdOrderExtRepository.updateJdOrderExt(saveJdOrderExt);
        }
    }

    /**
     * 计算voucher冻结数量
     */
    private void calculateVoucherFreezeNum(Long orderId, AfsDuccBo duccBo) {
        JdOrderExt jdOrderExtQuery = new JdOrderExt();
        jdOrderExtQuery.setOrderId(orderId);
        jdOrderExtQuery.setExtType(OrderExtTypeEnum.AFS_SERVICE_RECORDS.getType());
        AtomicReference<Integer> freezeNum = new AtomicReference<>(0);
        List<JdOrderExt> jdOrderExtList = jdOrderExtRepository.findJdOrderExtList(jdOrderExtQuery);
        if (CollUtil.isNotEmpty(jdOrderExtList)) {
            jdOrderExtList.forEach(s -> {
                String context = s.getExtContext();
                if (StringUtils.isBlank(context)) {
                    return;
                }

                AfsBo afsBo = JSON.parseObject(context, AfsBo.class);
                Map<String, Object> param = new HashMap<>();
                param.put("afsBo", afsBo);
                for (AfsRulesDuccBo rulesDuccBo : duccBo.getRulesDuccList()) {
                    if ((Boolean)AviatorEvaluator.compile(rulesDuccBo.getExpress(), Boolean.TRUE).execute(param)) {
                        switch (rulesDuccBo.getOperationType()) {
                            case 1:
                                freezeNum.updateAndGet(v -> v + 1);
                            case 2:
                            case 3:
                            case 4:
                        }
                    }
                }
            });
        }

        List<JdhVoucher> jdhVoucherList = voucherRepository.listByQuery(VoucherRepQuery.builder().sourceVoucherId(String.valueOf(orderId)).build());
        if (CollUtil.isNotEmpty(jdhVoucherList)) {
            for (JdhVoucher jdhVoucher : jdhVoucherList) {
                JdhVoucherExtend extend = jdhVoucher.getExtend();
                if (extend == null) {
                    extend = new JdhVoucherExtend();
                }
                extend.setFreezeNum(freezeNum.get());
                int count = voucherRepository.save(jdhVoucher);
                log.info("JdAfsListener calculateVoucherFreezeNum setFreezeResultCount={}", count);
            }
        }
    }
}
