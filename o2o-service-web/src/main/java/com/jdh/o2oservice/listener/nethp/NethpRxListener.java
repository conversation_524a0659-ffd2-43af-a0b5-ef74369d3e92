package com.jdh.o2oservice.listener.nethp;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.springboot.annotation.JmqListener;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.support.service.UserMarketApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.common.enums.UserMarketSceneEnum;
import com.jdh.o2oservice.common.enums.UserMarketTypeEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.usermarket.bo.*;
import com.jdh.o2oservice.export.promise.cmd.PromiseCallbackCmd;
import com.jdh.o2oservice.export.support.command.UserMarketCmd;
import com.jdh.o2oservice.export.support.query.UserMarketRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/18
 */
@Component
@Slf4j
public class NethpRxListener implements MessageListener {

    @Autowired
    @Lazy
    private UserMarketApplication userMarketApplication;

    @Autowired
    @Lazy
    private DuccConfig duccConfig;

    @Autowired
    @Lazy
    private PromiseRepository promiseRepository;

    /**
     * 缓存
     */
    @Autowired
    @Lazy
    private Cluster jimClient;

    /**
     * @param list
     * @throws Exception
     */
    @Override
    @JmqListener(id= "nethpRxConsumer", topics = {"${topics.net.nethpRx}"})
    public void onMessage(List<Message> list) throws Exception {
        log.info("NethpRxListener->onMessage start");
        if (CollectionUtil.isEmpty(list) ) {
            return;
        }
        for(Message message : list){
            this.processMessage(message);
        }
    }


    /**
     * processMessage
     *
     * @param message 讯息
     */
    private void processMessage(Message message) {
        String diagReportId = "";
        String patientId = "";
        try{
            String text = message.getText();
            NethpRxBo nethpRxBo = JsonUtil.parseObject(text, NethpRxBo.class);
            //过滤
            if (!StringUtils.equals("RX_CREATE",nethpRxBo.getEventType())){
                return;
            }

            if (!duccConfig.getRxSceneCodeSet().contains(nethpRxBo.getSceneCode())){
                return;
            }
            log.info("NethpRxListener->processMessage nethpRxBo:{}", JSON.toJSONString(nethpRxBo));

            String eventContent = nethpRxBo.getEventContent();
            JSONObject jsonObjectEvent = JSONObject.parseObject(eventContent);

            String after = jsonObjectEvent.getString("after");
            JSONObject jsonObjectAfter = JSONObject.parseObject(after);

            String rxInfo = jsonObjectAfter.getString("rxInfo");
            NethpRxInfoBO nethpRxInfoBO = JsonUtil.parseObject(rxInfo, NethpRxInfoBO.class);
            log.info("NethpRxListener->processMessage nethpRxInfoBO:{}", JSON.toJSONString(nethpRxInfoBO));
            NethpBusinessDataBO businessData = JsonUtil.parseObject(nethpRxInfoBO.getBusinessData(), NethpBusinessDataBO.class);
            log.info("NethpRxListener->processMessage businessData:{}", JSON.toJSONString(businessData));
            List<NethpDiagReportBO> diagReportDataList = businessData.getDiagReportDataList();
            for (NethpDiagReportBO diagReportBO : diagReportDataList) {
                if (StringUtils.equals("10006", diagReportBO.getDiagReportType())) {
                    String[] split = diagReportBO.getDiagReportId().split("_");
                    diagReportId = split[0];
                    patientId = split[1];
                    break;
                }
            }
            if (StringUtils.isBlank(diagReportId)){
                return;
            }
            ;

            String cacheKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.QUICK_CHECK_DRUG_PROMISE_PATIENT,diagReportId,patientId) ;
            log.info("NethpRxListener->processMessage cacheKey:{}", cacheKey);
            if (StringUtils.isNotBlank(jimClient.get(cacheKey))){
                log.info("NethpRxListener->processMessage cacheKey,result:{}", JSON.toJSONString(jimClient.get(cacheKey)));
                return;
            }
            jimClient.setEx(cacheKey,"exist",RedisKeyEnum.QUICK_CHECK_DRUG_PROMISE_PATIENT.getExpireTime(),RedisKeyEnum.QUICK_CHECK_DRUG_PROMISE_PATIENT.getExpireTimeUnit());

            JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder()
                    .promiseId(Long.valueOf(diagReportId))
                    .userPin(nethpRxInfoBO.getUserPin())
                    .build()
            );

            if (Objects.isNull(promise)){
                return;
            }

            String rxSkus = jsonObjectAfter.getString("rxSkus");
            List<NethpRxSkuBO> nethpRxSkuBOS = JsonUtil.parseArray(rxSkus, NethpRxSkuBO.class);
            log.info("NethpRxListener->nethpRxSkuBOS,nethpRxSkuBOS={}",JsonUtil.toJSONString(nethpRxSkuBOS));

            Long finalPatientId = Long.valueOf(patientId);
            JdhPromisePatient jdhPromisePatient = promise.getPatients().stream().filter(p -> Objects.equals(finalPatientId, p.getPatientId())).findFirst().orElse(null);


            UserMarketCmd userMarketCmd = new UserMarketCmd();
            userMarketCmd.setMarketDetail(String.valueOf(nethpRxInfoBO.getRxId()));
            userMarketCmd.setCreateUser(nethpRxInfoBO.getUserPin());
            userMarketCmd.setMarketType(UserMarketTypeEnum.NET_FORMULARY.getType());
            userMarketCmd.setCreateTime(new Date());
            userMarketCmd.setScene(UserMarketSceneEnum.QUICK_CHECK_REPORT_DRUG.getScene());
            userMarketCmd.setUserPin(nethpRxInfoBO.getUserPin());
            userMarketCmd.setPatientId(jdhPromisePatient.getPatientId());
            userMarketCmd.setUpdateUser(nethpRxInfoBO.getUserPin());
            userMarketCmd.setUpdateTime(new Date());
            userMarketCmd.setPromisePatientId(jdhPromisePatient.getPromisePatientId());
            userMarketCmd.setVerticalCode(promise.getVerticalCode());
            userMarketCmd.setServiceType(promise.getServiceType());
            userMarketCmd.setPromiseId(promise.getPromiseId());
            NethpRxSkuExtendBO extendBO = NethpRxSkuExtendBO.builder().storeId(nethpRxInfoBO.getStoreId()).nethpRxSkuBOList(nethpRxSkuBOS).build();
            userMarketCmd.setExtendInfo(JsonUtil.toJSONString(extendBO));

            userMarketApplication.saveUserMarket(userMarketCmd);

        }catch (Exception e) {
            if (StringUtil.isNotBlank(diagReportId) && StringUtil.isNotBlank(patientId)){}
            jimClient.del(RedisKeyEnum.getRedisKey(RedisKeyEnum.QUICK_CHECK_DRUG_PROMISE_PATIENT,diagReportId,patientId) );
            log.error("NethpRxListener->processMessage error", e);
        }catch (Throwable e){
            if (StringUtil.isNotBlank(diagReportId) && StringUtil.isNotBlank(patientId)){}
            jimClient.del(RedisKeyEnum.getRedisKey(RedisKeyEnum.QUICK_CHECK_DRUG_PROMISE_PATIENT,diagReportId,patientId) );
            log.error("NethpRxListener->processMessage error", e);
        }
    }

    public static void main(String[] args) {
        String jsonString = "{\\\"businessData\\\":[{\\\"diagReportType\\\":\\\"10006\\\",\\\"diagReportId\\\":\\\"111\\\"}]}";

        JSONObject jsonObjectEvent = JSONObject.parseObject(jsonString);
        String businessData = jsonObjectEvent.getString("businessData");
        JSONObject jsonObjectBusiness = JSONObject.parseObject(businessData);
        JSONArray diagReportDataList = JSONObject.parseArray(jsonObjectBusiness.getString("diagReportDataList"));
        String diagReportId = "";
        for (Object o : diagReportDataList) {
            JSONObject diagReportData = (JSONObject) o;
            if (StringUtils.equals("10006", diagReportData.getString("diagReportType"))) {
                diagReportId = diagReportData.getString("diagReportId");
                break;
            }
        }
        System.out.println(diagReportId);
    }

}
