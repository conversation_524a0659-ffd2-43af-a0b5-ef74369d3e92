package com.jdh.o2oservice.job.settlement;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.DateUtil;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelScheduleApplication;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.UmpKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.UmpUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.JobNatureEnum;
import com.jdh.o2oservice.core.domain.angel.enums.ScheduleStatusEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhStation;
import com.jdh.o2oservice.core.domain.angel.model.JdhStationIdentifier;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleItemTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleStatusEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlement;
import com.jdh.o2oservice.core.domain.settlement.model.ImportAngelSettleAdjust;
import com.jdh.o2oservice.core.domain.settlement.model.JdhAngelSettleAdjust;
import com.jdh.o2oservice.core.domain.settlement.model.JdhAngelSettleAdjustJoySkyBo;
import com.jdh.o2oservice.core.domain.settlement.repository.AngelSettlementRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.JdhAngelSettleAdjustRepository;
import com.jdh.o2oservice.core.domain.settlement.rpc.HySettleRpc;
import com.jdh.o2oservice.core.domain.settlement.vo.AngelAddAccountAmountVo;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.export.angel.dto.AngelScheduleDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.dto.ScheduleCalendarDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angel.query.AngelSchedulePageRequest;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 全职护士保底工资每日调整job
 *
 * <AUTHOR>
 * @date 2024/05/31
 */
@Slf4j
@Component
public class AngelSettlementDailyAdjustNewJob implements SimpleJob {

    /**
     *
     */
    @Resource
    DuccConfig duccConfig;
    /**
     *
     */
    @Resource
    AngelApplication angelApplication;
    /**
     *
     */
    @Resource
    private GenerateIdFactory generateIdFactory;
    /**
     * hySettleRpc
     */
    @Autowired
    private HySettleRpc hySettleRpc;
    /**
     *
     */
    @Resource
    private AngelScheduleApplication angelScheduleApplication;
    /**
     *
     */
    @Resource
    private AngelSettlementRepository angelSettlementRepository;
    /**
     * jdhAngelSettleAdjustRepository
     */
    @Autowired
    private JdhAngelSettleAdjustRepository jdhAngelSettleAdjustRepository;
    /**
     *
     */
    @Resource
    private AngelRepository angelRepository;
    /**
     *
     */
    @Resource
    private JdhStationRepository jdhStationRepository;

    /**
     * @param shardingContext
     */
    @Override
    public void execute(ShardingContext shardingContext) {
        if (duccConfig.getAngelSettlementDailyAdjustSwitch()){
            handleDailyAdjustExecuteNew(new Date(), null);
        }
    }

    /**
     * 处理全职护士保底工资每日调整
     */
    public void handleDailyAdjustExecuteNew(Date todayDate, Date exceptWithdrawTime) {
        /**Step1.构造查询参数*/
        AngelSettleQuery query = this.getAngelSettleQuery(todayDate);
        log.info("AngelSettlementDailyAdjustJob.handleDailyAdjustExecuteNew.todayDate={},query={}", todayDate, JsonUtil.toJSONString(query));
        /**Step2.查询护士排班*/
        int pageNo = 1;
        int pageSize = 100;
        while (true) {
            AngelSchedulePageRequest request = new AngelSchedulePageRequest();
            request.setScheduleDate(DateUtil.formatDate(query.getSettleTimeStart(), CommonConstant.YMD));
            request.setPageNum(pageNo);
            request.setPageSize(pageSize);
            request.setAngelJobNature(JobNatureEnum.FULL_TIME.getValue());
            request.setStatus(Stream.of(ScheduleStatusEnum.ENABLED.getValue()).collect(Collectors.toSet()));
            log.info("AngelSettlementDailyAdjustJob.handleDailyAdjustExecuteNew.request={}", JSON.toJSONString(request));
            PageDto<AngelScheduleDto> angelScheduleDtos = angelScheduleApplication.queryAngelSchedulePage(request);
            pageNo = pageNo + 1;
            log.info("AngelSettlementDailyAdjustJob.handleDailyAdjustExecuteNew.angelScheduleDtos={}", JSON.toJSONString(angelScheduleDtos));
            //多查询一次 如果彻底查不到了break掉
            if (Objects.isNull(angelScheduleDtos) || CollectionUtils.isEmpty(angelScheduleDtos.getList())) {
                log.info("AngelSettlementDailyAdjustNewJob.handleDailyAdjustExecuteNew end");
                break;
            }
            Set<Long> angelBlackListSet = duccConfig.getAngelBlackListSet();
            Optional.ofNullable(angelScheduleDtos.getList()).map(List::stream).orElseGet(Stream::empty).forEach(angelScheduleDto -> {
                //查询护士身份
                JdhAngelRepQuery jdhAngelRepQuery = JdhAngelRepQuery.builder().angelId(angelScheduleDto.getAngelId()).build();
                JdhAngel jdhAngel = angelRepository.queryAngelDetail(jdhAngelRepQuery);
                //黑名单逻辑+全职护士逻辑判断
                if (!angelBlackListSet.contains(angelScheduleDto.getAngelId())) {
                    handleDailyAdjustLogic(angelScheduleDto, query, jdhAngel, exceptWithdrawTime);
                }
            });
        }
        log.info("[handleDailyAdjustExecuteNew.execute]，end");
    }

    /**
     * 处理每日保底工资逻辑
     *
     * @param
     * @param
     * @param
     */
    private void handleDailyAdjustLogic(AngelScheduleDto angelScheduleDto, AngelSettleQuery angelSettleQuery, JdhAngel jdhAngel, Date exceptWithdrawTime) {
        if (CollectionUtils.isNotEmpty(angelScheduleDto.getScheduleCalendarDtoList())) {
            log.info("AngelSettlementDailyAdjustNewJob.handleDailyAdjustLogic.angelScheduleDto={},angelSettleQuery={},jdhAngel={}", JSON.toJSONString(angelScheduleDto), JSON.toJSONString(angelSettleQuery), JSON.toJSONString(jdhAngel));
            /**step2.查询护士所在区域及等级获取保底工资*/
            String angelDailyIntervalBasicSalary = this.getAngelDailyBasicSalary(jdhAngel);
            log.info("AngelSettlementDailyAdjustNewJob.getAngelDailyBasicSalary.angelDailyIntervalBasicSalary={}", angelDailyIntervalBasicSalary);
            /**step2.根据当前promiseId查询工单绑定流水List*/
            AngelSettlementQueryContext queryContext = new AngelSettlementQueryContext();
            queryContext.setAngelId(angelScheduleDto.getAngelId());
            queryContext.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
            queryContext.setSettleTimeStart(angelSettleQuery.getSettleTimeStart());
            queryContext.setSettleTimeEnd(angelSettleQuery.getSettleTimeEnd());
            queryContext.setItemTypeList(Arrays.asList(SettleItemTypeEnum.TESTING.getType(),SettleItemTypeEnum.NURSING.getType(),SettleItemTypeEnum.FEE.getType()));
            log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.queryContext={}", JSON.toJSONString(queryContext));
            List<AngelSettlement> angelSettlements = angelSettlementRepository.querySettlementList(queryContext);
            /**Step3.将流水表按照angelId进行分组，按照settleAmount进行同组求和，得到angelSettlementMap*/
            BigDecimal performance = Optional.ofNullable(angelSettlements).map(List::stream).orElseGet(Stream::empty).map(AngelSettlement::getSettleAmount).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.performance={}", performance);
            //查询当天有排班
            if (CollectionUtils.isNotEmpty(angelScheduleDto.getScheduleCalendarDtoList())) {
                //查询只有一天暴力取0 index
                ScheduleCalendarDto scheduleCalendarDto = angelScheduleDto.getScheduleCalendarDtoList().get(0);
                //查询当天排班的时段list不为空
                if (CollectionUtils.isNotEmpty(scheduleCalendarDto.getScheduleIntervalList())) {
                    //几个时段：intervalNum
                    int intervalNum = scheduleCalendarDto.getScheduleIntervalList().size();
                    BigDecimal dailyBaseSalary = new BigDecimal(angelDailyIntervalBasicSalary).multiply(BigDecimal.valueOf(intervalNum));
                    log.info("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.dailyBaseSalary={}", dailyBaseSalary);
                    if (dailyBaseSalary.compareTo(new BigDecimal(duccConfig.getAngelDailyBaseSalaryUpper()))>0){
                        UmpUtil.showWarnMsg(UmpKeyEnum.ANGEL_DAILY_ADJUST_DAILY_BASE_SALARY_ERROR,"angelId:"+angelScheduleDto.getAngelId()+"-dailyBaseSalary:"+dailyBaseSalary);
                        log.error("JdServiceSettleApplicationImpl.handleMergeAngelSettlementLogic.has error dailyBaseSalary={}", dailyBaseSalary);
                        return;
                    }
                    if (performance.compareTo(dailyBaseSalary) < 0) {
                        //触发保底工资逻辑
                        Date expectSettleTime = Objects.nonNull(exceptWithdrawTime) ? exceptWithdrawTime : getNextMonth10thDayMidnight(angelSettleQuery.getSettleTimeStart());
                        this.addWithdrawAccountAmount(angelScheduleDto.getAngelId(), dailyBaseSalary.subtract(performance), expectSettleTime);
                    }
                }
            }
        }
    }


    private String getAngelDailyBasicSalary(JdhAngel jdhAngel) {
        /**step1.获取ducc配置： key 职级*地区码 value对应单价 demo:36*51226 : 50.0 */
        Map<String, String> angelDailyIntervalBasicSalaryMap = duccConfig.getAngelDailyIntervalBasicSalaryMap();
        try {
            log.info("AngelSettlementDailyAdjustNewJob.getAngelDailyBasicSalary.jdhAngel={}", JSON.toJSONString(jdhAngel));
            String professionCode = jdhAngel.getJdhAngelProfessionRelList().get(0).getProfessionTitleCode();
            JdhStation jdhStation = jdhStationRepository.find(JdhStationIdentifier.builder().angelStationId(jdhAngel.getStationId()).build());
            log.info("AngelSettlementDailyAdjustNewJob.getAngelDailyBasicSalary.jdhStation={}", JSON.toJSONString(jdhStation));
            /**step3.服务站地域优先级从细—>粗，进行匹配*/
            String[] locations = {
                    new StringBuilder(professionCode).append("*").append(jdhStation.getCountyCode()).toString(),
                    new StringBuilder(professionCode).append("*").append(jdhStation.getDistrictCode()).toString(),
                    new StringBuilder(professionCode).append("*").append(jdhStation.getCityCode()).toString(),
                    new StringBuilder(professionCode).append("*").append(jdhStation.getProvinceCode()).toString()
            };
            for (String location : locations) {
                if (StringUtils.isNotBlank(angelDailyIntervalBasicSalaryMap.get(location))) {
                    return angelDailyIntervalBasicSalaryMap.get(location);
                }
            }
            /**step4.兜底*/
            return angelDailyIntervalBasicSalaryMap.get("default");
        } catch (Exception e) {
            log.error("AngelSettlementDailyAdjustJob.getAngelDailyBasicSalary has error", e);
            return angelDailyIntervalBasicSalaryMap.get("default");
        }
    }


    /**
     * 获取查询参数 查询时间为 T-7的0点到24点
     *
     * @return
     */
    public AngelSettleQuery getAngelSettleQuery(Date todayDate) {
        Calendar calendarToday = Calendar.getInstance();
        calendarToday.setTime(todayDate);
        //构造查询参数
        AngelSettleQuery angelSettleQuery = new AngelSettleQuery();
        //当前时间-7天的0点
        Calendar calendarStart = calendarToday;
        calendarStart.add(Calendar.DAY_OF_MONTH, -7);
        calendarStart.set(Calendar.HOUR_OF_DAY, 0);
        calendarStart.set(Calendar.MINUTE, 0);
        calendarStart.set(Calendar.SECOND, 0);
        angelSettleQuery.setSettleTimeStart(calendarStart.getTime());
        //当前时间-7天的24点
        Calendar calendarEnd = calendarStart;
        calendarEnd.set(Calendar.HOUR_OF_DAY, 24);
        calendarEnd.set(Calendar.MINUTE, 0);
        calendarEnd.set(Calendar.SECOND, 0);
        angelSettleQuery.setSettleTimeEnd(calendarEnd.getTime());
        angelSettleQuery.setPageNum(1);
        angelSettleQuery.setPageSize(10000);
        return angelSettleQuery;
    }

    /**
     * 调用调整项RPC
     *
     * @param angelId
     * @param settleAmount
     * @param expectSettleTime
     * @return
     */
    private void addWithdrawAccountAmount(Long angelId, BigDecimal settleAmount, Date expectSettleTime) {
        try {
            log.info("AngelSettlementDailyAdjustJob.addWithdrawAccountAmount.angelId={},settleAmount={},expectSettleTime={}", angelId, settleAmount, expectSettleTime);
            AngelAddAccountAmountVo angelAddAccountAmountVo = new AngelAddAccountAmountVo();
            JdhAngelDto jdhAngelDto = getNethpDocId(angelId);
            angelAddAccountAmountVo.setAccountId(jdhAngelDto.getNethpDocId());
            angelAddAccountAmountVo.setAmount(settleAmount);
            angelAddAccountAmountVo.setRecordTime(new Date());
            angelAddAccountAmountVo.setWithdrawalTime(expectSettleTime);
            angelAddAccountAmountVo.setChangeBusinessId(generateIdFactory.getIdStr());
            angelAddAccountAmountVo.setFeeType(SettleItemTypeEnum.ADJUST.getHuYiFeeType());
            angelAddAccountAmountVo.setOutBusinessData(JsonUtil.toJSONString(angelAddAccountAmountVo));
            log.info("AngelSettlementDailyAdjustJob.addWithdrawAccountAmount.angelAddAccountAmountVo={}", JSON.toJSONString(angelAddAccountAmountVo));
            Boolean result = hySettleRpc.addWithdrawAccountAmount(angelAddAccountAmountVo);
            if (!result) {
                log.error("AngelSettlementJob.addWithdrawAccountAmount,angelId={},settleAmount={}", angelId, settleAmount);
            }
            //同步插入angel_settlement表
            this.insertDailyAdjustIntoSettlement(jdhAngelDto, settleAmount, expectSettleTime,result);
        } catch (Exception e) {
            UmpUtil.showWarnMsg(UmpKeyEnum.ANGEL_DAILY_ADJUST_JOB_ERROR,"angelId:" + angelId);
            log.error("AngelSettlementDailyAdjustNewJob.addWithdrawAccountAmount has error", e);
        }
    }

    /**
     * 同步插入angel_settlement表
     *
     * @param jdhAngelDto
     * @param settleAmount
     * @param expectSettleTime
     * @param
     */
    private void insertDailyAdjustIntoSettlement(JdhAngelDto jdhAngelDto,BigDecimal settleAmount, Date expectSettleTime,Boolean syncHuYiResult) {
        AngelSettlement angelSettlement = new AngelSettlement();
        angelSettlement.setSettleId(generateIdFactory.getId());
        angelSettlement.setAngelId(jdhAngelDto.getAngelId());
        angelSettlement.setAngelName(jdhAngelDto.getAngelName());
        angelSettlement.setSettlementType(SettleTypeEnum.INCOME.getType());
        angelSettlement.setSettleAmount(settleAmount);
        angelSettlement.setExpectSettleTime(expectSettleTime);
        angelSettlement.setCreateTime(new Date());
        angelSettlement.setUpdateTime(new Date());
        angelSettlement.setItemType(SettleItemTypeEnum.ADJUST.getType());
        angelSettlement.setJobNature(JobNatureEnum.FULL_TIME.getValue());
        angelSettlement.setSettleStatus(SettleStatusEnum.FREEZE.getType());
        angelSettlement.setSettleTime(new Date());
        Long adjustId = saveAdjustSettlement(jdhAngelDto,angelSettlement);
        angelSettlement.setItemSourceId(adjustId);
        angelSettlement.setSyncStatus(syncHuYiResult?1:0);

        List<AngelSettlement> angelSettlementList = new ArrayList<>();
        angelSettlementList.add(angelSettlement);
        AngelSettlement angelSettlement2 = new AngelSettlement();
        BeanUtil.copyProperties(angelSettlement,angelSettlement2);
        angelSettlement2.setSettleId(generateIdFactory.getId());
        angelSettlement2.setSettleStatus(SettleStatusEnum.INIT.getType());
        angelSettlementList.add(angelSettlement2);

        log.info("com.jdh.o2oservice.job.settlement.AngelSettlementDailyAdjustJob.insertDailyAdjustIntoSettlement.angelSettlement={}", JsonUtil.toJSONString(angelSettlement));
        angelSettlementRepository.batchSaveAngelSettlementAndDetail(angelSettlementList,null);
    }

    /**
     * 保存调账
     * @param jdhAngelDto
     * @param angelSettlement
     */
    private Long saveAdjustSettlement(JdhAngelDto jdhAngelDto,AngelSettlement angelSettlement){
        JdhAngelSettleAdjust jdhAngelSettleAdjust = new JdhAngelSettleAdjust();
        jdhAngelSettleAdjust.setAngelId(jdhAngelDto.getAngelId());
        jdhAngelSettleAdjust.setAngelName(jdhAngelDto.getAngelName());
        jdhAngelSettleAdjust.setNethpDocId(jdhAngelDto.getNethpDocId());
        jdhAngelSettleAdjust.setJobNature(jdhAngelDto.getJobNature());
        jdhAngelSettleAdjust.setAdjustAmount(angelSettlement.getSettleAmount());
        jdhAngelSettleAdjust.setAdjustDate(angelSettlement.getSettleTime());
        jdhAngelSettleAdjust.setCashDate(angelSettlement.getExpectSettleTime());
        jdhAngelSettleAdjust.setAdjustType(SettleItemTypeEnum.ADJUST.getType());
        jdhAngelSettleAdjust.setYn(0);
        Long adjustId = generateIdFactory.getId();
        jdhAngelSettleAdjust.setAdjustId(adjustId);
        jdhAngelSettleAdjust.setAdjustDate(new Date());
        jdhAngelSettleAdjust.setAdjustTypeDesc(SettleItemTypeEnum.getSettleTypeDescByType(jdhAngelSettleAdjust.getAdjustType()));
        jdhAngelSettleAdjustRepository.save(jdhAngelSettleAdjust);
        return adjustId;
    }

    /**
     * 获取互医docId
     *
     * @param angelId
     * @return
     */
    private JdhAngelDto getNethpDocId(Long angelId) {
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelId(angelId);
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.nonNull(jdhAngelDto) && Objects.nonNull(jdhAngelDto.getNethpDocId())) {
            return jdhAngelDto;
        } else {
            throw new BusinessException(TradeErrorCode.ANGEL_INFO_NULL);
        }
    }

    /**
     * 获取当前日期的下个月第10天0点0分0秒
     *
     * @param date
     * @return
     */
    public static Date getNextMonth10thDayMidnight(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 10);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
}
