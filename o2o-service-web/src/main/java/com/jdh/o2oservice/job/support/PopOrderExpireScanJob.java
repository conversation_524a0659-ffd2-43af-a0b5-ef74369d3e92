package com.jdh.o2oservice.job.support;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.foward.MapperHandler;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.VoucherRemindBeforeExpireConfig;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.event.VoucherExpireEventBody;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucher;
import com.jdh.o2oservice.core.domain.promise.model.JdhVoucherItem;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.VoucherRepPageQuery;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhVoucherStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.VoucherPageRequest;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.elasticjob.api.ShardingContext;
import org.apache.shardingsphere.elasticjob.simple.job.SimpleJob;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 *
 * 履约单扫描，扫描未核销和临近过期的预约单
 * @author: yangxiyu
 * @date: 2024/7/29 16:33
 * @version: 1.0
 */
@Slf4j
@Component
public class PopOrderExpireScanJob implements SimpleJob {

    /**
     *
     */
    @Resource
    private JdhPromisePoMapper jdhPromisePoMapper;
    /**
     *
     */
    @Resource
    @Lazy
    private ReachApplication reachApplication;

    /**
     * jdhVoucherApplication
     */
    @Resource
    @Lazy
    private VoucherApplication voucherApplication;


    @Resource
    @Lazy
    private DuccConfig duccConfig;

    /**
     * 达达交接统一处理器
     */
    @Resource
    private MapperHandler mapperHandler;

    /**
     * skuInfoRpc
     */
    @Resource
    private SkuInfoRpc skuInfoRpc;

    @Resource
    private Cluster jimClient;

    private String voucherCacheKey = "voucher_cache_key_%s";


    @Override
    public void execute(ShardingContext shardingContext) {

        try{
            this.voucherRemindBeforeExpireSendSms(shardingContext);
        }catch (Exception ex){
            log.error("PopOrderExpireScanJob->voucherRemindBeforeExpireSendSms error", ex);
        }

       try{
           tenDaySms();
       }catch (Exception e){
           log.error("PopOrderExpireScanJob->tenDaySms error", e);
       }

    }

    /**
     * 卡临过期提醒
     */
    public void voucherRemindBeforeExpireSendSms(ShardingContext shardingContext){
        log.info("voucherRemindBeforeExpireSendSms 我执行了 shardingContext={}",JSON.toJSONString(shardingContext));
        List<VoucherRemindBeforeExpireConfig> voucherRemindBeforeExpireConfigList = duccConfig.getVoucherRemindBeforeExpireConfigList();
        voucherRemindBeforeExpireConfigList.forEach(this::reach);
    }

    /**
     * 触达消息
     * @param voucherRemindBeforeExpireConfig
     */
    private void reach(VoucherRemindBeforeExpireConfig voucherRemindBeforeExpireConfig){
        VoucherPageRequest pageRequest = VoucherPageRequest.builder()
                .verticalCode(voucherRemindBeforeExpireConfig.getVerticalCode())
                .statusNotInList(Arrays.asList(JdhVoucherStatusEnum.EXPIRED.getStatus(),JdhVoucherStatusEnum.COMPLETE.getStatus(),JdhVoucherStatusEnum.INVALID.getStatus()))
                .expireDateLt(TimeUtils.getDateStart(TimeUtils.addDays(new Date(),voucherRemindBeforeExpireConfig.getDayBeforeExpire()+1)))
                .expireDateGe(TimeUtils.getDateStart(TimeUtils.addDays(new Date(),voucherRemindBeforeExpireConfig.getDayBeforeExpire())))
                .build();
        Response<PageDto<VoucherDto>> page;
        Integer pageNum = 1;
        do{
            pageRequest.setPageNum(pageNum);
            pageRequest.setPageSize(voucherRemindBeforeExpireConfig.getPageSize());
            page = voucherApplication.pageQueryVoucher(pageRequest);
            log.info("VoucherExpireJob -> execute pagination:{}", JSON.toJSONString(page));
            if(Objects.nonNull(page) && Objects.nonNull(page.getData())){
                List<VoucherDto> voucherDtoList = page.getData().getList();
                voucherDtoList.forEach(v->{
                    String cacheData = jimClient.get(String.format(voucherCacheKey,v.getSourceVoucherId()));
                    log.info("缓存数据 key={} ,cacheData={}",String.format(voucherCacheKey,v.getSourceVoucherId()),cacheData);
                    if (StringUtils.isNotEmpty(cacheData)){
                        return;
                    }
                    //提交触达任务
                    JdhVoucher jdhVoucher = new JdhVoucher();
                    jdhVoucher.setVoucherId(v.getVoucherId());
                    VoucherExpireEventBody voucherExpireEventBody = new VoucherExpireEventBody();
                    voucherExpireEventBody.setDayBeforeExpire(voucherRemindBeforeExpireConfig.getDayBeforeExpire());
                    Event event = new Event(jdhVoucher, PromiseEventTypeEnum.VOUCHER_CLOSE_TO_EXPIRATION_ONE_DAY, null,voucherExpireEventBody);
                    reachApplication.submitTask(event);
                    jimClient.setEx(String.format(voucherCacheKey,v.getSourceVoucherId()),"1",1, TimeUnit.DAYS);
                });
            }
            pageNum ++;
        }while (Objects.nonNull(page) && Objects.nonNull(page.getData()) && pageNum <= page.getData().getTotalPage());
    }

    /**
     * 下单十天未核销短信提醒
     */
    public void tenDaySms(){

        List<Integer> promiseStatus = Lists.newArrayList(JdhPromiseStatusEnum.WAIT_PROMISE.getStatus()
                , JdhPromiseStatusEnum.APPOINTMENT_ING.getStatus(), JdhPromiseStatusEnum.APPOINTMENT_FAIL.getStatus(),JdhPromiseStatusEnum.APPOINTMENT_SUCCESS.getStatus()
        ,JdhPromiseStatusEnum.MODIFY_ING.getStatus(),JdhPromiseStatusEnum.MODIFY_SUCCESS.getStatus()
                , JdhPromiseStatusEnum.CANCEL_ING.getStatus(), JdhPromiseStatusEnum.CANCEL_SUCCESS.getStatus(), JdhPromiseStatusEnum.CANCEL_FAIL.getStatus());

        LocalDateTime startTime = LocalDate.now().plusDays(-10).atTime(0, 0, 0);
        LocalDateTime endTime = LocalDate.now().plusDays(-9).atTime(0, 0, 0);
        /**
         *
         */
        int pageNum = 0;
        int pageSize = 100;
        while (true){

            List<JdhPromisePo> pos = jdhPromisePoMapper.timeoutWrittenOffList(promiseStatus, TimeUtils.localDateTimeToDate(startTime),
                    TimeUtils.localDateTimeToDate(endTime), "xfylPop", pageNum*pageSize, pageSize);

            if (CollectionUtils.isEmpty(pos)){
                log.info("PopPromiseExpireTipJob tenDaySms promise is empty");
                return;
            }
            log.info("PopOrderExpireScanJob->tenDaySms size = {}", pos.size());
            for (JdhPromisePo po : pos) {

                // 达达交接是否执行开关
                if (Boolean.TRUE.equals(duccConfig.getPopForwardJobGlobalSwitch()) && Boolean.TRUE.equals(mapperHandler.handler(String.valueOf(po.getPromiseId()), "promiseIdMapper"))) {
                    log.info("PopOrderExpireScanJob->tenDaySms 达达交接命中规则 promiseId={}", po.getPromiseId());
                    continue;
                }

                JdhPromise promise = new JdhPromise();
                promise.setPromiseId(po.getPromiseId());
                Event event = new Event(promise, PromiseEventTypeEnum.TIMEOUT_NOT_WRITTEN_OFF_TEN, null, null);
                reachApplication.submitTask(event);
            }
            pageNum++;
        }

    }
}
