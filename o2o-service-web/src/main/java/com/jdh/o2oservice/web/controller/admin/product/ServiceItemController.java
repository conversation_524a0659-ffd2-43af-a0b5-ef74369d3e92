package com.jdh.o2oservice.web.controller.admin.product;

import cn.hutool.core.date.DateUtil;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.product.service.ProductServiceIndicatorApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.enums.ImpTypeEnum;
import com.jdh.o2oservice.common.result.excel.BaseExcelModel;
import com.jdh.o2oservice.common.result.response.ImportResult;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.product.cmd.*;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import com.jdh.o2oservice.web.controller.admin.common.AbsImpController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/16 2:07 下午
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/product/service/item")
public class ServiceItemController extends AbsImpController {

    @Autowired
    private ProductServiceItemApplication productServiceItemApplication;

    /**
     * 指标
     */
    @Resource
    private ProductServiceIndicatorApplication productServiceIndicatorApplication;


    /**
     * 保存项目
     *
     * @param serviceItemCmd
     * @return
     */

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/save")
    @LogAndAlarm(jKey = "ServiceItemController.saveServiceItem")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "保存服务项目", recordParamBizIdExpress = {"args[0].itemId"}, paramJudgeOperationTypeExpress = {"args[0].itemId"})
    public Response<Boolean> saveServiceItem(@RequestBody SaveServiceItemCmd serviceItemCmd) {
        serviceItemCmd.setCreateUser(LoginContext.getLoginContext().getPin());
        serviceItemCmd.setUpdateUser(LoginContext.getLoginContext().getPin());
        return Response.buildSuccessResult(productServiceItemApplication.batchSaveServiceItem(Arrays.asList(serviceItemCmd)));
    }


    /**
     * 分页查询项目列表
     *
     * @param serviceItemQuery
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryPage")
    @LogAndAlarm(jKey = "ServiceItemController.queryServiceItemPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "分页查询项目列表")
    public Response<PageDto<ServiceItemDto>> queryServiceItemPage(@RequestBody ServiceItemQuery serviceItemQuery) {
        return Response.buildSuccessResult(productServiceItemApplication.queryServiceItemPage(serviceItemQuery));
    }


    /**
     * 查询项目明细
     *
     * @param serviceItemQuery
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryDetail")
    @LogAndAlarm(jKey = "ServiceItemController.queryServiceItemDetail")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询项目明细")
    public Response<ServiceItemDto> queryServiceItemDetail(@RequestBody ServiceItemQuery serviceItemQuery) {
        return Response.buildSuccessResult(productServiceItemApplication.queryServiceItemDetail(serviceItemQuery));
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/importItemFile")
    @LogAndAlarm(jKey = "ServiceItemController.importItem")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "导入项目")
    public Response importItem(@RequestParam(value = "file", required = false) MultipartFile file) {
        if (Objects.isNull(file)) {
            return ResponseUtil.buildErrResponse(SystemErrorCode.PARAM_NULL_ERROR);
        }
        String pin = LoginContext.getLoginContext().getPin();
        return super.preImport(file, ImpTypeEnum.PRODUCT_ITEM_LIST, 5 * 1024 * 1024L, pin);
    }

    /**
     * 导入项目
     *
     * @param cmd cmd
     * @return response
     */
    @PostMapping(value = "/importProductItem")
    @LogAndAlarm(jKey = "ServiceItemController.importProductItem")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "导入商品项目")
    public Response<Boolean> importProductItem(@RequestBody ServiceItemImportCmd cmd) {
        String erp = LoginContext.getLoginContext().getPin();
        cmd.setErp(erp);
        Boolean ret = productServiceItemApplication.importProductItem(cmd);
        return ResponseUtil.buildSuccResponse(ret);
    }

    @Override
    protected Response specificWork(MultipartFile file, String pin, Object obj) {
        long start = System.currentTimeMillis();
        try {
            ImportResult imptResult = new ImportResult();
            imptResult.setFileName(file.getOriginalFilename());
            imptResult.setImportTime(DateUtil.format(new Date(), CommonConstant.YMDHMSFF));
            InputStream inputStream = file.getInputStream();

            productServiceItemApplication.importServiceItem(inputStream, pin, imptResult);

            Response<Object> boardImportRes = Response.buildSuccessResult(imptResult);
            return boardImportRes;
        } catch (Exception e) {
            logger.error("导入项目信息失败", e);
            return Response.buildUnknownErrorResult();
        } finally {
            logger.info("specificWork-耗时{}ms", System.currentTimeMillis() - start);
        }
    }

    @Override
    protected <T extends BaseExcelModel> boolean checkNullRow(T t) {
        if (t == null) {
            return false;
        }
        return true;
    }
}
