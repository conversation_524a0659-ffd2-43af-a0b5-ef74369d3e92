package com.jdh.o2oservice.web.controller.admin.auth;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.support.service.AuthApplication;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.user.auth.uim.bo.Uim2DimResourceBO;
import com.jdh.o2oservice.core.domain.user.auth.uim.bo.Uim2MenuBO;
import com.jdh.o2oservice.core.domain.user.auth.uim.bo.Uim2RoleBO;
import com.jdh.o2oservice.core.domain.user.auth.uim.bo.UserBO;
import com.jdh.o2oservice.core.domain.user.auth.uim.context.UimUserContext;
import com.jdh.o2oservice.core.domain.user.auth.uim.param.Uim2UserContext;
import com.jdh.o2oservice.enums.OpTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/25 9:28 上午
 * @Description:
 */
@Slf4j
@RequestMapping("/auth")
@RestController
public class AuthController {

    @Autowired
    private AuthApplication authApplication;


    /**
     * 查询菜单信息
     *
     * @return
     */
    @PostMapping("/menu/queryList")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询菜单列表")
    public Response<List<Uim2MenuBO>> queryMenuList() {
        Uim2UserContext userContext = new Uim2UserContext();
        userContext.setErp(LoginContext.getLoginContext().getPin());
        List<Uim2MenuBO> menuVos = authApplication.getMenuTree(userContext);
        return ResponseUtil.buildSuccResponse(menuVos);
    }


    /**
     * 查询登录人信息
     *
     * @return
     */
    @RequestMapping("/queryUserInfo")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询登录人信息")
    public Response<UserBO> queryUserInfo() {
        log.info("登录人：{}", LoginContext.getLoginContext().getPin());
        UserBO userBO = authApplication.getUserInfo(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(userBO);
    }


    /**
     * 查询角色信息
     *
     * @return
     */
    @PostMapping("/role/info")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询角色信息")
    public Response<List<Uim2RoleBO>> getRole() {
        List<Uim2RoleBO> menuVos = authApplication.getRole(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(menuVos);
    }


    /**
     * 查询角色信息
     *
     * @return
     */
    @PostMapping("/role/resource")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询角色资源")
    public Response<List<Uim2DimResourceBO>> getDimResource() {
        List<Uim2DimResourceBO> menuVos = authApplication.getDimResource(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(menuVos);
    }


    /**
     * 查询角色信息
     *
     * @return
     */
    @PostMapping("/role/getUimContext")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询角色上线文信息")
    public Response<UimUserContext> getUimContext() {
        return ResponseUtil.buildSuccResponse(UimUserContext.get());
    }


    /**
     * 查询角色信息
     *
     * @return
     */
    @PostMapping("/role/ping")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询角色上线文信息")
    public Boolean ping() {
        return Boolean.TRUE;
    }
}
