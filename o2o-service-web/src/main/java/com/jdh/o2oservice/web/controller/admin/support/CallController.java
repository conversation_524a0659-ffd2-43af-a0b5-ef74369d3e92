package com.jdh.o2oservice.web.controller.admin.support;
import com.jdh.o2oservice.annotation.OperationLog;
import com.alibaba.fastjson.JSON;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.support.dto.CallBillingUserDataDto;
import com.jdh.o2oservice.export.support.dto.CallRecordDto;
import com.jdh.o2oservice.export.support.query.QueryCallRecordRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 运营端-外呼
 * @Date 2025/1/3 上午9:20
 * <AUTHOR>
 **/
@Slf4j
@RestController
@RequestMapping("/support/call")
public class CallController {

    @Resource
    private CallRecordApplication callRecordApplication;

    @Resource
    private Cluster jimClient;

    private static String CALL_BIND_SECRET_NO_CACHE_KEY = "O2O:CALL:BIND:SECRET:NO:REDIS:KEY:{0}:{1}";

    /**
     * 外呼记录列表
     * @param request
     */
    @RequestMapping("/queryCallRecordList")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.admin.support.CallController.queryCallRecordList")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "外呼记录列表")
    public Response<List<CallRecordDto>> queryCallRecordList(@RequestBody QueryCallRecordRequest request){
        List<CallRecordDto> result = callRecordApplication.queryCallRecordList(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询外呼url
     * @param request
     */
    @RequestMapping("/queryCallRecordUrl")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.admin.support.CallController.queryCallRecordUrl")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询外呼url")
    public Response<String> queryCallRecordUrl(@RequestBody QueryCallRecordRequest request){
        String result = callRecordApplication.queryCallRecordUrl(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询真实号码
     */
    @RequestMapping(value = "/queryRealNumber/{orderId}/{secretNo}")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.admin.support.CallController.queryRealNumber")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询真实号码")
    public Response<?> queryRealNumber(@PathVariable("orderId") String orderId, @PathVariable("secretNo") String secretNo){
        String secretNoCacheKey = MessageFormat.format(CALL_BIND_SECRET_NO_CACHE_KEY, orderId, secretNo);
        String str = jimClient.get(secretNoCacheKey);
        if (StringUtils.isBlank(str)){
            return ResponseUtil.buildSuccResponse("未查询到真实手机号");
        }
        CallBillingUserDataDto userDataDto = JSON.parseObject(str, CallBillingUserDataDto.class);
        Map<String,Object> map = new HashMap<>();
        map.put("phoneNoA", userDataDto.getPhoneNoA());
        map.put("phoneNoB", userDataDto.getPhoneNoB());
        map.put("angelPin", userDataDto.getAngelPin());
        map.put("userPin", userDataDto.getUserPin());
        return ResponseUtil.buildSuccResponse(map);
    }
}
