package com.jdh.o2oservice.web.controller.provider;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.core.domain.support.operationlog.context.OperationLogContext;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.vertical.enums.ShunFengShipStatusEnum;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.support.service.TemplateParseApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.ShunFengUtils;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.AngelDetailTypeEnum;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.common.result.response.ShipResponse;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelShipCallBackContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.export.angelpromise.cmd.ShipInfoForCallBackRequest;
import com.jdh.o2oservice.export.angelpromise.cmd.ShipInfoForSsCallBackRequest;
import com.jdh.o2oservice.export.angelpromise.cmd.ShunFengCallBackRequest;
import com.jdh.o2oservice.ext.ship.reponse.ShunFengResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;


/**
 *  ShipController：运单状态回传
 * <AUTHOR>
 * @date 2024-05-17 17:46
 */
@RequestMapping("/provider/ship")
@Controller
public class ShipController {
    /**
     * log
     */
    private static final Logger log = LoggerFactory.getLogger(ShipController.class);

    @Resource
    private AngelWorkApplication angelWorkApplication;

    @Resource
    TemplateParseApplication templateParseApplication;

    @Autowired
    private ShunFengUtils shunFengUtils;

    @Autowired
    private DuccConfig duccConfig;

    @RequestMapping(value="/dada/callback")
    @ResponseBody
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "达达回调", recordParamBizIdExpress = {"args[0].orderId"})
    public Response<Boolean> shipStatusCallback(@RequestBody ShipInfoForCallBackRequest callBackRequest){
        log.info("DadaShipCallbackController -> shipStatusCallback start, dadaCallbackRequest = {}", JSON.toJSONString(callBackRequest));
        try {
            if(Objects.isNull(callBackRequest)){
                return Response.buildErrorResult("400", "请求参数为空");
            }

            if(callBackRequest.getOrderStatus() == CommonConstant.FIVE && StringUtils.isBlank(callBackRequest.getCancelReason())) {
                callBackRequest.setCancelReason("因运力紧张");
            }
            return Response.buildSuccessResult(angelWorkApplication.shipStatusCallback(callBackRequest));
        }catch (BusinessException be){
            log.error("ShipController -> shipStatusCallback exception, dadaCallbackRequest = {}", JSON.toJSONString(callBackRequest), be);
            throw new BusinessException(AngelPromiseBizErrorCode.DADA_CALL_BACK_HANDLE_ERROR);
        }catch (Exception e){
            log.error("ShipController -> shipStatusCallback exception, dadaCallbackRequest = {}", JSON.toJSONString(callBackRequest), e);
            throw new BusinessException(AngelPromiseBizErrorCode.DADA_CALL_BACK_HANDLE_ERROR);
        }
    }

    /**
     * 闪送回调
     * @param ssCallBackRequest
     * @return
     */
    @RequestMapping(value="/ss/callback")
    @ResponseBody
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "闪送回调", recordParamBizIdExpress = {"args[0].orderNo"}, resultJudgeExpress = {"ret.status"}, resultSuccessCode = {"200"})
    public ShipResponse<Boolean> ssShipStatusCallback(@RequestBody ShipInfoForSsCallBackRequest ssCallBackRequest){
        log.info("ShipController -> ssShipStatusCallback start, ssCallBackRequest = {}", JSON.toJSONString(ssCallBackRequest));
        try {
            if(Objects.isNull(ssCallBackRequest)){
                return ShipResponse.buildErrorResult(400, "请求参数为空");
            }
            Map<String, Object> map = JSON.parseObject(JSON.toJSONString(ssCallBackRequest), Map.class);
            AngelShipCallBackContext angelShipCallBackContext = templateParseApplication.parseToShipCallbackContext(map, ssCallBackRequest.getOrderNo(), AngelDetailTypeEnum.SHANSONG_SUPPLIER);
            return ShipResponse.buildSuccessResult(angelWorkApplication.shipStatusCallback(angelShipCallBackContext));
        }catch (BusinessException be){
            log.error("ShipController -> ssShipStatusCallback exception, ssCallBackRequest = {}", JSON.toJSONString(ssCallBackRequest), be);
            throw new BusinessException(AngelPromiseBizErrorCode.DADA_CALL_BACK_HANDLE_ERROR);
        }catch (Exception e){
            log.error("ShipController -> ssShipStatusCallback exception, ssCallBackRequest = {}", JSON.toJSONString(ssCallBackRequest), e);
            throw new BusinessException(AngelPromiseBizErrorCode.DADA_CALL_BACK_HANDLE_ERROR);
        }
    }

    /**
     * 顺丰回调
     * @param body
     * @return
     */
    @RequestMapping(value="/shunfeng/callback")
    @ResponseBody
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "顺丰回调", resultJudgeExpress = {"ret.errorCode"}, resultSuccessCode = {"0"})
    public ShunFengResponse shunFengShipStatusCallback(@RequestBody String body, @RequestParam("sign") String sign){
        log.info("ShipController -> shunFengShipStatusCallback start, body = {}",body);
        try {
            log.info("theirSign={} body={}",sign,body);
            //校验sign
            String mySign = shunFengUtils.generateOpenSign(body,Long.parseLong(duccConfig.getShunFengConfig().getDevId()),duccConfig.getShunFengConfig().getDevKey());
            if(!mySign.equals(sign)){
                return ShunFengResponse.buildErrorResult(-1,"加密参数校验失败!!!");
            }
            //状态同步
            ShunFengCallBackRequest shunFengCancelCallBackRequest = JSON.parseObject(body, ShunFengCallBackRequest.class);
            if(!duccConfig.getShunFengUrlIndex().contains(shunFengCancelCallBackRequest.getUrl_index())){
                log.info("ShipController -> shipStatusCallback url_index前缀不匹配,逻辑终止!!!");
                return ShunFengResponse.buildSuccessResult(null);
            }
            OperationLogContext operationLogContext = new OperationLogContext();
            operationLogContext.setBizId(shunFengCancelCallBackRequest.getShop_order_id());
            OperationLogContext.put(operationLogContext);
            AngelShipCallBackContext angelShipCallBackContext = toAngelShipCallBackContext(shunFengCancelCallBackRequest);
            angelWorkApplication.shipStatusCallback(angelShipCallBackContext);
            return ShunFengResponse.buildSuccessResult(null);
        }catch (BusinessException be){
            log.error("ShipController -> shipStatusCallback exception, shunFengShipStatusCallback = {}", body, be);
            return ShunFengResponse.buildErrorResult(Integer.parseInt(be.getErrorCode().getCode()),be.getErrorCode().getDescription());
        }catch (Exception e){
            log.error("ShipController -> shipStatusCallback exception, shunFengShipStatusCallback = {}", body, e);
            return ShunFengResponse.buildErrorResult(Integer.parseInt(BusinessErrorCode.UNKNOWN_ERROR.getCode()),BusinessErrorCode.UNKNOWN_ERROR.getDescription());
        } finally {
            OperationLogContext.remove();
        }
    }

    /**
     * 顺丰入参对象转成内部对象
     * @param shunFengCancelCallBackRequest
     * @return
     */
    private AngelShipCallBackContext toAngelShipCallBackContext(ShunFengCallBackRequest shunFengCancelCallBackRequest){
        AngelShipCallBackContext angelShipCallBackContext = AngelShipCallBackContext.builder().build();
        if(StringUtils.isNotEmpty(shunFengCancelCallBackRequest.getRider_lat())){
            angelShipCallBackContext.setLatitude(Double.parseDouble(shunFengCancelCallBackRequest.getRider_lat()));
        }
        if(StringUtils.isNotEmpty(shunFengCancelCallBackRequest.getRider_lng())){
            angelShipCallBackContext.setLongitude(Double.parseDouble(shunFengCancelCallBackRequest.getRider_lng()));
        }


        angelShipCallBackContext.setOrderStatus(ShunFengShipStatusEnum.transferToStandShipStatus(shunFengCancelCallBackRequest.getOrder_status()));
        angelShipCallBackContext.setOrderId(Long.parseLong(shunFengCancelCallBackRequest.getShop_order_id()));
        angelShipCallBackContext.setDmId("0");
        angelShipCallBackContext.setDmName(shunFengCancelCallBackRequest.getOperator_name());
        angelShipCallBackContext.setDmMobile(shunFengCancelCallBackRequest.getOperator_phone());
        angelShipCallBackContext.setTime(TimeUtils.dateTimeToStr(new Date(shunFengCancelCallBackRequest.getPush_time()*1000)));
        return angelShipCallBackContext;
    }
}
