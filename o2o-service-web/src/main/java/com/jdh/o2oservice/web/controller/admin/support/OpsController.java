package com.jdh.o2oservice.web.controller.admin.support;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.angelpromise.AngelWorkStatusApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.provider.service.ProviderPromiseApplication;
import com.jdh.o2oservice.application.support.service.OpsApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.event.extension.EventRepository;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipCancelCodeStatusEnum;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.angelpromise.cmd.*;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseBindSpecimenCodeCmd;
import com.jdh.o2oservice.export.provider.cmd.ProviderCallbackCmd;
import com.jdh.o2oservice.export.support.command.*;
import com.jdh.o2oservice.export.support.query.*;
import com.jdh.o2oservice.interceptor.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * 研发运维接口
 */
@Slf4j
@RestController
@RequestMapping("/support/ops")
public class OpsController {

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * opsApplication
     */
    @Autowired
    private OpsApplication opsApplication;

    /**
     * providerPromiseApplication
     */
    @Autowired
    private ProviderPromiseApplication providerPromiseApplication;

    /**
     * 事件
     */
    @Resource
    private EventRepository eventRepository;

    /**
     * angelWorkStatusApplication
     */
    @Resource
    private AngelWorkStatusApplication angelWorkStatusApplication;

    /** */
    @Resource
    private AngelPromiseApplication angelPromiseApplication;

    /** */
    @Resource
    private AngelWorkApplication angelWorkApplication;

    /**
     * 按订单ID查询 订单信息
     *
     * @param orderId 订单id
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/queryOrderByOrderId")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryOrderByOrderId")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询订单信息")
    Response<Map<String, Object>> queryOrderByOrderId(Long orderId) {
        if (Objects.isNull(orderId)) {
            return ResponseUtil.buildSuccResponse(new HashMap<>());
        }
        Map<String, Object> result = opsApplication.queryByOrderId(orderId);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * 外部服务单ID查询
     *
     * @param sourceVoucherId sourceVoucherId
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/queryVoucherListBySourceVoucherId")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryVoucherListBySourceVoucherId")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "外部服务单ID查询")
    Response<List<Map<String, Object>>> queryVoucherListBySourceVoucherId(String sourceVoucherId) {
        if (StrUtil.isBlank(sourceVoucherId)) {
            return ResponseUtil.buildSuccResponse(new ArrayList<>());
        }
        List<Map<String, Object>> result = opsApplication.queryVoucherListBySourceVoucherId(sourceVoucherId);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询履约单列表
     *
     * @param voucherId voucherId
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/queryPromiseListByVoucherId")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryPromiseListByVoucherId")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询履约单列表")
    Response<List<Map<String, Object>>> queryPromiseListByVoucherId(Long voucherId) {
        if (Objects.isNull(voucherId)) {
            return ResponseUtil.buildSuccResponse(new ArrayList<>());
        }
        List<Map<String, Object>> result = opsApplication.queryPromiseListByVoucherId(voucherId);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询履约单历史信息列表
     *
     * @param promiseId promiseId
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/queryPromiseHistoryListByPromiseId")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryPromiseHistoryListByPromiseId")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询履约单历史信息列表")
    Response<List<Map<String, Object>>> queryPromiseHistoryListByPromiseId(Long promiseId) {
        if (Objects.isNull(promiseId)) {
            return ResponseUtil.buildSuccResponse(new ArrayList<>());
        }
        List<Map<String, Object>> result = opsApplication.queryPromiseHistoryListByPromiseId(promiseId);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * queryEventByAggregateId
     *
     * @param aggregateId aggregateId
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/queryEventByAggregateId")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryEventByAggregateId")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询事件列表")
    Response<List<Map<String, Object>>> queryEventByAggregateId(String aggregateId) {
        if (StrUtil.isBlank(aggregateId)) {
            return ResponseUtil.buildSuccResponse(new ArrayList<>());
        }
        List<Map<String, Object>> result = opsApplication.queryEventByAggregateId(aggregateId);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * queryEventConsumerByEventId
     *
     * @param eventId eventId
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/queryEventConsumerByEventId")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryEventConsumerByEventId")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询事件")
    Response<List<Map<String, Object>>> queryEventConsumerByEventId(Long eventId) {
        if (Objects.isNull(eventId)) {
            return ResponseUtil.buildSuccResponse(new ArrayList<>());
        }
        List<Map<String, Object>> result = opsApplication.queryEventConsumerByEventId(eventId);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * @param eventId
     * @param consumerCode
     * @return
     */
    @RequestMapping("/disposeAsyncEvent")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.disposeAsyncEvent")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "事件重放", recordParamBizIdExpress = {"args[0]"})
    public Response<Boolean> disposeAsyncEvent(Long eventId, String consumerCode) {
        if (Objects.isNull(eventId) || StrUtil.isBlank(consumerCode)) {
            return ResponseUtil.buildSuccResponse(Boolean.FALSE);
        }

        Event existEvent = eventRepository.getEvent(eventId);
        if (Objects.isNull(existEvent)) {
            throw new IllegalArgumentException("event not exist");
        }
        WrapperEventConsumer wrapper = EventConsumerRegister.getConsumer(existEvent, consumerCode);
        wrapper.accept(existEvent);
        return ResponseUtil.buildSuccResponse(Boolean.TRUE);
    }

    /**
     * 按SKU ID查询SKU信息
     *
     * @param skuId SKU ID
     * @return {@link Response}<{@link Map}<{@link String},{@link Object}>>
     */
    @RequestMapping("/querySkuInfoBySkuId")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.querySkuInfoBySkuId")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询SKU信息")
    public Response<Map<String, Object>> querySkuInfoBySkuId(String skuId) {
        if (StrUtil.isBlank(skuId)) {
            return ResponseUtil.buildSuccResponse(new HashMap<>());
        }
        Map<String, Object> result = opsApplication.querySkuInfoBySkuId(skuId);
        return ResponseUtil.buildSuccResponse(result);
    }

    @RequestMapping("/queryPromiseInfo")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryPromiseInfo")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询履约单详细")
    public Response<Map<String, Object>> queryPromiseInfo(String promiseId, String sourceVoucherId, String medPromiseId) {
        if (StrUtil.isBlank(promiseId) && StrUtil.isBlank(sourceVoucherId) && StrUtil.isBlank(medPromiseId)) {
            return ResponseUtil.buildSuccResponse(new HashMap<>());
        }
        Map<String, Object> result = opsApplication.queryPromiseInfo(promiseId, sourceVoucherId, medPromiseId);
        return ResponseUtil.buildSuccResponse(result);
    }


    @RequestMapping("/queryPromiseInfoV2")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryPromiseInfoV2")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询履约单详细")
    public Response<List<Map<String, Object>>> queryPromiseInfoV2(String sourceVoucherId, String promiseId, String medPromiseId, String orderId, String specimenCode) {
        if (StrUtil.isBlank(sourceVoucherId) && StrUtil.isBlank(promiseId) && StrUtil.isBlank(medPromiseId) && StrUtil.isBlank(orderId) && StrUtil.isBlank(specimenCode)) {
            return ResponseUtil.buildSuccResponse(new ArrayList<>());
        }
        List<Map<String, Object>> result = opsApplication.queryPromiseInfoV2(sourceVoucherId, promiseId, medPromiseId, orderId, specimenCode);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 分页查询事件
     *
     * @param request 请求
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @PostMapping("/queryEventPage")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryEventPage")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "分页查询事件")
    public Response<PageDto<Map<String, Object>>> queryEventPage(@RequestBody EventPageRequest request) {
        PageDto<Map<String, Object>> result = opsApplication.queryEventPage(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 模仿发布事件
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    @PostMapping("/imitatePublishEvent")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.imitatePublishEvent")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "模仿发布事件", recordParamBizIdExpress = {"args[0].aggregateId"})
    Response<Boolean> imitatePublishEvent(@RequestBody ImitatePublishEventCmd cmd) {
        return ResponseUtil.buildSuccResponse(opsApplication.imitatePublishEvent(cmd));
    }

    /**
     * 分页查询 业务身份列表
     *
     * @param request 请求
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @PostMapping("/queryVerticalCodePage")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryVerticalCodePage")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "业务身份列表")
    public Response<PageDto<Map<String, Object>>> queryVerticalCodePage(@RequestBody VerticalCodePageRequest request) {
        PageDto<Map<String, Object>> result = opsApplication.queryVerticalCodePage(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 删除 业务身份数据
     *
     * @param verticalCode verticalCode
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @RequestMapping("/deleteVerticalCode")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.deleteVerticalCode")
    @OperationLog(operationType = OpTypeEnum.DEL, operationDesc = "删除业务身份", recordParamBizIdExpress = {"args[0]"})
    public Response<Boolean> deleteVerticalCode(String verticalCode) {
        if (StrUtil.isBlank(verticalCode)) {
            return ResponseUtil.buildSuccResponse(Boolean.FALSE);
        }
        Boolean result = opsApplication.deleteVerticalCode(verticalCode);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 启用，停用 业务身份数据
     *
     * @param verticalCode verticalCode
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @RequestMapping("/enableOrDisableVerticalCode")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.enableOrDisableVerticalCode")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "启停业务身份", recordParamBizIdExpress = {"args[0]"})
    public Response<Boolean> enableOrDisableVerticalCode(String verticalCode, Integer opType) {
        if (StrUtil.isBlank(verticalCode) || !YnStatusEnum.contains(opType)) {
            return ResponseUtil.buildSuccResponse(Boolean.FALSE);
        }
        Boolean result = opsApplication.enableOrDisableVerticalCode(verticalCode, opType);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 新增 业务身份数据
     *
     * @param cmd cmd
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @PostMapping("/createVerticalCode")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.createVerticalCode")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "新增业务身份", recordParamBizIdExpress = {"args[0]"})
    public Response<Boolean> createVerticalCode(@RequestBody CreateVerticalCodeCmd cmd) {
        Boolean result = opsApplication.createVerticalCode(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 分页查询 serviceType列表
     *
     * @param request 请求
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @PostMapping("/queryServiceTypePage")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryServiceTypePage")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "分页查询serviceType列表")
    public Response<PageDto<Map<String, Object>>> queryServiceTypePage(@RequestBody ServiceTypePageRequest request) {
        PageDto<Map<String, Object>> result = opsApplication.queryServiceTypePage(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 创建 serviceType
     *
     * @param cmd cmd
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @PostMapping("/createServiceType")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.createServiceType")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "创建serviceType", recordParamBizIdExpress = {"args[0].serviceType"})
    public Response<Boolean> createServiceType(@RequestBody CreateServiceTypeCmd cmd) {
        Boolean result = opsApplication.createServiceType(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 删除 serviceType
     *
     * @param serviceType serviceType
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @RequestMapping("/deleteServiceType")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.deleteServiceType")
    @OperationLog(operationType = OpTypeEnum.DEL, operationDesc = "删除serviceType", recordParamBizIdExpress = {"args[0]"})
    public Response<Boolean> deleteServiceType(String serviceType) {
        if (StrUtil.isBlank(serviceType)) {
            return ResponseUtil.buildSuccResponse(Boolean.FALSE);
        }
        Boolean result = opsApplication.deleteServiceType(serviceType);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询 serviceType - 业务分类 列表
     *
     * @param request 请求
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @PostMapping("/queryServiceTypeCategoryRelation")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryServiceTypeCategoryRelation")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询serviceType列表")
    public Response<List<Map<String, Object>>> queryServiceTypeCategoryRelation(@RequestBody ServiceTypePageRequest request) {
        List<Map<String, Object>> result = opsApplication.queryServiceTypeCategoryRelation(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 创建 serviceType - category 关系
     *
     * @param cmd cmd
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @PostMapping("/createServiceTypeCategoryRelation")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.createServiceTypeCategoryRelation")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "创建serviceType-category关系", recordParamBizIdExpress = {"args[0].serviceType"})
    public Response<Boolean> createServiceTypeCategoryRelation(@RequestBody CreateServiceTypeCategoryRelationCmd cmd) {
        Boolean result = opsApplication.createServiceTypeCategoryRelation(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * 删除 serviceType - category 关系
     *
     * @param serviceType 服务类型
     * @param categoryId  类别id
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping("/deleteServiceTypeCategoryRelation")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.deleteServiceTypeCategoryRelation")
    @OperationLog(operationType = OpTypeEnum.DEL, operationDesc = "删除serviceType-category关系", recordParamBizIdExpress = {"args[0]"})
    public Response<Boolean> deleteServiceTypeCategoryRelation(String serviceType, Integer categoryId) {
        if (StrUtil.isBlank(serviceType) || Objects.isNull(categoryId)) {
            return ResponseUtil.buildSuccResponse(Boolean.FALSE);
        }
        Boolean result = opsApplication.deleteServiceTypeCategoryRelation(serviceType, categoryId);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * 分页查询 business列表
     *
     * @param request 请求
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @PostMapping("/queryBusinessModePage")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryBusinessModePage")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "分页查询businessModel列表")
    public Response<PageDto<Map<String, Object>>> queryBusinessModePage(@RequestBody BusinessModePageRequest request) {
        PageDto<Map<String, Object>> result = opsApplication.queryBusinessModePage(request);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * 创建 businessMode
     *
     * @param cmd cmd
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @PostMapping("/createBusinessMode")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.createBusinessMode")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "创建businessMode", recordParamBizIdExpress = {"args[0].code"})
    public Response<Boolean> createBusinessMode(@RequestBody CreateBusinessModeCmd cmd) {
        Boolean result = opsApplication.createBusinessMode(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 删除 businessMode
     *
     * @param code code
     * @return {@link Response}<{@link PageDto}<{@link Map}<{@link String},{@link Object}>>>
     */
    @RequestMapping("/deleteBusinessMode")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.deleteBusinessMode")
    @OperationLog(operationType = OpTypeEnum.DEL, operationDesc = "删除businessMode", recordParamBizIdExpress = {"args[0]"})
    public Response<Boolean> deleteBusinessMode(String code) {
        if (StrUtil.isBlank(code)) {
            return ResponseUtil.buildSuccResponse(Boolean.FALSE);
        }
        Boolean result = opsApplication.deleteBusinessMode(code);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询域列表
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/queryDomainList")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryDomainList")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询域列表")
    public Response<List<Map<String, String>>> queryDomainList() {
        List<Map<String, String>> result = opsApplication.queryDomainList();
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 按域查询聚合列表
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/queryAggregateListByDomainCode")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryAggregateListByDomainCode")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "按域查询聚合列表")
    public Response<List<Map<String, String>>> queryAggregateListByDomainCode(String domainCode) {
        List<Map<String, String>> result = opsApplication.queryAggregateListByDomainCode(domainCode);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 修改订单完成状态
     *
     * @param orderId 订单id
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping("/reviseOrderFinishState")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.reviseOrderFinishState")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "订单拉完成", recordParamBizIdExpress = {"args[0]"})
    public Response<Boolean> reviseOrderFinishState(Long orderId, Integer idCompanyBranch) {
        if (Objects.isNull(orderId)) {
            return ResponseUtil.buildSuccResponse(false);
        }
        Boolean result = opsApplication.reviseOrderFinishState(orderId, idCompanyBranch);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 根据聚合根id查找 event事件链
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/queryEventChain")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryEventChain")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "根据聚合根id查找event事件链")
    public Response<Map<String, List<Map<String, Object>>>> queryEventChain(String domainCode, String aggregateCode, String aggregateId) {
        if (StrUtil.isBlank(domainCode) || StrUtil.isBlank(aggregateId)) {
            return ResponseUtil.buildSuccResponse(new HashMap<>());
        }
        Map<String, List<Map<String, Object>>> result = opsApplication.queryEventChain(domainCode, aggregateCode, aggregateId);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * 根据聚合根id查找 event事件链 不聚合
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/queryEventChainNoAgg")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryEventChain")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "根据聚合根id查找event事件链-不聚合")
    public Response<List<Map<String, Object>>> queryEventChainNoAgg(String domainCode, String aggregateCode, String aggregateId) {
        if (StrUtil.isBlank(domainCode) || StrUtil.isBlank(aggregateId)) {
            return ResponseUtil.buildSuccResponse(new ArrayList<>());
        }
        List<Map<String, Object>> result = opsApplication.queryEventChainNoAgg(domainCode, aggregateCode, aggregateId);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * queryReachTemplatePage
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/queryReachTemplatePage")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询触达模板")
    public Response<PageDto<Map<String, Object>>> queryReachTemplatePage(@RequestBody ReachTemplatePageRequest request) {
        PageDto<Map<String, Object>> result = opsApplication.queryReachTemplatePage(request);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * queryReachMessageBizTypeList
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/queryReachMessageBizTypeList")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询触达类型列表")
    public Response<List<Map<String, Object>>> queryReachMessageBizTypeList() {
        List<Map<String, Object>> result = opsApplication.queryReachMessageTypeList();
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * queryReachAccountList
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/queryReachAccountList")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询触达账号列表")
    public Response<List<Map<String, Object>>> queryReachAccountList() {
        List<Map<String, Object>> result = opsApplication.queryReachAccountList();
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * queryReachTemplatePage
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/createReachTemplate")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "创建触达模板", recordParamBizIdExpress = {"args[0].templateId"})
    public Response<Boolean> createReachTemplate(@RequestBody CreateReachTemplateCmd cmd) {
        Boolean result = opsApplication.createReachTemplate(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * updateReachTemplate
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/updateReachTemplate")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "更新触达模板", recordParamBizIdExpress = {"args[0].templateId"})
    public Response<Boolean> updateReachTemplate(@RequestBody UpdateReachTemplateCmd cmd) {
        Boolean result = opsApplication.updateReachTemplate(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * deleteReachTemplate
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/deleteReachTemplate")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.DEL, operationDesc = "删除触达模板", recordParamBizIdExpress = {"args[0].templateId"})
    public Response<Boolean> deleteReachTemplate(@RequestBody DeleteReachTemplateCmd cmd) {
        Boolean result = opsApplication.deleteReachTemplate(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * queryReachTaskPage
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/queryReachTaskPage")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询触达任务")
    public Response<PageDto<Map<String, Object>>> queryReachTaskPage(@RequestBody ReachTaskPageRequest request) {
        PageDto<Map<String, Object>> result = opsApplication.queryReachTaskPage(request);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * queryReachTaskPage
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/queryReachTriggerPage")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询触达触发器")
    public Response<PageDto<Map<String, Object>>> queryReachTriggerPage(@RequestBody ReachTriggerPageRequest request) {
        PageDto<Map<String, Object>> result = opsApplication.queryReachTriggerPage(request);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * deleteReachTrigger
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/deleteReachTrigger")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.DEL, operationDesc = "删除触达触发器", recordParamBizIdExpress = {"args[0].triggerId"})
    public Response<Boolean> deleteReachTrigger(@RequestBody DeleteReachTriggerCmd cmd) {
        Boolean result = opsApplication.deleteReachTrigger(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * updateReachTrigger
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/updateReachTrigger")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "更新触达触发器", recordParamBizIdExpress = {"args[0].triggerId"})
    public Response<Boolean> updateReachTrigger(@RequestBody UpdateReachTriggerCmd cmd) {
        Boolean result = opsApplication.updateReachTrigger(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * createReachTrigger
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/createReachTrigger")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "创建触达触发器")
    public Response<Boolean> createReachTrigger(@RequestBody CreateReachTriggerCmd cmd) {
        Boolean result = opsApplication.createReachTrigger(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * queryTriggerSelectUserFunIdList
     *
     * @return {@link Response}<{@link List}<{@link String}>>
     */
    @RequestMapping("/queryTriggerSelectUserFunIdList")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询触达触发器用户函数列表")
    public Response<List<String>> queryTriggerSelectUserFunIdList() {
        List<String> result = opsApplication.queryTriggerSelectUserFunIdList();
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * queryTriggerSelectDataFunIdList
     *
     * @return {@link Response}<{@link List}<{@link String}>>
     */
    @RequestMapping("/queryTriggerSelectDataFunIdList")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询触达触发器数据函数列表")
    public Response<List<String>> queryTriggerSelectDataFunIdList() {
        List<String> result = opsApplication.queryTriggerSelectDataFunIdList();
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * queryReachMessagePage
     *
     * @return {@link Response}<{@link List}<{@link Map}<{@link String},{@link String}>>>
     */
    @RequestMapping("/queryReachMessagePage")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询触达消息列表")
    public Response<PageDto<Map<String, Object>>> queryReachMessagePage(@RequestBody ReachMessagePageRequest request) {
        PageDto<Map<String, Object>> result = opsApplication.queryReachMessagePage(request);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * 批量绑定条码
     * {"serviceType":"test","specimenCodeList":[{"medicalPromiseId":160345842778135,"promiseId":160345842778137,"specimenCode":"JD0014072736"}],"verticalCode":"xfylHomeSelfTest1Phase"}
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    @RequestMapping("/batchBindSpecimenCode")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "批量绑定条码")
    public Response<Boolean> batchBindSpecimenCode(@RequestBody MedicalPromiseBindSpecimenCodeCmd cmd) {
        return ResponseUtil.buildSuccResponse(opsApplication.batchBindSpecimenCode(cmd));
    }


    /**
     * queryEventConsumerByEventId
     *
     * @param medPromiseId medPromiseId
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/queryReportUrl")
    @LogAndAlarm(jKey = "com.jdh.o2oservice.web.controller.support.OpsController.queryReportUrl")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询报告地址")
    public Response<Map<String, String>> queryReportUrl(Long medPromiseId) {
        return ResponseUtil.buildSuccResponse(opsApplication.queryReportUrl(medPromiseId));
    }

    /**
     * callback
     *
     * @param param param
     * @return {@link Response }<{@link Boolean }>
     */
    @RequestMapping("/callback")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "供应商端回调接口", recordParamBizIdExpress = {"args[0].appointmentId"})
    public Response<Boolean> callback(@RequestBody ProviderCallbackCmd param) {
        Boolean res = providerPromiseApplication.callback(param);
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * angelWorkExecute
     * 出门、取消
     * @param workCmd workCmd
     * @return {@link Response }<{@link Boolean }>
     */
    @RequestMapping("/angelWorkExecute")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "模拟操作work", recordParamBizIdExpress = {"args[0].workId"})
    public Response<Boolean> angelWorkExecute(@RequestBody AngelWorkExecuteCmd workCmd) {
        if(Objects.isNull(workCmd)) {
            return ResponseUtil.buildSuccResponse(false);
        }
        angelWorkStatusApplication.executeAngelWork(workCmd);
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * codeVerification
     *
     * @param codeVerificationCmd codeVerificationCmd
     * @return {@link Response }<{@link Boolean }>
     */
    @RequestMapping("/codeVerification")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "核验消费码", recordParamBizIdExpress = {"args[0].workId"})
    public Response<Boolean> codeVerification(@RequestBody AngelWorkCodeVerificationCmd codeVerificationCmd) {
        Boolean result = angelPromiseApplication.codeVerification(codeVerificationCmd);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * taskBindSpecimenCode
     *
     * @param bindSpecimenCodeCmd bindSpecimenCodeCmd
     * @return {@link Response }<{@link Boolean }>
     */
    @RequestMapping("/taskBindSpecimenCode")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "任务单绑条码", recordParamBizIdExpress = {"args[0].taskId"})
    public Response<Boolean> taskBindSpecimenCode(@RequestBody  AngelWorkBindSpecimenCodeCmd bindSpecimenCodeCmd) {
        Boolean result = angelPromiseApplication.bindSpecimenCode(bindSpecimenCodeCmd);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * workSubmitBindBarCode
     *
     * @param angelCheckBarCodeCmd angelCheckBarCodeCmd
     * @return {@link Response }<{@link Boolean }>
     */
    @RequestMapping("/workSubmitBindBarCode")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "提交样本信息条码", recordParamBizIdExpress = {"args[0].workId"})
    public Response<Boolean> workSubmitBindBarCode(@RequestBody AngelCheckBarCodeCmd angelCheckBarCodeCmd) {
        Boolean result = angelWorkApplication.submitBindBarCode(angelCheckBarCodeCmd);
        return ResponseUtil.buildSuccResponse(result);
    }


    /**
     * submitWorkCompleteInfo
     *
     * @param cmd cmd
     * @return {@link Response }<{@link Boolean }>
     */
    @RequestMapping("/submitWorkCompleteInfo")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "提交工单完成", recordParamBizIdExpress = {"args[0].workId"})
    public Response<Boolean> submitWorkCompleteInfo(@RequestBody SubmitWorkCompleteInfoCmd cmd) {
        return ResponseUtil.buildSuccResponse(angelPromiseApplication.submitWorkCompleteInfo(cmd));
    }

    /**
     * 取消运单
     *
     * @param cmd cmd
     * @return  {@link Response }<{@link Boolean }>
     */
    @RequestMapping("/angelCancelShip")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "取消运单", recordParamBizIdExpress = {"args[0].workId"})
    public Response<Boolean> workCancelShip(AngelWorkCancelShipCmd cmd) {
        cmd.setStandCancelCode(AngelShipCancelCodeStatusEnum.NURSE_CANCEL.getType());
        return ResponseUtil.buildSuccResponse(angelWorkApplication.cancelShip(cmd));
    }


    /**
     * 自配送送达
     *
     * @param deliverCmd deliverCmd
     * @return  {@link Response }<{@link Boolean }>
     */
    @RequestMapping("/workDeliver")
    @LogAndAlarm
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "自配送送达", recordParamBizIdExpress = {"args[0].workId"})
    public Response<Boolean> workDeliver(DeliverCmd deliverCmd) {
        return ResponseUtil.buildSuccResponse(angelPromiseApplication.deliver(deliverCmd));
    }



}
