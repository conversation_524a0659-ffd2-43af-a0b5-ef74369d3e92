package com.jdh.o2oservice.web.controller.admin.provider;

import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.provider.enums.ProviderErrorCode;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.product.cmd.ServiceItemImportCmd;
import com.jdh.o2oservice.export.provider.cmd.*;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 商家门店项目
 *
 * <AUTHOR>
 * @date 2024/04/23
 */
@Slf4j
@RestController
@RequestMapping("/provider/station/serviceItem")
public class ProviderStationServiceItemController {
    
    /**
     * 门店项目接口信息
     */
    @Resource
    ProviderStoreApplication providerStoreApplication;

    /**
     * 门店项目保存
     *
     * @param cmd cmd
     * @return response
     */
    @PostMapping(value = "/add")
    @LogAndAlarm(jKey = "ProviderStationServiceItemController.add")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "新增门店项目信息", recordParamBizIdExpress = {"args[0].stationId"})
    public Response<Boolean> add(@Validated @RequestBody JdhStationServiceItemRelCreateCmd cmd) {
        Boolean ret = providerStoreApplication.addStationServiceItemRel(cmd);
        if (Boolean.TRUE.equals(ret)) {
            return ResponseUtil.buildSuccResponse(ret);
        }
        return ResponseUtil.buildErrResponse(ProviderErrorCode.PROVIDER_STATION_SERVICE_ITEM_SAVE_FAIL);
    }
    
    /**
     * 门店项目更新
     *
     * @param cmd cmd
     * @return response
     */
    @PostMapping(value = "/update")
    @LogAndAlarm(jKey = "ProviderStationServiceItemController.update")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "更新门店项目信息", recordParamBizIdExpress = {"args[0].stationId"})
    public Response<Boolean> update(@Validated @RequestBody JdhStationServiceItemRelUpdateCmd cmd) {
        Boolean ret = providerStoreApplication.updateStationServiceItemRel(cmd);
        if (Boolean.TRUE.equals(ret)) {
            return ResponseUtil.buildSuccResponse(ret);
        }
        return ResponseUtil.buildErrResponse(ProviderErrorCode.PROVIDER_STATION_SERVICE_ITEM_UPDATE_FAIL);
    }

    /**
     * 门店项目删除
     *
     * @param cmd cmd
     * @return response
     */
    @PostMapping(value = "/delete")
    @LogAndAlarm(jKey = "ProviderStationServiceItemController.delete")
    @OperationLog(operationType = OpTypeEnum.DEL, operationDesc = "删除门店项目信息", recordParamBizIdExpress = {"args[0].stationId"})
    public Response<Boolean> delete(@Validated @RequestBody JdhStationServiceItemRelDeleteCmd cmd) {
        Boolean ret = providerStoreApplication.deleteStationServiceItemRel(cmd);
        if (Boolean.TRUE.equals(ret)) {
            return ResponseUtil.buildSuccResponse(ret);
        }
        return ResponseUtil.buildErrResponse(ProviderErrorCode.PROVIDER_STATION_SERVICE_ITEM_DELETE_FAIL);
    }
    
    /**
     * 查询门店项目
     *
     * @param request request
     * @return response
     */
    @PostMapping(value = "/query")
    @LogAndAlarm(jKey = "ProviderStationServiceItemController.query")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询门店项目信息")
    public Response<JdhStationServiceItemRelDto> query(@RequestBody JdhStationServiceItemRelRequest request) {
        request.setQueryIgnoreOnOffShelf(true);
        JdhStationServiceItemRelDto dto = providerStoreApplication.queryStationServiceItemRel(request);
        return ResponseUtil.buildSuccResponse(dto);
    }
    
    /**
     * 分页查询门店项目
     *
     * @param request request
     * @return response
     */
    @PostMapping(value = "/queryPageList")
    @LogAndAlarm(jKey = "ProviderStationServiceItemController.queryPageList")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "分页查询门店项目信息")
    public Response<PageDto<JdhStationServiceItemRelDto>> queryPageList(@RequestBody JdhStationServiceItemRelRequest request) {
        if (request != null && request.getOnOffShelf() == null) {
            request.setQueryIgnoreOnOffShelf(true);
        }
        PageDto<JdhStationServiceItemRelDto> pageJdhSkuInfo = providerStoreApplication.queryStationServiceItemRelPage(request);
        return ResponseUtil.buildSuccResponse(pageJdhSkuInfo);
    }

    /**
     * 分页查询门店项目
     *
     * @param request request
     * @return response
     */
    @PostMapping(value = "/queryList")
    @LogAndAlarm(jKey = "ProviderStationServiceItemController.queryList")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询门店项目列表信息")
    public Response<List<JdhStationServiceItemRelDto>> queryList(@RequestBody JdhStationServiceItemRelRequest request) {
        request.setQueryIgnoreOnOffShelf(true);
        List<JdhStationServiceItemRelDto> pageJdhSkuInfo = providerStoreApplication.queryStationServiceItemRelList(request);
        return ResponseUtil.buildSuccResponse(pageJdhSkuInfo);
    }

    /**
     *
     * 根据实验室名称查询实验室列表
     * @param listLaboratoryByStoreNameCmd
     * @return response
     */
    @PostMapping(value = "/listLaboratoryByStoreName")
    @LogAndAlarm(jKey = "ProviderStationServiceItemController.listLaboratoryByStoreName")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "根据实验室名称查询实验室列表")
    public Response<List<StoreInfoDto>> listLaboratoryByStoreName(@Validated @RequestBody ListLaboratoryByStoreNameCmd listLaboratoryByStoreNameCmd) {
        listLaboratoryByStoreNameCmd.setBusinessType(16);
        listLaboratoryByStoreNameCmd.setPageNum(CommonConstant.ONE);
        listLaboratoryByStoreNameCmd.setPageSize(CommonConstant.TEN);
        List<StoreInfoDto> result = providerStoreApplication.listLaboratoryByStoreName(listLaboratoryByStoreNameCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 导入项目
     *
     * @param cmd cmd
     * @return response
     */
    @PostMapping(value = "/importServiceItem")
    @LogAndAlarm(jKey = "ProviderStationServiceItemController.importServiceItem")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "导入项目")
    public Response<Boolean> importServiceItem(@RequestBody StationServiceItemImportCmd cmd) {
        String erp = LoginContext.getLoginContext().getPin();
        cmd.setErp(erp);
        Boolean ret = providerStoreApplication.importServiceItem(cmd);
        return ResponseUtil.buildSuccResponse(ret);
    }
}
