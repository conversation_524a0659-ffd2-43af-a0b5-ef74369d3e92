package com.jdh.o2oservice.web.controller.admin.angelPromise;

import com.alibaba.fastjson.JSON;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.application.ztools.AngelWorkToolsApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailForManDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelWorkDetailForManRequest;
import com.jdh.o2oservice.export.ztools.cmd.ManBindSpecimenCodeCmd;
import com.jdh.o2oservice.export.ztools.query.AngelWorkPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @ClassName:AngelWorkController
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/23 15:40
 * @Vserion: 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/angelWork")
public class AngelWorkController {

    @Resource
    private AngelWorkApplication angelWorkApplication;

    @Resource
    private AngelWorkToolsApplication angelWorkToolsApplication;

    /**
     * fileManageApplication
     */
    @Resource
    private FileManageApplication fileManageApplication;

    /**
     * 查询工单详情
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/queryAngelWorkDetail")
    @LogAndAlarm(jKey = "AngelWorkController.queryAngelWorkDetail")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询工单详情")
    public Response<AngelWorkDetailForManDto> queryAngelWorkDetail(@RequestBody AngelWorkDetailForManRequest request) {
        request.setOperator(LoginContext.getLoginContext().getPin());
        return ResponseUtil.buildSuccResponse(angelWorkApplication.queryAngelWorkForMan(request));
    }


    @ResponseBody
    @LogAndAlarm(jKey = "AngelWorkController.manBindSpecimenCode")
    @RequestMapping(method = RequestMethod.POST, value = "/manBindSpecimenCode")
    @OperationLog(operationType = OpTypeEnum.UPDATE, operationDesc = "运营工具绑码", recordParamBizIdExpress = {"args[0].medicalPromiseId"})
    public Response<Boolean> manBindSpecimenCode(@RequestBody ManBindSpecimenCodeCmd manBindSpecimenCodeCmd) {
        log.info("[AngelWorkManToolController -> manBindSpecimenCode],运营工具绑码!manBindSpecimenCodeCmd={}", JSON.toJSONString(manBindSpecimenCodeCmd));
        manBindSpecimenCodeCmd.setSubmitToStation(Boolean.TRUE);
        angelWorkToolsApplication.checkMedicalPromiseStatus("bindSpecimenCode", manBindSpecimenCodeCmd);
        manBindSpecimenCodeCmd.setManSource(CommonConstant.ONE);
        return ResponseUtil.buildSuccResponse(angelWorkToolsApplication.manBindSpecimenCode(manBindSpecimenCodeCmd));
    }
}
