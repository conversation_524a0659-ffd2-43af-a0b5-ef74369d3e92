package com.jdh.o2oservice.web.controller.admin.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.common.web.LoginContext;
import com.jdh.o2oservice.annotation.OperationLog;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.ErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.enums.FileImportTypeEnum;
import com.jdh.o2oservice.enums.OpTypeEnum;
import com.jdh.o2oservice.export.support.FileManageJsfExport;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.support.dto.FileTaskDto;
import com.jdh.o2oservice.export.support.query.PageFileTaskRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 文件管理controller接口
 * @author: yangxiyu
 * @date: 2024/3/20 4:47 下午
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/support/fileManage")
public class FileManageController {

    /**
     * fileManageApplication
     */
    @Resource
    private FileManageApplication fileManageApplication;
    @Autowired
    private FileManageJsfExport fileManageJsfExport;


    /**
     * 获取文件上传的链接
     *
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/generatePutUrl")
    @LogAndAlarm(jKey = "FileManageController.generatePutUrl")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "获取文件上传的链接")
    Response<FilePreSignedUrlDto> generatePutUrl(@RequestBody GeneratePutUrlCommand command) {
        String  pin = LoginContext.getLoginContext().getPin();
        command.setUserPin(pin);
        FilePreSignedUrlDto dto = fileManageApplication.generatePutUrl(command);
        return ResponseUtil.buildSuccResponse(dto);
    }

    /**
     * 获取文件上传的链接
     *
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/generateGetUrl")
    @LogAndAlarm(jKey = "FileManageController.generateGetUrl")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "获取文件上传的链接")
    Response<List<FilePreSignedUrlDto>> generateGetUrl(@RequestBody GenerateGetUrlCommand command) {
        String  pin = LoginContext.getLoginContext().getPin();
        command.setUserPin(pin);
        List<FilePreSignedUrlDto>  dto = fileManageApplication.generateGetUrl(command);
        return ResponseUtil.buildSuccResponse(dto);
    }
    /**
     * 获取文件上传的链接
     *
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/generateTemplateUrl")
    @LogAndAlarm(jKey = "FileManageController.generateTemplateUrl")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "获取文件上传的链接")
    Response<List<FilePreSignedUrlDto>> generateTemplateUrl(@RequestBody GenerateGetUrlCommand command) {
        List<FilePreSignedUrlDto>  dto = fileManageApplication.generateGetUrl(command);
        return ResponseUtil.buildSuccResponse(dto);
    }
    /**
     * 上传文件，需要传所属域及文件业务唯一标识
     *
     * @return {@link Response}<{@link JSONObject}>
     */
    @RequestMapping("/upload")
    // 文件对象解析入参为JSON对象会报错,工具类暂时不报警
    // @LogAndAlarm(jKey = "FileManageController.upload")
    @OperationLog(operationType = OpTypeEnum.ADD, operationDesc = "上传文件")
    Response<FilePreSignedUrlDto> upload(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "paramJson") String paramJson) {
        if (file !=null) {
            log.info("FileManageController#upload fileName={} paramJson={}", file.getOriginalFilename(), JSON.toJSONString(paramJson));
        } else {
            log.info("FileManageController#upload file is null paramJson={}", JSON.toJSONString(paramJson));
        }
        GeneratePutUrlCommand data = JSONUtil.toBean(paramJson, GeneratePutUrlCommand.class);
        String pin = LoginContext.getLoginContext().getPin();
        data.setUserPin(pin);
        if (file == null || StringUtils.isBlank(file.getOriginalFilename())) {
            throw new SystemException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("上传文件为空"));
        }
        // 获取文件后缀
        String fileName = file.getOriginalFilename();
        FilePreSignedUrlDto dto;
        try {
            dto = fileManageApplication.upload(fileName, file.getInputStream(), LocalDateTime.now().plusHours(24), data);
        } catch (IOException e) {
            log.error("FileManageApplicationImpl#upload e", e);
            throw new BusinessException(SystemErrorCode.OSS_PUT_ERROR);
        }
        return ResponseUtil.buildSuccResponse(dto);
    }

    /**
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/pageFileTask")
    @LogAndAlarm(jKey = "FileManageController.pageFileTask")
    @OperationLog(operationType = OpTypeEnum.QUERY, operationDesc = "查询文件列表")
    public Response<PageDto<FileTaskDto>> pageFileTask(@RequestBody PageFileTaskRequest request) {
        AssertUtils.hasText(request.getScene(), BusinessErrorCode.ILLEGAL_ARG_ERROR);
        if (Boolean.TRUE.equals(request.getOnlyQueryUserPin())) {
            request.setUserPin(LoginContext.getLoginContext().getPin());
        }
        PageDto<FileTaskDto> res = fileManageApplication.pageFileTask(request);
        List<FileTaskDto> list = res.getList();
        if(CollUtil.isNotEmpty(list)){
            for(FileTaskDto fileTaskDto : list){
                String fileUrl = fileManageJsfExport.getDownLoadUrlByFilePath(fileTaskDto.getFileUrl()).getData();
                fileTaskDto.setFileUrl(fileUrl);
                String desc = Optional.ofNullable(FileImportTypeEnum.findType(fileTaskDto.getOperationType()))
                        .map(FileImportTypeEnum::getDesc)
                        .orElseGet(() -> Optional.ofNullable(FileExportTypeEnum.findType(fileTaskDto.getOperationType()))
                                .map(FileExportTypeEnum::getDesc)
                                .orElse(""));
                fileTaskDto.setOperationTypeDesc(desc);
                //fileTaskDto.setOperationTypeDesc(FileExportTypeEnum.findType(fileTaskDto.getOperationType()).getDesc());
            }
        }
        return ResponseUtil.buildSuccResponse(res);
    }

}
