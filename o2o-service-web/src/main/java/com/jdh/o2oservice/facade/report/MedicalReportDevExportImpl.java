package com.jdh.o2oservice.facade.report;

import com.amazonaws.services.s3.model.ObjectMetadata;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportResultApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.export.report.MedicalReportDevExport;
import com.jdh.o2oservice.export.report.cmd.FixStructReportCmd;
import com.jdh.o2oservice.export.report.cmd.MedicalReportIndicatorFlushCmd;
import com.jdh.o2oservice.export.report.cmd.PromisePatientSkuFinishCmd;
import com.jdh.o2oservice.export.report.cmd.SyncServiceItemIndicatorCmd;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * MedicalReportDevExportImpl
 * <AUTHOR>
 * @date 2024-11-14 10:15
 */
@Service
@Slf4j
public class MedicalReportDevExportImpl implements MedicalReportDevExport {

    /**
     * 自动注入的医疗报告结果应用程序实例，用于处理与医疗报告结果相关的业务逻辑。
     */
    @Autowired
    private MedicalReportResultApplication medicalReportResultApplication;

    @Autowired
    private FileManageService fileManageService;

    @Autowired
    private ProductServiceItemApplication productServiceItemApplication;

    /**
     * 刷新医疗报告指标的ID。
     * @param medicalReportIndicatorFlushCmd 包含要刷新的医疗报告指标ID的命令对象。
     * @return 是否成功刷新指标ID。
     */
    @Override
    @LogAndAlarm
    public Boolean flushReportIndicatorId(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd) {
        medicalReportResultApplication.flushReportIndicatorId(medicalReportIndicatorFlushCmd);
        return null;
    }

    @Override
    @LogAndAlarm
    public Boolean flushReportIndicator(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd) {
        medicalReportResultApplication.flushReportIndicator(medicalReportIndicatorFlushCmd);
        return null;
    }

    /**
     * 刷新医疗报告指标的检查时间。
     *
     * @param medicalReportIndicatorFlushCmd 包含需要刷新的医疗报告指标信息的命令对象。
     * @return 如果成功刷新了检查时间，则返回 true；否则返回 false。
     */
    @Override
    @LogAndAlarm
    public Boolean flushCheckTime(MedicalReportIndicatorFlushCmd medicalReportIndicatorFlushCmd) {
        medicalReportResultApplication.flushCheckTime(medicalReportIndicatorFlushCmd);
        return null;
    }

    /**
     * 修复结构化报告。
     *
     * @param fixStructReportCmd 修复结构化报告的命令对象。
     * @return 修复操作是否成功。
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> fixStructReport(FixStructReportCmd fixStructReportCmd) {
        medicalReportResultApplication.fixStructReport(fixStructReportCmd);
        return null;
    }

    /**
     * 模拟预约单完成事件(人+sku)。
     *
     * @param promisePatientSkuFinishCmd 包含完成承诺所需信息的命令对象。
     * @return 操作结果，true表示成功，false表示失败。
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> mockPromisePatientSkuFinish(PromisePatientSkuFinishCmd promisePatientSkuFinishCmd) {
        Boolean b = medicalReportResultApplication.mockPromisePatientSkuFinish(promisePatientSkuFinishCmd);
        return Response.buildSuccessResult(b);
    }

    /**
     * 刷新报告中心ID
     *
     * @param fixStructReportCmd 固定结构的报告命令对象
     * @return 操作结果，true表示成功，false表示失败
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> flushReportCenterId(FixStructReportCmd fixStructReportCmd) {
        Boolean b = medicalReportResultApplication.flushReportCenterId(fixStructReportCmd);
        return Response.buildSuccessResult(b);
    }

    /**
     * 通过检测单同步报告中心的数据。
     *
     * @param fixStructReportCmd 报告中心同步命令对象，包含了同步所需的信息。
     * @return 同步操作的结果，true表示成功，false表示失败。
     */
    @Override
    public Response<Boolean> syncReportCenterByMp(FixStructReportCmd fixStructReportCmd) {
        medicalReportResultApplication.syncReportCenterByMp(fixStructReportCmd);
        return null;
    }

    /**
     * @param allPathFileName
     * @param bucket
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<String> getObjectMetadata(String allPathFileName, String bucket) {
        ObjectMetadata objectMetadataWithBucket = fileManageService.getObjectMetadataWithBucket(allPathFileName, bucket);
        return ResponseUtil.buildSuccResponse(JsonUtil.toJSONString(objectMetadataWithBucket));
    }

    /**
     * 修复空白 JPG 图片。
     *
     * @param fixStructReportCmd 包含修复信息的命令对象。
     * @return 修复操作是否成功。
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> fixEmptyJpg(FixStructReportCmd fixStructReportCmd) {
        Boolean b = medicalReportResultApplication.fixEmptyJpg(fixStructReportCmd);
        return ResponseUtil.buildSuccResponse(b);

    }

    /**
     * @param fixStructReportCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> flushReportCtValue(FixStructReportCmd fixStructReportCmd) {
        Boolean b = medicalReportResultApplication.flushReportCtValue(fixStructReportCmd);
        return ResponseUtil.buildSuccResponse(b);
    }

    /**
     * 同步项目和指标
     *
     * @param syncServiceItemIndicatorCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<Boolean> syncServiceItemIndicator(SyncServiceItemIndicatorCmd syncServiceItemIndicatorCmd) {
        Boolean res = productServiceItemApplication.syncServiceItemIndicator(syncServiceItemIndicatorCmd);
        return ResponseUtil.buildSuccResponse(res);

    }
}
