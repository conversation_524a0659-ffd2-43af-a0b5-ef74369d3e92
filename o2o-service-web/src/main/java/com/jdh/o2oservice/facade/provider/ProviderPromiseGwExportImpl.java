package com.jdh.o2oservice.facade.provider;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.provider.service.ProviderPromiseApplication;
import com.jdh.o2oservice.application.provider.service.ProviderQueryApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.model.Pagination;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.provider.model.Provider;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderRepository;
import com.jdh.o2oservice.core.domain.provider.service.ProviderDomainService;
import com.jdh.o2oservice.export.provider.ProviderPromiseGwExport;
import com.jdh.o2oservice.export.provider.cmd.ProviderModifyAppointCmd;
import com.jdh.o2oservice.export.provider.cmd.ProviderPromiseConfirmCmd;
import com.jdh.o2oservice.export.provider.cmd.ProviderPromiseRejectCmd;
import com.jdh.o2oservice.export.provider.dto.ProviderPromiseDto;
import com.jdh.o2oservice.export.provider.dto.ProviderPromisePrivacyNumberDto;
import com.jdh.o2oservice.export.provider.dto.ProviderPromiseUserDto;
import com.jdh.o2oservice.export.provider.query.PageProviderPromiseRequest;
import com.jdh.o2oservice.export.provider.query.PromisePrivacyNumberRequest;
import com.jdh.o2oservice.export.provider.query.QueryPromiseUserRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 商家端网关接口
 * <AUTHOR>
 * @created 2024-01-04-6:46 下午
 */
@Service
@Slf4j
public class ProviderPromiseGwExportImpl implements ProviderPromiseGwExport {

    /**
     * providerPromiseApplication
     */
    @Resource
    private ProviderQueryApplication providerQueryApplication;
    /**
     * providerPromiseApplication
     */
    @Resource
    private ProviderPromiseApplication providerPromiseApplication;

    /**
     * venderBasicRpc
     */
    @Resource
    private ProviderDomainService providerDomainService;
    @Resource
    private ProviderRepository providerRepository;

    /**
     * 查询预约列表
     *
     * @param param param
     * @return {@link Response}<{@link Pagination}<{@link ProviderPromiseDto}>>
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.export.provider.ProviderPromiseGwExport.pageAppointment")
    public Response<PageDto<ProviderPromiseDto>> pageAppointment(Map<String, String> param) {
        return ResponseUtil.buildSuccResponse(null);
    }

    /**
     * 查询预约人信息
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.export.provider.ProviderPromiseGwExport.queryProviderPromiseUser")
    public Response<ProviderPromiseUserDto> queryProviderPromiseUser(Map<String, String> param) {
        QueryPromiseUserRequest request = GwMapUtil.convertToParam(param, QueryPromiseUserRequest.class);
        log.info("[ProviderPromiseGwExportImpl -> queryProviderPromiseUser],联系客户参数!request={}", JSON.toJSONString(request));
        return providerQueryApplication.queryUser(request);
    }

    /**
     * 商家修改预约单
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.export.provider.ProviderPromiseGwExport.modifyAppointment")
    public Response<Boolean> modifyAppointment(Map<String, String> param) {
        ProviderModifyAppointCmd cmd = GwMapUtil.convertToParam(param, ProviderModifyAppointCmd.class);
        Provider provider = providerRepository.findByUserPin(GwMapUtil.getPin(param), cmd.getEnvType(), cmd.getRoleType());
        providerDomainService.userAuth(provider);
        cmd.setChannelNo(provider.getChannelNo());
        return ResponseUtil.buildSuccResponse(providerPromiseApplication.providerModify(cmd));
    }

    /**
     * 确认
     * @param param
     * @return
     */
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.export.provider.ProviderPromiseGwExport.confirm")
    @Override
    public Response<Boolean> confirm(Map<String, String> param) {
        ProviderPromiseConfirmCmd cmd = GwMapUtil.convertToParam(param, ProviderPromiseConfirmCmd.class);
        log.info("[ProviderPromiseGwExportImpl -> confirm], cmd={}", JSON.toJSONString(cmd));
        Provider provider = providerRepository.findByUserPin(GwMapUtil.getPin(param), cmd.getEnvType(), cmd.getRoleType());
        providerDomainService.userAuth(provider);
        cmd.setChannelNo(provider.getChannelNo());
        return ResponseUtil.buildSuccResponse(providerPromiseApplication.confirm(cmd));
    }

    /**
     * 驳回
     * @param param
     * @return
     */
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.export.provider.ProviderPromiseGwExport.reject")
    @Override
    public Response<Boolean> reject(Map<String, String> param) {
        ProviderPromiseRejectCmd cmd = GwMapUtil.convertToParam(param, ProviderPromiseRejectCmd.class);
        log.info("[ProviderPromiseGwExportImpl -> reject], cmd={}", JSON.toJSONString(cmd));
        Provider provider = providerRepository.findByUserPin(GwMapUtil.getPin(param), cmd.getEnvType(), cmd.getRoleType());
        providerDomainService.userAuth(provider);
        cmd.setChannelNo(provider.getChannelNo());
        return ResponseUtil.buildSuccResponse(providerPromiseApplication.reject(cmd));
    }


    /**
     * 获取隐私号码
     *
     * @param param param
     * @return {@link Response}<{@link ProviderPromisePrivacyNumberDto}>
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.export.provider.ProviderPromiseGwExport.getPrivacyNumber")
    @Override
    public Response<ProviderPromisePrivacyNumberDto> getPrivacyNumber(Map<String, String> param) {
        PromisePrivacyNumberRequest request = GwMapUtil.convertToParam(param, PromisePrivacyNumberRequest.class);
        ProviderPromisePrivacyNumberDto ret = providerQueryApplication.queryPrivacyNumber(request);
        return ResponseUtil.buildSuccResponse(ret);
    }
}
