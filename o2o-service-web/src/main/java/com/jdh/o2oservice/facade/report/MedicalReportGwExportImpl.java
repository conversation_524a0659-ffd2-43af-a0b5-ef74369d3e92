package com.jdh.o2oservice.facade.report;

import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.report.MedicalReportGwExport;
import com.jdh.o2oservice.export.report.dto.*;
import com.jdh.o2oservice.export.report.query.*;
import com.jdh.o2oservice.export.support.command.RxNewPrescriptAddCartCmd;
import com.jdh.o2oservice.export.support.query.QuickCheckMedicineRequest;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-17 20:26
 * @Desc :  医学检测报告
 */
@Service("medicalReportGwExportImpl")
public class MedicalReportGwExportImpl implements MedicalReportGwExport {
    @Autowired
    MedicalReportApplication medicalReportApplication;

    @Autowired
    private DuccConfig duccConfig;

    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.MedicalReportGwExportImpl.queryReportDetail")
    @Override
    public Response<MedicalReportOrderDTO> queryReportDetail(Map<String, String> param) {
        try{
            MedicalReportOrderRequest request = GwMapUtil.convertToParam(param, MedicalReportOrderRequest.class);
            MedicalReportOrderDTO resDTO = medicalReportApplication.queryByOrderId(request);
            return ResponseUtil.buildSuccResponse(resDTO);
        }catch (Exception e){
            return ResponseUtil.buildErrResponse(BusinessErrorCode.ILLEGAL_ARG_ERROR, e.getMessage());
        }
    }

    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.MedicalReportGwExportImpl.queryUrl")
    @Override
    public Response<MedicalReportUrlDTO> queryUrl(Map<String, String> param) {
        MedicalReportRequest request = GwMapUtil.convertToParam(param, MedicalReportRequest.class);
        String pin = GwMapUtil.getPin(param);
        request.setUserPin(pin);
        MedicalReportUrlDTO res = medicalReportApplication.queryUrl(request);
        return ResponseUtil.buildSuccResponse(res);
    }

    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.MedicalReportGwExportImpl.clickDoctorReadReport")
    @Override
    public Response<String> clickDoctorReadReport(Map<String, String> param){
        MedicalReportReadDTO dto = GwMapUtil.convertToParam(param, MedicalReportReadDTO.class);
        String flag = medicalReportApplication.clickDoctorReadReport(dto);
        if(StringUtils.isBlank(flag)){
            return ResponseUtil.buildErrResponse(BusinessErrorCode.UNKNOWN_ERROR, "没有跳转链接");
        }
        return ResponseUtil.buildSuccResponse(flag);
    }

    /**
     * 查询报告项目纬度百科
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<ServiceItemEncyclopediaDTO> queryServiceItemEncyclopedia(Map<String, String> param) {
        ServiceItemEncyclopediaRequest request = GwMapUtil.convertToParam(param, ServiceItemEncyclopediaRequest.class);
        ServiceItemEncyclopediaDTO serviceItemEncyclopediaDTO = medicalReportApplication.queryServiceItemEncyclopedia(request);
        return ResponseUtil.buildSuccResponse(serviceItemEncyclopediaDTO);

    }

    /**
     * 查询报告分享
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<ReportShareDTO> queryReportShare(Map<String, String> param) {
        ReportShareRequest reportShareRequest = GwMapUtil.convertToParam(param, ReportShareRequest.class);
        AssertUtils.nonNull(reportShareRequest.getPromiseId(),"履约单ID为空");
        ReportShareDTO reportShareDTO = medicalReportApplication.queryReportShare(reportShareRequest);
        return ResponseUtil.buildSuccResponse(reportShareDTO);
    }

    /**
     * 免pin查询报告链接（前置需要先鉴权，pop报告根据短信鉴权）
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    @UserPinCheck
    public Response<MedicalReportUrlDTO> queryReportWithNoPin(Map<String, String> param) {
        MedicalReportNoPinRequest request = GwMapUtil.convertToParam(param,MedicalReportNoPinRequest.class);
        AssertUtils.nonNull(request.getPromiseId(),"履约单ID为空");
        AssertUtils.nonNull(request.getAuthentication(),"鉴权方式为空");
        request.setUserPin(GwMapUtil.getPin(param));

        MedicalReportUrlDTO res = medicalReportApplication.queryUrlWithAuth(request);
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * 免pin查询报告链接（前置需要先鉴权，pop报告根据短信鉴权）
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<MedicalReportAuthDTO> checkReportAuth(Map<String, String> param) {
        MedicalReportNoPinRequest request = GwMapUtil.convertToParam(param,MedicalReportNoPinRequest.class);
        AssertUtils.nonNull(request.getPromiseId(),"履约单ID为空");
        AssertUtils.nonNull(request.getAuthentication(),"鉴权方式为空");
        if (Objects.equals(CommonConstant.ONE,request.getAuthentication())){
            String uuid = GwMapUtil.getUuid(param);
            AssertUtils.hasText(uuid,"设备号获取失败");
            request.setDeviceNumber(uuid);
        }
        MedicalReportAuthDTO res = medicalReportApplication.checkReportAuth(request);
        return ResponseUtil.buildSuccResponse(res);
    }

    /**
     * 查询报告(串+并行查询)
     *
     * @param param
     */
    @Override
    @LogAndAlarm
    @UserPinCheck
    public Response<Object> queryReportDetailNew(Map<String, String> param) {
        try{
            String pin = GwMapUtil.getPin(param);
            MedicalReportOrderRequest request = GwMapUtil.convertToParam(param, MedicalReportOrderRequest.class);
            request.setUserPin(pin);
            MedicalReportOrderDTO resDTO = medicalReportApplication.queryByOrderIdNew(request);
            return ResponseUtil.buildSuccResponse(resDTO);
        }catch (Exception e){
            return ResponseUtil.buildErrResponse(BusinessErrorCode.ILLEGAL_ARG_ERROR, e.getMessage());
        }
    }

    /**
     * 查询一键购药跳转链接。
     *
     * @param param 查询参数，包含必要的信息。
     * @return 快速跳转链接的响应结果。
     */
    @Override
    @LogAndAlarm
    @UserPinCheck
    public Response<String> queryQuickCheckJumpLink(Map<String, String> param) {
        String pin = GwMapUtil.getPin(param);
        QuickCheckMedicineRequest request = GwMapUtil.convertToParam(param, QuickCheckMedicineRequest.class);
        request.setUserPin(pin);
        String jumpLink = medicalReportApplication.queryQuickCheckJumpLink(request);
        return ResponseUtil.buildSuccResponse(jumpLink);
    }

    /**
     * 添加处方到购物车V2版本。
     *
     * @param param 包含处方相关信息的参数，例如处方ID、商品ID等。
     * @return 操作结果，true表示成功，false表示失败。
     */
    @Override
    @LogAndAlarm
    @UserPinCheck
    public Response<AddCartForPrescriptDTO> addCartForPrescriptV2(Map<String, String> param) {
        String pin = GwMapUtil.getPin(param);
        RxNewPrescriptAddCartCmd rxNewPrescriptAddCartCmd = GwMapUtil.convertToParam(param, RxNewPrescriptAddCartCmd.class);
        rxNewPrescriptAddCartCmd.setPin(pin);
        AddCartForPrescriptDTO result = medicalReportApplication.addCartForPrescriptV2(rxNewPrescriptAddCartCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 查询已出报告图片链接
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    @UserPinCheck
    public Response<String> queryReportSummaryImg(Map<String, String> param) {
        String pin = GwMapUtil.getPin(param);
        MedicalReportImgRequest medicalReportImgRequest = GwMapUtil.convertToParam(param, MedicalReportImgRequest.class);
        medicalReportImgRequest.setUserPin(pin);
        String queryReportSummaryImg = medicalReportApplication.queryReportSummaryImg(medicalReportImgRequest);
        return ResponseUtil.buildSuccResponse(queryReportSummaryImg);
    }

    /**
     * 按报告id查询报告url列表(跳转档案的PDF组件)
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm
    public Response<MedicalReportUrlDTO> queryUrlForCenter(Map<String, String> param) {
        MedicalReportRequest request = GwMapUtil.convertToParam(param, MedicalReportRequest.class);
        MedicalReportUrlDTO res = medicalReportApplication.queryUrlForCenter(request);
        return ResponseUtil.buildSuccResponse(res);
    }
}
