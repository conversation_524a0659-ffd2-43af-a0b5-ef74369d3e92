package com.jdh.o2oservice.facade.trade;

import com.alibaba.fastjson.JSONObject;
import com.jd.fastjson.JSON;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.trade.service.InspectionSheetApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.vo.JdOrderExtendVo;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.product.dto.InspectProjectChildDetailDTO;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.trade.InspectionSheetJsfExport;
import com.jdh.o2oservice.export.trade.dto.InspectionSheetButtonDTO;
import com.jdh.o2oservice.export.trade.dto.QueryMedicalPromiseDTO;
import com.jdh.o2oservice.export.trade.dto.QueryOrderDTO;
import com.jdh.o2oservice.export.trade.query.InspectionSheetButtonParam;
import com.jdh.o2oservice.export.trade.query.QueryMedicalPromiseInfoRequest;
import com.jdh.o2oservice.export.trade.query.QueryOrderInfoRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.codehaus.groovy.runtime.DefaultGroovyMethods.collect;

/**
 * @ClassName PromiseJsfExportFacadeImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/23 14:53
 **/
@Slf4j
@Service
public class InspectionSheetJsfExportImpl implements InspectionSheetJsfExport {

    /**
     * tradeApplication
     */
    @Resource
    private InspectionSheetApplication inspectionSheetApplication;

    @Resource
    private JdOrderRepository jdOrderRepository;

    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    @Resource
    private PromiseApplication promiseApplication;


    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.trade.InspectionSheetJsfExportImpl.queryInspectionSheetButton")
    public Response<InspectionSheetButtonDTO> queryInspectionSheetButton(InspectionSheetButtonParam param) {
        String userPin = "";
        try {
            AssertUtils.nonNull(param, "param is not null");
            AssertUtils.hasText(param.getUserPin(), "userPin is not null");
            if (Objects.isNull(param.getSheetId()) && StringUtils.isBlank(param.getFullAddress())) {
                throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
            }
            InspectionSheetButtonDTO result = inspectionSheetApplication.queryInspectionSheetButton(param);
            log.info("InspectionSheetJsfExportImpl queryInspectionSheetButton 查询是否可以下检验单 userPin:{} param:{} result:{}",
                    userPin, JSONObject.toJSONString(result), JSONObject.toJSONString(result));
            return Response.buildSuccessResult(result);
        } catch (BusinessException e) {
            log.error("InspectionSheetJsfExportImpl queryInspectionSheetButton 查询是否可以下检验单 business error userPin:{} param:{} ", userPin,
                    JSONObject.toJSONString(param), e);
            return Response.buildErrorResult(e.getErrorCode().getCode(), e.getErrorCode().getDescription());
        } catch (Exception e) {
            log.error("InspectionSheetJsfExportImpl queryInspectionSheetButton 查询是否可以下检验单 unknown error userPin:{} param:{}", userPin,
                    JSONObject.toJSONString(param), e);
            return Response.buildUnknownErrorResult();
        }
    }

    @Override
    public List<InspectProjectChildDetailDTO> test(InspectionSheetButtonParam param) {
        return inspectionSheetApplication.querySettlementProjectChild(param);
    }

    @Override
    @LogAndAlarm
    public Response<QueryOrderDTO> queryOrderInfo(QueryOrderInfoRequest request) {
        AssertUtils.nonNull(request, "request is null");
        AssertUtils.nonNull(request.getPartnerSource(), "request PartnerSource is null");
        AssertUtils.nonNull(request.getPartnerSourceOrderId(), "request PartnerSourceOrderId is null");

        JdOrder jdOrder = JdOrder.builder().partnerSourceOrderId(request.getPartnerSourceOrderId()).partnerSource(request.getPartnerSource()).build();
        List<JdOrder> jdOrderList = jdOrderRepository.findOrderListByPartnerSource(jdOrder);
        if (StringUtils.isNotBlank(request.getSaleChannelId())) {
            jdOrderList = jdOrderList.stream().filter(s -> request.getSaleChannelId().equalsIgnoreCase(JSON.parseObject(s.getExtend(), JdOrderExtendVo.class).getSaleChannelId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(jdOrderList)) {
            log.error("InspectionSheetJsfExportImpl queryOrderInfo 查询订单信息不存在!request={}", JSON.toJSONString(request));
            throw new BusinessException(BusinessErrorCode.ORDER_NOT_EXIST);
        }
        jdOrder = jdOrderList.get(0);
        QueryOrderDTO queryOrderDTO = QueryOrderDTO.builder()
                .orderId(jdOrder.getOrderId())
                .orderStatus(jdOrder.getOrderStatus())
                .partnerSourceOrderId(jdOrder.getPartnerSourceOrderId())
                .redirectUrl("")
                .build();
        return Response.buildSuccessResult(queryOrderDTO);
    }

    @Override
    @LogAndAlarm
    public Response<List<QueryMedicalPromiseDTO>> queryMedicalPromiseInfoBatch(QueryMedicalPromiseInfoRequest request) {
        AssertUtils.nonNull(request, "request is not null");
        AssertUtils.isNotEmpty(request.getSpecimenCodes(), "request specimenCodes is empty");
        AssertUtils.collectionLength(request.getSpecimenCodes(), 50, "request specimenCodes size max equal 50");

        MedicalPromiseListRequest medicalPromiseListRequest = MedicalPromiseListRequest.builder().specimenCodeList(request.getSpecimenCodes().stream().collect(Collectors.toList())).patientDetail(Boolean.TRUE).build();
        List<MedicalPromiseDTO> medicalPromiseDTOList = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
        log.info("InspectionSheetJsfExportImpl queryMedicalPromiseInfoBatch 查询检测单信息 medicalPromiseDTOList:{}", JSON.toJSONString(medicalPromiseDTOList));
        if (CollectionUtils.isNotEmpty(medicalPromiseDTOList)) {
            
            List<Long> promiseIds = medicalPromiseDTOList.stream().map(MedicalPromiseDTO::getPromiseId).collect(Collectors.toList());
            List<PromiseDto> promiseList = promiseApplication.findJdhPromiseList(PromiseRepQuery.builder().promiseIds(promiseIds).build());
            log.info("InspectionSheetJsfExportImpl queryMedicalPromiseInfoBatch 查询履约单信息 promiseList:{}", JSON.toJSONString(promiseList));
            if (CollectionUtils.isNotEmpty(promiseList)) {
                List<Long> orderIds = promiseList.stream().map(promise -> Long.parseLong(promise.getSourceVoucherId())).collect(Collectors.toList());
                List<JdOrder> orderList = jdOrderRepository.findOrdersByList(orderIds);
                log.info("InspectionSheetJsfExportImpl queryMedicalPromiseInfoBatch 查询订单信息 orderList:{}", JSON.toJSONString(orderList));
                if (CollectionUtils.isNotEmpty(orderList)) {

                }
            }
        }
        return null;
    }
}