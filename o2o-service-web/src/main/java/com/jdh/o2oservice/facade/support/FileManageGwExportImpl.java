package com.jdh.o2oservice.facade.support;

import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.FileManageGwExport;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.command.PdfSignatureCmd;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.support.dto.PdfSignatureResult;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 文件管理 网关color接口
 * @author: yangxiyu
 * @date: 2024/4/28 4:46 下午
 * @version: 1.0
 */
@Component("fileManageGwExport")
public class FileManageGwExportImpl implements FileManageGwExport {

    /**
     *
     */
    @Resource
    private FileManageApplication fileManageApplication;

    /**
     *
     * @param params
     * @return
     */
    @Override
    public Response<FilePreSignedUrlDto> generatePutUrl(Map<String, String> params) {
        GeneratePutUrlCommand cmd = GwMapUtil.convertToParam(params, GeneratePutUrlCommand.class);

        FilePreSignedUrlDto dto = fileManageApplication.generatePutUrl(cmd);
        return ResponseUtil.buildSuccResponse(dto);
    }

    /**
     * 获取文件访问预签名
     * @param params
     * @return
     */
    @Override
    @UserPinCheck
    public Response<List<FilePreSignedUrlDto>> generateGetUrl(Map<String, String> params) {

        GenerateGetUrlCommand cmd = GwMapUtil.convertToParam(params, GenerateGetUrlCommand.class);
        List<FilePreSignedUrlDto> list = fileManageApplication.generateGetUrl(cmd);
        return ResponseUtil.buildSuccResponse(list);
    }

    /**
     * PDF追加签名信息
     * @param params
     * @return
     */
    @Override
    @UserPinCheck
    public Response<PdfSignatureResult> pdfSignature(Map<String, String> params) {
        PdfSignatureCmd cmd = GwMapUtil.convertToParam(params, PdfSignatureCmd.class);
        PdfSignatureResult result = fileManageApplication.pdfSignature(cmd);
        return ResponseUtil.buildSuccResponse(result);
    }


}
