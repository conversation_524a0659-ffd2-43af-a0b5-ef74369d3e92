package com.jdh.o2oservice.facade.support;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.file.context.PutFileResult;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.export.support.FileManageJsfExport;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.support.dto.FileTaskDto;
import com.jdh.o2oservice.export.support.dto.FileUrlDto;
import com.jdh.o2oservice.export.support.dto.PutFileResultDto;
import com.jdh.o2oservice.export.support.query.FileInputStreamRequest;
import com.jdh.o2oservice.export.support.query.GetFileUrlRequest;
import com.jdh.o2oservice.export.support.query.PageFileTaskRequest;
import com.jdh.o2oservice.export.support.query.PutFileRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * FileManageJsfExportImpl
 *
 * <AUTHOR>
 * @date 2024/07/17
 */
@Slf4j
@Service
public class FileManageJsfExportImpl implements FileManageJsfExport {

    /**
     * fileManageApplication
     */
    @Resource
    private FileManageApplication fileManageApplication;

    /**
     * 文件管理领域服务
     */
    @Autowired
    private FileManageService fileManageService;

    @Autowired
    private JdhFileRepository jdhFileRepository;

    /**
     * 生成文件上传的预签名链接
     *
     * @param cmd cmd
     * @return {@link Response}<{@link FilePreSignedUrlDto}>
     */
    @Override
    @LogAndAlarm
    public Response<FilePreSignedUrlDto> generatePutUrl(GeneratePutUrlCommand cmd) {
        return ResponseUtil.buildSuccResponse(fileManageApplication.generatePutUrl(cmd));
    }

    /**
     * 生成获取文件的地址
     *
     * @param cmd cmd
     * @return {@link Response}<{@link List}<{@link FilePreSignedUrlDto}> >
     */
    @Override
    @LogAndAlarm
    public Response<List<FilePreSignedUrlDto>> generateGetUrl(GenerateGetUrlCommand cmd) {
        return ResponseUtil.buildSuccResponse(fileManageApplication.generateGetUrl(cmd));
    }

    /**
     * 文件解析任务分页查询
     *
     * @param request 请求
     * @return {@link Response}<{@link PageDto}<{@link FileTaskDto}>>
     */
    @Override
    @LogAndAlarm
    public Response<PageDto<FileTaskDto>> pageFileTask(PageFileTaskRequest request) {
        return ResponseUtil.buildSuccResponse(fileManageApplication.pageFileTask(request));
    }

    /**
     * 获取文件网址
     *
     * @param request 请求
     * @return {@link Response}<{@link String}>
     */
    @Override
    @LogAndAlarm
    public Response<String> getFileUrl(GetFileUrlRequest request) {
        return ResponseUtil.buildSuccResponse(fileManageApplication.getFileUrl(request));
    }

    /**
     * 获取文件网址
     *
     * @param ossFilePath 请求
     * @return {@link Response}<{@link String}>
     */
    @Override
    public Response<String> getDownLoadUrlByFilePath(String ossFilePath) {
        if (StringUtils.isBlank(ossFilePath)) {
            return ResponseUtil.buildSuccResponse(null);
        }
        return ResponseUtil.buildSuccResponse(fileManageService.getPublicUrl(ossFilePath, true, DateUtil.offsetDay(new Date(), 1)));
    }

    /**
     * 获取多个文件网址
     *
     * @param request 请求
     * @return {@link Response}<{@link String}>
     */
    @Override
    public Response<List<FileUrlDto>> getMultiFileUrl(GetFileUrlRequest request) {
        return ResponseUtil.buildSuccResponse(fileManageApplication.getMultiFileUrl(request));
    }

    @Override
    public Response<InputStream> getFileInputStream(FileInputStreamRequest fileInputStreamRequest) {
        JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(fileInputStreamRequest.getFileId()).build());
        AssertUtils.nonNull(jdhFile, SupportErrorCode.SUPPORT_FILE_UPLOAD_FILE_NOT_EXIST);
        return ResponseUtil.buildSuccResponse(fileManageService.get(jdhFile.getFilePath()));
    }

    @Override
    public Response<PutFileResultDto> putFile(PutFileRequest putFileRequest) {
        PutFileResult putFileResult = fileManageService.put(putFileRequest.getKey(), new ByteArrayInputStream(putFileRequest.getDataBtes()), FileManageServiceImpl.FolderPathEnum.FILE_MANAGE, putFileRequest.getContentType(), putFileRequest.getIsPublic());
        log.info("FileManageJsfExportImpl putFile putFileResult={}",JSON.toJSONString(putFileResult));
        String fileUrl = fileManageService.getPublicUrl(putFileResult.getFilePath(), true, DateUtil.offsetDay(new Date(), 5));
        putFileResult.setFileUrl(fileUrl);
        return ResponseUtil.buildSuccResponse(JSON.parseObject(JSON.toJSONString(putFileResult), PutFileResultDto.class));
    }

}
