package com.jdh.o2oservice.facade.angelpromise;

import cn.hutool.core.lang.UUID;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.application.angelpromise.AngelWorkStatusApplication;
import com.jdh.o2oservice.application.angelpromise.context.AngelWorkQueryContext;
import com.jdh.o2oservice.application.angelpromise.service.AngelPromiseApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.dispatch.service.DispatchApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.annotation.UserPinCheck;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.GwMapUtil;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipCancelCodeStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.dispatch.enums.DispatchErrorCode;
import com.jdh.o2oservice.export.angelpromise.AngelPromiseGwExport;
import com.jdh.o2oservice.export.angelpromise.cmd.*;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.*;
import com.jdh.o2oservice.export.dispatch.query.DispatchWaitingReceiveDetailRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.AutoConfigureTestEntityManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @author:lichen55
 * @createTime: 2024-04-18 17:05
 * @Description: 服务者工单网关接口
 */
@Component("angelPromiseGwExport")
@Slf4j
public class AngelPromiseGwExportImpl implements AngelPromiseGwExport {

    /** */
    @Resource
    private AngelPromiseApplication angelPromiseApplication;

    /** */
    @Resource
    private AngelWorkApplication angelWorkApplication;

    @Resource
    private AngelWorkStatusApplication angelWorkStatusApplication;

    /** */
    @Resource
    private DispatchApplication dispatchApplication;

    /** */
    @Resource
    private RedisLockUtil redisLockUtil;

    @Autowired
    private DuccConfig duccConfig;


    /**
     * 创建服务者工单
     *
     * @param jdhAngelWorkSaveCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelPromiseSaveExportImpl.createAngelWork")
    public Response<Boolean> createAngelWork(JdhAngelWorkSaveCmd jdhAngelWorkSaveCmd) {
        String lockKey = MessageFormat.format(RedisKeyEnum.SAVE_SERVICE_INDICATOR_LOCK_KEY.getRedisKeyPrefix(), jdhAngelWorkSaveCmd.getSourceId());
        boolean lockFlag = redisLockUtil.tryLock(lockKey, UUID.fastUUID().toString(),
                RedisKeyEnum.SAVE_SERVICE_INDICATOR_LOCK_KEY.getExpireTime(), RedisKeyEnum.SAVE_SERVICE_INDICATOR_LOCK_KEY.getExpireTimeUnit());

        if(!lockFlag){
            throw new BusinessException(AngelPromiseBizErrorCode.REPEAT_CREATE);
        }
        angelWorkApplication.createAngelWork(jdhAngelWorkSaveCmd);
        return ResponseUtil.buildSuccResponse(true);
    }

    /**
     * 执行工单
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkExecuteExportImpl.execute")
    public Response<AngelWorkStatusDto> execute(Map<String, String> param) {
        AngelWorkExecuteCmd workCmd = GwMapUtil.convertToParam(param, AngelWorkExecuteCmd.class);
        if(Objects.isNull(workCmd) || StringUtils.isBlank(workCmd.getWorkId()) || StringUtils.isBlank(workCmd.getEventCode())){
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL);
        }
        //检查时间，在服务开始前3个内不能取消
        if(workCmd.getEventCode().equals(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_CANCEL_SERVED.getCode())){
            AngelWorkQuery angelWorkQuery = new AngelWorkQuery();
            angelWorkQuery.setWorkId(Long.valueOf(workCmd.getWorkId()));
            AngelWorkDetailDto angelWorkDetailDto = angelPromiseApplication.queryAngelWork(angelWorkQuery);
            if(angelWorkDetailDto==null){
                throw new BusinessException(AngelPromiseBizErrorCode.WORK_CANCEL_NO_WORK_ERROR);
            }
            Date workStartTime = angelWorkDetailDto.getWorkStartTime();
            LocalDateTime localDateTime = TimeUtils.dateToLocalDateTime(workStartTime);
            long hourAbs = Math.abs(Duration.between(localDateTime, LocalDateTime.now()).toHours());
            log.error("[AngelWorkExecuteExportImpl.execute],当前时间和距离服务开始时间{}小时!", hourAbs);
            if(hourAbs < Long.valueOf(duccConfig.getCancelAngelWorkHour())){
                log.error("[AngelWorkExecuteExportImpl.execute],当前时间和距离服务开始时间小于1小时,不能取消工单!");
                throw new BusinessException(AngelPromiseBizErrorCode.WORK_CANCEL_SMALL_THREE_HOUR_ERROR);
            }
        }
        return ResponseUtil.buildSuccResponse(angelWorkStatusApplication.executeAngelWork(workCmd));
    }

    /**
     * 查询服务单详情
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.angelpromise.AngelPromiseGwExportImpl.queryAngelWorkDetail")
    public Response<AngelWorkDto> queryAngelWorkDetail(Map<String, String> param) {
        AngelWorkDetailQuery query = GwMapUtil.convertToParam(param, AngelWorkDetailQuery.class);
        //派单成功待接单也查询此接口，通过入参做类型区分。新增入参workStatus，当新增入参workStatus为1=待接单是，查询派单信息
        if (Objects.equals(AngelWorkStatusEnum.WAIT_RECEIVE.getType(), query.getWorkStatus())) {
            DispatchWaitingReceiveDetailRequest request = GwMapUtil.convertToParam(param, DispatchWaitingReceiveDetailRequest.class);
            request.setDispatchDetailId(Objects.isNull(request.getDispatchDetailId()) ? query.getWorkId() : request.getDispatchDetailId());
            AngelWorkDto result = dispatchApplication.queryDispatchWaitingReceiveDetail(request);
            if (Objects.isNull(result)) {
                return ResponseUtil.buildErrResponse(DispatchErrorCode.DISPATCH_DETAIL_NOT_EXIST);
            }
            //如果是已接单的派单任务，页面需要提醒护士刷新，需要把护士工单ID返回前端做为刷新的入参
            List<AngelWorkDto> angelWorkDtoList = angelPromiseApplication.queryAngelWorkList(AngelWorkListQuery.builder().angelId(result.getAngelId()).promiseId(result.getPromiseId()).queryAngelTask(Boolean.TRUE).pageIndex(1).pageSize(10).build());
            if (CollectionUtils.isNotEmpty(angelWorkDtoList)) {
                angelWorkDtoList.sort(Comparator.comparing(AngelWorkDto::getStatus));
                result.setWorkId(angelWorkDtoList.get(0).getWorkId());
            }
//            result.setAngelWorkSpecimens(angelPromiseApplication.queryAngelWorkSpecimens(query));
            result.setNeedAcceptGuideFloor(Boolean.FALSE);
            return ResponseUtil.buildSuccResponse(result);
        }
        //除待接单状态之外，查询服务者履约域接口
        AngelWorkDto result = angelPromiseApplication.queryAngelWorkDetailForPage(query);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 检查绑定条码，用于护士上门绑定条码页面下的下一步按钮
     *
     * @param param
     * @return
     */
    @Override
    @UserPinCheck
    @LogAndAlarm(jKey = "AngelPromiseGwExportImpl.submitBindBarCode")
    public Response<Boolean> submitBindBarCode(Map<String, String> param) {
        AngelCheckBarCodeCmd angelCheckBarCodeCmd = GwMapUtil.convertToParam(param, AngelCheckBarCodeCmd.class);
        return ResponseUtil.buildSuccResponse(angelWorkApplication.submitBindBarCode(angelCheckBarCodeCmd));
    }

    /**
     * 服务完成(护工护理)
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelPromiseGwExportImpl.finishService")
    public Response<Boolean> finishService(Map<String, String> param) {
        AngelCheckBarCodeCmd angelCheckBarCodeCmd = GwMapUtil.convertToParam(param, AngelCheckBarCodeCmd.class);
        return ResponseUtil.buildSuccResponse(angelWorkApplication.finishService(angelCheckBarCodeCmd));
    }

    /**
     * 确认全部订单已配送(护士检测)
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelPromiseGwExportImpl.confirmTransferOrderDeliver")
    public Response<Boolean> confirmTransferOrderDeliver(Map<String, String> param) {
        AngelCheckBarCodeCmd angelCheckBarCodeCmd = GwMapUtil.convertToParam(param, AngelCheckBarCodeCmd.class);
        return ResponseUtil.buildSuccResponse(angelWorkApplication.confirmTransferOrderDeliver(angelCheckBarCodeCmd));
    }

    /**
     * 护士呼叫选择配送方式
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelPromiseGwExportImpl.createDeliveryShip")
    public Response<Boolean> createDeliveryShip(Map<String, String> param) {
        AngelWorkCreateShipCmd angelWorkCreateShipCmd = GwMapUtil.convertToParam(param, AngelWorkCreateShipCmd.class);
        String pin = GwMapUtil.getPin(param);
        angelWorkCreateShipCmd.setCPin(pin);
        return ResponseUtil.buildSuccResponse(angelWorkApplication.createDeliveryShip(angelWorkCreateShipCmd));
    }

    /**
     * 查询天使工单列表
     * @param param 入参参数描述
     * @return 响应的参数描述
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.angelpromise.AngelPromiseGwExportImpl.queryAngelWorkList")
    public Response<AngelWorkListDto> queryAngelWorkList(Map<String, String> param) {
        AngelWorkListQuery angelWorkListQuery = GwMapUtil.convertToParam(param, AngelWorkListQuery.class);
        AngelWorkListDto result = angelPromiseApplication.queryAngelWorkListForPage(angelWorkListQuery);
        return ResponseUtil.buildSuccResponse(result);
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.angelpromise.AngelPromiseGwExportImpl.queryAngelWorkShowTemplate")
    public Response<List<AngelWorkShowTemplateDto>> queryAngelWorkShowTemplate(Map<String, String> param) {
        AngelWorkShowTemplateQuery templateQuery = GwMapUtil.convertToParam(param, AngelWorkShowTemplateQuery.class);
        List<AngelWorkShowTemplateDto> result = angelPromiseApplication.queryAngelWorkShowTemplate(templateQuery);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 自配送送达
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelPromiseGwExportImpl.deliver")
    public Response<Boolean> deliver(Map<String, String> param) {
        DeliverCmd deliverCmd = GwMapUtil.convertToParam(param, DeliverCmd.class);
        return ResponseUtil.buildSuccResponse(angelPromiseApplication.deliver(deliverCmd));
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.angelpromise.AngelPromiseGwExportImpl.codeVerification")
    public Response<Boolean> codeVerification(Map<String, String> param) {
        AngelWorkCodeVerificationCmd codeVerificationCmd = GwMapUtil.convertToParam(param, AngelWorkCodeVerificationCmd.class);
        Boolean result = angelPromiseApplication.codeVerification(codeVerificationCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.angelpromise.AngelPromiseGwExportImpl.bindSpecimenCode")
    public Response<Boolean> bindSpecimenCode(Map<String, String> param) {
        AngelWorkBindSpecimenCodeCmd bindSpecimenCodeCmd = GwMapUtil.convertToParam(param, AngelWorkBindSpecimenCodeCmd.class);
        Boolean result = angelPromiseApplication.bindSpecimenCode(bindSpecimenCodeCmd);
        return ResponseUtil.buildSuccResponse(result);
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.facade.angelpromise.AngelPromiseGwExportImpl.queryEnum")
    public Response<List<AngelWorkEnumDto>> queryEnum(Map<String, String> param) {
        AngelWorkEnumQuery enumQuery = GwMapUtil.convertToParam(param, AngelWorkEnumQuery.class);
        List<AngelWorkEnumDto> result = angelPromiseApplication.queryEnum(enumQuery);
        return ResponseUtil.buildSuccResponse(result);
    }

    /**
     * 取消运单
     *
     * @param param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelPromiseGwExportImpl.cancelShip")
    public Response<Boolean> cancelShip(Map<String, String> param) {
        AngelWorkCancelShipCmd enumQuery = GwMapUtil.convertToParam(param, AngelWorkCancelShipCmd.class);
        String pin = GwMapUtil.getPin(param);
        enumQuery.setOperator(pin);
        enumQuery.setStandCancelCode(AngelShipCancelCodeStatusEnum.NURSE_CANCEL.getType());
        return ResponseUtil.buildSuccResponse(angelWorkApplication.cancelShip(enumQuery));
    }

    /**
     * 确认工单任务信息
     * @param param
     * @return
     */
    @LogAndAlarm(jKey = "AngelPromiseGwExportImpl.confirmTaskPatientInfo")
    @Override
    public Response<Boolean> confirmTaskInfo(Map<String, String> param) {
        ConfirmTaskInfoCmd cmd = GwMapUtil.convertToParam(param, ConfirmTaskInfoCmd.class);
        return ResponseUtil.buildSuccResponse(angelPromiseApplication.confirmTaskInfo(cmd));
    }

    /**
     * 提交工单完成信息
     * @param param
     * @return
     */
    @LogAndAlarm(jKey = "AngelPromiseGwExportImpl.submitWorkCompleteInfo")
    @Override
    public Response<Boolean> submitWorkCompleteInfo(Map<String, String> param) {

        SubmitWorkCompleteInfoCmd cmd = GwMapUtil.convertToParam(param, SubmitWorkCompleteInfoCmd.class);

        return ResponseUtil.buildSuccResponse(angelPromiseApplication.submitWorkCompleteInfo(cmd));
    }

    /**
     * 提交服务记录录音信息
     * @param param
     * @return
     */
    @LogAndAlarm(jKey = "AngelPromiseGwExportImpl.submitSoundRecording")
    @Override
    public Response<Boolean> submitRecording(Map<String, String> param) {
        SubmitSoundRecordingCmd cmd = GwMapUtil.convertToParam(param, SubmitSoundRecordingCmd.class);
        return ResponseUtil.buildSuccResponse(angelPromiseApplication.submitSoundRecording(cmd));
    }

    /**
     * 查询第三方物流供应商列表
     *
     * @param param 查询参数
     * @return 第三方物流供应商DTO列表
     */
    @Override
    @LogAndAlarm
    public Response<List<ThirdShipSupplierDTO>> queryThirdShipSupplierList(Map<String, String> param) {
        List<ThirdShipSupplierDTO> thirdShipSupplierDTOS = JsonUtil.parseArray(duccConfig.getThirdShipSupplier(), ThirdShipSupplierDTO.class);
        return ResponseUtil.buildSuccResponse(thirdShipSupplierDTOS);
    }
}
