package com.jdh.o2oservice.test.appliacation.support;

import com.jdh.o2oservice.StartApplication;
import com.jdh.o2oservice.application.support.service.ReachApplication;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.reach.enums.ReachTypeEnum;
import com.jdh.o2oservice.core.domain.support.reach.model.JdhReachMessage;
import com.jdh.o2oservice.core.domain.support.reach.repository.db.JdhReachConfigRepository;
import com.jdh.o2oservice.export.support.command.ReachMsgReadCommand;
import com.jdh.o2oservice.export.support.query.ReachMsgBoxQuery;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhReachMessagePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachMessagePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:ReachMessgeApplicationTest
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/5/11 11:46
 * @Vserion: 1.0
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = StartApplication.class)
public class ReachMessageApplicationTest {

    @Resource
    private ReachApplication reachApplication;

    @Test
    public void testQueryMsgBox(){
        ReachMsgBoxQuery reachMsgBoxQuery = new ReachMsgBoxQuery();
        reachMsgBoxQuery.setAppId("angel");
        reachMsgBoxQuery.setMessageGroupNo("msg_box_service_record");
        reachMsgBoxQuery.setCursor(0L);
        reachMsgBoxQuery.setOffset(10);
        reachApplication.queryMsgBoxPage(reachMsgBoxQuery);
    }

    @Test
    public void testQueryUnreadMsg(){
        ReachMsgBoxQuery reachMsgBoxQuery = new ReachMsgBoxQuery();
        reachMsgBoxQuery.setAppId("angel");
        reachMsgBoxQuery.setMessageGroupNo("msg_box_service_record");
        reachApplication.getUnreadCount(reachMsgBoxQuery);
    }

    @Test
    public void testreadMessage(){
        ReachMsgReadCommand readCommand = new ReachMsgReadCommand();
        readCommand.setAppId("angel");
        readCommand.setMessageId("1715078766987");
        reachApplication.readMessage(readCommand);
    }

    @Resource
    private JdhReachMessagePoMapper jdhReachMessagePoMapper;

    @Test
    public void testSendSmsCode(){
        LocalDateTime start = TimeUtils.timeStrToLocalDate("2025-05-01 00:00:00", TimeFormat.LONG_PATTERN_LINE);
        LocalDateTime end = TimeUtils.timeStrToLocalDate("2025-05-02 00:00:00", TimeFormat.LONG_PATTERN_LINE);
        try {
            // 每次
            while (start.isBefore(end)) {
                log.info("updateMessageBoxIndex==== startTime={}", start);
                LocalDateTime close = start.plusMinutes(1);
                List<JdhReachMessagePo> pos = jdhReachMessagePoMapper.listBox("testPin", TimeUtils.localDateTimeToDate(start), TimeUtils.localDateTimeToDate(close));
                if (CollectionUtils.isEmpty(pos)){
                    log.info("updateMessageBoxIndex==== closeTime={}", close);
                }else{
                    log.info("updateMessageBoxIndex==== pos size={}", pos.size());
                    for (JdhReachMessagePo po : pos) {
                        if (StringUtils.isBlank(po.getBoxMessageGroupIndex())){
                            if (Objects.equals(po.getReachType(), ReachTypeEnum.APP_NOTIFY.getCode())
                                    && StringUtils.equals("angel", po.getAppId())){
                                JdhReachConfigRepository configRepository = SpringUtil.getBean(JdhReachConfigRepository.class);
                                Integer integer = configRepository.findBoxGroupByMsgType(po.getMessageBizType());
                                String index = MessageFormat.format(JdhReachMessage.MESSAGE_BOX_INDEX_TEMPLATE, po.getUserPin(), integer);
                                jdhReachMessagePoMapper.updateBoxIndex(po.getMessageId(), index);
                            }
                        }
                    }
                }

                start = close;
                log.info("updateMessageBoxIndex==== closeTime={}", close);
            }

        }catch (Throwable e){
            log.info("updateMessageBoxIndex error============", e);
        }
        }
}
