package com.jdh.o2oservice.test.appliacation.base;

import com.jd.jim.cli.Cluster;
import com.jd.jim.cli.driver.exception.ScriptNotFoundException;
import com.jdh.o2oservice.StartApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.jd.jim.cli.protocol.ScriptOutputType.MULTI;

/**
 * ScriptCommandTest JimDB脚本单测
 *
 * <AUTHOR>
 * @version 2025/02/11 17:45
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = StartApplication.class)
public class ScriptCommandTest {

    @Resource
    Cluster jimClient;

    @Test
    public void scriptLoadTest() throws Exception {
        String sha1 = "fc252e9977e89cc1eac820fea00c55a98ecd579e";
        String script = "local keySku = KEYS[1]\n" +
                "local keyProvince = KEYS[2]\n" +
                "local keyCity = KEYS[3]\n" +
                "local keyCounty = KEYS[4]\n" +
                "local keyTown= KEYS[5]\n" +
                "local sku = ARGV[1]\n" +
                "local province = ARGV[2]\n" +
                "local city = ARGV[3]\n" +
                "local county = ARGV[4]\n" +
                "local town = ARGV[5]\n" +
                "local currentValue = redis.call('sismember', keySku, sku)\n" +
                "\n" +
                "if currentValue == 0 then\n" +
                "  return nil\n" +
                "else\n" +
                "  local existAddress = 0;\n" +
                "  if town ~= nil then\n" +
                "    existAddress = redis.call('sismember', keyTown, town)\n" +
                "  elseif county ~= nil then\n" +
                "    existAddress = redis.call('sismember', keyCounty, county)\n" +
                "  elseif city ~= nil then\n" +
                "    existAddress = redis.call('sismember', keyCity, city)\n" +
                "  elseif county ~= nil then\n" +
                "    existAddress = redis.call('sismember', keyProvince, province)\n" +
                "  end\n" +
                "  return existAddress > 0\n" +
                "end";
        String sha = jimClient.scriptLoad(script);
        Assert.assertEquals(sha, sha1);
    }


    @Test
    public void evalShaListErrorTest2() throws Exception {
        String sha = "178dedb2d5da08fcf96a3fcdf598d56ad4e9dc30";
        boolean catchException = false;
        List<String> keys = new ArrayList<String>();
        keys.add("{PRODUCT_LABEL}_SKU_TYPE2");
        keys.add("{PRODUCT_LABEL}_AVAIABLE_PROVINCE");
        keys.add("{PRODUCT_LABEL}_AVAIABLE_CITY");
        keys.add("{PRODUCT_LABEL}_AVAIABLE_COUNTY");
        keys.add("{PRODUCT_LABEL}_AVAIABLE_TOWN");
        List<String> args = new ArrayList<String>();
        args.add("100124166907");
        args.add("1");
        args.add("2802");
        args.add("54741");
        try {
            //readonly:1)true(走客户端配置的读组) 2)false:走主
            Object result = jimClient.evalsha(sha, keys, args, true, MULTI);
        } catch (ScriptNotFoundException ex) {
            //捕获此异常后，需要调用scriptReLoad方法将脚本重新上传一次，然后重新执行evalsha
            catchException = true;
        }
        Assert.assertEquals(catchException, true);
    }

    @Test
    public void evalShaListTest2() throws Exception {
        String script = "return {KEYS[1],KEYS[2],ARGV[1],ARGV[2],ARGV[3]}";
        String sha = jimClient.scriptLoad(script);

        List<String> keys = new ArrayList<String>();
        keys.add("nkey1{xx}bb");
        keys.add("nkey2{xx}bb");
        List<String> args = new ArrayList<String>();
        args.add("first");
        args.add("second");
        args.add("third");

        List<String> responses = (List<String>) jimClient.evalsha(sha, keys, args, true, MULTI);

        Assert.assertEquals(5, responses.size());
        Assert.assertEquals("nkey1{xx}bb", responses.get(0));
        Assert.assertEquals("nkey2{xx}bb", responses.get(1));
        Assert.assertEquals("first", responses.get(2));
        Assert.assertEquals("second", responses.get(3));
        Assert.assertEquals("third", responses.get(4));

    }

}