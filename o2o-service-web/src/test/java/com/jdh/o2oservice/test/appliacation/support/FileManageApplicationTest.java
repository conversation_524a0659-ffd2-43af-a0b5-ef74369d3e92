package com.jdh.o2oservice.test.appliacation.support;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.jdh.o2oservice.StartApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.command.PdfSignatureCmd;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.support.dto.PdfSignatureResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: yangxiyu
 * @date: 2024/3/25 11:31 上午
 * @version: 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = StartApplication.class)
public class FileManageApplicationTest {

    /** */
    @Resource
    private FileManageApplication fileManageApplication;



    @Test
    public void testExport(){
        Map<String, Object> param = new HashMap<>();

        param.put("operatorType", FileExportTypeEnum.JM_PROMISE_EXPORT.getType());
        param.put("userPin", "pin");
        param.put("scene", "jmPromiseExport");
        fileManageApplication.export(param);
    }

    @Test
    public void generatePutUrlTest(){
        GeneratePutUrlCommand command = new GeneratePutUrlCommand();

        command.setDomainCode("angel");
        command.setIsPublic(true);
        command.setFileBizType("FIleTYpe");
        command.setUserPin("testPin");
        FilePreSignedUrlDto dto = fileManageApplication.generatePutUrl(command);
        log.info("----------------");
        log.info(JSON.toJSONString(dto));
    }


    @Test
    public void generateGetUrlTest(){
        GenerateGetUrlCommand command = new GenerateGetUrlCommand();

        command.setDomainCode("angel");
        command.setIsPublic(true);
        command.setUserPin("testPin");

        Set<Long> fileIds = Sets.newHashSet(1714291558810L, 1714291722738L);
        command.setFileIds(fileIds);
        List<FilePreSignedUrlDto> dto = fileManageApplication.generateGetUrl(command);
        log.info("-----------**-----");
        log.info(JSON.toJSONString(dto));
    }

    @Test
    public void pdfSignatureTest(){
        PdfSignatureCmd command = new PdfSignatureCmd();

        command.setDomainCode("angelPromise");
        command.setFileBizType("letter_of_consent");
        command.setSignatureImageFileId(10000L);

        PdfSignatureResult dto = fileManageApplication.pdfSignature(command);
        log.info("-----------**-----");
        log.info(JSON.toJSONString(dto));
    }
}
