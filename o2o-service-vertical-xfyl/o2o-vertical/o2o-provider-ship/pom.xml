<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jdh.o2oservice</groupId>
        <artifactId>o2o-vertical</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>o2o-provider-ship</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>provider-autotest</module>
        <module>provider-dada</module>
        <module>provider-shunfeng</module>
        <module>provider-shansong</module>
        <module>provider-jdlogistics</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>


</project>