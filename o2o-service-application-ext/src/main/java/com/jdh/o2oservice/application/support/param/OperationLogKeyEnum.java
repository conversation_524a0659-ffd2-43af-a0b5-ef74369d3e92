package com.jdh.o2oservice.application.support.param;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/27 21:31
 */
public enum OperationLogKeyEnum {


    DISPATCH_RISK_QUERY("dispatchRiskStrategyQuery", "派单查询风控策略",4);

    OperationLogKeyEnum(String sceneKey, String sceneDesc, Integer operateType) {
        this.sceneKey = sceneKey;
        this.operateType = operateType;
    }

    private String sceneKey;
    private String sceneDesc;
    private Integer operateType;

    public String getSceneKey() {
        return sceneKey;
    }

    public String getSceneDesc() {
        return sceneDesc;
    }

    public Integer getOperateType() {
        return operateType;
    }
}
