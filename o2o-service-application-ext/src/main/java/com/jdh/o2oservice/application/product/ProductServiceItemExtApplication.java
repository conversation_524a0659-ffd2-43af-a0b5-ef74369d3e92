package com.jdh.o2oservice.application.product;

import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.ServiceItemExtQuery;


import java.util.List;
import java.util.Set;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/16 5:32 下午
 * @Description:
 */
public interface ProductServiceItemExtApplication {


    /**
     * 查询项目列表
     *
     * @param serviceItemIdSet
     * @return
     */
    List<ServiceItemDto> queryServiceItemList(Set<Long> serviceItemIdSet);


    /**
     * 查询服务列表
     *
     * @param serviceItemExtQuery
     * @return
     */
    List<ServiceItemDto> queryServiceItemList(ServiceItemExtQuery serviceItemExtQuery);
}
