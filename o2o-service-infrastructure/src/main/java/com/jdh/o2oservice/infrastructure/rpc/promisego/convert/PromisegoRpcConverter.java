package com.jdh.o2oservice.infrastructure.rpc.promisego.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jdh.o2o.promisego.common.enums.BusinessModeEnum;
import com.jdh.o2o.promisego.common.enums.PrePromisegoQuerySceneEnum;
import com.jdh.o2o.promisego.export.dto.*;
import com.jdh.o2o.promisego.export.query.*;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * promisego rpc转换器
 *
 * <AUTHOR>
 * @date 2024/11/28
 */
@Mapper
public interface PromisegoRpcConverter {

    /**
     * PromisegoRpcConverter
     */
    PromisegoRpcConverter INSTANCE = Mappers.getMapper(PromisegoRpcConverter.class);


    /**
     * bo2请求
     *
     * @param userPromisegoRequestBo 用户promisego请求bo
     * @return {@link UserPromisegoRequest }
     */
    default UserPromisegoRequest bo2Request(UserPromisegoRequestBo userPromisegoRequestBo) {
        if(userPromisegoRequestBo == null) {
            return null;
        }
        UserPromisegoRequest userPromisegoRequest = new UserPromisegoRequest();
        //基本信息
        userPromisegoRequest.setPromiseId(userPromisegoRequestBo.getPromiseId());
        userPromisegoRequest.setAggregateStatus(userPromisegoRequestBo.getAggregateStatus());
        userPromisegoRequest.setBusinessMode(BusinessModeEnum.getEnumByCode(userPromisegoRequestBo.getBusinessMode()));

        //时间信息
        PromisegoRequestAppointmentTime appointmentTime = userPromisegoRequestBo.getAppointmentTime();
        if(Objects.nonNull(appointmentTime)) {
            userPromisegoRequest.setAppointmentTime(AppointmentTime.builder()
                    .dateType(appointmentTime.getDateType())
                    .isImmediately(appointmentTime.getImmediately())
                    .appointmentStartTime(appointmentTime.getAppointmentStartTime())
                    .appointmentEndTime(appointmentTime.getAppointmentEndTime())
                    .build());
        }

        //地址信息
        if(Objects.nonNull(userPromisegoRequestBo.getAppointmentAddress())) {
            userPromisegoRequest.setAppointmentAddress(AppointmentAddress.builder()
                    .provinceId(userPromisegoRequestBo.getAppointmentAddress().getProvinceId())
                    .cityId(userPromisegoRequestBo.getAppointmentAddress().getCityId())
                    .countyId(userPromisegoRequestBo.getAppointmentAddress().getCountyId())
                    .townId(userPromisegoRequestBo.getAppointmentAddress().getTownId())
                    .provinceName(userPromisegoRequestBo.getAppointmentAddress().getProvinceName())
                    .cityName(userPromisegoRequestBo.getAppointmentAddress().getCityName())
                    .countyName(userPromisegoRequestBo.getAppointmentAddress().getCountyName())
                    .townName(userPromisegoRequestBo.getAppointmentAddress().getTownName())
                    .fullAddress(userPromisegoRequestBo.getAppointmentAddress().getFullAddress())
                    .latitude(userPromisegoRequestBo.getAppointmentAddress().getLatitude())
                    .longitude(userPromisegoRequestBo.getAppointmentAddress().getLongitude())
                    .coordType(userPromisegoRequestBo.getAppointmentAddress().getCoordType())
                    .build());
        }

        //是否查询出报告预估时间
        userPromisegoRequest.setQueryTermScript(userPromisegoRequestBo.getQueryTermScript());
        userPromisegoRequest.setPreSampleFlag(userPromisegoRequestBo.getPreSampleFlag());
        return userPromisegoRequest;
    }

    /**
     * bo2Request
     *
     * @param requestBo requestBo
     * @return {@link PreUserPromisegoRequest }
     */
    default PreUserPromisegoRequest bo2Request(PreUserPromisegoRequestBo requestBo) {


        PreUserPromisegoRequest preUserPromisegoRequest = PreUserPromisegoRequest.builder()
                .businessMode(BusinessModeEnum.getEnumByCode(requestBo.getBusinessMode()))
                .scene(PrePromisegoQuerySceneEnum.getByScene(requestBo.getScene()))
                .build();

        PreUserPromisegoRequestCondition condition = new PreUserPromisegoRequestCondition();
        //预约时间
        PromisegoRequestAppointmentTime appointmentTime = requestBo.getAppointmentTime();
        if(Objects.nonNull(appointmentTime)) {
            AppointmentTime appointmentTimeReq = AppointmentTime.builder()
                    .dateType(appointmentTime.getDateType())
                    .isImmediately(appointmentTime.getImmediately())
                    .appointmentStartTime(appointmentTime.getAppointmentStartTime())
                    .appointmentEndTime(appointmentTime.getAppointmentEndTime())
                    .build();
            condition.setAppointmentTime(appointmentTimeReq);
        }


        //预约地址
        PromisegoRequestAddress appointmentAddressReq = requestBo.getAppointmentAddress();
        if(Objects.nonNull(appointmentAddressReq)) {
            AppointmentAddress appointmentAddress = AppointmentAddress.builder()
                    .addressId(appointmentAddressReq.getAddressId())
                    .provinceId(appointmentAddressReq.getProvinceId())
                    .cityId(appointmentAddressReq.getCityId())
                    .countyId(appointmentAddressReq.getCountyId())
                    .townId(appointmentAddressReq.getTownId())
                    .provinceName(appointmentAddressReq.getProvinceName())
                    .cityName(appointmentAddressReq.getCityName())
                    .countyName(appointmentAddressReq.getCountyName())
                    .townName(appointmentAddressReq.getTownName())
                    .fullAddress(appointmentAddressReq.getFullAddress())
                    .latitude(appointmentAddressReq.getLatitude())
                    .longitude(appointmentAddressReq.getLongitude())
                    .coordType(appointmentAddressReq.getCoordType())
                    .build();

            List<PromisegoAngelStation> stationList = appointmentAddressReq.getAngelStationList();
            appointmentAddress.setJdhAngelStationList(stationList.stream().map(this::promisegoStation2Station).collect(Collectors.toList()));
            condition.setAppointmentAddress(appointmentAddress);
        }

        //预约的商品服务信息
        List<PromisegoRequestAppointmentService> serviceList = requestBo.getAppointmentServiceList();
        if(CollUtil.isNotEmpty(serviceList)) {
            List<AppointmentService> appointmentServiceList = new ArrayList<>();
            for (PromisegoRequestAppointmentService promisegoRequestAppointmentService : serviceList) {
                AppointmentService service = new AppointmentService();
                service.setSkuId(promisegoRequestAppointmentService.getSkuId());
                service.setServiceType(promisegoRequestAppointmentService.getServiceType());
                service.setSkuType(promisegoRequestAppointmentService.getSkuType());

                List<AppointmentServiceItem> itemList = new ArrayList<>();
                List<PromisegoRequestAppointmentServiceItem> promisegoRequestAppointmentServiceItemList = promisegoRequestAppointmentService.getItemList();
                for (PromisegoRequestAppointmentServiceItem appointmentServiceItem : promisegoRequestAppointmentServiceItemList) {
                    AppointmentServiceItem item = new AppointmentServiceItem();
                    item.setItemId(appointmentServiceItem.getItemId());
                    item.setItemName(appointmentServiceItem.getItemName());
                    itemList.add(item);
                }
                service.setItemList(itemList);
                appointmentServiceList.add(service);
            }
            condition.setAppointmentServiceList(appointmentServiceList);
        }

        preUserPromisegoRequest.setCondition(condition);
        preUserPromisegoRequest.setPreSampleFlag(requestBo.getPreSampleFlag());

        return preUserPromisegoRequest;
    }

    /**
     * promisegoStation
     *
     * @param promisegoStation promisegoStation
     * @return {@link JdhAngelStation }
     */
    default JdhAngelStation promisegoStation2Station(PromisegoAngelStation promisegoStation) {
        JdhAngelStation jdhAngelStation = new JdhAngelStation();
        jdhAngelStation.setAngelStationId(promisegoStation.getAngelStationId());
        jdhAngelStation.setAngelStationName(promisegoStation.getAngelStationName());
        jdhAngelStation.setStationId(promisegoStation.getStationId());
        jdhAngelStation.setStationName(promisegoStation.getStationName());
        jdhAngelStation.setStationProvinceId(promisegoStation.getStationProvinceId());
        jdhAngelStation.setStationCityId(promisegoStation.getStationCityId());
        jdhAngelStation.setStationCountyId(promisegoStation.getStationCountyId());
        jdhAngelStation.setStationTownId(promisegoStation.getStationTownId());
        jdhAngelStation.setStationProvinceName(promisegoStation.getStationProvinceName());
        jdhAngelStation.setStationCityName(promisegoStation.getStationCityName());
        jdhAngelStation.setStationCountyName(promisegoStation.getStationCountyName());
        jdhAngelStation.setStationTownName(promisegoStation.getStationTownName());
        jdhAngelStation.setStationFullAddress(promisegoStation.getStationFullAddress());
        jdhAngelStation.setStationLongitude(promisegoStation.getStationLongitude());
        jdhAngelStation.setStationLatitude(promisegoStation.getStationLatitude());
        jdhAngelStation.setStationCoordType(promisegoStation.getStationCoordType());
        return jdhAngelStation;
    }

    /**
     * 将UserPromisegoDto转换为UserPromisegoBo对象。
     * @param userPromisegoDto 要转换的UserPromisegoDto对象。
     * @return 转换后的UserPromisegoBo对象。
     */
    default UserPromisegoBo dto2Bo(UserPromisegoDto userPromisegoDto){
        if (userPromisegoDto == null) {
            return null;
        }
        UserPromisegoBo res = new UserPromisegoBo();
        ScriptDto currScript = userPromisegoDto.getCurrScript();
        ScriptDto termScript = userPromisegoDto.getTermScript();
        ScriptDto warmTipScript = userPromisegoDto.getWarmTipScript();
        if (Objects.nonNull(currScript)) {
            ScriptBo currScriptBo = ScriptBo.builder()
                    .scriptContent(currScript.getScriptContent())
                    .beginTime(currScript.getBeginTime())
                    .duration(currScript.getDuration())
                    .buffer(currScript.getBuffer())
                    .endTime(currScript.getEndTime())
                    .estimateData(etaDtoList2Bo(currScript.getEstimateData()))
                    .build();
            res.setCurrScript(currScriptBo);
        }
        if (Objects.nonNull(termScript)) {
            ScriptBo termScriptBo = ScriptBo.builder()
                    .scriptContent(termScript.getScriptContent())
                    .beginTime(termScript.getBeginTime())
                    .duration(termScript.getDuration())
                    .buffer(termScript.getBuffer())
                    .endTime(termScript.getEndTime())
                    .estimateData(etaDtoList2Bo(termScript.getEstimateData()))
                    .build();
            res.setTermScript(termScriptBo);
        }

        if(Objects.nonNull(warmTipScript)){
            ScriptBo warmTipScriptBo = ScriptBo.builder()
                    .scriptContent(warmTipScript.getScriptContent())
                    .beginTime(warmTipScript.getBeginTime())
                    .duration(warmTipScript.getDuration())
                    .endTime(warmTipScript.getEndTime())
                    .buffer(warmTipScript.getBuffer())
                    .estimateData(etaDtoList2Bo(warmTipScript.getEstimateData()))
                    .build();
            res.setWarmTipScript(warmTipScriptBo);
        }
        return res;
    }

    /**
     * dto 2 bo
     *
     * @param dto DTO
     * @return {@link PreUserPromisegoBo }
     */
    default PreUserPromisegoBo dto2Bo(PreUserPromisegoDto dto){
        ScriptDto script = dto.getScript();
        if(Objects.nonNull(script)){
            return PreUserPromisegoBo.builder()
                    .script(ScriptBo.builder()
                            .scriptContent(script.getScriptContent())
                            .beginTime(script.getBeginTime())
                            .duration(script.getDuration())
                            .buffer(script.getBuffer())
                            .estimateData(etaDtoList2Bo(script.getEstimateData()))
                            .build())
                    .build();

        }
        return null;
    }

    /**
     * 将EtaDto列表转换为EtaBo列表
     * @param etaDtoList 需要转换的EtaDto对象列表
     * @return 转换后的EtaBo对象列表，若输入列表为空则返回null
     */
    default List<EtaBo> etaDtoList2Bo(List<EtaDto> etaDtoList){
        if (CollectionUtil.isEmpty(etaDtoList)) {
            return null;
        }
        List<EtaBo> res = new ArrayList<>();
        for (EtaDto etaDto : etaDtoList) {
            EtaBo etaBo = etaDto2Bo(etaDto);
            res.add(etaBo);
        }
        return res;
    }

    /**
     * 将EtaDto对象转换为EtaBo对象。
     * @param etaDto 需要转换的EtaDto对象。
     * @return 转换后的EtaBo对象，若etaDto为null则返回null。
     */
    default EtaBo etaDto2Bo(EtaDto etaDto){
        if (etaDto == null) {
            return null;
        }
        EtaBo res = new EtaBo();
        res.setBuffer(etaDto.getBuffer());
        res.setDuration(etaDto.getDuration());
        res.setStageCode(etaDto.getStageCode());
        res.setEstimateBeginTime(etaDto.getEstimateBeginTime());
        res.setOperatorCode(etaDto.getOperatorCode());
        return res;
    }

    /**
     * bo2LabRequest
     *
     * @param requestBo requestBo
     * @return {@link LabPromisegoRequest }
     */
    default LabPromisegoRequest bo2LabRequest(LabPromisegoRequestBo requestBo){
        if(requestBo == null) {
            return null;
        }
        LabPromisegoRequest labPromisegoRequest = new LabPromisegoRequest();
        //基本信息
        labPromisegoRequest.setPromiseId(requestBo.getPromiseId());
        labPromisegoRequest.setAggregateStatus(requestBo.getAggregateStatus());
        labPromisegoRequest.setMedicalPromiseId(requestBo.getMedicalPromiseId());
        labPromisegoRequest.setBusinessMode(BusinessModeEnum.getEnumByCode(requestBo.getBusinessMode()));

        //时间信息
        PromisegoRequestAppointmentTime appointmentTime = requestBo.getAppointmentTime();
        if(Objects.nonNull(appointmentTime)) {
            labPromisegoRequest.setAppointmentTime(AppointmentTime.builder()
                    .dateType(appointmentTime.getDateType())
                    .isImmediately(appointmentTime.getImmediately())
                    .appointmentStartTime(appointmentTime.getAppointmentStartTime())
                    .appointmentEndTime(appointmentTime.getAppointmentEndTime())
                    .build());
        }

        //地址信息
        if(Objects.nonNull(requestBo.getAppointmentAddress())) {
            labPromisegoRequest.setAppointmentAddress(AppointmentAddress.builder()
                    .provinceId(requestBo.getAppointmentAddress().getProvinceId())
                    .cityId(requestBo.getAppointmentAddress().getCityId())
                    .countyId(requestBo.getAppointmentAddress().getCountyId())
                    .townId(requestBo.getAppointmentAddress().getTownId())
                    .provinceName(requestBo.getAppointmentAddress().getProvinceName())
                    .cityName(requestBo.getAppointmentAddress().getCityName())
                    .countyName(requestBo.getAppointmentAddress().getCountyName())
                    .townName(requestBo.getAppointmentAddress().getTownName())
                    .fullAddress(requestBo.getAppointmentAddress().getFullAddress())
                    .latitude(requestBo.getAppointmentAddress().getLatitude())
                    .longitude(requestBo.getAppointmentAddress().getLongitude())
                    .coordType(requestBo.getAppointmentAddress().getCoordType())
                    .build());
        }

        //是否查询出term预估时间
        labPromisegoRequest.setQueryTermScript(requestBo.getQueryTermScript());

        return labPromisegoRequest;
    }

    /**
     * dto2LabBo
     *
     * @param labPromisegoDto labPromisegoDto
     * @return {@link LabPromisegoBo }
     */
    default LabPromisegoBo dto2LabBo(LabPromisegoDto labPromisegoDto){
        if (labPromisegoDto == null) {
            return null;
        }
        LabPromisegoBo res = new LabPromisegoBo();
        ScriptDto currScript = labPromisegoDto.getCurrScript();
        ScriptDto termScript = labPromisegoDto.getTermScript();
        if (Objects.nonNull(currScript)) {
            ScriptBo currScriptBo = ScriptBo.builder()
                    .scriptContent(currScript.getScriptContent())
                    .beginTime(currScript.getBeginTime())
                    .duration(currScript.getDuration())
                    .buffer(currScript.getBuffer())
                    .endTime(currScript.getEndTime())
                    .estimateData(etaDtoList2Bo(currScript.getEstimateData()))
                    .build();
            res.setCurrScript(currScriptBo);
        }
        if (Objects.nonNull(termScript)) {
            ScriptBo termScriptBo = ScriptBo.builder()
                    .scriptContent(termScript.getScriptContent())
                    .beginTime(termScript.getBeginTime())
                    .duration(termScript.getDuration())
                    .buffer(termScript.getBuffer())
                    .endTime(termScript.getEndTime())
                    .estimateData(etaDtoList2Bo(termScript.getEstimateData()))
                    .build();
            res.setTermScript(termScriptBo);
        }

        return res;
    }
}
