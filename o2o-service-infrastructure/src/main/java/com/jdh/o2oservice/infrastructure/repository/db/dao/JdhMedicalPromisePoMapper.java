package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.core.domain.medpromise.bo.MedicalPromiseFreezeBO;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhMedicalPromisePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Description: 检测单mapper
 * @Interface: JdhMedicalPromisePoMapper
 * @Author: wangpengfei144
 * @Date: 2024/4/16
 */
@Mapper
public interface JdhMedicalPromisePoMapper extends BaseMapper<JdhMedicalPromisePo> {

    /**
     * 批量更新
     * @param jdhMedicalPromisePos
     * @return
     */
    Integer updateBatch(@Param("jdhMedicalPromisePos") List<JdhMedicalPromisePo> jdhMedicalPromisePos);

    /**
     *
     * @param medicalPromiseListQuery
     * @return
     */
    List<JdhMedicalPromisePo> listByPatients(@Param("param") MedicalPromiseListQuery medicalPromiseListQuery);

    /**
     * 批量更新
     * @param medicalPromiseFreezeBO
     * @return
     */
    Integer updateBatchFreeze(@Param("medicalPromiseFreezeBO") MedicalPromiseFreezeBO medicalPromiseFreezeBO);

}
