package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.cache.MybatisPlusTwoLevelCache;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhServiceItemPo;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * @InterfaceName:JdhServiceItemPoMapper
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/20 11:57
 * @Vserion: 1.0
 **/
@CacheNamespace(implementation= MybatisPlusTwoLevelCache.class,eviction= MybatisPlusTwoLevelCache.class)
public interface JdhServiceItemPoMapper extends BaseMapper<JdhServiceItemPo> {
}
