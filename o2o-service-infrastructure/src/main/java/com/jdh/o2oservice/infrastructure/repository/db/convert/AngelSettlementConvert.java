package com.jdh.o2oservice.infrastructure.repository.db.convert;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleItemTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlement;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlementDetail;
import com.jdh.o2oservice.core.domain.settlement.model.JdhAngelWorkSettleSnapshot;
import com.jdh.o2oservice.infrastructure.repository.db.po.AngelSettlementDetailPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.AngelSettlementPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelWorkSettleSnapshotPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhServiceItemPo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 2:15 下午
 * @Description:
 */
@Mapper
public interface AngelSettlementConvert {
    AngelSettlementConvert ins = Mappers.getMapper(AngelSettlementConvert.class);

    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "updateTime", expression = "java(new java.util.Date())")
    @Mapping(target = "yn", expression = "java(com.jdh.o2oservice.base.enums.YnStatusEnum.YES.getCode())")
    AngelSettlementPo dao2AngelSettlementPo(AngelSettlement angelSettlement);


    AngelSettlement convert2AngelSettlement(AngelSettlementPo angelSettlement);

    @Named("getAngelSettlementPoQueryWrapper")
    default LambdaQueryWrapper<AngelSettlementPo> getAngelSettlementPoQueryWrapper(AngelSettlement angelSettlement) {
        LambdaQueryWrapper<AngelSettlementPo> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(angelSettlement.getSettleId())) {
            queryWrapper.eq(AngelSettlementPo::getSettleId, angelSettlement.getSettleId());
        }
        queryWrapper.eq(AngelSettlementPo::getYn, YnStatusEnum.YES.getCode());
        return queryWrapper;
    }


    @Named("getQueryWrapper")
    default LambdaQueryWrapper<AngelSettlementPo> getQueryWrapper(AngelSettlementQueryContext queryContext) {
        LambdaQueryWrapper<AngelSettlementPo> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(queryContext.getAngelId())) {
            queryWrapper.eq(AngelSettlementPo::getAngelId, queryContext.getAngelId());
        }
        if (CollUtil.isNotEmpty(queryContext.getAngelIdList())) {
            queryWrapper.in(AngelSettlementPo::getAngelId, queryContext.getAngelIdList());
        }
        if (Objects.nonNull(queryContext.getSettleId())) {
            queryWrapper.eq(AngelSettlementPo::getSettleId, queryContext.getSettleId());
        }
        if (StringUtils.isNotBlank(queryContext.getSettlementBusinessId())) {
            queryWrapper.eq(AngelSettlementPo::getSettlementBusinessId, queryContext.getSettlementBusinessId());
        }
        if (Objects.nonNull(queryContext.getOrderId())) {
            queryWrapper.eq(AngelSettlementPo::getOrderId, queryContext.getOrderId());
        }
        if (Objects.nonNull(queryContext.getSettlementType())) {
            queryWrapper.eq(AngelSettlementPo::getSettlementType, queryContext.getSettlementType());
        }
        if (Objects.nonNull(queryContext.getSettlementNo())) {
            queryWrapper.eq(AngelSettlementPo::getSettlementNo, queryContext.getSettlementNo());
        }
        if (Objects.nonNull(queryContext.getJobNature())) {
            queryWrapper.eq(AngelSettlementPo::getJobNature, queryContext.getJobNature());
        }
        if (Objects.nonNull(queryContext.getSettleStatus())) {
            queryWrapper.eq(AngelSettlementPo::getSettleStatus, queryContext.getSettleStatus());
//            queryWrapper.and(warp -> warp.eq(AngelSettlementPo::getSettleStatus, queryContext.getSettleStatus()).or()
//            .eq(AngelSettlementPo::getSettlementType, SettleItemTypeEnum.ADJUST.getType()));
        }

        if (Objects.nonNull(queryContext.getItemType())) {
            queryWrapper.eq(AngelSettlementPo::getItemType, queryContext.getItemType());
        }
        if (Objects.nonNull(queryContext.getExpectSettleTime())) {
            queryWrapper.le(AngelSettlementPo::getExpectSettleTime, queryContext.getExpectSettleTime());
        }

        if (Objects.nonNull(queryContext.getSettleTimeStart())) {
            queryWrapper.ge(AngelSettlementPo::getSettleTime, queryContext.getSettleTimeStart());
        }
        if (Objects.nonNull(queryContext.getSettleTimeEnd())) {
            queryWrapper.lt(AngelSettlementPo::getSettleTime, queryContext.getSettleTimeEnd());
        }

        if (Objects.nonNull(queryContext.getCashTimeStart())) {
            queryWrapper.ge(AngelSettlementPo::getExpectSettleTime, queryContext.getCashTimeStart());
        }
        if (Objects.nonNull(queryContext.getCashTimeEnd())) {
            queryWrapper.lt(AngelSettlementPo::getExpectSettleTime, queryContext.getCashTimeEnd());
        }

        if (Objects.nonNull(queryContext.getCreateTimeStart())) {
            queryWrapper.ge(AngelSettlementPo::getCreateTime, queryContext.getCreateTimeStart());
        }
        if (Objects.nonNull(queryContext.getCreateTimeEnd())) {
            queryWrapper.le(AngelSettlementPo::getCreateTime, queryContext.getCreateTimeEnd());
        }
        if (Objects.nonNull(queryContext.getPromiseId())) {
            queryWrapper.eq(AngelSettlementPo::getPromiseId, queryContext.getPromiseId());
        }
        if (Objects.nonNull(queryContext.getOrderId())) {
            queryWrapper.eq(AngelSettlementPo::getOrderId, queryContext.getOrderId());
        }
        //费项类型list
        if (CollectionUtils.isNotEmpty(queryContext.getItemTypeList())){
            queryWrapper.in(AngelSettlementPo::getItemType,queryContext.getItemTypeList());
        }
        //结算状态0 初始化 1 冻结中 2 已结算
        if (CollectionUtils.isNotEmpty(queryContext.getSettleStatusList())){
            queryWrapper.in(AngelSettlementPo::getSettleStatus,queryContext.getSettleStatusList());
        }
        if (Objects.nonNull(queryContext.getItemSourceId())) {
            queryWrapper.eq(AngelSettlementPo::getItemSourceId, queryContext.getItemSourceId());
        }
        queryWrapper.eq(AngelSettlementPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(AngelSettlementPo::getId);
        return queryWrapper;
    }

    Page<AngelSettlement> dao2AngelSettlementPage(IPage<AngelSettlementPo> page);

    List<AngelSettlement> dao2AngelSettlements(List<AngelSettlementPo> res);

    List<AngelSettlementPo> angelSettlementsToPo(List<AngelSettlement> res);

    List<AngelSettlementDetailPo> angelSettlementDetailsToPo(List<AngelSettlementDetail> res);

    @Named("getDetailQueryWrapper")
    default LambdaQueryWrapper<AngelSettlementDetailPo> getDetailQueryWrapper(AngelSettlementQueryContext queryContext) {
        LambdaQueryWrapper<AngelSettlementDetailPo> queryWrapper = new LambdaQueryWrapper<>();

        if (Objects.nonNull(queryContext.getSettleId())) {
            queryWrapper.eq(AngelSettlementDetailPo::getSettleId, queryContext.getSettleId());
        }
        queryWrapper.eq(AngelSettlementDetailPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(AngelSettlementDetailPo::getId);
        return queryWrapper;
    }

    List<AngelSettlementDetail> detailDao2AngelSettlements(List<AngelSettlementDetailPo> page);
    /**
     *
     * @param po
     * @return
     */
    JdhAngelWorkSettleSnapshot convert2JdhAngelWorkSettleSnapshot(JdhAngelWorkSettleSnapshotPo po);
    /**
     *
     * @param jdhAngelWorkSettleSnapshot
     * @return
     */
    JdhAngelWorkSettleSnapshotPo convert2JdhAngelWorkSettleSnapshotPo(JdhAngelWorkSettleSnapshot jdhAngelWorkSettleSnapshot);
}
