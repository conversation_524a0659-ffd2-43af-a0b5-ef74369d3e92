package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.AngelSettlementPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhDispatchDetailPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName JdhDispatchDetailPoMapper
 * @Description
 * <AUTHOR>
 * @Date 2024/4/23 14:33
 **/
@Mapper
public interface JdhDispatchDetailPoMapper extends BaseMapper<JdhDispatchDetailPo> {

    /**
     * 批量插入
     *
     * @param list
     * @return {@link Integer}
     */
    Integer batchInsert(@Param("list") List<JdhDispatchDetailPo> list);

    /**
     * 批量更新
     * @param list
     * @return
     */
    Integer updateBatch(@Param("list") List<JdhDispatchDetailPo> list);
}