package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.core.domain.product.model.JdhMaterialPackage;
import com.jdh.o2oservice.core.domain.product.model.JdhMaterialPackageIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhMaterialPackageRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhMaterialPackagePoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhMaterialPackagePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhMaterialPackagePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 京东健康耗材包数据数据仓
 *
 * <AUTHOR>
 * @date 2024/04/21
 */
@Repository
@Slf4j
public class JdhMaterialPackageRepositoryImpl implements JdhMaterialPackageRepository {

    /**
     * jdhSkuPoMapper
     */
    @Resource
    JdhMaterialPackagePoMapper jdhMaterialPackagePoMapper;

    /**
     * 生成ID工厂
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param jdhMaterialPackageIdentifier identifier
     */
    @Override
    public JdhMaterialPackage find(JdhMaterialPackageIdentifier jdhMaterialPackageIdentifier) {
        LambdaQueryWrapper<JdhMaterialPackagePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhMaterialPackagePo::getMaterialPackageId, jdhMaterialPackageIdentifier.getMaterialPackageId())
                .eq(JdhMaterialPackagePo::getYn, YnStatusEnum.YES.getCode());
        return JdhMaterialPackagePoConverter.INSTANCE.poToModel(jdhMaterialPackagePoMapper.selectOne(queryWrapper));
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param entity
     */
    @Override
    public int remove(JdhMaterialPackage entity) {
        return 0;
    }

    /**
     * 保存加项包
     *
     * @param jdhMaterialPackage jdhMaterialPackage
     * @return count
     */
    @Override
    public int save(JdhMaterialPackage jdhMaterialPackage) {
        JdhMaterialPackagePo po = JdhMaterialPackagePo.builder()
                .materialPackageId(generateIdFactory.getId())
                .materialPackageName(jdhMaterialPackage.getMaterialPackageName())
                .materialPackageDetail(jdhMaterialPackage.getMaterialPackageDetail())
                .skuId(jdhMaterialPackage.getSkuId())
                .remark(jdhMaterialPackage.getRemark())
                .build();
        Date now = new Date();
        po.setCreateUser(jdhMaterialPackage.getCreateUser());
        po.setCreateTime(now);
        po.setUpdateUser(jdhMaterialPackage.getUpdateUser());
        po.setUpdateTime(now);
        po.setYn(YnStatusEnum.YES.getCode());
        return jdhMaterialPackagePoMapper.insert(po);
    }

    /**
     * 更新加项包
     *
     * @param jdhMaterialPackage jdhMaterialPackage
     * @return count
     */
    @Override
    public int update(JdhMaterialPackage jdhMaterialPackage) {
        LambdaUpdateWrapper<JdhMaterialPackagePo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StringUtils.isNotBlank(jdhMaterialPackage.getMaterialPackageName()), JdhMaterialPackagePo::getMaterialPackageName, jdhMaterialPackage.getMaterialPackageName())
                .set(StringUtils.isNotBlank(jdhMaterialPackage.getMaterialPackageDetail()), JdhMaterialPackagePo::getMaterialPackageDetail, jdhMaterialPackage.getMaterialPackageDetail())
                .set(jdhMaterialPackage.getSkuId() != null, JdhMaterialPackagePo::getSkuId, jdhMaterialPackage.getSkuId())
                .setSql("`version` = `version` + 1")
                .set(JdhMaterialPackagePo::getUpdateUser, jdhMaterialPackage.getUpdateUser())
                .eq(JdhMaterialPackagePo::getMaterialPackageId, jdhMaterialPackage.getMaterialPackageId())
                .eq(JdhMaterialPackagePo::getVersion, jdhMaterialPackage.getVersion());
        return jdhMaterialPackagePoMapper.update(null, updateWrapper);
    }

    /**
     * 查询加项包
     *
     * @param jdhMaterialPackage jdhMaterialPackage
     * @return count
     */
    @Override
    public JdhMaterialPackage query(JdhMaterialPackage jdhMaterialPackage) {
        LambdaQueryWrapper<JdhMaterialPackagePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(jdhMaterialPackage.getMaterialPackageId() != null, JdhMaterialPackagePo::getMaterialPackageId, jdhMaterialPackage.getMaterialPackageId())
                .eq(StringUtils.isNotBlank(jdhMaterialPackage.getMaterialPackageName()), JdhMaterialPackagePo::getMaterialPackageName, jdhMaterialPackage.getMaterialPackageName())
                .eq(jdhMaterialPackage.getSkuId() != null, JdhMaterialPackagePo::getSkuId, jdhMaterialPackage.getSkuId())
                .eq(JdhMaterialPackagePo::getYn, YnStatusEnum.YES.getCode());
        return JdhMaterialPackagePoConverter.INSTANCE.poToModel(jdhMaterialPackagePoMapper.selectOne(queryWrapper));
    }

    /**
     * 查询加项包
     *
     * @param jdhMaterialPackageList jdhMaterialPackageList
     * @return count
     */
    @Override
    public List<JdhMaterialPackage> queryList(List<JdhMaterialPackage> jdhMaterialPackageList) {
        LambdaQueryWrapper<JdhMaterialPackagePo> queryWrapper = Wrappers.lambdaQuery();
        Set<Long> idList = jdhMaterialPackageList.stream().filter(e -> Objects.nonNull(e.getMaterialPackageId())).map(JdhMaterialPackage::getMaterialPackageId).collect(Collectors.toSet());
        Set<String> NameList = jdhMaterialPackageList.stream().filter(e -> StringUtils.isNotBlank(e.getMaterialPackageName())).map(s -> s.getMaterialPackageName().trim()).collect(Collectors.toSet());
        queryWrapper.in(CollUtil.isNotEmpty(idList), JdhMaterialPackagePo::getMaterialPackageId, idList)
                .in(CollUtil.isNotEmpty(NameList), JdhMaterialPackagePo::getMaterialPackageName, NameList)
                .eq(JdhMaterialPackagePo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhMaterialPackagePo::getCreateTime);
        return JdhMaterialPackagePoConverter.INSTANCE.poToModel(jdhMaterialPackagePoMapper.selectList(queryWrapper));
    }

    /**
     * 查询加项包
     *
     * @param jdhMaterialPackage jdhMaterialPackage
     * @return count
     */
    @Override
    public Page<JdhMaterialPackage> queryPage(JdhMaterialPackage jdhMaterialPackage) {
        Page<JdhMaterialPackagePo> param = new Page<>(jdhMaterialPackage.getPageNum(), jdhMaterialPackage.getPageSize());
        LambdaQueryWrapper<JdhMaterialPackagePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(jdhMaterialPackage.getMaterialPackageId() != null, JdhMaterialPackagePo::getMaterialPackageId, jdhMaterialPackage.getMaterialPackageId())
                .eq(jdhMaterialPackage.getSkuId() != null, JdhMaterialPackagePo::getSkuId, jdhMaterialPackage.getSkuId())
                .like(StringUtils.isNotBlank(jdhMaterialPackage.getMaterialPackageName()), JdhMaterialPackagePo::getMaterialPackageName, jdhMaterialPackage.getMaterialPackageName())
                .eq(JdhMaterialPackagePo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhMaterialPackagePo::getCreateTime);
        IPage<JdhMaterialPackagePo> page = jdhMaterialPackagePoMapper.selectPage(param, queryWrapper);
        List<JdhMaterialPackage> list = JdhMaterialPackagePoConverter.INSTANCE.poToModel(page.getRecords());
        return JdhBasicPoConverter.initPage(page, list);
    }
}
