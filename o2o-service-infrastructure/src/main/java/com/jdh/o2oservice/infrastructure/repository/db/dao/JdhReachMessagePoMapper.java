package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachMessagePo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 触达消息mapper接口
 * @author: yang<PERSON>yu
 * @date: 2024/4/21 4:44 下午
 * @version: 1.0
 */
public interface JdhReachMessagePoMapper  extends BaseMapper<JdhReachMessagePo> {

    /**
     * 批量插入数据
     * @param pos
     * @return
     */
    int batchInsert(@Param("messages") List<JdhReachMessagePo> pos);

    /**
     * 获取未读消息数量
     * @return
     */
    int findUnReadingCount(@Param("userPin") String pin,  @Param("indexKey") String indexKey );

    List<Long> listMessageIds(@Param("startTime")Date startTime,
                              @Param("endTime") Date endTime,
                              @Param("reachTypes") List<Integer> reachTypes);

    /**
     *
     * @param pin
     * @param indexKey
     * @param cursor 时间戳游标
     * @param offset 偏移量（分页查询）
     * @return
     */
    List<JdhReachMessagePo> listByCursor(@Param("userPin") String pin,
                                         @Param("indexKey") String indexKey ,
                                         @Param("cursor") Long cursor,
                                         @Param("offset")Integer offset);


   Integer batchUpdateReadStatus(@Param("userPin") String pin, @Param("indexKey") String indexKey );
    List<JdhReachMessagePo> listBox(@Param("userPin") String pin,
                   @Param("startTime")Date startTime,
                   @Param("endTime") Date endTime);

    Integer updateBoxIndex(@Param("messageId")Long messageId, @Param("indexKey") String indexKey);


}
