package com.jdh.o2oservice.infrastructure.repository.db.convert;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipHistory;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWorkHistory;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelWorkExtVo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelShipHistoryPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelTaskPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelWorkHistoryPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelWorkPo;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface JdhAngelWorkPoConverter {

    JdhAngelWorkPoConverter INSTANCE = Mappers.getMapper(JdhAngelWorkPoConverter.class);

    List<JdhAngelWorkPo> convertToPoList(List<AngelWork> angelWorks);

    @Mapping(target = "jdhAngelWorkExtVo", source = "extend", qualifiedByName = "workExtObjConvert")
    AngelWork convertToAngelWork(JdhAngelWorkPo jdhAngelWorkPo);

    List<AngelWork> convertToAngelWorkList(List<JdhAngelWorkPo> jdhAngelWorkPos);

    PageDto<AngelWork>  convertToAngelWorkPage(Page<JdhAngelWorkPo> jdhAngelWorkPoPages);

    @Mapping(target = "extend", source = "jdhAngelWorkExtVo", qualifiedByName = "workExtJsonConvert")
    JdhAngelWorkPo convertToAngelWorkSave(AngelWork angelWork);

    JdhAngelWorkHistoryPo convertToJdhAngelWorkHistoryPo(AngelWorkHistory angelWorkHistory);

    AngelWorkHistory convertToAngelWorkHistory(JdhAngelWorkHistoryPo jdhAngelWorkHistoryPo);

    List<AngelWorkHistory> convertToAngelWorkHistoryList(List<JdhAngelWorkHistoryPo> jdhAngelWorkHistoryPos);

    List<JdhAngelWorkHistoryPo> convertToJdhAngelWorkHistoryPos(List<AngelWorkHistory> angelWorkHistoryList);

    AngelShipHistory convertToAngelShipHistory(JdhAngelShipHistoryPo jdhAngelShipHistoryPo);

    List<AngelShipHistory> convertToAngelShipHistoryList(List<JdhAngelShipHistoryPo> jdhAngelShipHistoryPos);

    @Named("workExtObjConvert")
    default JdhAngelWorkExtVo workExtObjConvert(String extJson){
        if(StringUtils.isBlank(extJson)){
            return null;
        }
        return JSON.parseObject(extJson, JdhAngelWorkExtVo.class);
    }
    @Named("workExtJsonConvert")
    default String workExtJsonConvert(JdhAngelWorkExtVo workExtVo){
        return JSON.toJSONString(workExtVo);
    }
}
