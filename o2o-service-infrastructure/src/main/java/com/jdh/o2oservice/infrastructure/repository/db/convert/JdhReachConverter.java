package com.jdh.o2oservice.infrastructure.repository.db.convert;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.support.reach.model.*;
import com.jdh.o2oservice.core.domain.support.reach.valueobject.ReachTriggerDelayStrategy;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachMessagePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachTaskPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachTemplatePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhReachTriggerPo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 触达 仓储层转换
 * @author: yangxiyu
 * @date: 2024/3/25 9:38 下午
 * @version: 1.0
 */
@Mapper
public interface JdhReachConverter {
    /**  */
    JdhReachConverter INSTANCE = Mappers.getMapper(JdhReachConverter.class);

    /**
     * 任务PO转换
     * @param taskList
     * @return
     */
    List<JdhReachTaskPo> convert2ReachTaskPo(List<JdhReachTask> taskList);

    /***
     *
     * @param task
     * @return
     */
    @Mapping(target = "eventBody", source = "eventBody", qualifiedByName = "map2Json")
    @Mapping(target = "delay",  expression = "java(com.jdh.o2oservice.base.enums.YnStatusEnum.convertInt(task.getDelay()))")
    JdhReachTaskPo convert2ReachTaskPo(JdhReachTask task);
    /**
     * 任务聚合转换
     * @param taskPo
     * @return
     */
    @Mapping(target = "eventBody", source = "eventBody", qualifiedByName = "json2Map")
    @Mapping(target = "delay",  expression = "java(com.jdh.o2oservice.base.enums.YnStatusEnum.convert(taskPo.getDelay()))")
    JdhReachTask convert2ReachTask(JdhReachTaskPo taskPo);

    /**
     * reachTrigger转换
     * @param taskList
     * @return
     */
    List<JdhReachTrigger> convert2Trigger(List<JdhReachTriggerPo> taskList);

    /**
     * triggerPo2Entity
     *
     * @param jdhReachTriggerPo jdhReachTriggerPo
     * @return {@link JdhReachTrigger}
     */
    default JdhReachTrigger triggerPo2Entity(JdhReachTriggerPo jdhReachTriggerPo){

        if(Objects.isNull(jdhReachTriggerPo)){
            return null;
        }

        JdhReachTrigger trigger = new JdhReachTrigger();
        trigger.setId(jdhReachTriggerPo.getId());
        trigger.setTriggerId(jdhReachTriggerPo.getTriggerId());
        trigger.setTriggerName(jdhReachTriggerPo.getTriggerName());
        trigger.setDomainCode(jdhReachTriggerPo.getDomainCode());
        trigger.setAggregateCode(jdhReachTriggerPo.getAggregateCode());
        trigger.setEventCode(jdhReachTriggerPo.getEventCode());
        trigger.setExpressionFillFunction(jdhReachTriggerPo.getExpressionFillFunction());
        trigger.setExpressionStr(jdhReachTriggerPo.getExpressionStr());
        trigger.setTemplateId(jdhReachTriggerPo.getTemplateId());
        trigger.setSelectUserFunctionId(jdhReachTriggerPo.getSelectUserFunctionId());
        trigger.setStrategy(StrUtil.isBlank(jdhReachTriggerPo.getStrategy()) ? null : JSON.parseObject(jdhReachTriggerPo.getStrategy(), ReachTriggerDelayStrategy.class));
        trigger.setYn(jdhReachTriggerPo.getYn());
        return trigger;
    }

    /**
     * triggerEntity2Po
     *
     * @param trigger trigger
     * @return {@link JdhReachTriggerPo}
     */
    default JdhReachTriggerPo triggerEntity2Po(JdhReachTrigger trigger){
        if(Objects.isNull(trigger)){
            return null;
        }

        JdhReachTriggerPo po = new JdhReachTriggerPo();
        po.setId(trigger.getId());
        po.setTriggerId(trigger.getTriggerId());
        po.setTriggerName(trigger.getTriggerName());
        po.setDomainCode(trigger.getDomainCodeField());
        po.setAggregateCode(trigger.getAggregateCodeField());
        po.setEventCode(trigger.getEventCode());
        po.setExpressionStr(trigger.getExpressionStr());
        po.setExpressionFillFunction(trigger.getExpressionFillFunction());
        po.setTemplateId(trigger.getTemplateId());
        po.setSelectUserFunctionId(trigger.getSelectUserFunctionId());
        po.setStrategy(Objects.isNull(trigger.getStrategy()) ? null : JSON.toJSONString(trigger.getStrategy()));
        po.setYn(trigger.getYn());
        return po;
    }

    /**
     *
     * @param jdhReachMessagePo
     * @return
     */
    @Mapping(target = "extras", source = "extras", qualifiedByName = "convertToMap")
    JdhReachMessage convertToJdhReachMessage(JdhReachMessagePo jdhReachMessagePo);

    /**
     * messagePo转实体
     * @param jdhReachMessagePos
     * @return
     */
    List<JdhReachMessage> convertToJdhReachMessageList(List<JdhReachMessagePo> jdhReachMessagePos);

    /**
     *
     * @param templatePos
     * @return
     */
    List<JdhReachTemplate> convert2ReachTemplateList(List<JdhReachTemplatePo> templatePos);
    /**
     * 触达模版实体转换
     * @param templatePo
     * @return
     */
    default JdhReachTemplate convert2ReachTemplate(JdhReachTemplatePo templatePo){
        if (Objects.isNull(templatePo)){
            return null;
        }
        JdhReachTemplate template = new JdhReachTemplate();
        BeanUtils.copyProperties(templatePo, template);

        if (StringUtils.isNotBlank(templatePo.getParamParse())){
            List<ReachParamParse> paramParseFunction = JSON.parseArray(templatePo.getParamParse(), ReachParamParse.class);
            template.setParamParse(paramParseFunction);
        }

        if (StringUtils.isNotBlank(templatePo.getUrlParse())) {
            ReachParamParse pushUrl = JSON.parseObject(templatePo.getUrlParse(), ReachParamParse.class);
            template.setUrlParse(pushUrl);
        }


        if (Objects.equals(templatePo.getBell(), YnStatusEnum.YES.getCode())){
            template.setBell(Boolean.TRUE);
        }else{
            template.setBell(Boolean.FALSE);
        }

        return template;
    }


    /**
     * 消息entity转po
     * @param message
     * @return
     */
    default JdhReachMessagePo convert2ReachTemplate(JdhReachMessage message) {
        String extras = JSON.toJSONString(message.getExtras());
        Date sendTime = null;
        Date readTime = null;
        if (Objects.nonNull(message.getSendTime())){
            sendTime = TimeUtils.localDateTimeToDate(message.getSendTime());
        }
        if (Objects.nonNull(message.getReadTime())){
            readTime = TimeUtils.localDateTimeToDate(message.getReadTime());
        }
        JdhReachMessagePo po = JdhReachMessagePo.builder()
                .messageId(message.getMessageId())
                .domainCode(message.getDomainCode())
                .eventCode(message.getEventCode())
                .aggregateCode(message.getAggregateCode())
                .aggregateId(message.getAggregateId())
                .userPin(message.getUserPin())
                .angelId(message.getAngelId())
                .messageBizType(message.getMessageBizType())
                .messageTitle(message.getMessageTitle())
                .messagePayload(message.getMessagePayload())
                .extras(extras)
                .sendTime(sendTime)
                .readTime(readTime)
                .isRead(message.getIsRead())
                .errorInfo(message.getErrorInfo())
                .reachType(message.getReachType())
                .appId(message.getAppId())
                .url(message.getUrl())
                .timestamp(message.getTimestamp())
                .taskId(message.getTaskId())
                .eventId(message.getEventId())
                .userPhone(message.getUserPhone())
                .userPhoneIndex(message.getUserPhone())
                .boxMessageGroupIndex(message.getBoxMessageGroupIndex())
                .build();
        return po;
    }

        /**
         * 触达模版实体转换
         * @param messages
         * @return
         */
    default List<JdhReachMessagePo> convert2ReachTemplate(List<JdhReachMessage> messages){
        if (Objects.isNull(messages)){
            return null;
        }

        List<JdhReachMessagePo> pos = Lists.newArrayList();
        for (JdhReachMessage message : messages) {
            pos.add(convert2ReachTemplate(message));
        }

        return pos;
    }

    @Named("convertToMap")
    default Map<String, String> convertToMap(String source){
        if(StringUtils.isBlank(source)){
            return null;
        }
        return JSON.parseObject(source, Map.class);
    }

    /**
     * map转json
     * @param map
     * @return
     */
    @Named("map2Json")
    default String map2Json(Map<String, Object> map){
        return JSON.toJSONString(map);
    }

    /**
     * json转map
     * @param json
     * @return
     */
    @Named("json2Map")
    default Map<String, Object> json2Map(String json){
        if(StringUtils.isBlank(json)){
            return null;
        }
        return JSON.parseObject(json);
    }
}
