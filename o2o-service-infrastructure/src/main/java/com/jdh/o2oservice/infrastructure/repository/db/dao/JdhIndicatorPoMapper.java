package com.jdh.o2oservice.infrastructure.repository.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jdh.o2oservice.infrastructure.repository.db.cache.MybatisPlusTwoLevelCache;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhIndicatorPo;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * @InterfaceName:JdhIndicatorPoMapper
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/20 11:58
 * @Vserion: 1.0
 **/
@CacheNamespace(implementation= MybatisPlusTwoLevelCache.class,eviction= MybatisPlusTwoLevelCache.class)
public interface JdhIndicatorPoMapper extends BaseMapper<JdhIndicatorPo> {
}
