package com.jdh.o2oservice.infrastructure.repository.db.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.jim.cli.Cluster;
import com.jd.jim.cli.PipelineClient;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.TwoLevelCacheConfig;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.cache.Cache;
import org.apache.ibatis.cache.CacheKey;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * @ClassName MybatisPlusTwoLevelCache
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/9/7 1:48 PM
 * @Version 1.0
 **/
@Slf4j
public class MybatisPlusTwoLevelCache implements Cache {

    private final String id;

    private final String callTotalNum = "callTotalNum_%s";

    private final String hitCacheTotalNum = "hitCacheTotalNum_%s";

    private final ThreadLocal<Long> threadLocal = new ThreadLocal<>();

    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    public MybatisPlusTwoLevelCache(String id) {
        if (id == null) {
            throw new IllegalArgumentException("未获取到缓存实例id");
        }
        this.id = id;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public void putObject(Object key, Object value) {
        //log.info("MybatisPlusTwoLevelCache putObject() key={} value={}", JSON.toJSONString(key),JSON.toJSONString(value));
        DuccConfig duccConfig = SpringUtil.getApplicationContext().getBean(DuccConfig.class);
        if(!duccConfig.getTwoLevelCacheConfig().getTwoLevelCacheOpen()){
            log.info("MybatisPlusTwoLevelCache putObject 缓存开关是已关闭状态,不走二级缓存!!");
            return;
        }
        if(value==null||CollectionUtils.isEmpty((List)value)){
            log.info("MybatisPlusTwoLevelCache putObject() value=null");
            return;
        }
        DecorateCache decorateCache = new DecorateCache( (CacheKey)key,this.getId());
        Class poClass = null;
        try{
            //获取po class对象
            poClass = decorateCache.getPoClass();
            //poName命中了黑名单,不走缓存逻辑
            if(CollectionUtils.isNotEmpty(duccConfig.getTwoLevelCacheConfig().getBlackPoNames())&&duccConfig.getTwoLevelCacheConfig().getBlackPoNames().contains(poClass.getSimpleName())){
                log.info("MybatisPlusTwoLevelCache poName={} 命中了黑名单,不走二级缓存",poClass.getSimpleName());
                return;
            }
            TwoLevelCacheConfig.PoConfig poConfig = duccConfig.getTwoLevelCacheConfig().getPoConfigMap().get(poClass.getSimpleName());
                //获取where条件的字段名称和值
            String whereSql = decorateCache.getWhereSql();
            //log.info("MybatisPlusTwoLevelCache whereSql={}",JSON.toJSONString(whereSql));
            //获取po聚合字段
            Field aggregateField = this.getAggregateField(poClass);
            aggregateField.setAccessible(true);



            //获取table数据 表版本号
            Long tableVersion =threadLocal.get();


            //批量往数据key中写数据
            PipelineClient pipelineClient = SpringUtil.getApplicationContext().getBean(Cluster.class).pipelineClient();
            List result = (List)value;
            List<Long> aggregateIds = new ArrayList<>();
            for (Object t: result) {
                DataModel dataModel = new DataModel();
                dataModel.setData(t);
                dataModel.setVersion(tableVersion);
                aggregateIds.add(Long.parseLong(aggregateField.get(t)+""));
                String dataModelKey = DataModel.getDataKey(poClass,aggregateField.get(t)+"");
                String dataModelString = JSON.toJSONString(dataModel);
                //log.info("MybatisPlusTwoLevelCache 保存数据 dataModelKey={} dataModelString={}",dataModelKey,dataModelString);
                pipelineClient.setEx(dataModelKey,dataModelString,poConfig==null?5:poConfig.getExpireMinute(),TimeUnit.MINUTES);
            }
            pipelineClient.flush();
            //维护缓存方法数据
            MethodModel methodModel = new MethodModel();
            methodModel.setData(aggregateIds);
            methodModel.setVersion(tableVersion);
            String methodKey = MethodModel.getMethodKey(poClass,decorateCache.getMethodName(),whereSql);
            String methodModelString = JSON.toJSONString(methodModel);
            //log.info("MybatisPlusTwoLevelCache 保存方法key methodKey={} dataModelString={}",methodKey,methodModelString);

            SpringUtil.getApplicationContext().getBean(Cluster.class).setEx(methodKey
                    ,methodModelString
                    ,poConfig==null?5:poConfig.getExpireMinute()
                    ,TimeUnit.MINUTES);

        }catch(BusinessException businessException){
            log.info("MybatisPlusTwoLevelCache MybatisPlusTwoLevelCache.putObject 异常,不影响逻辑:{}",businessException.getErrorCode().getDescription());
        } catch (Exception e){
            log.info("MybatisPlusTwoLevelCache MybatisPlusTwoLevelCache.putObject 异常,不影响逻辑",e);
        }finally {
            //释放锁
            RedisUtil redisUtil = SpringUtil.getApplicationContext().getBean(RedisUtil.class);
            redisUtil.unLock(poClass.getSimpleName());
        }
    }

    /**
     * id = com.jdh.o2oservice.infrastructure.repository.db.dao.JdOrderPoMapper
     * key class org.apache.ibatis.cache.CacheKey
     * key {"updateCount":7}
     * @param key
     * @return
     */
    @Override
    public Object getObject(Object key) {
        //log.info("MybatisPlusTwoLevelCache getObject key={}", JSON.toJSONString(key));
        DuccConfig duccConfig = SpringUtil.getApplicationContext().getBean(DuccConfig.class);
        if(!duccConfig.getTwoLevelCacheConfig().getTwoLevelCacheOpen()){
            log.info("MybatisPlusTwoLevelCache getObject 缓存开关是已关闭状态,不走二级缓存!!");
            return null;
        }
        //分页
        CacheKey cacheKey = (CacheKey)key;
        DecorateCache decorateCache = new DecorateCache(cacheKey,this.getId());
        try {
            //获取po class对象
            Class poClass = decorateCache.getPoClass();

            //poName命中了黑名单,不走缓存逻辑
            if(CollectionUtils.isNotEmpty(duccConfig.getTwoLevelCacheConfig().getBlackPoNames())&&duccConfig.getTwoLevelCacheConfig().getBlackPoNames().contains(poClass.getSimpleName())){
                log.info("MybatisPlusTwoLevelCache poName={} 命中了黑名单,不走二级缓存",poClass.getSimpleName());
                return null;
            }

            //统计数,异步处理
            SpringUtil.getApplicationContext().getBean(ExecutorPoolFactory.class).get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
                SpringUtil.getApplicationContext().getBean(Cluster.class).incr(String.format(callTotalNum,poClass.getSimpleName()));
            });

            //获取where条件的字段名称和值
            String whereSql = decorateCache.getWhereSql();
            //log.info("MybatisPlusTwoLevelCache whereSql={}",JSON.toJSONString(whereSql));
            RedisUtil redisUtil = SpringUtil.getApplicationContext().getBean(RedisUtil.class);
            if(!redisUtil.tryLock(poClass.getSimpleName(),3L,TimeUnit.SECONDS)){
                //获取锁失败
                log.info("获取锁失败!!!,不走缓存,走mysql,不影响逻辑");
                return null;
            }


            Object result = this.getFromMethodCache(poClass,decorateCache.getMethodName(),whereSql);
            //log.info("MybatisPlusTwoLevelCache getObject 缓存数据 result={}",JSON.toJSONString(result));
            if(result!=null){
                //统计数,异步处理
                log.info("MybatisPlusTwoLevelCache getObject 命中缓存次数加1");
                SpringUtil.getApplicationContext().getBean(ExecutorPoolFactory.class).get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
                    SpringUtil.getApplicationContext().getBean(Cluster.class).incr(String.format(hitCacheTotalNum,poClass.getSimpleName()));
                });
            }
            return result;
        }catch(BusinessException businessException){
            log.info("MybatisPlusTwoLevelCache MybatisPlusTwoLevelCache.getObject 异常,不影响逻辑:{}",businessException.getErrorCode().getDescription());
        } catch (Exception e) {
            log.info("MybatisPlusTwoLevelCache 异常",e);
        }
        return null;
    }

    /**
     * 从方法缓存key获取数据
     * @param poClass
     */
    private Object getFromMethodCache(Class poClass,String methodName, String whereSql) {
        //获取table数据
        String tableKey = TableModel.getHashFieldKey(poClass);
        Long tableVersion = Long.parseLong(StringUtils.defaultIfBlank(SpringUtil.getApplicationContext().getBean(Cluster.class).hGet(TableModel.TABLE_KEY,tableKey),"0"));
        //记录当前线程的版本
        threadLocal.set(tableVersion);
        //只有一个查询参数且是aggregateField;
        String methodKey = MethodModel.getMethodKey(poClass,methodName,whereSql);
        //log.info("MybatisPlusTwoLevelCache methodKey={}",methodKey);
        String s = SpringUtil.getApplicationContext().getBean(Cluster.class).get(methodKey);
        //获取data数据
        MethodModel methodModel = JSON.parseObject(s, MethodModel.class);
        if(methodModel==null){
            log.info("MybatisPlusTwoLevelCache methodModel 为空,不走缓存");
            return null;
        }
        //对比版本号是否相等,不等则不走缓存
        if(!tableVersion.equals(methodModel.getVersion())){
            log.info("MybatisPlusTwoLevelCache 版本号不相等,不走缓存");
            return null;
        }
        return this.getFromDataCache(poClass,methodModel.toDataListString());
    }

    /**
     * 将多个参数生成一个拼接字符串
     * @param sqlParamsList
     * @return
     */
    private String buildParamsString(List<SqlParams> sqlParamsList){
        StringBuffer sb = new StringBuffer();
        for (SqlParams sqlParams:sqlParamsList){
            sb.append(sqlParams.getFieldName()+"="+sqlParams.getFieldValue()+"|");
        }
        return sb.toString();
    }

    /**
     * 直接数据key中获取数据
     * @param poClass
     * @param aggregateIds
     * @return
     */
    private Object getFromDataCache(Class poClass,List<String> aggregateIds){

        //获取table数据
        String tableKey = TableModel.getHashFieldKey(poClass);
        Long tableVersion = Long.parseLong(StringUtils.defaultIfBlank(SpringUtil.getApplicationContext().getBean(Cluster.class).hGet(TableModel.TABLE_KEY,tableKey),"0"));

        //只有一个查询参数且是aggregateField;
        List<String> dataKeys = DataModel.getBatchDataKey(poClass,aggregateIds);
        List<String> ss = SpringUtil.getApplicationContext().getBean(Cluster.class).mGet(dataKeys.toArray(new String[0]));
        //log.info("MybatisPlusTwoLevelCache tableKey={} tableVersion={} dataKeys={} ss={}",tableKey,tableVersion,JSON.toJSONString(dataKeys),JSON.toJSONString(ss));
        //获取data数据
        JSONArray jsonArray1 = JSONArray.parseArray(JSON.toJSONString(ss));
        if(CollectionUtils.isEmpty(jsonArray1)){
            log.info("MybatisPlusTwoLevelCache dataModelList 为空,不走缓存");
            return null;
        }

        //对比版本号是否相等,不等则不走缓存
        for (Object object:jsonArray1) {
            if(object==null){
                log.info("MybatisPlusTwoLevelCache object=null 不走缓存");
                return null;
            }
            JSONObject dataModelJsonObject = JSON.parseObject((String)object);
            if(!tableVersion.equals(dataModelJsonObject.getLong(TableModel.version))){
                log.info("MybatisPlusTwoLevelCache 版本号不相等,不走缓存");
                return null;
            }
        }
        //聚合数据
        List<JSONObject> result = new ArrayList<>();
        for (Object object:jsonArray1) {
            JSONObject dataModelJsonObject = JSON.parseObject((String)object);
            JSONObject jsonObject = dataModelJsonObject.getJSONObject(DataModel.DATA);
            result.add(jsonObject);
        }
        if(result.size()!=aggregateIds.size()){
            log.info("MybatisPlusTwoLevelCache 传入聚合id数量和查询出的数据的数量不相等!!!");
            return null;
        }
        return JSON.parseArray(JSON.toJSONString(result),poClass);
    }

    /**
     * mybatis保留方法
     * @param key
     * @return
     */
    @Override
    public Object removeObject(Object key) {
        log.info("MybatisPlusTwoLevelCache 执行了 removeObject key={}", JSON.toJSONString(key));
        return null;
    }

    /**
     * 清空缓存，在增删改时会自动调用
     */
    @Override
    public void clear() {
        log.info("MybatisPlusTwoLevelCache clear() 方法执行了");
        DecorateCache decorateCache = new DecorateCache(null,this.getId());
        Class poClass = decorateCache.getPoClass();
        String tableKey = TableModel.getHashFieldKey(poClass);
        //版本号直接加1,数据key和方法key均失效
        SpringUtil.getApplicationContext().getBean(Cluster.class).hIncrBy(TableModel.TABLE_KEY,tableKey,1);
    }

    @Override
    public int getSize() {
        return 0;
    }


    /**
     * 查询标记了AggregateId的Field
     * @param clazz
     * @return
     */
    private Field getAggregateField(Class clazz){
        // 遍历MyClass的所有字段
        for (Field field : clazz.getDeclaredFields()) {
            // 检查字段是否被@MyAnnotation注解标记
            if (field.isAnnotationPresent(AggregateId.class)) {
                // 如果被标记，则输出字段名
                return field;
            }
        }
        return null;
    }

    /**
     * 将驼峰命名转换为下划线命名
     *
     * @param camelCase 驼峰命名的字符串
     * @return 下划线命名的字符串
     */
    private String camelCaseToSnakeCase(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }
        StringBuilder snakeCase = new StringBuilder();
        boolean first = true; // 用于标记是否是第一个字符
        for (char c : camelCase.toCharArray()) {
            if (Character.isUpperCase(c)) {
                if (!first) {
                    snakeCase.append('_');
                }
                snakeCase.append(Character.toLowerCase(c));
            } else {
                snakeCase.append(c);
            }
            first = false;
        }
        return snakeCase.toString();
    }

}
