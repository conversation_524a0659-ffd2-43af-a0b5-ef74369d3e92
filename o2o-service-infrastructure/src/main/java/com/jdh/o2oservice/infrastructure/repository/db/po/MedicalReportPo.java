package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-18 18:30
 * @Desc : 医学检测单id
 */
@Data
@TableName(value = "medical_report",autoResultMap = true)
public class MedicalReportPo {
    /**
     * <pre>
     * 主键
     * </pre>
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 履约单id
     */
    private Long promiseId;

    /**
     * 检测单id
     */
    private Long medicalPromiseId;

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 亲属类型:0:未绑定关系 1-自己 21-父母 22-配偶 23-子女 24-其他
     */
    private Integer relativesType;
    /**
     * 类型：1 快检报告  2 体检报告
     */
    private Integer medicalType;
    /**
     * 报告类型：1-主检报告 2-子报告
     */
    private Integer reportType;

    /**
     * 父id，如果没有父报告id，就为0
     */
    private Long parentId;

    /**
     * 检查时间
     */
    private String examinationTime;

    /**
     * 文件内容md5，多个按逗号分隔
     */
    private String fileMd5;

    /**
     * oss地址
     */
    private String reportOss;

    /**
     * 源报告oss地址
     */
    private String sourceOss;

    /**
     * 京东标准结构化报告地址
     */
    private String structReportOss;

    /**
     * 结构化报告来源 1-供应商 2-解析平台
     */
    private Integer reportSource;
    /**
     * 供应商编码
     */
    private Long channelNo;
    /**
     * 供应商报告编号，不保证唯一，但是同一个品牌的报告编号应该保证唯一,由出具报告的机构提供，或者解析PDF获取
     */
    private String channelReportNo;
    /**
     * 机构出报告时间
     */
    private Date reportTime;
    /**
     * 报告展示状态0:不展示；1:可展示
     */
    private Integer reportStatus;

    /**
     * 是否有效0无效 1有效
     */
    private Integer yn;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
        /**
     * 设备厂商编号
     */
    private String manufacturerNumber;

    /**
     * 设备号
     */
    private String snCode;

    /**
     * 报告中心id
     */
    private String reportCenterId;
    /**
     * 报告图片的OSS地址
     */
    private String reportJpgOss;


}
