package com.jdh.o2oservice.infrastructure.repository.db.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 触达模版PO
 * @author: yangxiyu
 * @date: 2024/4/21 3:41 下午
 * @version: 1.0
 */
@Data
@TableName(value = "jdh_reach_template",autoResultMap = true)
public class JdhReachTemplatePo extends JdhBasicTimePo{

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 任务ID
     */
    private Long templateId;
    /**
     *
     */
    private String templateName;
    /**
     * 模版绑定的触达任务类型
     */
    private Integer type;
    /**
     * 消息编码类型
     */
    private String messageBizType;
    /**
     * 模版内容
     */
    private String content;
    /**
     * 参数填充规则
     */
    private String paramParse;

    /**
     * 跳转的url解析
     */
    private String urlParse;

    /**
     * 触达渠道模版ID
     */
    private String channelTemplateId;
    /**
     *
     */
    private String accountId;
    /**
     * 使用有铃声
     */
    private Integer bell;

    /**
     * 描述
     */
    @TableField("`desc`")
    private String desc;
}
