package com.jdh.o2oservice.infrastructure.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.product.model.ServiceItem;
import com.jdh.o2oservice.core.domain.report.bo.MedicalReportPageBO;
import com.jdh.o2oservice.core.domain.report.bo.MedicalReportQueryBO;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportChangeLog;
import com.jdh.o2oservice.core.domain.report.model.MedicalReportIndicator;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhIndicatorPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.MedicalReportConvert;
import com.jdh.o2oservice.infrastructure.repository.db.convert.MedicalReportConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.MedicalReportChangeLogMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.MedicalReportMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhServiceItemPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.MedicalReportChangeLogPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.MedicalReportIndicatorPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.MedicalReportPo;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-18 18:27
 * @Desc : 医学检测报告
 */
@Service
public class MedicalReportRepositoryImpl implements MedicalReportRepository {
    @Autowired
    MedicalReportMapper medicalReportMapper;

    /**
     * 报告变更记录，仅记录变更前数据
     */
    @Resource
    MedicalReportChangeLogMapper medicalReportChangeLogMapper;

    @Override
    public Long insert(MedicalReport medicalReport) {
        MedicalReportPo po = convert2MedicalReportPo(medicalReport);
        medicalReportMapper.insert(po);
        return po.getId();
    }

    @Override
    public boolean update(MedicalReport medicalReport) {
        MedicalReportPo po = convert2MedicalReportPo(medicalReport);
        return medicalReportMapper.updateById(po) > 0;
    }

    /**
     * 按报告id查询报告
     * @return
     */
    @Override
    public MedicalReport queryByReportId(Long reportId, String userPin){
        LambdaQueryWrapper<MedicalReportPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MedicalReportPo::getId, reportId)
                .eq(MedicalReportPo::getUserPin, userPin).eq(MedicalReportPo::getYn,YnStatusEnum.YES.getCode());
        MedicalReportPo medicalReport =  medicalReportMapper.selectOne(queryWrapper);
        return convert2MedicalReport(medicalReport);
    }

    /**
     * 按履约单id集合查询报告列表
     * @return
     */
    @Override
    public List<MedicalReport> selectByPromiseId(String userPin, Long promiseId){
        LambdaQueryWrapper<MedicalReportPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MedicalReportPo::getPromiseId, promiseId)
                .eq(MedicalReportPo::getUserPin, userPin).eq(MedicalReportPo::getYn,YnStatusEnum.YES.getCode());
        List<MedicalReportPo> medicalReportPoList =  medicalReportMapper.selectList(queryWrapper);
        return convert2MedicalReportList(medicalReportPoList);
    }

    @Override
    public MedicalReport getById(Long id) {
        MedicalReportPo po = medicalReportMapper.selectById(id);
        return convert2MedicalReport(po);
    }

    @Override
    public MedicalReport getByMedicalPromiseId(Long medicalPromiseId) {
        LambdaQueryWrapper<MedicalReportPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MedicalReportPo::getMedicalPromiseId, medicalPromiseId).eq(MedicalReportPo::getYn,YnStatusEnum.YES.getCode());
        MedicalReportPo medicalReport =  medicalReportMapper.selectOne(queryWrapper);
        return convert2MedicalReport(medicalReport);
    }

    @Override
    public List<MedicalReport> getByMedicalPromiseIdList(List<Long> medicalPromiseIdList) {
        LambdaQueryWrapper<MedicalReportPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(MedicalReportPo::getMedicalPromiseId, medicalPromiseIdList).eq(MedicalReportPo::getYn,YnStatusEnum.YES.getCode());
        List<MedicalReportPo> medicalReportList =  medicalReportMapper.selectList(queryWrapper);
        return convert2MedicalReportList(medicalReportList);
    }

    @Override
    public List<MedicalReport> queryByPromiseId(Long promiseId, String userPin, Long patientId) {
        LambdaQueryWrapper<MedicalReportPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MedicalReportPo::getPromiseId, promiseId).eq(MedicalReportPo::getUserPin, userPin).
                eq(MedicalReportPo::getParentId, patientId)
                .eq(MedicalReportPo::getYn,YnStatusEnum.YES.getCode())
        ;
        List<MedicalReportPo> poList = medicalReportMapper.selectList(queryWrapper);
        return convert2MedicalReportList(poList);
    }

    @Override
    public List<MedicalReport> queryByPromiseIdList(List<Long> promiseId, String userPin, Long patientId) {
        return null;
    }

    /**
     * 按不同入参查询报告
     *
     * @param medicalReportQueryBO
     * @return
     */
    @Override
    public List<MedicalReport> selectByCondition(MedicalReportQueryBO medicalReportQueryBO) {
        LambdaQueryWrapper<MedicalReportPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(StringUtils.isNotBlank(medicalReportQueryBO.getUserPin()),MedicalReportPo::getUserPin, medicalReportQueryBO.getUserPin())
                .eq(Objects.nonNull(medicalReportQueryBO.getPromiseId()),MedicalReportPo::getPromiseId, medicalReportQueryBO.getPromiseId())
                .eq(Objects.nonNull(medicalReportQueryBO.getId()),MedicalReportPo::getId, medicalReportQueryBO.getId())
                .in(CollectionUtils.isNotEmpty(medicalReportQueryBO.getIds()),MedicalReportPo::getId, medicalReportQueryBO.getIds())
                .in(CollectionUtils.isNotEmpty(medicalReportQueryBO.getMedicalPromiseIds()),MedicalReportPo::getMedicalPromiseId, medicalReportQueryBO.getMedicalPromiseIds())
                .eq(StringUtils.isNotBlank(medicalReportQueryBO.getReportCenterId()),MedicalReportPo::getReportCenterId, medicalReportQueryBO.getReportCenterId())
                .eq(MedicalReportPo::getYn,YnStatusEnum.YES.getCode())
                .ge(Objects.nonNull(medicalReportQueryBO.getOffset()),MedicalReportPo::getId,medicalReportQueryBO.getOffset())
                .lt(Objects.nonNull(medicalReportQueryBO.getOffsetEnd()),MedicalReportPo::getId,medicalReportQueryBO.getOffsetEnd())
                .orderByAsc(Boolean.TRUE.equals(medicalReportQueryBO.getOrderByAsc()),MedicalReportPo::getId)
        ;
        List<MedicalReportPo> poList = medicalReportMapper.selectList(queryWrapper);
        return convert2MedicalReportList(poList);
    }

    /**
     * 更新部分字段信息
     *
     * @param medicalReport
     * @return
     */
    @Override
    public boolean updateByCondition(MedicalReport medicalReport) {
        MedicalReportPo po = convert2MedicalReportPo(medicalReport);
        LambdaUpdateWrapper<MedicalReportPo> userQueryWrapper = Wrappers.<MedicalReportPo>lambdaUpdate()
                .set(StringUtils.isNotBlank(po.getSourceOss()), MedicalReportPo::getSourceOss, po.getSourceOss())
                .set(StringUtils.isNotBlank(po.getStructReportOss()), MedicalReportPo::getStructReportOss, po.getStructReportOss())
                .set(StringUtils.isNotBlank(po.getReportCenterId()), MedicalReportPo::getReportCenterId, po.getReportCenterId())
                .set(MedicalReportPo::getUpdateTime, new Date())
                .eq(MedicalReportPo::getMedicalPromiseId, po.getMedicalPromiseId());
        return medicalReportMapper.update(null, userQueryWrapper) > 0;
    }

    /**
     * 保存报告变更记录，仅保存变更前日志，最新报告在medical_report
     *
     * @param medicalReportChangeLog
     * @return
     */
    @Override
    public int insertMedicalReportChangeLog(MedicalReportChangeLog medicalReportChangeLog) {
        return medicalReportChangeLogMapper.insert(MedicalReportConverter.INS.boToPo(medicalReportChangeLog));
    }

    /**
     * 查询报告变更记录
     *
     * @param medicalReportChangeLog
     * @return
     */
    @Override
    public List<MedicalReportChangeLog> queryMedicalReportChangeLogList(MedicalReportChangeLog medicalReportChangeLog) {
        LambdaQueryWrapper<MedicalReportChangeLogPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MedicalReportChangeLogPo::getMedicalPromiseId, medicalReportChangeLog.getMedicalPromiseId())
                .eq(MedicalReportChangeLogPo::getYn,YnStatusEnum.YES.getCode());
        return MedicalReportConverter.INS.poToBoList(medicalReportChangeLogMapper.selectList(queryWrapper));
    }

    /**
     * 分野查询报告
     *
     * @param medicalReportPageBO
     * @return
     */
    @Override
    public PageDto<MedicalReport> pageMedicalReport(MedicalReportPageBO medicalReportPageBO) {

        IPage<MedicalReportPo> iPage = new Page<>(medicalReportPageBO.getPageNum(), medicalReportPageBO.getPageSize());
        LambdaQueryWrapper<MedicalReportPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(MedicalReportPo::getYn, YnStatusEnum.YES.getCode())
                .isNotNull(Boolean.TRUE.equals(medicalReportPageBO.getNeedReportJpg()),MedicalReportPo::getReportJpgOss)
                .ge(Objects.nonNull(medicalReportPageBO.getStartTime()),MedicalReportPo::getCreateTime,medicalReportPageBO.getStartTime())
                .le(Objects.nonNull(medicalReportPageBO.getEndTime()),MedicalReportPo::getCreateTime,medicalReportPageBO.getEndTime())
                .orderByAsc(MedicalReportPo::getId)
        ;

        IPage<MedicalReportPo> page = medicalReportMapper.selectPage(iPage, queryWrapper);

        if (Objects.nonNull(page)){
            PageDto<MedicalReport> pageDto = new PageDto<>();
            pageDto.setPageSize(page.getSize());
            pageDto.setPageNum(page.getCurrent());
            pageDto.setTotalPage(page.getPages());
            pageDto.setTotalCount(page.getTotal());
            if (CollectionUtils.isNotEmpty(page.getRecords())){
                List<MedicalReport> medicalReports = convert2MedicalReportList(page.getRecords());
                pageDto.setList(medicalReports);
            }
            return pageDto;
        }
        return null;

    }

    private List<MedicalReport> convert2MedicalReportList(List<MedicalReportPo> medicalReportPoList) {
        if(CollectionUtils.isEmpty(medicalReportPoList)){
            return Lists.newArrayList();
        }
        List<MedicalReport> resultList = Lists.newArrayList();
        for(MedicalReportPo po: medicalReportPoList){
            MedicalReport medicalReport = convert2MedicalReport(po);
            resultList.add(medicalReport);
        }
        return resultList;
    }

    private MedicalReportPo convert2MedicalReportPo(MedicalReport medicalReport) {
        if (medicalReport == null) {
            return null;
        }
        MedicalReportPo medicalReportPo = new MedicalReportPo();
        medicalReportPo.setId(medicalReport.getId());
        medicalReportPo.setPromiseId(medicalReport.getPromiseId());
        medicalReportPo.setMedicalPromiseId(medicalReport.getMedicalPromiseId());
        medicalReportPo.setUserPin(medicalReport.getUserPin());
        medicalReportPo.setPatientId(medicalReport.getPatientId());
        medicalReportPo.setPatientName(medicalReport.getPatientName());
        medicalReportPo.setRelativesType(medicalReport.getRelativesType());
        medicalReportPo.setParentId(medicalReport.getParentId());
        medicalReportPo.setReportType(medicalReport.getReportType());
        medicalReportPo.setExaminationTime(medicalReport.getExaminationTime());
        medicalReportPo.setFileMd5(medicalReport.getFileMd5());
        medicalReportPo.setReportOss(medicalReport.getReportOss());
        medicalReportPo.setSourceOss(medicalReport.getSourceOss());
        medicalReportPo.setStructReportOss(medicalReport.getStructReportOss());
        medicalReportPo.setReportSource(medicalReport.getReportSource());
        medicalReportPo.setChannelNo(medicalReport.getChannelNo());
        medicalReportPo.setChannelReportNo(medicalReport.getChannelReportNo());
        medicalReportPo.setMedicalType(medicalReport.getMedicalType());
        medicalReportPo.setReportStatus(medicalReport.getReportStatus());
        medicalReportPo.setYn(medicalReport.getYn());
        medicalReportPo.setCreateTime(medicalReport.getCreateTime());
        medicalReportPo.setUpdateTime(medicalReport.getUpdateTime());
        medicalReportPo.setReportTime(medicalReport.getReportTime());
        medicalReportPo.setManufacturerNumber(medicalReport.getManufacturerNumber());
        medicalReportPo.setSnCode(medicalReport.getSnCode());
        medicalReportPo.setReportCenterId(medicalReport.getReportCenterId());
        medicalReportPo.setReportJpgOss(medicalReport.getReportJpgOss());
        return medicalReportPo;
    }
    private MedicalReport convert2MedicalReport(MedicalReportPo po) {
        if (po == null) {
            return null;
        }
        MedicalReport medicalReport = new MedicalReport();
        medicalReport.setId(po.getId());
        medicalReport.setPromiseId(po.getPromiseId());
        medicalReport.setMedicalPromiseId(po.getMedicalPromiseId());
        medicalReport.setUserPin(po.getUserPin());
        medicalReport.setPatientId(po.getPatientId());
        medicalReport.setPatientName(po.getPatientName());
        medicalReport.setRelativesType(po.getRelativesType());
        medicalReport.setReportType(po.getReportType());
        medicalReport.setParentId(po.getParentId());
        medicalReport.setExaminationTime(po.getExaminationTime());
        medicalReport.setFileMd5(po.getFileMd5());
        medicalReport.setReportOss(po.getReportOss());
        medicalReport.setSourceOss(po.getSourceOss());
        medicalReport.setStructReportOss(po.getStructReportOss());
        medicalReport.setReportSource(po.getReportSource());
        medicalReport.setChannelNo(po.getChannelNo());
        medicalReport.setChannelReportNo(po.getChannelReportNo());
        medicalReport.setMedicalType(po.getMedicalType());
        medicalReport.setReportTime(po.getReportTime());
        medicalReport.setReportStatus(po.getReportStatus());
        medicalReport.setYn(po.getYn());
        medicalReport.setCreateTime(po.getCreateTime());
        medicalReport.setUpdateTime(po.getUpdateTime());
        medicalReport.setReportCenterId(po.getReportCenterId());
        medicalReport.setReportJpgOss(po.getReportJpgOss());
        return medicalReport;
    }

}
