package com.jdh.o2oservice.infrastructure.rpc;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;


import com.jd.medicine.base.common.util.JsonUtil;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.util.O2oHttpClient;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DuccRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName DuccRpcImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/16 21:55
 **/
@Service
@Slf4j
public class DuccRpcImpl implements DuccRpc {

    @Value("${laf.config.manager.baseUrl}")
    private String baseUrl;

    /**
     *
     */
    @Value("${laf.config.manager.profile}")
    private String profile;

    /**
     *
     */
    @Value("${laf.config.manager.application}")
    private String application;

    /**
     *
     */
    @Value("${laf.config.manager.token}")
    private String token;

    /**
     *
     * @param configKey
     * @param configContent
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.DuccRpcImpl.updateDuccConfig")
    public void updateDuccConfig(String configKey, String configContent) {
        String methodUrl = "/v1/namespace/{0}/config/{1}/profile/{2}/item/{3}";
        //uri格式详解参见：https://git.jd.com/laf/laf-config/wikis/客户端使用指南->UCC配置服务
        String uri =  MessageFormat.format(methodUrl, "jdh_o2o_service", "common", profile, configKey);
        Map<String,String> header = new HashMap<>();
        header.put("application",application);
        header.put("token",token);
        String json = O2oHttpClient.putJson(baseUrl + uri, header, buildDuccReqBody(configKey,configContent).toJSONString());
        log.info("DuccRpcImpl -> updateDuccConfig ducc原配置, key={}, value={}", configKey, json);
    }

    /**
     *
     * @param configKey
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.DuccRpcImpl.getDuccConfig")
    public String getDuccConfig(String configKey) {
        String methodUrl = "/v1/namespace/{0}/config/{1}/profile/{2}/item/{3}";
        //uri格式详解参见：https://git.jd.com/laf/laf-config/wikis/客户端使用指南->UCC配置服务
        String uri =  MessageFormat.format(methodUrl, "jdh_o2o_service", "common", profile, configKey);
        Map<String,String> header = new HashMap<>();
        header.put("application",application);
        header.put("token",token);
        String json = O2oHttpClient.get(baseUrl + uri, header);
        log.info("DuccRpcImpl -> updateDuccConfig ducc原配置, key={}, value={}", configKey, json);
        if (StringUtils.isNotEmpty(json)) {
            JSONObject jsonObject = JsonUtil.parseObject(json);
            return Objects.isNull(jsonObject) || Objects.isNull(jsonObject.getJSONObject("data")) ? "" : jsonObject.getJSONObject("data").getString("value");
        }
        return json;
    }

    /**
     * releaseDuccConfig
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.infrastructure.rpc.DuccRpcImpl.releaseDuccConfig")
    public boolean releaseDuccConfig(){
        String methodUrl = "/v1/namespace/{0}/config/{1}/profile/{2}/release";
        String uri = MessageFormat.format(methodUrl, "jdh_o2o_service","common", profile);
        HttpResponse execute = HttpRequest.put(baseUrl + uri).header("application", application).header("token", token).body("{}").execute();
        String body = execute.body();
        log.info("DuccRpcImpl -> releaseDuccConfig 新增配置,发布 body:{}",body);
        JSONObject bodyJson = JSON.parseObject(body);
        int code = bodyJson.getInteger("code");
        if(Objects.equals(200, code)){
            return true;
        }
        return false;
    }

    /**
     *
     * @param configKey
     * @param configContent
     * @return
     */
    private JSONObject buildDuccReqBody(String configKey, String configContent){
        JSONObject reqBody = new JSONObject();
        reqBody.put("key",configKey);
        reqBody.put("value",configContent);
        return reqBody;
    }
}