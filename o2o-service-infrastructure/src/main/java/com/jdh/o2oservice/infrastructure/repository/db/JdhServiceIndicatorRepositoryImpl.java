package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.core.domain.product.context.ServiceIndicatorExactQueryContext;
import com.jdh.o2oservice.core.domain.product.context.ServiceIndicatorQueryContext;
import com.jdh.o2oservice.core.domain.product.model.Indicator;
import com.jdh.o2oservice.core.domain.product.model.IndicatorIdentifier;
import com.jdh.o2oservice.core.domain.product.model.JdhStationIndicatorRel;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceIndicatorRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhIndicatorPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhIndicatorPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhStationIndicatorRelMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdOrderPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhIndicatorPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhServiceItemPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhStationIndicatorRelPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName:JdhServiceIndicatorRepositoryImpl
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/20 15:03
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class JdhServiceIndicatorRepositoryImpl implements JdhServiceIndicatorRepository {

    @Resource
    private JdhIndicatorPoMapper jdhIndicatorPoMapper;

    /**
     * 实验室自定义指标
     */
    @Resource
    private JdhStationIndicatorRelMapper jdhStationIndicatorRelMapper;

    /**
     * ducc配置
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param indicatorIdentifier
     */
    @Override
    public Indicator find(IndicatorIdentifier indicatorIdentifier) {
        LambdaQueryWrapper<JdhIndicatorPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhIndicatorPo::getIndicatorId, indicatorIdentifier.getIndicatorId())
                .eq(JdhIndicatorPo::getYn, YnStatusEnum.YES.getCode());
        return JdhIndicatorPoConverter.ins.convertToIndicator(jdhIndicatorPoMapper.selectOne(queryWrapper));
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *Ï
     * @param aggregate
     */
    @Override
    public int remove(Indicator aggregate) {
        return 0;
    }

    /**
     * 保存一个Aggregate
     * 保存后自动重置追踪条件
     *
     * @param indicator
     */
    @Override
    public int save(Indicator indicator) {
        JdhIndicatorPo jdhIndicatorPo = JdhIndicatorPoConverter.ins.convertToJdhIndicatorPo(indicator);
        int count = 0;
        if (StringUtils.isNotBlank(indicator.getIndicatorName())) {
            LambdaQueryWrapper<JdhIndicatorPo> checkQueryWrapper = new LambdaQueryWrapper<>();
            checkQueryWrapper.eq(JdhIndicatorPo::getIndicatorName, indicator.getIndicatorName());
            checkQueryWrapper.eq(JdhIndicatorPo::getFirstIndicatorCategory, indicator.getFirstIndicatorCategory());
            List<JdhIndicatorPo> exist = jdhIndicatorPoMapper.selectList(checkQueryWrapper);
            if (CollectionUtils.isNotEmpty(exist) && !Objects.equals(exist.get(0).getId(), indicator.getId())) {
                throw new ArgumentsException(new DynamicErrorCode("100001", "该指标名称已存在!"));
            }
        }
        if (Objects.isNull(jdhIndicatorPo.getId())) {
            // 新数据保存直接替换
            jdhIndicatorPo.setIndicatorName(replaceNameCnCharToEnChar(jdhIndicatorPo.getIndicatorName()));
            count = jdhIndicatorPoMapper.insert(jdhIndicatorPo);
        } else {
            // 替换中文符号为英文符号,不在查询之前替换,兼容已存在数据
            indicator.setIndicatorName(replaceNameCnCharToEnChar(indicator.getIndicatorName()));
            LambdaUpdateWrapper<JdhIndicatorPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(StringUtils.isNotBlank(indicator.getIndicatorName()), JdhIndicatorPo::getIndicatorName, indicator.getIndicatorName())
                    .set(StringUtils.isNotBlank(indicator.getIndicatorMean()), JdhIndicatorPo::getIndicatorMean, indicator.getIndicatorMean())
                    .set(StringUtils.isNotBlank(indicator.getIndicatorSuitable()), JdhIndicatorPo::getIndicatorSuitable, indicator.getIndicatorSuitable())
                    .set(Objects.nonNull(indicator.getFirstIndicatorCategory()), JdhIndicatorPo::getFirstIndicatorCategory, indicator.getFirstIndicatorCategory())
                    .set(Objects.nonNull(indicator.getSecondIndicatorCategory()), JdhIndicatorPo::getSecondIndicatorCategory, indicator.getSecondIndicatorCategory())
                    .set(Objects.nonNull(indicator.getThirdIndicatorCategory()), JdhIndicatorPo::getThirdIndicatorCategory, indicator.getThirdIndicatorCategory())
                    .set(StringUtils.isNotBlank(indicator.getFirstIndicatorCategoryName()), JdhIndicatorPo::getFirstIndicatorCategoryName, indicator.getFirstIndicatorCategoryName())
                    .set(StringUtils.isNotBlank(indicator.getSecondIndicatorCategoryName()), JdhIndicatorPo::getSecondIndicatorCategoryName, indicator.getSecondIndicatorCategoryName())
                    .set(StringUtils.isNotBlank(indicator.getThirdIndicatorCategoryName()), JdhIndicatorPo::getThirdIndicatorCategoryName, indicator.getThirdIndicatorCategoryName())
                    .set(JdhIndicatorPo::getVersion, indicator.getVersion() + 1)
                    .set(JdhIndicatorPo::getJdhWikiId, indicator.getJdhWikiId())
                    .set(Objects.nonNull(indicator.getTestResultType()), JdhIndicatorPo::getTestResultType, indicator.getTestResultType())
                    .set(JdhIndicatorPo::getTags, indicator.getTags())
                    .set(JdhIndicatorPo::getUpdateUser,indicator.getUpdateUser())
                    .eq(JdhIndicatorPo::getIndicatorId, indicator.getIndicatorId())
                    .eq(JdhIndicatorPo::getVersion, indicator.getVersion());

            count = jdhIndicatorPoMapper.update(null, updateWrapper);
        }
        if (CollUtil.isNotEmpty(indicator.getJdhStationIndicatorRelList())) {
            LambdaQueryWrapper<JdhStationIndicatorRelPo> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(JdhStationIndicatorRelPo::getIndicatorId, jdhIndicatorPo.getIndicatorId());
            jdhStationIndicatorRelMapper.delete(deleteWrapper);
            return jdhStationIndicatorRelMapper.batchInsert(JdhIndicatorPoConverter.ins.convertToJdhStationIndicatorRelPoList(indicator.getJdhStationIndicatorRelList()));
        }
        return count;
    }

    /**
     * 分页查询指标数据
     *
     * @param serviceIndicatorQueryContext
     * @return
     */
    @Override
    public Page<Indicator> queryIndicatorPageInfo(ServiceIndicatorQueryContext serviceIndicatorQueryContext) {
        LambdaQueryWrapper<JdhIndicatorPo> queryWrapper = buildIndicatorQueryWrapper(serviceIndicatorQueryContext);
        IPage<JdhIndicatorPo> page = new Page<>(serviceIndicatorQueryContext.getPageNum(), serviceIndicatorQueryContext.getPageSize());
        IPage<JdhIndicatorPo> jdhIndicatorPoIPage = jdhIndicatorPoMapper.selectPage(page, queryWrapper);
        if (jdhIndicatorPoIPage == null || CollectionUtil.isEmpty(jdhIndicatorPoIPage.getRecords())) {
            return null;
        }
        List<JdhIndicatorPo> records = jdhIndicatorPoIPage.getRecords();
        List<Indicator> indicators = JdhIndicatorPoConverter.ins.convertToIndicatorList(records);
        return JdhBasicPoConverter.initPage(jdhIndicatorPoIPage, indicators);
    }

    /**
     * 查询指标集合
     *
     * @param indicatorQueryContext
     * @return
     */
    @Override
    public List<Indicator> queryIndicatorList(ServiceIndicatorQueryContext indicatorQueryContext) {
        LambdaQueryWrapper<JdhIndicatorPo> queryWrapper = buildIndicatorQueryWrapper(indicatorQueryContext);
        List<JdhIndicatorPo> jdhIndicatorPos = jdhIndicatorPoMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(jdhIndicatorPos)) {
            return null;
        }
        return JdhIndicatorPoConverter.ins.convertToIndicatorList(jdhIndicatorPos);
    }

    /**
     * 精确查询服务指标
     *
     * @param exactQueryContext
     * @return
     */
    @Override
    public List<Indicator> queryIndicatorExactList(ServiceIndicatorExactQueryContext exactQueryContext) {
        LambdaQueryWrapper<JdhIndicatorPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtils.isNotEmpty(exactQueryContext.getIndicatorName()), JdhIndicatorPo::getIndicatorName, exactQueryContext.getIndicatorName())
                .in(CollectionUtils.isNotEmpty(exactQueryContext.getFirstIndicatorCategory()), JdhIndicatorPo::getFirstIndicatorCategory, exactQueryContext.getFirstIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(exactQueryContext.getSecondIndicatorCategory()), JdhIndicatorPo::getSecondIndicatorCategory, exactQueryContext.getSecondIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(exactQueryContext.getIndicatorIds()), JdhIndicatorPo::getIndicatorId, exactQueryContext.getIndicatorIds())
                .like(StringUtils.isNotBlank(exactQueryContext.getIndicatorSuitable()), JdhIndicatorPo::getIndicatorSuitable, exactQueryContext.getIndicatorSuitable())
//                .eq(Objects.equals(CommonConstant.TWO,exactQueryContext.getIndicatorType()),)
                .eq(JdhIndicatorPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhIndicatorPo::getId);
        return JdhIndicatorPoConverter.ins.convertToIndicatorList(jdhIndicatorPoMapper.selectList(queryWrapper));
    }

    /**
     * 查询指标下个性化配置实验室列表
     *
     * @param jdhStationIndicatorRel
     * @return
     */
    @Override
    public List<JdhStationIndicatorRel> queryStationIndicatorList(JdhStationIndicatorRel jdhStationIndicatorRel) {
        LambdaQueryWrapper<JdhStationIndicatorRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhStationIndicatorRelPo::getIndicatorId, jdhStationIndicatorRel.getIndicatorId())
                .eq(JdhStationIndicatorRelPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhStationIndicatorRelPo> stationIndicatorRelPoList = jdhStationIndicatorRelMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(stationIndicatorRelPoList)) {
            return Collections.emptyList();
        }
        return JdhIndicatorPoConverter.ins.convertToJdhStationIndicatorRelList(stationIndicatorRelPoList);
    }

    /**
     * 查询指标下个性化实验室配置
     *
     * @param jdhStationIndicatorRel
     * @return
     */
    @Override
    public JdhStationIndicatorRel queryStationIndicator(JdhStationIndicatorRel jdhStationIndicatorRel) {
        LambdaQueryWrapper<JdhStationIndicatorRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhStationIndicatorRelPo::getIndicatorId, jdhStationIndicatorRel.getIndicatorId())
                .eq(JdhStationIndicatorRelPo::getStationId, jdhStationIndicatorRel.getStationId())
                .eq(JdhStationIndicatorRelPo::getYn, YnStatusEnum.YES.getCode());
        JdhStationIndicatorRelPo stationIndicatorRel = jdhStationIndicatorRelMapper.selectOne(queryWrapper);
        return JdhIndicatorPoConverter.ins.convertToJdhStationIndicatorRel(stationIndicatorRel);
    }

    /**
     * 构建包装类
     *
     * @param serviceIndicatorQueryContext
     * @return
     */
    private LambdaQueryWrapper<JdhIndicatorPo> buildIndicatorQueryWrapper(ServiceIndicatorQueryContext serviceIndicatorQueryContext) {
        LambdaQueryWrapper<JdhIndicatorPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StringUtils.isNotBlank(serviceIndicatorQueryContext.getIndicatorName()), JdhIndicatorPo::getIndicatorName, serviceIndicatorQueryContext.getIndicatorName())
                .in(CollectionUtils.isNotEmpty(serviceIndicatorQueryContext.getFirstIndicatorCategory()), JdhIndicatorPo::getFirstIndicatorCategory, serviceIndicatorQueryContext.getFirstIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(serviceIndicatorQueryContext.getSecondIndicatorCategory()), JdhIndicatorPo::getSecondIndicatorCategory, serviceIndicatorQueryContext.getSecondIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(serviceIndicatorQueryContext.getIndicatorIds()), JdhIndicatorPo::getIndicatorId, serviceIndicatorQueryContext.getIndicatorIds())
                .like(StringUtils.isNotBlank(serviceIndicatorQueryContext.getIndicatorSuitable()), JdhIndicatorPo::getIndicatorSuitable, serviceIndicatorQueryContext.getIndicatorSuitable())
                .eq(StringUtils.isNotBlank(serviceIndicatorQueryContext.getJdhWikiId()), JdhIndicatorPo::getJdhWikiId, serviceIndicatorQueryContext.getJdhWikiId())
                .eq(Objects.nonNull(serviceIndicatorQueryContext.getTestResultType()), JdhIndicatorPo::getTestResultType, serviceIndicatorQueryContext.getTestResultType())
                .eq(Objects.nonNull(serviceIndicatorQueryContext.getIndicatorId()), JdhIndicatorPo::getIndicatorId, serviceIndicatorQueryContext.getIndicatorId())
                .eq(Objects.nonNull(serviceIndicatorQueryContext.getFirstIndicatorCategoryId()), JdhIndicatorPo::getFirstIndicatorCategory, serviceIndicatorQueryContext.getFirstIndicatorCategoryId())
                .eq(Objects.nonNull(serviceIndicatorQueryContext.getSecondIndicatorCategoryId()), JdhIndicatorPo::getSecondIndicatorCategory, serviceIndicatorQueryContext.getSecondIndicatorCategoryId())
                .eq(Objects.nonNull(serviceIndicatorQueryContext.getThirdIndicatorCategoryId()), JdhIndicatorPo::getThirdIndicatorCategory, serviceIndicatorQueryContext.getThirdIndicatorCategoryId())
                .in(CollectionUtils.isNotEmpty(serviceIndicatorQueryContext.getIndicatorNameList()), JdhIndicatorPo::getIndicatorName, serviceIndicatorQueryContext.getIndicatorNameList())
                .eq(JdhIndicatorPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhIndicatorPo::getId);
        return queryWrapper;
    }

    /**
     * 中文字符转英文
     *
     * @return str
     */
    private String replaceNameCnCharToEnChar(String originStr){
        try {
            if (StringUtils.isBlank(originStr)) {
                return originStr;
            }
            Map<String,String> cnCharToEnChar = duccConfig.getCnCharToEnChar();
            for (Map.Entry<String, String> entry : cnCharToEnChar.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                originStr = originStr.replace(key, value);
            }
            return originStr;
        } catch (Exception e) {
            log.error("replaceNameCnCharToEnChar exception", e);
            return originStr;
        }
    }
}
