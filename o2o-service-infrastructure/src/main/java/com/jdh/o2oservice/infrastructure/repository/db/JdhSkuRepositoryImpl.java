package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.constatnt.CacheConstant;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.common.enums.SkuExtendAttributeEnum;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.model.*;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhSkuItemRelPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhSkuPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhSkuRelPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSkuExtendPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSkuItemRelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSkuPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhSkuRelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSkuExtendPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSkuItemRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSkuPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhSkuRelPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品仓库实现类
 *
 * <AUTHOR>
 * @date 2024/04/21
 */
@Repository
@Slf4j
public class JdhSkuRepositoryImpl implements JdhSkuRepository {

    /**
     * jdhSkuPoMapper
     */
    @Resource
    JdhSkuPoMapper jdhSkuPoMapper;

    /**
     * jdhSkuPoMapper
     */
    @Resource
    JdhSkuItemRelPoMapper jdhSkuItemRelPoMapper;

    /**
     * jdhSkuPoMapper
     */
    @Resource
    JdhSkuRelPoMapper jdhSkuRelPoMapper;

    /**
     * jdhSkuPoMapper
     */
    @Resource
    JdhSkuExtendPoMapper jdhSkuExtendPoMapper;

    /**
     * 删除
     *
     * @param
     */
    @Override
    public int remove(JdhSku jdhSku) {
        return 0;
    }

    /**
     * 订单详情
     *
     * @param jdhSku jdhSku
     * @return count
     */
    @Override
    @CacheEvict(cacheNames = CacheConstant.SKU_CACHE, key = "#jdhSku.getSkuId()",condition = "#jdhSku.getSkuId() != null")
    public int save(JdhSku jdhSku) {
        if (jdhSku.getSkuId() == null) {
            throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuId"));
        }
        JdhSkuPo po = JdhSkuPo.builder().skuId(jdhSku.getSkuId()).serviceType(jdhSku.getServiceType())
                .serviceDuration(jdhSku.getServiceDuration()).serviceResourceType(jdhSku.getServiceResourceType())
                .advanceAppointTime(jdhSku.getAdvanceAppointTime()).maxScheduleDays(jdhSku.getMaxScheduleDays())
                .dayTimeFrame(jdhSku.getDayTimeFrame()).stationAssignType(jdhSku.getStationAssignType())
                .requiredInsure(jdhSku.getRequiredInsure()).channelId(jdhSku.getChannelId()).tags(jdhSku.getTags())
                .serviceNotice(jdhSku.getServiceNotice()).serviceProcessImg(jdhSku.getServiceProcessImg())
                .requiredRealName(jdhSku.getRequiredRealName()).tutorialUrl(jdhSku.getTutorialUrl())
                .informedConsentUrl(jdhSku.getInformedConsentUrl()).appointTemplateId(jdhSku.getAppointTemplateId())
                .ageRange(jdhSku.getAgeRange()).genderLimit(jdhSku.getGenderLimit())
                .customerConfirmType(jdhSku.getCustomerConfirmType()).serviceRecordType(jdhSku.getServiceRecordType())
                .resourceSettlementPrice(jdhSku.getResourceSettlementPrice()).remark(jdhSku.getRemark()).skuType(jdhSku.getSkuType())
                .tutorialCarousel(jdhSku.getTutorialCarousel())
                .tutorialVideo(jdhSku.getTutorialVideo())
                .tutorialVideoThumbnail(jdhSku.getTutorialVideoThumbnail())
                .tutorialMethod(jdhSku.getTutorialMethod())
                .tutorialMethodJumpUrl(jdhSku.getTutorialMethodJumpUrl())
                .technicalLevel(jdhSku.getTechnicalLevel()).buyValidPeriod(jdhSku.getBuyValidPeriod())
                .activityFloor(JSON.toJSONString(jdhSku.getActivityFloors()))
                .build();
        Date now = new Date();
        po.setCreateUser(jdhSku.getCreateUser());
        po.setCreateTime(now);
        po.setUpdateUser(jdhSku.getUpdateUser());
        po.setUpdateTime(now);
        po.setSaleStatus(1);
        // 导入时存在草稿状态
        if (Boolean.TRUE.equals(jdhSku.getDraft())) {
            po.setYn(YnStatusEnum.NO.getCode());
            po.setRemark("草稿状态");
        } else {
            po.setYn(YnStatusEnum.YES.getCode());
        }
        int count = jdhSkuPoMapper.insert(po);
        batchInsertSkuItemRel(jdhSku, now);
        saveExtend(jdhSku);
        return count;
    }

    /**
     * 更新
     *
     * @param jdhSku jdhSku
     * @return count
     */
    @Override
    @CacheEvict(cacheNames = CacheConstant.SKU_CACHE, key = "#jdhSku.getSkuId()",condition = "#jdhSku.getSkuId() != null")
    public int update(JdhSku jdhSku) {
        if (jdhSku.getSkuId() == null) {
            throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuId"));
        }
        LambdaUpdateWrapper<JdhSkuPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhSkuPo po = JdhSkuPo.builder().build();
        updateWrapper.set(jdhSku.getSaleStatus() != null, JdhSkuPo::getSaleStatus, jdhSku.getSaleStatus())
                .set(JdhSkuPo::getServiceType, jdhSku.getServiceType())
                .set(JdhSkuPo::getServiceDuration, jdhSku.getServiceDuration())
                .set(JdhSkuPo::getServiceResourceType, jdhSku.getServiceResourceType())
                // 可以设置为空
                .set(JdhSkuPo::getAdvanceAppointTime, jdhSku.getAdvanceAppointTime())
                .set(JdhSkuPo::getMaxScheduleDays, jdhSku.getMaxScheduleDays())
                .set(JdhSkuPo::getDayTimeFrame, jdhSku.getDayTimeFrame())
                .set(JdhSkuPo::getStationAssignType, jdhSku.getStationAssignType())
                .set(JdhSkuPo::getRequiredInsure, jdhSku.getRequiredInsure())
                .set(jdhSku.getChannelId() != null, JdhSkuPo::getChannelId, jdhSku.getChannelId())
                .set(JdhSkuPo::getTags, jdhSku.getTags())
                .set(JdhSkuPo::getServiceNotice, jdhSku.getServiceNotice())
                .set(JdhSkuPo::getServiceProcessImg, jdhSku.getServiceProcessImg())
                .set(JdhSkuPo::getRequiredRealName, jdhSku.getRequiredRealName())
                // 可以设置为空
                .set(JdhSkuPo::getTutorialUrl, jdhSku.getTutorialUrl())
                .set(JdhSkuPo::getInformedConsentUrl, jdhSku.getInformedConsentUrl())
                .set(JdhSkuPo::getAppointTemplateId, jdhSku.getAppointTemplateId())
                // 可以设置为空
                .set(JdhSkuPo::getAgeRange, jdhSku.getAgeRange())
                .set(JdhSkuPo::getGenderLimit, jdhSku.getGenderLimit())
                // 可以设置为空
                .set(JdhSkuPo::getCustomerConfirmType, jdhSku.getCustomerConfirmType())
                .set(JdhSkuPo::getServiceRecordType, jdhSku.getServiceRecordType())
                .set(JdhSkuPo::getResourceSettlementPrice, jdhSku.getResourceSettlementPrice())
                .set(JdhSkuPo::getSkuType, jdhSku.getSkuType())
                .set(JdhSkuPo::getTechnicalLevel, jdhSku.getTechnicalLevel())
                .set(JdhSkuPo::getBuyValidPeriod, jdhSku.getBuyValidPeriod())
                //轮播图
                .set(JdhSkuPo::getTutorialCarousel, jdhSku.getTutorialCarousel())
                //采样教程视频
                .set(JdhSkuPo::getTutorialVideo, jdhSku.getTutorialVideo())
                //采样教程视频 封面图
                .set(JdhSkuPo::getTutorialVideoThumbnail, jdhSku.getTutorialVideoThumbnail())
                //采样方法说明
                .set(JdhSkuPo::getTutorialMethod,jdhSku.getTutorialMethod())
                .set(JdhSkuPo::getTutorialMethodJumpUrl,jdhSku.getTutorialMethodJumpUrl())
                .set(JdhSkuPo::getActivityFloor, JSON.toJSONString(jdhSku.getActivityFloors()))
                .setSql("`version` = version+1").eq(JdhSkuPo::getSkuId, jdhSku.getSkuId()).eq(JdhSkuPo::getVersion, jdhSku.getVersion());
        Date now = new Date();
        po.setUpdateUser(jdhSku.getUpdateUser());
        po.setUpdateTime(now);
        int count = jdhSkuPoMapper.update(po, updateWrapper);
        batchInsertSkuItemRel(jdhSku, now);
        saveExtend(jdhSku);
        return count;
    }

    /**
     * 更新
     *
     * @param jdhSku jdhSku
     * @return count
     */
    @Override
    @CacheEvict(cacheNames = CacheConstant.SKU_CACHE, key = "#jdhSku.getSkuId()",condition = "#jdhSku.getSkuId() != null")
    public int updateSaleStatus(JdhSku jdhSku) {
        if (jdhSku.getSkuId() == null) {
            throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("skuId"));
        }
        LambdaUpdateWrapper<JdhSkuPo> updateWrapper = Wrappers.lambdaUpdate();
        JdhSkuPo po = JdhSkuPo.builder().build();
        updateWrapper.set(JdhSkuPo::getSaleStatus, jdhSku.getSaleStatus())
                .setSql("`version` = version+1").eq(JdhSkuPo::getSkuId, jdhSku.getSkuId()).eq(JdhSkuPo::getVersion, jdhSku.getVersion());
        Date now = new Date();
        po.setUpdateUser(jdhSku.getUpdateUser());
        po.setUpdateTime(now);
        return jdhSkuPoMapper.update(po, updateWrapper);
    }

    /**
     * 通过Identify 查询
     *
     * @param identifier
     */
    @Override
    @Cacheable(cacheNames = CacheConstant.SKU_CACHE,key = "#identifier.serialize()")
    public JdhSku find(JdhSkuIdentifier identifier) {
        Long skuId = identifier.getSkuId();
        LambdaQueryWrapper<JdhSkuPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhSkuPo::getSkuId, skuId).eq(JdhSkuPo::getYn, YnStatusEnum.YES.getCode());
        JdhSkuPo jdhSkuPo = jdhSkuPoMapper.selectOne(queryWrapper);
        return JdhSkuPoConverter.INSTANCE.jdhSkuPoToJdhSku(jdhSkuPo);
    }

    /**
     * 查询列表
     *
     * @param jdhSkus jdhSkus
     */
    @Override
    public List<JdhSku> queryMultiSku(List<JdhSku> jdhSkus) {
        LambdaQueryWrapper<JdhSkuPo> queryWrapper = Wrappers.lambdaQuery();
        Set<Long> skuIds = jdhSkus.stream().map(JdhSku::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        queryWrapper.in(JdhSkuPo::getSkuId, skuIds).eq(JdhSkuPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSkuPo::getCreateTime);
        List<JdhSkuPo> list = jdhSkuPoMapper.selectList(queryWrapper);

        List<JdhSku> jdhSkusList = JdhSkuPoConverter.INSTANCE.jdhSkuPoToJdhSku(list);
        this.enhanse(jdhSkusList);
        return jdhSkusList;
    }

    /**
     * 字段增强
     * @param jdhSkus
     */
    private void enhanse(List<JdhSku> jdhSkus){
        LambdaQueryWrapper<JdhSkuExtendPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhSkuExtendPo::getSkuId,jdhSkus.stream().map(JdhSku::getSkuId).collect(Collectors.toList()));
        List<JdhSkuExtendPo> jdhSkuExtendPos =  jdhSkuExtendPoMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(jdhSkuExtendPos)){
            return;
        }
        jdhSkus.forEach(sku->{
            JdhSkuExtendPo jdhSkuExtendPo = jdhSkuExtendPos.stream().filter(t->t.getSkuId().equals(sku.getSkuId())&&SkuExtendAttributeEnum.HIGH_QUALITY_STORE_ID.getAttribute().equals(t.getAttribute())).findFirst().orElse(null);
            if(jdhSkuExtendPo==null){
                return;
            }
            sku.setHighQualityStoreId(JSON.parseArray(jdhSkuExtendPo.getValue(),String.class));
        });
    }

    /**
     * 查询列表
     *
     * @param request request
     */
    @Override
    public List<JdhSku> queryMultiSku(JdhSkuListRequest request) {
        LambdaQueryWrapper<JdhSkuPo> queryWrapper = Wrappers.lambdaQuery();
        if(CollectionUtils.isNotEmpty(request.getSkuIdList())){
            queryWrapper.in(JdhSkuPo::getSkuId, request.getSkuIdList());
        }
        if(Objects.nonNull(request.getSkuType())){
            queryWrapper.eq(JdhSkuPo::getSkuType, request.getSkuType());
        }
        queryWrapper.eq(JdhSkuPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhSkuPo::getCreateTime);
        List<JdhSkuPo> list = jdhSkuPoMapper.selectList(queryWrapper);
        return JdhSkuPoConverter.INSTANCE.jdhSkuPoToJdhSku(list);
    }

    /**
     * 查询单个sku项目列表
     *
     * @param rel rel
     */
    @Override
    public List<JdhSkuItemRel> queryJdhSkuItemRelList(JdhSkuItemRel rel) {
        if (rel.getSkuId() == null && StringUtils.isBlank(rel.getSkuItemId())) {
            throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR);
        }
        LambdaQueryWrapper<JdhSkuItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(rel.getSkuId() != null, JdhSkuItemRelPo::getSkuId, rel.getSkuId())
                .eq(StringUtils.isNotBlank(rel.getSkuItemId()), JdhSkuItemRelPo::getSkuItemId, rel.getSkuItemId())
                .eq(rel.getSkuItemType() != null, JdhSkuItemRelPo::getSkuItemType, rel.getSkuItemType())
                .eq(JdhSkuItemRelPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSkuItemRelPo::getCreateTime);
        return JdhSkuItemRelPoConverter.INSTANCE.poToJdhSkuItemRel(jdhSkuItemRelPoMapper.selectList(queryWrapper));
    }

    /**
     * 查询多个sku项目列表
     *
     * @param jdhSkuItemRels jdhSkuItemRels
     */
    @Override
    public List<JdhSkuItemRel> queryJdhSkuItemRelList(List<JdhSkuItemRel> jdhSkuItemRels, Long channelId, Integer skuItemType, Boolean limit) {
        Set<Long> skuIds = jdhSkuItemRels.stream().map(JdhSkuItemRel::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<String> skuItems = jdhSkuItemRels.stream().map(JdhSkuItemRel::getSkuItemId).filter(Objects::nonNull).collect(Collectors.toSet());
        log.info("ProviderStoreRepositoryImpl queryList stationIds={}, serviceItemIds={}", JSON.toJSONString(skuIds), JSON.toJSONString(skuItems));
        if (CollUtil.isEmpty(skuIds) && CollUtil.isEmpty(skuItems)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<JdhSkuItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollUtil.isNotEmpty(skuIds), JdhSkuItemRelPo::getSkuId, skuIds)
                .in(CollUtil.isNotEmpty(skuItems), JdhSkuItemRelPo::getSkuItemId, skuItems)
                .eq(channelId != null, JdhSkuItemRelPo::getChannelId, channelId)
                .eq(JdhSkuItemRelPo::getSkuItemType, skuItemType)
                .eq(JdhSkuItemRelPo::getYn, YnStatusEnum.YES.getCode()).last(Boolean.TRUE.equals(limit), "limit 500").orderByDesc(JdhSkuItemRelPo::getCreateTime);
        return JdhSkuItemRelPoConverter.INSTANCE.poToJdhSkuItemRel(jdhSkuItemRelPoMapper.selectList(queryWrapper));
    }

    /**
     * 分页查询sku列表
     *
     * @param jdhSku
     */
    @Override
    public Page<JdhSku> queryPageJdkSku(JdhSku jdhSku) {
        LambdaQueryWrapper<JdhSkuPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(jdhSku.getSaleStatus() != null, JdhSkuPo::getSaleStatus, jdhSku.getSaleStatus()).eq(jdhSku.getSkuId() != null, JdhSkuPo::getSkuId, jdhSku.getSkuId())
                .eq(jdhSku.getServiceType() != null, JdhSkuPo::getServiceType, jdhSku.getServiceType()).eq(jdhSku.getSkuType() != null, JdhSkuPo::getSkuType, jdhSku.getSkuType())
                .eq(jdhSku.getChannelId() != null, JdhSkuPo::getChannelId, jdhSku.getChannelId())
                .in(CollUtil.isNotEmpty(jdhSku.getSkuIdList()), JdhSkuPo::getSkuId, jdhSku.getSkuIdList())
                .eq(JdhSkuPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhSkuPo::getCreateTime);
        Page<JdhSkuPo> param = new Page<>(jdhSku.getPageNum(), jdhSku.getPageSize());
        IPage<JdhSkuPo> jdhSkuPoIPage = jdhSkuPoMapper.selectPage(param, queryWrapper);
        List<JdhSku> jdhSkus = JdhSkuPoConverter.INSTANCE.jdhSkuPoToJdhSku(jdhSkuPoIPage.getRecords());
        return JdhBasicPoConverter.initPage(jdhSkuPoIPage, jdhSkus);
    }

    /**
     * 保存sku加项信息
     *
     * @param jdhSkuRel
     */
    @Override
    public int saveSkuRelInfo(JdhSkuRel jdhSkuRel) {
        if (jdhSkuRel.getSkuId() == null || jdhSkuRel.getParentSkuId() == null) {
            throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR);
        }
        JdhSkuRelPo po = JdhSkuRelPoConverter.INSTANCE.toJdhSkuRelPo(jdhSkuRel);
        Date now = new Date();
        po.setCreateUser(jdhSkuRel.getCreateUser());
        po.setCreateTime(now);
        po.setUpdateUser(jdhSkuRel.getUpdateUser());
        po.setUpdateTime(now);
        po.setYn(YnStatusEnum.YES.getCode());
        return jdhSkuRelPoMapper.insert(po);
    }

    /**
     * 批量保存sku加项信息
     *
     * @param jdhSkuRelList
     */
    @Override
    public int batchSaveSkuRelInfo(JdhSkuRel parentSku, List<JdhSkuRel> jdhSkuRelList) {
        if (parentSku.getParentSkuId() == null || CollUtil.isEmpty(jdhSkuRelList)) {
            throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR);
        }
        Date now = new Date();
        List<JdhSkuRelPo> insertList = new ArrayList<>();
        for (JdhSkuRel skuRel : jdhSkuRelList) {
            JdhSkuRelPo po = JdhSkuRelPoConverter.INSTANCE.toJdhSkuRelPo(skuRel);
            if (po.getParentSkuId() == null && skuRel.getParentSkuId() != null) {
                po.setParentSkuId(skuRel.getParentSkuId());
            }
            if (StringUtils.isBlank(po.getParentShortName()) && StringUtils.isNotBlank(skuRel.getParentShortName())) {
                po.setParentShortName(skuRel.getParentShortName());
            }
            po.setCreateUser(skuRel.getCreateUser());
            po.setCreateTime(now);
            po.setUpdateUser(skuRel.getUpdateUser());
            po.setUpdateTime(now);
            po.setYn(YnStatusEnum.YES.getCode());
            po.setExtend(skuRel.getExtend());
            insertList.add(po);
        }
        // 更新前删除旧商品项目关联关系
        deleteSkuRelInfo(parentSku);
        return jdhSkuRelPoMapper.batchInsert(insertList);
    }

    /**
     * 删除sku加项信息
     *
     * @param jdhSkuRel jdhSkuRel
     *
     */
    @Override
    public int deleteSkuRelInfo(JdhSkuRel jdhSkuRel) {
        if (jdhSkuRel.getParentSkuId() == null) {
            throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("主商品id"));
        }
        LambdaQueryWrapper<JdhSkuRelPo> deleteWrapper = Wrappers.lambdaQuery();
        deleteWrapper.eq(JdhSkuRelPo::getParentSkuId, jdhSkuRel.getParentSkuId()).eq(JdhSkuRelPo::getSkuRelType, jdhSkuRel.getSkuRelType());
        // 更新前删除旧商品项目关联关系
        return jdhSkuRelPoMapper.delete(deleteWrapper);
    }

    /**
     * 查询sku加项信息
     *
     * @param jdhSkuRel jdhSkuRel
     */
    @Override
     public List<JdhSkuRel>  queryJdhSkuRelInfo(JdhSkuRel jdhSkuRel) {
        LambdaQueryWrapper<JdhSkuRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(jdhSkuRel.getParentSkuId() != null,JdhSkuRelPo::getParentSkuId, jdhSkuRel.getParentSkuId())
                .eq(JdhSkuRelPo::getSkuRelType, jdhSkuRel.getSkuRelType())
                .eq(jdhSkuRel.getSkuId() != null, JdhSkuRelPo::getSkuId, jdhSkuRel.getSkuId())
                .eq(JdhSkuRelPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSkuRelPo::getCreateTime);
        return JdhSkuRelPoConverter.INSTANCE.poToJdhSkuRel(jdhSkuRelPoMapper.selectList(queryWrapper));
    }

    /**
     * 查询多个sku加项信息
     *
     * @param jdhSkuRels jdhSkuRels
     */
    @Override
    public List<JdhSkuRel> queryMultiJdhSkuRelInfo(List<JdhSkuRel> jdhSkuRels, Integer skuRelType) {
        LambdaQueryWrapper<JdhSkuRelPo> queryWrapper = Wrappers.lambdaQuery();
        Set<Long> parentIds = jdhSkuRels.stream().map(JdhSkuRel::getParentSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollUtil.isEmpty(parentIds)) {
            return Collections.emptyList();
        }
        queryWrapper.in(CollUtil.isNotEmpty(parentIds), JdhSkuRelPo::getParentSkuId, parentIds)
                .eq(JdhSkuRelPo::getSkuRelType, skuRelType).eq(JdhSkuRelPo::getYn, YnStatusEnum.YES.getCode()).orderByDesc(JdhSkuRelPo::getCreateTime);
        return JdhSkuRelPoConverter.INSTANCE.poToJdhSkuRel(jdhSkuRelPoMapper.selectList(queryWrapper));
    }

    /**
     * 分页查询商品项目关系
     *
     * @param rel
     */
    @Override
    public Page<JdhSkuItemRel> queryJdhSkuItemRelPage(JdhSkuItemRel rel) {
        LambdaQueryWrapper<JdhSkuItemRelPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(rel.getSkuId() != null, JdhSkuItemRelPo::getSkuId, rel.getSkuId())
                .eq(StringUtils.isNotBlank(rel.getSkuItemId()), JdhSkuItemRelPo::getSkuItemId, rel.getSkuItemId())
                .eq(rel.getSkuItemType() != null, JdhSkuItemRelPo::getSkuItemType, rel.getSkuItemType())
                .eq(rel.getChannelId() != null, JdhSkuItemRelPo::getChannelId, rel.getChannelId())
                .eq(JdhSkuItemRelPo::getYn, YnStatusEnum.YES.getCode())
                .orderByDesc(JdhSkuItemRelPo::getCreateTime);
        Page<JdhSkuItemRelPo> param = new Page<>(rel.getPageNum(), rel.getPageSize());
        IPage<JdhSkuItemRelPo> jdhSkuPoIPage = jdhSkuItemRelPoMapper.selectPage(param, queryWrapper);
        List<JdhSkuItemRel> jdhSkus = JdhSkuItemRelPoConverter.INSTANCE.poToJdhSkuItemRel(jdhSkuPoIPage.getRecords());
        return JdhBasicPoConverter.initPage(jdhSkuPoIPage, jdhSkus);
    }

    /**
     * 查询商品扩展属性
     *
     * @param jdhSkuExtend
     * @return
     */
    @Override
    public List<JdhSkuExtend> queryJdhSkuExtendList(JdhSkuExtend jdhSkuExtend) {
        LambdaQueryWrapper<JdhSkuExtendPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhSkuExtendPo::getSkuId, jdhSkuExtend.getSkuId());
        queryWrapper.eq(StringUtils.isNotBlank(jdhSkuExtend.getAttribute()), JdhSkuExtendPo::getAttribute, jdhSkuExtend.getAttribute());
        queryWrapper.eq(StringUtils.isNotBlank(jdhSkuExtend.getValue()), JdhSkuExtendPo::getValue, jdhSkuExtend.getValue());
        queryWrapper.eq(JdhSkuExtendPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhSkuExtendPo> list = jdhSkuExtendPoMapper.selectList(queryWrapper);
        return JdhSkuPoConverter.INSTANCE.jdhSkuExtendPoToModel(list);
    }

    /**
     * 查询商品扩展属性
     *
     * @param jdhSkuExtend
     * @return
     */
    @Override
    public Boolean saveJdhSkuExtend(JdhSkuExtend jdhSkuExtend) {
        if (jdhSkuExtend == null || jdhSkuExtend.getSkuId() == null || StringUtils.isBlank(jdhSkuExtend.getAttribute())) {
            return false;
        }
        LambdaQueryWrapper<JdhSkuExtendPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhSkuExtendPo::getSkuId, jdhSkuExtend.getSkuId());
        queryWrapper.eq(JdhSkuExtendPo::getAttribute, jdhSkuExtend.getAttribute());
        queryWrapper.eq(JdhSkuExtendPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhSkuExtendPo> list = jdhSkuExtendPoMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            LambdaUpdateWrapper<JdhSkuExtendPo> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(JdhSkuExtendPo::getValue, jdhSkuExtend.getValue())
                    .eq(JdhSkuExtendPo::getSkuId, jdhSkuExtend.getSkuId())
                    .eq(JdhSkuExtendPo::getAttribute, jdhSkuExtend.getAttribute());
            return jdhSkuExtendPoMapper.update(null, updateWrapper) > 0;
        }
        JdhSkuExtendPo po = JdhSkuPoConverter.INSTANCE.jdhSkuExtendModelToPo(jdhSkuExtend);
        return jdhSkuExtendPoMapper.insert(po) > 0;
    }

    /**
     * 批量插入
     *
     * @param jdhSku j
     * @param now    time
     */
    private void batchInsertSkuItemRel(JdhSku jdhSku, Date now) {
        if (jdhSku.getSkuId() == null) {
            throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("商品id"));
        }
        if (jdhSku.getChannelId() == null) {
            JdhSku queryJdhSku = this.find(JdhSkuIdentifier.builder().skuId(jdhSku.getSkuId()).build());
            jdhSku.setChannelId(queryJdhSku.getChannelId());
        }
        // 入参有商品项目id列表，则删除旧的关联数据并重新插入
        if (CollUtil.isNotEmpty(jdhSku.getServiceItemIdList())) {
            LambdaQueryWrapper<JdhSkuItemRelPo> deleteWrapper = Wrappers.lambdaQuery();
            deleteWrapper.eq(JdhSkuItemRelPo::getSkuId, jdhSku.getSkuId()).eq(JdhSkuItemRelPo::getSkuItemType, jdhSku.getSkuItemType());
            // 更新前删除旧商品项目关联关系
            jdhSkuItemRelPoMapper.delete(deleteWrapper);
            // 插入关联表数据
            List<JdhSkuItemRelPo> insertRel = new ArrayList<>();
            for (Long itemId : jdhSku.getServiceItemIdList()) {
                JdhSkuItemRelPo relPo = new JdhSkuItemRelPo();
                relPo.setSkuId(jdhSku.getSkuId());
                relPo.setSkuItemType(jdhSku.getSkuItemType());
                relPo.setSkuItemId(itemId.toString());
                relPo.setChannelId(jdhSku.getChannelId());
                relPo.setCreateUser(jdhSku.getCreateUser());
                relPo.setCreateTime(now);
                relPo.setUpdateUser(jdhSku.getUpdateUser());
                relPo.setUpdateTime(now);
                relPo.setYn(YnStatusEnum.YES.getCode());
                insertRel.add(relPo);
            }
            jdhSkuItemRelPoMapper.batchInsert(insertRel);
        }
    }

    /**
     * 商品扩展保存
     * @param jdhSku
     */
    private void saveExtend(JdhSku jdhSku) {
        if (CollUtil.isNotEmpty(jdhSku.getHighQualityStoreId())) {
            JdhSkuExtend jdhSkuExtend = new JdhSkuExtend();
            jdhSkuExtend.setSkuId(jdhSku.getSkuId());
            jdhSkuExtend.setAttribute(SkuExtendAttributeEnum.HIGH_QUALITY_STORE_ID.getAttribute());
            jdhSkuExtend.setValue(JSON.toJSONString(jdhSku.getHighQualityStoreId()));
            saveJdhSkuExtend(jdhSkuExtend);
        }
    }
}
