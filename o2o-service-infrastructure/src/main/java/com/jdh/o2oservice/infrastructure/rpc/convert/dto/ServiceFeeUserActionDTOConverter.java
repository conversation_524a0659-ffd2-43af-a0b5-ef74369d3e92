package com.jdh.o2oservice.infrastructure.rpc.convert.dto;

import com.jd.trade2.base.export.relation.Relation;
import com.jd.trade2.core.domain.servicefee.export.dto.model.AbstractServiceFeeItemDTO;
import com.jd.trade2.core.domain.servicefee.export.dto.model.ServiceFeeCollectionDTO;
import com.jd.trade2.vertical.tool.ParseTool;
import com.jdh.o2oservice.base.enums.IdentityUserActionEnum;
import com.jdh.o2oservice.core.domain.trade.enums.FeeAggregateTypeEnum;
import com.jdh.o2oservice.core.domain.trade.vo.OrderTradeValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.OrderUserActionValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.ServiceFeeInfoValueObject;
import com.jdh.o2oservice.core.domain.trade.vo.ServiceFeeItemInfoValueObject;
import com.jdh.o2oservice.infrastructure.rpc.convert.TradeParamConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ServiceFeeUserActionDTOConverter 用户行为-服务费
 *
 * <AUTHOR>
 * @version 2024/3/11 18:49
 **/
@Slf4j
@Component
public class ServiceFeeUserActionDTOConverter extends AbstractUserActionDTOConverter<OrderTradeValueObject> {

    /**
     * 上门服务费
     */
    private final static String ANGEL_HOME_SERVICE_FEE = "3901";

    @Resource
    private TradeParamConverter tradeParamInfrastructureAssembler;

    @Override
    public boolean identityContainsUserAction(List<String> identityUserActionList) {
        return identityUserActionList.contains(IdentityUserActionEnum.SERVICE_FEE_USER_ACTION.getUserActionType());
    }

    @Override
    public void handlerIdentityTradeOrder(ParseTool parseTool, OrderTradeValueObject orderTradeResult, Relation relation, String identity) {
        ServiceFeeInfoValueObject serviceFeeInfo = buildServiceFeeInfo(parseTool);
        if (serviceFeeInfo == null) {
            return;
        }
        orderTradeResult.setServiceFeeInfo(serviceFeeInfo);
    }

    @Override
    public void handlerIdentityUserAction(ParseTool parseTool, OrderUserActionValueObject orderUserActionResult, Relation relation, String identity) {
        ServiceFeeInfoValueObject serviceFeeInfo = buildServiceFeeInfo(parseTool);
        if (serviceFeeInfo == null) {
            return;
        }
        orderUserActionResult.setServiceFeeInfo(serviceFeeInfo);
    }

    private ServiceFeeInfoValueObject buildServiceFeeInfo(ParseTool parseTool) {
        ServiceFeeCollectionDTO serviceFeeCollectionDTO = parseTool.getCollectionDTO(ServiceFeeCollectionDTO.class);
        if (serviceFeeCollectionDTO == null) {
            return null;
        }
        List<AbstractServiceFeeItemDTO> abstractServiceFeeItemDTOList = serviceFeeCollectionDTO.getChildList();
        if (CollectionUtils.isEmpty(abstractServiceFeeItemDTOList)) {
            return null;
        }
        ServiceFeeInfoValueObject serviceFeeInfo = new ServiceFeeInfoValueObject();
        List<ServiceFeeItemInfoValueObject> serviceFeeItemInfoList = new ArrayList<>();
        for (AbstractServiceFeeItemDTO dto : abstractServiceFeeItemDTOList) {
            if(BigDecimal.ZERO.compareTo(dto.getServiceFee()) == 0){
                continue;
            }
            ServiceFeeItemInfoValueObject serviceFeeItemInfo = new ServiceFeeItemInfoValueObject();
            serviceFeeItemInfo.setServiceFeeType(dto.getServiceFeeType());
            serviceFeeItemInfo.setServiceFee(dto.getServiceFee());
            serviceFeeItemInfoList.add(serviceFeeItemInfo);
            // 上门服务费
            if (ANGEL_HOME_SERVICE_FEE.equals(dto.getServiceFeeType())) {
                Integer subType = StringUtils.isNumeric(dto.getTemplateId()) ? Integer.valueOf(dto.getTemplateId()) : null;
                FeeAggregateTypeEnum feeTypeEnum = FeeAggregateTypeEnum.findBySubType(subType);
                if (feeTypeEnum != null) {
                    serviceFeeItemInfo.setServiceFeeType(feeTypeEnum.getCode());
                    serviceFeeItemInfo.setShowIndex(feeTypeEnum.getShowIndex());
                }
            }
        }

        serviceFeeInfo.setServiceFeeItemInfoList(serviceFeeItemInfoList.stream().sorted(Comparator.comparing(ServiceFeeItemInfoValueObject::getShowIndex)).collect(Collectors.toList()));
        return serviceFeeInfo;
    }
}
