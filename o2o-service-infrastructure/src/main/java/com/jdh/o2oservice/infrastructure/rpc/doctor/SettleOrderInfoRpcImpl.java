package com.jdh.o2oservice.infrastructure.rpc.doctor;

import cn.hutool.core.collection.CollUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.settlement.center.export.service.app.BankCardEasyExportService;
import com.jdh.o2oservice.application.angel.AngelExtApplication;
import com.jdh.o2oservice.application.product.ProductExtApplication;
import com.jdh.o2oservice.application.product.ProductServiceItemExtApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.settlement.JdServiceSettleExtApplication;
import com.jdh.o2oservice.application.trade.TradeExtApplication;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import com.jdh.o2oservice.core.domain.trade.enums.TradeErrorCode;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderMoney;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDetailDto;
import com.jdh.o2oservice.export.angel.query.AngelDetailRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.VoucherIdRequest;
import com.jdh.o2oservice.export.trade.dto.*;
import com.jdh.o2oservice.export.trade.query.CalcAngelSettleFeeParam;
import com.jdh.o2oservice.export.trade.query.JdServiceSettleParam;
import com.jdh.o2oservice.infrastructure.rpc.convert.DirectionConverter;
import com.jdh.o2oservice.infrastructure.rpc.convert.OrderInfoConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 结算订单信息
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
@Service
@Slf4j
public class SettleOrderInfoRpcImpl implements SettleOrderInfoRpc {

    /**
     * angelExtApplication
     */
    @Autowired
    private AngelExtApplication angelExtApplication;

    /**
     * tradeExtApplication
     */
    @Autowired
    private TradeExtApplication tradeExtApplication;
    /**
     * productExtApplication
     */
    @Autowired
    private ProductExtApplication productExtApplication;
    /**
     * productServiceItemExtApplication
     */
    @Autowired
    private ProductServiceItemExtApplication productServiceItemExtApplication;

    /**
     * 查询服务者结算价
     */
    @Resource
    private JdServiceSettleExtApplication jdServiceSettleExtApplication;
    /**
     *
     */
    @Autowired
    private VoucherApplication voucherApplication;


    /**
     * @param orderItemId
     * @return
     */
    @Override
    public Long queryJdOrderItemByOrderItemId(Long orderItemId) {
        return null;
    }

    /**
     * 查询护士信息
     *
     * @param angelId
     * @return
     */
    @Override
    public JdhAngelInfoBo queryJdhAngelInfo(Long angelId) {
        AngelDetailRequest angelDetailRequest = new AngelDetailRequest();
        angelDetailRequest.setAngelId(angelId);
        angelDetailRequest.setIsQueryNotOpenSkill(false);
        JdhAngelDetailDto jdhAngelDetailDto = angelExtApplication.queryAngelInfo(angelDetailRequest);
        AssertUtils.nonNull(jdhAngelDetailDto, TradeErrorCode.ANGEL_INFO_NULL);

        JdhAngelInfoBo jdhAngelInfoBo = new JdhAngelInfoBo();
        jdhAngelInfoBo.setJobNature(jdhAngelDetailDto.getJobNature());
        jdhAngelInfoBo.setStationId(jdhAngelDetailDto.getStationId());
        jdhAngelInfoBo.setNethpDocId(jdhAngelDetailDto.getNethpDocId());
        if(CollUtil.isNotEmpty(jdhAngelDetailDto.getJdhAngelProfessionRelList())){
            jdhAngelInfoBo.setProfessionTitleCode(jdhAngelDetailDto.getJdhAngelProfessionRelList().get(0).getProfessionTitleCode());
        }
        return jdhAngelInfoBo;
    }

    /**
     * 查询订单详情
     *
     * @param orderId
     */
    @Override
    public JdOrderDetailBo queryOrderFullDetail(Long orderId) {
        JdOrderDTO jdOrderDTO = tradeExtApplication.queryOrderFullDetail(orderId);
        AssertUtils.nonNull(jdOrderDTO, TradeErrorCode.ORDER_IS_NULL);
        return DirectionConverter.convertor.convertJdOrderDetailBo(jdOrderDTO);
    }

    /**
     * 查询订单结算数据详情
     *
     * @param orderId
     * @param serviceId
     * @return
     */
    @Override
    public JdOrderDetailBo queryOrderSettleDetail(Long orderId, String serviceId) {
        log.info("SettleOrderInfoRpcImpl -> queryOrderSettleDetail start orderId:{},serviceId:{}", orderId,serviceId);
        JdOrderDTO jdOrderDTO = tradeExtApplication.queryOrderSettleDetail(orderId,serviceId);
        AssertUtils.nonNull(jdOrderDTO, TradeErrorCode.ORDER_IS_NULL);
        return DirectionConverter.convertor.convertJdOrderDetailBo(jdOrderDTO);
    }

    /**
     *
     * @param orderId
     * @param serviceId
     * @param promiseId
     * @return
     */
    @Override
    public JdOrderDetailBo getSplitOrderSettleDetail(Long orderId, String serviceId,Long promiseId) {
        log.info("SettleOrderInfoRpcImpl -> getSplitOrderSettleDetail start orderId:{},serviceId:{}", orderId,serviceId);
        JdOrderDTO jdOrderDTO = tradeExtApplication.getSplitOrderSettleDetail(orderId,serviceId,promiseId);
        AssertUtils.nonNull(jdOrderDTO, TradeErrorCode.ORDER_IS_NULL);
        return DirectionConverter.convertor.convertJdOrderDetailBo(jdOrderDTO);
    }

    /**
     *
     * @param orderId
     * @return
     */
    @Override
    public String findJdOrderExtContext(Long orderId){
        return tradeExtApplication.findJdOrderExtContext(orderId);
    }

    /**
     * @param skuId
     * @return
     */
    @Override
    public JdhSkuBo queryJdhSkuInfoBySkuId(Long skuId) {
        JdhSkuDto jdhSkuDto = productExtApplication.queryJdhSkuInfoBySkuId(skuId);
        AssertUtils.nonNull(jdhSkuDto, TradeErrorCode.ORDER_IS_NULL);
        return DirectionConverter.convertor.convertJdhSkuBo(jdhSkuDto);
    }

    /**
     * @param serviceItemIdList
     * @return
     */
    @Override
    public List<ServiceItemBo> queryServiceItemList(List<Long> serviceItemIdList) {
        Set<Long> serviceItemIdSet = new HashSet(serviceItemIdList);
        List<ServiceItemDto> serviceItemDtoList = productServiceItemExtApplication.queryServiceItemList(serviceItemIdSet);
        if(CollUtil.isNotEmpty(serviceItemDtoList)){
            return DirectionConverter.convertor.convertServiceItemBoList(serviceItemDtoList);
        }
        return null;
    }

    /**
     * 护士费用细项
     * @param addressInfo
     * @param dispatchAppointmentTimeBo
     * @return
     */
    @Override
    public List<ServiceFeeDetailBo> calcNoFreeOrderServiceFee(AddressInfoBo addressInfo,DispatchAppointmentTimeBo dispatchAppointmentTimeBo) {
        CalcAngelSettleFeeParam calcAngelSettleFeeParam = new CalcAngelSettleFeeParam();
        calcAngelSettleFeeParam.setAddressUpdateParam(DirectionConverter.convertor.convertAddressUpdateParam(addressInfo));
        calcAngelSettleFeeParam.setAppointmentTimeParam(DirectionConverter.convertor.convertAppointmentTimeParam(dispatchAppointmentTimeBo));
        List<ServiceFeeDetailDTO> serviceFeeDetailDtoList = tradeExtApplication.calcNoFreeOrderServiceFee(calcAngelSettleFeeParam);
        if(CollUtil.isNotEmpty(serviceFeeDetailDtoList)){
            return DirectionConverter.convertor.convertServiceFeeDetailBoList(serviceFeeDetailDtoList);
        }
        return null;
    }

    /**
     * 派单预估结算价
     *
     * @param angelId
     * @param orderId
     * @return
     */
    @Override
    public String getOrderSettleSnapshotAmount(Long angelId, Long orderId,Long promiseId) {
        JdServiceSettleParam jdServiceSettleParam = new JdServiceSettleParam();
        jdServiceSettleParam.setOrderId(orderId);
        jdServiceSettleParam.setAngelId(angelId);
        jdServiceSettleParam.setPromiseId(promiseId);
        jdServiceSettleParam.setSaveAngelsettleAmountSnapshot(Boolean.TRUE);
        return jdServiceSettleExtApplication.getOrderSettleSnapshotAmount(jdServiceSettleParam);
    }

    /**
     * @param orderId
     * @return
     */
    @Override
    public List<JdOrderMoneyBo> getJdOrderMoneyList(Long orderId) {
        List<JdOrderMoneyDTO> jdOrderMoneyDTOList = tradeExtApplication.getJdOrderMoneyList(orderId);
        return OrderInfoConverter.convertor.jdOrderMoneyDTOToList(jdOrderMoneyDTOList);
    }

    /**
     * @param voucherId
     * @return
     */
    @Override
    public VoucherBo getVoucherDto(Long voucherId) {
        VoucherDto jdhVoucher = voucherApplication.findByVoucherId(VoucherIdRequest.builder().voucherId(voucherId).build());
        return OrderInfoConverter.convertor.converterVoucherBo(jdhVoucher);
    }

}
