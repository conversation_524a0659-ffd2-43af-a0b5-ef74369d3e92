package com.jdh.o2oservice.infrastructure.repository.db;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.angel.enums.ItemTypeEnum;
import com.jdh.o2oservice.core.domain.product.context.*;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.model.*;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhBasicPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhIndicatorPoConverter;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhServiceItemAngelSkillRelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhServiceItemIndicatorRelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhServiceItemMaterialPackageRelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhServiceItemPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhServiceItemAngelSkillRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhServiceItemIndicatorRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhServiceItemMaterialPackageRelPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhServiceItemPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @ClassName:JdhServiceItemRepository
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/22 11:26
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class JdhServiceItemRepositoryImpl implements JdhServiceItemRepository {

    @Resource
    private JdhServiceItemPoMapper jdhServiceItemPoMapper;

    @Resource
    private JdhServiceItemIndicatorRelPoMapper jdhServiceItemIndicatorRelPoMapper;

    @Resource
    private JdhServiceItemMaterialPackageRelPoMapper jdhServiceItemMaterialPackageRelPoMapper;


    @Resource
    private JdhServiceItemAngelSkillRelPoMapper jdhServiceItemAngelSkillRelPoMapper;

    /**
     * ducc配置
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 通过ID寻找Aggregate。
     * 找到的Aggregate自动是可追踪的
     *
     * @param serviceItemIdentifier
     */
    @Override
    public ServiceItem find(ServiceItemIdentifier serviceItemIdentifier) {
        LambdaQueryWrapper<JdhServiceItemPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhServiceItemPo::getItemId, serviceItemIdentifier.getItemId())
                .eq(JdhServiceItemPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhServiceItemPo::getId);
        ServiceItem serviceItem = JdhIndicatorPoConverter.ins.convertToServiceItem(jdhServiceItemPoMapper.selectOne(queryWrapper));
        if (Objects.isNull(serviceItem)) {
            return null;
        }

        LambdaQueryWrapper<JdhServiceItemIndicatorRelPo> itemIndicatorRelWrapper = Wrappers.lambdaQuery();
        itemIndicatorRelWrapper.eq(JdhServiceItemIndicatorRelPo::getItemId, serviceItem.getItemId())
                .eq(JdhServiceItemIndicatorRelPo::getYn, YnStatusEnum.YES.getCode());
        itemIndicatorRelWrapper.orderByDesc(JdhServiceItemIndicatorRelPo::getId);
        List<JdhServiceItemIndicatorRelPo> jdhServiceItemIndicatorRelPos = jdhServiceItemIndicatorRelPoMapper.selectList(itemIndicatorRelWrapper);
        if (CollectionUtils.isNotEmpty(jdhServiceItemIndicatorRelPos)) {
            List<ServiceItemIndicatorRel> serviceItemIndicatorRels = JdhIndicatorPoConverter.ins.convertToServiceItemIndicatorRels(jdhServiceItemIndicatorRelPos);
            serviceItem.setIndicatorRelList(serviceItemIndicatorRels);
        }
        return serviceItem;
    }

    /**
     * 将一个Aggregate从Repository移除
     * 操作后的aggregate对象自动取消追踪
     *
     * @param aggregate
     */
    @Override
    public int remove(ServiceItem aggregate) {
        return 0;
    }

    /**
     * 保存一个Aggregate
     * 保存后自动重置追踪条件
     *
     * @param serviceItem
     */
    @Override
    public int save(ServiceItem serviceItem) {
        int result;
        // 替换中文符号为英文符号
        serviceItem.setItemName(replaceNameCnCharToEnChar(serviceItem.getItemName()));
        if (Objects.equals(serviceItem.getItemType(), ItemTypeEnum.TESTING.getCode())
                || Objects.equals(serviceItem.getItemType(), ItemTypeEnum.NURSING.getCode())) {
            result = saveServiceItem(serviceItem);
            //保存项目所需指标
            if (CollectionUtils.isNotEmpty(serviceItem.getIndicatorIdList())) {
                saveServiceItemIndicatorRel(serviceItem);
            }
            //保存项目所需技能
            if (CollectionUtils.isNotEmpty(serviceItem.getAngelSkillCodeList())) {
                saveServiceItemSkillRel(serviceItem);
            }
            //保存项目关联耗材
            if (CollectionUtils.isNotEmpty(serviceItem.getMaterialList())) {
                saveServiceItemMaterialRel(serviceItem);
            }
        } else {
            JdhServiceItemPo jdhServiceItemPo = JdhIndicatorPoConverter.ins.convertToJdhServiceItemPo(serviceItem);
            List<JdhServiceItemIndicatorRelPo> jdhServiceItemIndicatorRelPos = JdhIndicatorPoConverter.ins.convertToJdhServiceItemIndicatorRelPos(serviceItem.getIndicatorRelList());
            if (Objects.isNull(jdhServiceItemPo.getId())) {
                result = jdhServiceItemPoMapper.insert(jdhServiceItemPo);
            } else {
                LambdaUpdateWrapper<JdhServiceItemPo> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
                lambdaUpdateWrapper.set(Objects.nonNull(serviceItem.getItemName()), JdhServiceItemPo::getItemName, serviceItem.getItemName())
                        .set(Objects.nonNull(serviceItem.getFirstIndicatorCategory()), JdhServiceItemPo::getFirstIndicatorCategory, serviceItem.getFirstIndicatorCategory())
                        .set(StringUtils.isNotBlank(serviceItem.getFirstIndicatorCategoryName()), JdhServiceItemPo::getFirstIndicatorCategoryName, serviceItem.getFirstIndicatorCategoryName())
                        .set(Objects.nonNull(serviceItem.getSecondIndicatorCategory()), JdhServiceItemPo::getSecondIndicatorCategory, serviceItem.getSecondIndicatorCategory())
                        .set(StringUtils.isNotBlank(serviceItem.getSecondIndicatorCategoryName()), JdhServiceItemPo::getSecondIndicatorCategoryName, serviceItem.getSecondIndicatorCategoryName())
                        .set(StringUtils.isNotBlank(serviceItem.getItemMean()), JdhServiceItemPo::getItemMean, serviceItem.getItemMean())
                        .set(StringUtils.isNotBlank(serviceItem.getItemSuitable()), JdhServiceItemPo::getItemSuitable, serviceItem.getItemSuitable())
                        .set(JdhServiceItemPo::getVersion, serviceItem.getVersion() + 1)
                        .eq(JdhServiceItemPo::getItemId, serviceItem.getItemId())
                        .eq(JdhServiceItemPo::getVersion, serviceItem.getVersion());
                result = jdhServiceItemPoMapper.update(null, lambdaUpdateWrapper);
                if (CollectionUtils.isNotEmpty(jdhServiceItemIndicatorRelPos)) {
                    LambdaQueryWrapper<JdhServiceItemIndicatorRelPo> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.eq(JdhServiceItemIndicatorRelPo::getItemId, serviceItem.getItemId())
                            .eq(JdhServiceItemIndicatorRelPo::getYn, YnStatusEnum.YES.getCode());
                    jdhServiceItemIndicatorRelPoMapper.delete(queryWrapper);
                }
            }
            if (CollectionUtils.isNotEmpty(jdhServiceItemIndicatorRelPos)) {
                jdhServiceItemIndicatorRelPos.forEach(rel -> jdhServiceItemIndicatorRelPoMapper.insert(rel));
            }
        }

        return result;
    }

    /**
     * 分页查询服务项目
     *
     * @param serviceItemQueryContext
     * @return
     */
    @Override
    public Page<ServiceItem> queryServiceItemPageInfo(ServiceItemQueryContext serviceItemQueryContext) {
        LambdaQueryWrapper<JdhServiceItemPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtils.isNotEmpty(serviceItemQueryContext.getFirstIndicatorCategory()), JdhServiceItemPo::getFirstIndicatorCategory, serviceItemQueryContext.getFirstIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(serviceItemQueryContext.getSecondIndicatorCategory()), JdhServiceItemPo::getSecondIndicatorCategory, serviceItemQueryContext.getSecondIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(serviceItemQueryContext.getThirdIndicatorCategory()), JdhServiceItemPo::getThirdIndicatorCategory, serviceItemQueryContext.getThirdIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(serviceItemQueryContext.getItemIds()), JdhServiceItemPo::getItemId, serviceItemQueryContext.getItemIds())
                .like(StringUtils.isNotBlank(serviceItemQueryContext.getItemName()), JdhServiceItemPo::getItemName, serviceItemQueryContext.getItemName())
                .eq(StringUtils.isNotBlank(serviceItemQueryContext.getCreateUser()), JdhServiceItemPo::getCreateUser, serviceItemQueryContext.getCreateUser())
                .eq(Objects.nonNull(serviceItemQueryContext.getVendorType()), JdhServiceItemPo::getVendorType, serviceItemQueryContext.getVendorType())
                .eq(Objects.nonNull(serviceItemQueryContext.getItemType()), JdhServiceItemPo::getItemType, serviceItemQueryContext.getItemType())
                .eq(StringUtils.isNotBlank(serviceItemQueryContext.getUpdateUser()), JdhServiceItemPo::getUpdateUser, serviceItemQueryContext.getUpdateUser())
                .eq(StringUtils.isNotBlank(serviceItemQueryContext.getChannelNo()), JdhServiceItemPo::getChannelNo, serviceItemQueryContext.getChannelNo());
        if (StringUtils.isNotBlank(serviceItemQueryContext.getItemNameEn())) {
            queryWrapper.like(JdhServiceItemPo::getItemNameEn, serviceItemQueryContext.getItemNameEn());
        }
        if (Objects.nonNull(serviceItemQueryContext.getItemId())) {
            queryWrapper.eq(JdhServiceItemPo::getItemId, serviceItemQueryContext.getItemId());
        }
        if (Objects.nonNull(serviceItemQueryContext.getItemSource())) {
            queryWrapper.like(JdhServiceItemPo::getItemSource, serviceItemQueryContext.getItemSource());
        }
        if (CollectionUtils.isNotEmpty(serviceItemQueryContext.getItemTypes())) {
            queryWrapper.in(JdhServiceItemPo::getItemType, serviceItemQueryContext.getItemTypes());
        }
        queryWrapper.orderByDesc(JdhServiceItemPo::getId);

        IPage<JdhServiceItemPo> iPage = new Page<>(serviceItemQueryContext.getPageNum(), serviceItemQueryContext.getPageSize());
        IPage<JdhServiceItemPo> jdhServiceItemPoIPage = jdhServiceItemPoMapper.selectPage(iPage, queryWrapper);

        if (Objects.isNull(jdhServiceItemPoIPage) || CollectionUtils.isEmpty(jdhServiceItemPoIPage.getRecords())) {
            return null;
        }
        List<JdhServiceItemPo> records = jdhServiceItemPoIPage.getRecords();
        List<ServiceItem> serviceItems = JdhIndicatorPoConverter.ins.convertToServiceItems(records);
        return JdhBasicPoConverter.initPage(jdhServiceItemPoIPage, serviceItems);
    }

    /**
     * 查询服务项目
     *
     * @param itemListQuery
     * @return
     */
    @Override
    public List<ServiceItem> queryServiceItemList(JdhItemListQueryContext itemListQuery) {
        LambdaQueryWrapper<JdhServiceItemPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtils.isNotEmpty(itemListQuery.getItemIdList()), JdhServiceItemPo::getItemId, itemListQuery.getItemIdList())
                .in(CollectionUtils.isNotEmpty(itemListQuery.getFirstIndicatorCategory()), JdhServiceItemPo::getFirstIndicatorCategory, itemListQuery.getFirstIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(itemListQuery.getSecondIndicatorCategory()), JdhServiceItemPo::getSecondIndicatorCategory, itemListQuery.getSecondIndicatorCategory())
                .eq(Objects.nonNull(itemListQuery.getFirstSkuCategory()), JdhServiceItemPo::getFirstSkuCategory, itemListQuery.getFirstSkuCategory())
                .eq(Objects.nonNull(itemListQuery.getVendorType()), JdhServiceItemPo::getVendorType, itemListQuery.getVendorType())
                .eq(JdhServiceItemPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhServiceItemPo::getId);
        List<JdhServiceItemPo> jdhServiceItemPos = jdhServiceItemPoMapper.selectList(queryWrapper);
        return JdhIndicatorPoConverter.ins.convertToServiceItems(jdhServiceItemPos);
    }

    /**
     * 查询标准京东项目
     *
     * @param serviceItemQueryContext
     * @return
     */
    @Override
    public List<ServiceItem> queryJdhItemList(ServiceItemQueryContext serviceItemQueryContext) {
        LambdaQueryWrapper<JdhServiceItemPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtils.isNotEmpty(serviceItemQueryContext.getFirstIndicatorCategory()), JdhServiceItemPo::getFirstIndicatorCategory, serviceItemQueryContext.getFirstIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(serviceItemQueryContext.getSecondIndicatorCategory()), JdhServiceItemPo::getSecondIndicatorCategory, serviceItemQueryContext.getSecondIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(serviceItemQueryContext.getThirdIndicatorCategory()), JdhServiceItemPo::getThirdIndicatorCategory, serviceItemQueryContext.getThirdIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(serviceItemQueryContext.getItemIds()), JdhServiceItemPo::getItemId, serviceItemQueryContext.getItemIds())
                .eq(Objects.nonNull(serviceItemQueryContext.getItemId()), JdhServiceItemPo::getItemId, serviceItemQueryContext.getItemId())
                .like(StringUtils.isNotBlank(serviceItemQueryContext.getItemName()), JdhServiceItemPo::getItemName, serviceItemQueryContext.getItemName())
                .eq(StringUtils.isNotBlank(serviceItemQueryContext.getCreateUser()), JdhServiceItemPo::getCreateUser, serviceItemQueryContext.getCreateUser())
                .eq(Objects.nonNull(serviceItemQueryContext.getVendorType()), JdhServiceItemPo::getVendorType, serviceItemQueryContext.getVendorType())
                .eq(JdhServiceItemPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhServiceItemPo::getId);

        return JdhIndicatorPoConverter.ins.convertToServiceItems(jdhServiceItemPoMapper.selectList(queryWrapper));
    }

    /**
     * 精确查询服务项目列表
     *
     * @param exactQueryContext
     * @return
     */
    @Override
    public List<ServiceItem> queryServiceItemExactList(ServiceItemExactQueryContext exactQueryContext) {
        LambdaQueryWrapper<JdhServiceItemPo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtils.isNotEmpty(exactQueryContext.getItemName()), JdhServiceItemPo::getItemName, exactQueryContext.getItemName())
                .in(CollectionUtils.isNotEmpty(exactQueryContext.getFirstIndicatorCategory()), JdhServiceItemPo::getFirstIndicatorCategory, exactQueryContext.getFirstIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(exactQueryContext.getSecondIndicatorCategory()), JdhServiceItemPo::getSecondIndicatorCategory, exactQueryContext.getSecondIndicatorCategory())
                .in(CollectionUtils.isNotEmpty(exactQueryContext.getItemIds()), JdhServiceItemPo::getItemId, exactQueryContext.getItemIds())
                .eq(Objects.nonNull(exactQueryContext.getVendorType()), JdhServiceItemPo::getVendorType, exactQueryContext.getVendorType())
                .in(CollUtil.isNotEmpty(exactQueryContext.getItemTypeList()), JdhServiceItemPo::getItemType, exactQueryContext.getItemTypeList())
                .eq(JdhServiceItemPo::getYn, YnStatusEnum.YES.getCode());
        queryWrapper.orderByDesc(JdhServiceItemPo::getId);
        List<JdhServiceItemPo> jdhServiceItemPos = jdhServiceItemPoMapper.selectList(queryWrapper);
        return JdhIndicatorPoConverter.ins.convertToServiceItems(jdhServiceItemPos);
    }


    /**
     * 保存项目标品数据
     *
     * @param serviceItem
     * @return
     */
    public Integer saveServiceItem(ServiceItem serviceItem) {
        Integer result;
        //1 校验名称
        if (Objects.equals(serviceItem.getItemType(), ItemTypeEnum.TESTING.getCode())
                || Objects.equals(serviceItem.getItemType(), ItemTypeEnum.NURSING.getCode())) {
            if (StringUtils.isNotBlank(serviceItem.getItemName())) {
                LambdaQueryWrapper<JdhServiceItemPo> checkQueryWrapper = new LambdaQueryWrapper<>();
                checkQueryWrapper
                        .eq(JdhServiceItemPo::getItemName, serviceItem.getItemName())
                        .in(JdhServiceItemPo::getItemType, Lists.newArrayList(ItemTypeEnum.TESTING.getCode(), ItemTypeEnum.NURSING.getCode()))
                ;
                List<JdhServiceItemPo> exist = jdhServiceItemPoMapper.selectList(checkQueryWrapper);
                if (CollectionUtils.isNotEmpty(exist) && !Objects.equals(exist.get(0).getItemId(), serviceItem.getItemId())) {
                    throw new ArgumentsException(ProductErrorCode.NULL_ERROR_FORMAT.formatDescription("该项目中文名称已存在!"));
                }
            }
            if (StringUtils.isNotBlank(serviceItem.getItemNameEn())) {
                LambdaQueryWrapper<JdhServiceItemPo> checkQueryWrapper = new LambdaQueryWrapper<>();
                checkQueryWrapper.eq(JdhServiceItemPo::getItemNameEn, serviceItem.getItemNameEn());
                List<JdhServiceItemPo> exist = jdhServiceItemPoMapper.selectList(checkQueryWrapper);
                if (CollectionUtils.isNotEmpty(exist) && !Objects.equals(exist.get(0).getItemId(), serviceItem.getItemId())) {
                    throw new ArgumentsException(ProductErrorCode.NULL_ERROR_FORMAT.formatDescription("该项目英文名称已存在!"));
                }
            }
            if (StringUtils.isNotBlank(serviceItem.getReferenceItemCode())) {
                LambdaQueryWrapper<JdhServiceItemPo> checkQueryWrapper = new LambdaQueryWrapper<>();
                checkQueryWrapper.eq(JdhServiceItemPo::getReferenceItemCode, serviceItem.getReferenceItemCode());
                List<JdhServiceItemPo> exist = jdhServiceItemPoMapper.selectList(checkQueryWrapper);
                if (CollectionUtils.isNotEmpty(exist) && !Objects.equals(exist.get(0).getItemId(), serviceItem.getItemId())) {
                    throw new ArgumentsException(ProductErrorCode.NULL_ERROR_FORMAT.formatDescription("该项目编码已存在!"));
                }
            }
        }
        // 替换中文符号为英文符号
        serviceItem.setItemName(replaceNameCnCharToEnChar(serviceItem.getItemName()));
        JdhServiceItemPo jdhServiceItemPo = JdhIndicatorPoConverter.ins.convertToJdhServiceItemPoDefault(serviceItem);
        if (Objects.nonNull(jdhServiceItemPo.getItemId())) {
            LambdaQueryWrapper<JdhServiceItemPo> queryWrapper = JdhIndicatorPoConverter.ins.getJdhServiceItemPoQueryWrapper(serviceItem);
            List<JdhServiceItemPo> exist = jdhServiceItemPoMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(exist)) {
                if (Boolean.TRUE.equals(serviceItem.getAllowInsertItemId())) {
                    result = jdhServiceItemPoMapper.insert(jdhServiceItemPo);
                } else {
                    throw new ArgumentsException(ProductErrorCode.PRODUCT_ITEM_CODE_DATA_NO_EXIST);
                }
            } else {
                jdhServiceItemPo.setId(exist.get(0).getId());
                jdhServiceItemPo.setVersion(exist.get(0).getVersion() + 1);
                jdhServiceItemPo.setCreateUser(null);
                jdhServiceItemPo.setCreateTime(null);
                result = jdhServiceItemPoMapper.updateById(jdhServiceItemPo);
            }
        } else {
            jdhServiceItemPo.setItemId(SpringUtil.getBean(GenerateIdFactory.class).getId());
            serviceItem.setItemId(jdhServiceItemPo.getItemId());
            result = jdhServiceItemPoMapper.insert(jdhServiceItemPo);
        }
        if (result < 1) {
            throw new ArgumentsException(ProductErrorCode.PRODUCT_ITEM_CODE_SAVE_FAIL);
        }
        return result;
    }


    /**
     * 保存项目指标
     *
     * @param serviceItem
     * @return
     */
    public Integer saveServiceItemIndicatorRel(ServiceItem serviceItem) {
        if (CollectionUtils.isEmpty(serviceItem.getIndicatorList()) || Objects.isNull(serviceItem.getItemId())) {
            return 0;
        }
        AtomicReference<Integer> result = new AtomicReference<>(0);
        LambdaQueryWrapper<JdhServiceItemIndicatorRelPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhServiceItemIndicatorRelPo::getItemId, serviceItem.getItemId());
        queryWrapper.eq(JdhServiceItemIndicatorRelPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhServiceItemIndicatorRelPo> oldData = jdhServiceItemIndicatorRelPoMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(oldData)) {
            oldData.stream().forEach(e -> {
                e.setYn(YnStatusEnum.NO.getCode());
                Integer offset = jdhServiceItemIndicatorRelPoMapper.updateById(e);
                result.updateAndGet(v -> v + offset);
            });
        }
        List<JdhServiceItemIndicatorRelPo> indicatorList = JdhIndicatorPoConverter.ins.convertToIndicatorRelList(serviceItem);
        indicatorList.stream().forEach(e -> {
            Integer offset = jdhServiceItemIndicatorRelPoMapper.insert(e);
            result.updateAndGet(v -> v + offset);
        });
        if (result.get() < 1) {
            throw new ArgumentsException(ProductErrorCode.PRODUCT_INDICATOR_CODE_SAVE_FAIL);
        }
        return result.get();
    }


    /**
     * 查询项目指标
     *
     * @param queryContext
     * @return
     */
    @Override
    public List<ServiceItemIndicatorRel> queryServiceItemIndicatorRel(JdhItemIndicatorRelQueryContext queryContext) {
        LambdaQueryWrapper<JdhServiceItemIndicatorRelPo> queryWrapper = JdhIndicatorPoConverter.ins.packIndicatorRelQueryWrapper(queryContext);
        List<JdhServiceItemIndicatorRelPo> relPoList = jdhServiceItemIndicatorRelPoMapper.selectList(queryWrapper);
        return JdhIndicatorPoConverter.ins.po2ServiceItemIndicatorRels(relPoList);
    }


    /**
     * 保存项目耗材
     *
     * @param serviceItem
     * @return
     */
    public Integer saveServiceItemMaterialRel(ServiceItem serviceItem) {
        if (CollectionUtils.isEmpty(serviceItem.getMaterialList()) || Objects.isNull(serviceItem.getItemId())) {
            return 0;
        }
        AtomicReference<Integer> result = new AtomicReference<>(0);
        LambdaQueryWrapper<JdhServiceItemMaterialPackageRelPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhServiceItemMaterialPackageRelPo::getItemId, serviceItem.getItemId());
        queryWrapper.eq(JdhServiceItemMaterialPackageRelPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhServiceItemMaterialPackageRelPo> oldData = jdhServiceItemMaterialPackageRelPoMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(oldData)) {
            oldData.stream().forEach(e -> {
                e.setYn(YnStatusEnum.NO.getCode());
                Integer offset = jdhServiceItemMaterialPackageRelPoMapper.updateById(e);
                result.updateAndGet(v -> v + offset);
            });
        }
        List<JdhServiceItemMaterialPackageRelPo> indicatorList = JdhIndicatorPoConverter.ins.convertToMaterialPackageRelList(serviceItem);
        log.error("saveServiceItemMaterialRel,param={}", JSONObject.toJSONString(indicatorList));
        indicatorList.stream().forEach(e -> {
            Integer offset = jdhServiceItemMaterialPackageRelPoMapper.insert(e);
            result.updateAndGet(v -> v + offset);
        });
        if (result.get() < 1) {
            throw new ArgumentsException(ProductErrorCode.PRODUCT_MATERIAL_CODE_SAVE_FAIL);
        }
        return result.get();
    }

    /**
     * 查询项目耗材
     *
     * @param queryContext
     * @return
     */
    @Override
    public List<ServiceItemMaterialPackageRel> queryServiceItemMaterialPackageRel(JdhItemMaterialPackageRelQueryContext queryContext) {
        LambdaQueryWrapper<JdhServiceItemMaterialPackageRelPo> queryWrapper = JdhIndicatorPoConverter.ins.packMaterialPackageRelQueryWrapper(queryContext);
        List<JdhServiceItemMaterialPackageRelPo> relPoList = jdhServiceItemMaterialPackageRelPoMapper.selectList(queryWrapper);
        return JdhIndicatorPoConverter.ins.po2ServiceItemMaterialPackageRels(relPoList);
    }


    /**
     * 保存项目技能
     *
     * @param serviceItem
     * @return
     */
    public Integer saveServiceItemSkillRel(ServiceItem serviceItem) {
        if (CollectionUtils.isEmpty(serviceItem.getAngelSkillCodeList()) || Objects.isNull(serviceItem.getItemId())) {
            return 0;
        }
        AtomicReference<Integer> result = new AtomicReference<>(0);
        LambdaQueryWrapper<JdhServiceItemAngelSkillRelPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JdhServiceItemAngelSkillRelPo::getItemId, serviceItem.getItemId());
        queryWrapper.eq(JdhServiceItemAngelSkillRelPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhServiceItemAngelSkillRelPo> oldData = jdhServiceItemAngelSkillRelPoMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(oldData)) {
            oldData.stream().forEach(e -> {
                e.setYn(YnStatusEnum.NO.getCode());
                Integer offset = jdhServiceItemAngelSkillRelPoMapper.updateById(e);
                result.updateAndGet(v -> v + offset);
            });
        }
        List<JdhServiceItemAngelSkillRelPo> skillRelPos = JdhIndicatorPoConverter.ins.convertToAngelSkillRelList(serviceItem);
        log.error("saveServiceItemSkillRel,param={}", JSONObject.toJSONString(skillRelPos));
        skillRelPos.stream().forEach(e -> {
            Integer offset = jdhServiceItemAngelSkillRelPoMapper.insert(e);
            result.updateAndGet(v -> v + offset);
        });
        if (result.get() < 1) {
            throw new ArgumentsException(ProductErrorCode.PRODUCT_SKILL_CODE_SAVE_FAIL);
        }
        return result.get();
    }

    /**
     * 查询项目技能
     *
     * @param queryContext
     * @return
     */
    @Override
    public List<ServiceItemAngelSkillRel> queryServiceItemAngelSkillRel(JdhItemAngelSkillRelQueryContext queryContext) {
        LambdaQueryWrapper<JdhServiceItemAngelSkillRelPo> queryWrapper = JdhIndicatorPoConverter.ins.packAngelSkillRelQueryWrapper(queryContext);
        List<JdhServiceItemAngelSkillRelPo> relPoList = jdhServiceItemAngelSkillRelPoMapper.selectList(queryWrapper);
        return JdhIndicatorPoConverter.ins.po2ServiceItemAngelSkillRels(relPoList);
    }

    /**
     * 中文字符转英文
     *
     * @return str
     */
    private String replaceNameCnCharToEnChar(String originStr){
        try {
            if (StringUtils.isBlank(originStr)) {
                return originStr;
            }
            Map<String,String> cnCharToEnChar = duccConfig.getCnCharToEnChar();
            for (Map.Entry<String, String> entry : cnCharToEnChar.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                originStr = originStr.replace(key, value);
            }
            return originStr;
        } catch (Exception e) {
            log.error("replaceNameCnCharToEnChar exception", e);
            return originStr;
        }
    }

}
