<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdh.o2oservice.infrastructure.repository.db.dao.JdhDispatchDetailPoMapper">

    <insert id="batchInsert">
        INSERT INTO jdh_dispatch_detail
        (dispatch_detail_id, dispatch_id, vertical_code, service_type, promise_id, voucher_id, dispatch_detail_type, expire_date, dispatch_detail_status, dispatch_round, angel_id, out_angel_id, angel_name, extend_info, branch, version, yn, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.dispatchDetailId}, #{item.dispatchId}, #{item.verticalCode}, #{item.serviceType}, #{item.promiseId},
            #{item.voucherId},#{item.dispatchDetailType}, #{item.expireDate}, #{item.dispatchDetailStatus}, #{item.dispatchRound},
            #{item.angelId}, #{item.outAngelId}, #{item.angelName},
            #{item.extendInfo}, #{item.branch}, #{item.version}, #{item.yn},#{item.createTime},#{item.updateTime})
        </foreach>
    </insert>

    <update id="updateBatch" >
        <foreach collection="list" item="item" index="index" separator=";">
            update jdh_dispatch_detail
            <set>
                <if test="item.promiseId != null">
                    promise_id = #{item.promiseId},
                </if>
                <if test="item.voucherId != null">
                    voucher_id = #{item.voucherId},
                </if>
                <if test="item.dispatchDetailType != null">
                    dispatch_detail_type = #{item.dispatchDetailType},
                </if>
                <if test="item.expireDate != null">
                    expire_date = #{item.expireDate},
                </if>
                <if test="item.dispatchDetailStatus != null">
                    dispatch_detail_status = #{item.dispatchDetailStatus},
                </if>
                <if test="item.dispatchRound != null">
                    dispatch_round = #{item.dispatchRound},
                </if>
                <if test="item.angelId != null">
                    angel_id = #{item.angelId},
                </if>
                <if test="item.outAngelId != null">
                    out_angel_id = #{item.outAngelId},
                </if>
                <if test="item.angelName != null">
                    angel_name = #{item.angelName},
                </if>
                <if test="item.extendInfo != null">
                    extend_info = #{item.extendInfo},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                version = version+1,
            </set>
            <where>
                dispatch_id = #{item.dispatchId}
                and dispatch_detail_id = #{item.dispatchDetailId}
                and version = #{item.version}
            </where>
        </foreach>
    </update>
</mapper>