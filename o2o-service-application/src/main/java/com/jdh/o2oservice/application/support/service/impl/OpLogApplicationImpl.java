package com.jdh.o2oservice.application.support.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.medicine.b2c.base.export.domain.Pagination;
import com.jd.medicine.oplog.center.export.dto.OperationLogInfoDTO;
import com.jdh.o2oservice.application.support.convert.OpLogApplicationConverter;
import com.jdh.o2oservice.application.support.OperationLogApplication;
import com.jdh.o2oservice.application.support.param.OperationLogKeyEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.support.operationlog.model.OperationLog;
import com.jdh.o2oservice.core.domain.support.operationlog.model.OperationLogIdentifier;
import com.jdh.o2oservice.core.domain.support.operationlog.repository.OperationLogRepository;
import com.jdh.o2oservice.core.domain.support.oplog.OpLogReadService;
import com.jdh.o2oservice.core.domain.support.oplog.OpLogRecordService;
import com.jdh.o2oservice.export.support.command.OperationLogCmd;
import com.jdh.o2oservice.export.support.dto.MedicalCenterOpExtendsDTO;
import com.jdh.o2oservice.export.support.dto.MedicalCenterOpLogDTO;
import com.jdh.o2oservice.export.support.dto.MedicalCenterOperationLogDTO;
import com.jdh.o2oservice.export.support.dto.OperationLogDto;
import com.jdh.o2oservice.export.support.query.MedicalCenterOperationLogRequest;
import com.jdh.o2oservice.export.support.query.OperationLogPageRequest;
import com.jdh.o2oservice.export.support.query.OperationLogRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

/**
 * 操作日志接口
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Slf4j
@Component
public class OpLogApplicationImpl implements OperationLogApplication {

    /**
     * 操作日志记录保存
     */
    @Resource
    OpLogRecordService opLogRecordService;

    /**
     * 操作日志记录查询
     */
    @Resource
    OpLogReadService opLogReadService;

    /**
     * 日志仓库
     */
    @Resource
    OperationLogRepository operationLogRepository;

    /**
     * executorPoolFactory
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * 获取操作日志
     * 注意：查询的是健康日志中台RPC
     *
     * @param operationLogRequest
     * @return
     */
    @Override
    public PageDto<MedicalCenterOperationLogDTO> getOpLogByQueryPageFromMedicalLogCenter(MedicalCenterOperationLogRequest operationLogRequest) {
        log.info("OpLogApplicationImpl#getOpLogByQueryPage operationLogRequest:{}", JSON.toJSONString(operationLogRequest));
        Pagination<OperationLogInfoDTO> pagination = opLogReadService.getOpLogByQueryPage(OpLogApplicationConverter.instance.toOperationLogInfoQueryDTO(operationLogRequest));
        PageDto<MedicalCenterOperationLogDTO> result = new PageDto<>();
        result.setPageNum(pagination.getPageNo());
        result.setPageSize(pagination.getPageSize());
        result.setTotalCount(pagination.getTotalCount());
        result.setList(OpLogApplicationConverter.instance.toOperationLogDTOList(pagination.getData()));
        return result;
    }

    /**
     * 保存操作日志
     * 注意：保存调用的是健康日志中台RPC，此场景仅适用记录操作动作，如果需要记录入参、出错数据较大，不建议使用，中台接口会截取原始数据保存，导致日志缺失。
     * @param opLogInfo opLogInfo
     * @param opExtendsInfo opExtendsInfo
     * @return
     */
    @Override
    public Boolean insertOpLogToMedicalLogCenter(MedicalCenterOpLogDTO opLogInfo, MedicalCenterOpExtendsDTO opExtendsInfo) {
        return opLogRecordService.insertOpLog(OpLogApplicationConverter.instance.toOpLogInfoDTO(opLogInfo), OpLogApplicationConverter.instance.toOpExtendsInfoDTO(opExtendsInfo));
    }

    /**
     * 保存操作日志-异步
     * 注意：保存调用的是健康日志中台RPC，此场景仅适用记录操作动作，如果需要记录入参、出错数据较大，不建议使用，中台接口会截取原始数据保存，导致日志缺失。
     * @param opLogInfo opLogInfo
     * @param opExtendsInfo opExtendsInfo
     * @return
     */
    @Override
    public Boolean insertOpLogAsyncToMedicalLogCenter(MedicalCenterOpLogDTO opLogInfo, MedicalCenterOpExtendsDTO opExtendsInfo) {
        return opLogRecordService.insertOpLogAsync(OpLogApplicationConverter.instance.toOpLogInfoDTO(opLogInfo), OpLogApplicationConverter.instance.toOpExtendsInfoDTO(opExtendsInfo));
    }

    /**
     * 保存操作日志至O2O数据库
     * 注意：不建议插入查询类操作
     *
     * @param operationLogCmd operationLogCmd
     * @return boolean
     */
    @Override
    public Boolean insertToLocalDB(OperationLogCmd operationLogCmd) {
        return operationLogRepository.save(OpLogApplicationConverter.instance.toOperationLog(operationLogCmd)) > 0;
    }

    /**
     * 异步保存操作日志至O2O数据库
     * 注意：不建议插入查询类操作
     *
     * @param operationLogCmd operationLogCmd
     * @return boolean
     */
    @Override
    public Boolean insertAsyncToLocalDB(OperationLogCmd operationLogCmd) {
        CompletableFuture.runAsync(() -> {
            operationLogRepository.save(OpLogApplicationConverter.instance.toOperationLog(operationLogCmd));
        }, executorPoolFactory.get(ThreadPoolConfigEnum.OPLOG_SAVE_THREAD_POOL));
        return true;
    }

    /**
     * 异步保存操作日志至O2O数据库
     * 注意：不建议插入查询类操作
     *
     * @param operationLogCmd operationLogCmd
     * @return boolean
     */
    @Override
    public Boolean batchInsertAsyncToLocalDB(List<OperationLogCmd> operationLogCmd) {
        CompletableFuture.runAsync(() -> {
            operationLogRepository.batchInsert(OpLogApplicationConverter.instance.toOperationLogList(operationLogCmd));
        }, executorPoolFactory.get(ThreadPoolConfigEnum.OPLOG_SAVE_THREAD_POOL));
        return true;
    }

    /**
     * 从O2O查询操作日志
     *
     * @param operationLogPageRequest operationLogPageRequest
     * @return page
     */
    @Override
    public PageDto<OperationLogDto> queryPageFromLocalDB(OperationLogPageRequest operationLogPageRequest) {
        PageDto<OperationLogDto> pageDto = new PageDto<>();
        Page<OperationLog> pageRet = operationLogRepository.queryPage(OpLogApplicationConverter.instance.reqToOperationLog(operationLogPageRequest));
        pageDto.setPageNum(pageRet.getCurrent());
        pageDto.setPageSize(pageRet.getSize());
        pageDto.setTotalPage(pageRet.getPages());
        pageDto.setTotalCount(pageRet.getTotal());
        pageDto.setList(OpLogApplicationConverter.instance.toOperationLogDtoList(pageRet.getRecords()));
        return pageDto;
    }

    /**
     * 从O2O查询操作日志
     *
     * @param operationLogRequest operationLogRequest
     * @return dto
     */
    @Override
    public OperationLogDto queryFromLocalDB(OperationLogRequest operationLogRequest) {
        AssertUtils.nonNull(operationLogRequest, "入参不允许为空");
        AssertUtils.nonNull(operationLogRequest.getId(), "id不允许为空");
        return OpLogApplicationConverter.instance.toOperationLogDto(operationLogRepository.find(OperationLogIdentifier.builder().id(operationLogRequest.getId()).build()));
    }

    @Override
    public <T, R> R execute(Function<T, R> function, T t, String operator, OperationLogKeyEnum staticLogKeyEnum, String uniqId) {
        R r = null;
        OperationLog record = new OperationLog();
        try{
            r = function.apply(t);
            record.setResult(JSON.toJSONString(r));
        }catch (Exception e){
            log.error("execute error", e);
            record.setResult(e.getMessage());
        }

        CompletableFuture.runAsync(() -> {
            record.setBizSceneKey(staticLogKeyEnum.getSceneKey());
            record.setBizSceneDesc(staticLogKeyEnum.getSceneDesc());
            record.setOperateType(staticLogKeyEnum.getOperateType());
            record.setOperator(operator);
            record.setBizUnionId(uniqId);
            record.setParam(JSON.toJSONString(t));
            operationLogRepository.save(record);
        }, executorPoolFactory.get(ThreadPoolConfigEnum.OPLOG_SAVE_THREAD_POOL));

        return r;
    }

}
