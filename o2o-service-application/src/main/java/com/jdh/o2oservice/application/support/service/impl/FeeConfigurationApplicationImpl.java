package com.jdh.o2oservice.application.support.service.impl;

import IceInternal.Ex;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.addresstranslation.api.base.JDDistrictInfo;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.support.convert.FeeConfigurationApplicationConverter;
import com.jdh.o2oservice.application.support.service.FeeConfigurationApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.application.support.service.impl.param.FeeConfigurationCheckParam;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.fee.HomeAndTimeFeeConfig;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.common.enums.DictFieldTypeEnum;
import com.jdh.o2oservice.common.enums.OperateLogSceneEnum;
import com.jdh.o2oservice.common.enums.OperateTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.product.enums.FeeConfigParseStatusEnum;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.model.JdhAreaFeeConfig;
import com.jdh.o2oservice.core.domain.product.model.JdhAreaFeeConfigdentifier;
import com.jdh.o2oservice.core.domain.product.model.JdhRegionFeeConfig;
import com.jdh.o2oservice.core.domain.product.repository.JdhRegionFeeConfigRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhAreaFeeConfigRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.JdhAreaFeeConfigQuery;
import com.jdh.o2oservice.core.domain.product.repository.query.JdhRegionFeeConfigQuery;
import com.jdh.o2oservice.core.domain.provider.enums.ProviderErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.JDDistrictBo;
import com.jdh.o2oservice.core.domain.support.feeConfig.model.FeeConfig;
import com.jdh.o2oservice.core.domain.support.feeConfig.model.FeeConfigIdentifier;
import com.jdh.o2oservice.core.domain.support.feeConfig.model.FixedSkuConfig;
import com.jdh.o2oservice.core.domain.support.feeConfig.model.FixedSkuConfigIdentifier;
import com.jdh.o2oservice.core.domain.support.feeConfig.repository.FeeConfigurationRepository;
import com.jdh.o2oservice.core.domain.support.feeConfig.repository.FixedSkuConfigRepository;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.core.domain.support.operateLog.model.OperateLog;
import com.jdh.o2oservice.core.domain.support.operateLog.repository.OperateLogRepository;
import com.jdh.o2oservice.export.support.FileManageJsfExport;
import com.jdh.o2oservice.export.support.command.*;
import com.jdh.o2oservice.export.support.dto.*;
import com.jdh.o2oservice.export.support.query.*;
import com.jdh.o2oservice.infrastructure.repository.db.convert.JdhFeeConfigPoConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 费项配置实现类
 */
@Component
@Slf4j
public class FeeConfigurationApplicationImpl implements FeeConfigurationApplication {


    @Autowired
    private FeeConfigurationCheckParam feeConfigurationCheckParam;


    @Autowired
    private FeeConfigurationRepository feeConfigurationRepository;


    @Autowired
    private FixedSkuConfigRepository fixedSkuConfigRepository;

    @Autowired
    private OperateLogRepository operateLogRepository;

    @Autowired
    private DuccConfig duccConfig;

    @Autowired
    private Cluster jimClient;

    @Autowired
    private FileManageService fileManageService;

    @Autowired
    private JdhFileRepository jdhFileRepository;

    @Autowired
    private ExecutorPoolFactory executorPoolFactory;

    @Autowired
    private FileManageJsfExport fileManageJsfExport;

    @Autowired
    private JdhAreaFeeConfigRepository jdhAreaFeeConfigRepository;
    /**
     *
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    @Resource
    private JdhRegionFeeConfigRepository jdhRegionFeeConfigRepository;

    @Resource
    private AddressRpc addressRpc;

    @Resource
    private FileManageApplication fileManageApplication;


    /**
     * 新增费项配置
     *
     * @param addFeeConfigCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FeeConfigurationApplicationImpl.addFeeConfig")
    public void addFeeConfig(AddFeeConfigCmd addFeeConfigCmd) {
        //参数校验
        feeConfigurationCheckParam.addFeeConfig(addFeeConfigCmd);
        this.importFeeConfigExcel(addFeeConfigCmd);
    }

    private void importFeeConfigExcel(AddFeeConfigCmd addFeeConfigCmd){
        JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(Long.valueOf(addFeeConfigCmd.getFeeConfigFileId())).build());
        AssertUtils.nonNull(jdhFile, SupportErrorCode.SUPPORT_FILE_UPLOAD_FILE_NOT_EXIST);
        //如果id不为空，修改费项配置
        if (Objects.nonNull(addFeeConfigCmd.getId())) {
            UploadFeeConfigCmd uploadFeeConfigCmd = JSON.parseObject(JSON.toJSONString(addFeeConfigCmd), UploadFeeConfigCmd.class);
            FeeConfig feeConfigUpdate = new FeeConfig();
            feeConfigUpdate.setFeeConfigUrl(jdhFile.getFilePath());
            feeConfigUpdate.setFeeConfigName(uploadFeeConfigCmd.getFeeConfigName());
            feeConfigUpdate.setUpdateUser(uploadFeeConfigCmd.getErp());
            feeConfigUpdate.setFeeConfigId(uploadFeeConfigCmd.getFeeConfigFileId());
            feeConfigUpdate.setId(uploadFeeConfigCmd.getId());
            uploadFeeConfigCmd.setOperateContent(jdhFile.getFilePath());
            feeConfigUpdate.setParseStatus(FeeConfigParseStatusEnum.PARSE_ING.getCode());
            feeConfigurationRepository.update(feeConfigUpdate);
            CompletableFuture.runAsync(() -> excelAllSheetAnalysis(uploadFeeConfigCmd,jdhFile), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_IMPORT_HAND_POOL));
        } else {//否则，新增费项配置
            addFeeConfigCmd.setOperateContent(jdhFile.getFilePath());
            addConfig(addFeeConfigCmd,jdhFile);
        }
    }

    private void addConfig(AddFeeConfigCmd addFeeConfigCmd,JdhFile jdhFile) {
        //实体转换
        FeeConfig feeConfig = FeeConfigurationApplicationConverter.instance.cmd2FeeConfig(addFeeConfigCmd);
        //缓存key
        feeConfig.setCacheKeyPrefix(feeConfig.getChannelId() + "*" + feeConfig.getServiceType());
        //校验serviceType+channelId+yn 不重复
        List<FeeConfig> feeConfigs = feeConfigurationRepository.queryList(feeConfig);
        if (CollectionUtils.isNotEmpty(feeConfigs)) {
            throw new BusinessException(SystemErrorCode.CONFIG_ERROR.formatDescription("该业务身份和销售渠道下已配置费项规则"));
        }
        //操作db
        log.info("FeeConfigurationApplicationImpl.addFeeConfig.feeConfig={}", JSON.toJSONString(feeConfig));
        try{
            Long id = feeConfigurationRepository.insertReturnId(feeConfig);
            //上传Excel
            UploadFeeConfigCmd uploadFeeConfigCmd = new UploadFeeConfigCmd();
            uploadFeeConfigCmd.setId(id);
            uploadFeeConfigCmd.setFeeConfigName(addFeeConfigCmd.getFeeConfigName());
            uploadFeeConfigCmd.setFeeConfigFileId(addFeeConfigCmd.getFeeConfigFileId());
            uploadFeeConfigCmd.setErp(addFeeConfigCmd.getErp());
            uploadFeeConfigCmd.setOperateContent(addFeeConfigCmd.getOperateContent());
            log.info("FeeConfigurationApplicationImpl.addFeeConfig.uploadFeeConfigCmd={}", JSON.toJSONString(uploadFeeConfigCmd));
            CompletableFuture.runAsync(() -> excelAllSheetAnalysis(uploadFeeConfigCmd,jdhFile), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_IMPORT_HAND_POOL));
        }catch (Exception e) {
            log.error("FeeConfigurationApplicationImpl.addFeeConfig.e:", e);
            throw new BusinessException(SystemErrorCode.ILLEGAL_OPERATION.formatDescription(e.getMessage()));
        }
    }

    /**
     * 分页查询费项配置
     *
     * @param feeConfigPageRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FeeConfigurationApplicationImpl.queryFeeConfigByPage")
    public PageDto<FeeConfigPageDto> queryFeeConfigByPage(FeeConfigPageRequest feeConfigPageRequest) {
        //参数校验
        feeConfigurationCheckParam.queryFeeConfigByPage(feeConfigPageRequest);
        //入参转换
        FeeConfig feeConfig = FeeConfigurationApplicationConverter.instance.queryPage2FeeConfig(feeConfigPageRequest);
        //分页查询
        Page<FeeConfig> page = feeConfigurationRepository.queryPage(feeConfig);
        //出参转换
        PageDto<FeeConfigPageDto> pageDto = new PageDto<>();
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<FeeConfigPageDto> feeConfigPageDtos = page.getRecords().stream().
                    map(FeeConfigurationApplicationConverter.instance::convertFeeConfig2Dto).collect(Collectors.toList());
            Optional.ofNullable(feeConfigPageDtos).map(List::stream).orElseGet(Stream::empty).forEach(feeConfigPageDto -> {
                String channelIdDesc = getFieldDesc(DictFieldTypeEnum.CHANNEL_ID.getFieldValue(), feeConfigPageDto.getChannelId());
                String serviceTypeDesc = getFieldDesc(DictFieldTypeEnum.SERVICE_TYPE.getFieldValue(), feeConfigPageDto.getServiceType());
                feeConfigPageDto.setChannelIdDesc(channelIdDesc);
                feeConfigPageDto.setServiceTypeDesc(serviceTypeDesc);
                feeConfigPageDto.setParseStatusDesc(FeeConfigParseStatusEnum.getParseStatusDesc(feeConfigPageDto.getParseStatus()));
                String feeConfigUrl = fileManageJsfExport.getDownLoadUrlByFilePath(feeConfigPageDto.getFeeConfigUrl()).getData();
                feeConfigPageDto.setFeeConfigUrl(feeConfigUrl);
            });
            pageDto.setList(feeConfigPageDtos);
        }
        return pageDto;
    }

    /**
     * @param fieldType
     * @param fieldValue
     * @return
     */
    private String getFieldDesc(String fieldType, String fieldValue) {
        QueryFeeConfigDictRequest request = new QueryFeeConfigDictRequest();
        request.setFieldType(fieldType);
        List<FeeConfigDictDto> feeConfigDictDtos = queryFeeConfigDict(request);
        return feeConfigDictDtos.stream().
                filter(feeConfigDictDto -> feeConfigDictDto.getFieldValue().equals(fieldValue)).findFirst().get().getFieldDesc();

    }

    /**
     * 批量上传费项配置
     *
     * @param
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FeeConfigurationApplicationImpl.uploadFeeConfig")
    public String uploadFeeConfig(UploadFeeConfigCmd uploadFeeConfigCmd) {
        //参数校验
        feeConfigurationCheckParam.uploadFeeConfig(uploadFeeConfigCmd);
        //excel解析
//        String returnUrl = excelAllSheetAnalysis(uploadFeeConfigCmd);
        // uploadFeeConfigExcel(uploadFeeConfigCmd);
//        CompletableFuture.runAsync(() -> excelAllSheetAnalysis(uploadFeeConfigCmd), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_IMPORT_HAND_POOL));
        return "";
    }

    /**
     *
     * @param batchInsertSkuCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FeeConfigurationApplicationImpl.batchInsertFixedSku")
    public Boolean batchInsertFixedSku(BatchInsertSkuCmd batchInsertSkuCmd) {
        //参数校验
        feeConfigurationCheckParam.batchInsertFixedSku(batchInsertSkuCmd);
        //入参转换
        String skuListStr = batchInsertSkuCmd.getSkuListStr();
        List<String> skuIdList = Arrays.asList(skuListStr.split(","));

        //入参转换
        FixedSkuConfig query = new FixedSkuConfig();
        query.setSkuIdList(skuIdList);
        //分页查询
        List<FixedSkuConfig> fixedSkuConfigList = fixedSkuConfigRepository.queryFixedSkuConfigList(query);
        if(CollectionUtils.isNotEmpty(fixedSkuConfigList)){
            throw new BusinessException(ProductErrorCode.PRODUCT_SKU_CONFIG_EXIST_CHECK.
                    formatDescription(fixedSkuConfigList.stream().map(FixedSkuConfig::getSkuId).collect(Collectors.toList())));
        }

        List<FixedSkuConfig> fixedSku2List = new ArrayList<>();
        Set<String> skuIdSet = new HashSet<>(skuIdList);
        for (String skuId : skuIdSet) {
            if(!StringUtils.isNumeric(skuId)){
                throw new BusinessException(ProductErrorCode.PRODUCT_SKU_NOT_EXIST_CHECK_FORMAT.formatDescription(skuId));
            }
            FixedSkuConfig fixedSkuConfig = new FixedSkuConfig();
            fixedSkuConfig.setSkuId(skuId);
            fixedSkuConfig.setCreateUser(batchInsertSkuCmd.getErp());
            fixedSkuConfig.setUpdateUser(batchInsertSkuCmd.getErp());
            fixedSku2List.add(fixedSkuConfig);
        }
        //批量插入主表
        fixedSkuConfigRepository.batchSave(fixedSku2List);
        //插入日志表
        saveOperateLog(batchInsertSkuCmd);
        return true;
    }


    /**
     * excel解析
     * excelAllSheetAnalysis
     *
     * @param
     */
    public String excelAllSheetAnalysis(UploadFeeConfigCmd uploadFeeConfigCmd,JdhFile jdhFile) {
        List<FeeConfigExcelRowRecord> feeConfigExcelRowRecords = new ArrayList<>();
        AtomicBoolean flag = new AtomicBoolean(true);
        try {
            log.info("FeeConfigurationApplicationImpl.excelAllSheetAnalysis.uploadFeeConfigCmd={}", JSON.toJSONString(uploadFeeConfigCmd));
            //1、根据fileId 获取真实文件流
            InputStream fileInputStream = fileManageService.get(jdhFile.getFilePath());
            EasyExcel.read(fileInputStream, FeeConfigExcelRowData.class, new AnalysisEventListener<FeeConfigExcelRowData>() {
                @Override
                public void invoke(FeeConfigExcelRowData feeConfigExcelRowData, AnalysisContext context) {
                    log.info("[批量费项配置数据] excel行数据{}", JSON.toJSONString(feeConfigExcelRowData));
                    addFeeConfigRowData(feeConfigExcelRowData, feeConfigExcelRowRecords, context, flag);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                }
            }).doReadAll();
            //处理结果
            //a.修改费项配置
            Map<String, List<FeeConfigExcelRowRecord>> feeConfigRowDataMap = feeConfigExcelRowRecords.stream().collect(Collectors.groupingBy(FeeConfigExcelRowRecord::getSheetName));
            if (flag.get()) {
                UploadFeeConfigCmd cmd = JSON.parseObject(JSON.toJSONString(uploadFeeConfigCmd), UploadFeeConfigCmd.class);
                CompletableFuture.runAsync(() -> updateFeeConfig(cmd, feeConfigRowDataMap, jdhFile), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_IMPORT_HAND_POOL));
                log.info("FeeConfigurationApplicationImpl.excelAllSheetAnalysis has updateFeeConfig");
                return "";
            } else {
                //处理
                byte[] bytes = generateFailRecord(feeConfigRowDataMap);
                InputStream inputStream = new ByteArrayInputStream(bytes);
                String fileName = uploadFeeConfigCmd.getFeeConfigFileId()+ ".xlsx";
                fileManageService.put(fileName, inputStream, FileManageServiceImpl.FolderPathEnum.FILE_MANAGE, null, Boolean.FALSE);
                updateFailFeeConfig(uploadFeeConfigCmd,FileManageServiceImpl.FolderPathEnum.FILE_MANAGE.getPath() + fileName);
            }
        } catch (Exception e) {
            log.error("FeeConfigurationApplicationImpl.excelAllSheetAnalysis has error", e);
        }
        return "";
    }

    /**
     * 上传费项配置保存操作日志
     *
     * @param uploadFeeConfigCmd
     */
    private void saveOperateLog(UploadFeeConfigCmd uploadFeeConfigCmd) {
        OperateLog operateLog = new OperateLog();
        operateLog.setOperator(uploadFeeConfigCmd.getErp());
        operateLog.setOperateType(OperateTypeEnum.UPDATE.getOperateDesc());
        operateLog.setOperateContent(JSON.toJSONString(uploadFeeConfigCmd.getOperateContent()));
        operateLog.setScene(OperateLogSceneEnum.FEE_CONFIG.getOperateLogScene());
        operateLogRepository.save(operateLog);
    }

    /**
     * 删除一口价sku保存操作日志
     *
     * @param deleteSkuCmd
     */
    private void saveOperateLog(DeleteSkuCmd deleteSkuCmd, String skuId) {
        OperateLog operateLog = new OperateLog();
        operateLog.setOperator(deleteSkuCmd.getErp());
        operateLog.setOperateType(OperateTypeEnum.DELETE.getOperateDesc());
        operateLog.setOperateContent(JSON.toJSONString(skuId));
        operateLog.setScene(OperateLogSceneEnum.FIXED_SKU_CONFIG.getOperateLogScene());
        operateLogRepository.save(operateLog);
    }

    /**
     * 批量新增一口价sku保存操作日志
     *
     * @param batchInsertSkuCmd
     */
    private void saveOperateLog(BatchInsertSkuCmd batchInsertSkuCmd) {
        OperateLog operateLog = new OperateLog();
        operateLog.setOperator(batchInsertSkuCmd.getErp());
        operateLog.setOperateType(OperateTypeEnum.ADD.getOperateDesc());
        operateLog.setOperateContent(JSON.toJSONString(batchInsertSkuCmd.getSkuListStr()));
        operateLog.setScene(OperateLogSceneEnum.FIXED_SKU_CONFIG.getOperateLogScene());
        operateLogRepository.save(operateLog);
    }


    /**
     * 生成失败记录字节数组
     *
     * @param feeConfigRowDataMap
     * @return
     */
    private byte[] generateFailRecord(Map<String, List<FeeConfigExcelRowRecord>> feeConfigRowDataMap) {
        log.info("FeeConfigurationApplicationImpl.generateFailRecord2Return.feeConfigRowDataMap={}", JSON.toJSONString(feeConfigRowDataMap));
        //存储输出流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 写入 Excel 数据到输出流
        Integer index = 0;
        ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream).build();

        for (Map.Entry<String, List<FeeConfigExcelRowRecord>> entry : feeConfigRowDataMap.entrySet()) {
            log.info("FeeConfigurationApplicationImpl.generateFailRecord2Return.index={},key={},value={}", index, JSON.toJSONString(entry.getKey()), JSON.toJSONString(entry.getValue()));
            List<FeeConfigExcelRowErrRecord> rowErrRecordList = bulidFeeConfigExcelErrTile();
            List<FeeConfigExcelRowErrRecord> rowErrRecords = JSON.parseArray(JSON.toJSONString(entry.getValue()), FeeConfigExcelRowErrRecord.class);
            rowErrRecordList.addAll(rowErrRecords);
            WriteSheet writeSheet = EasyExcel.writerSheet().sheetNo(index).sheetName(entry.getKey()).build();
            excelWriter.write(rowErrRecordList, writeSheet);
            index++;
        }
        excelWriter.finish();
        try {
            byteArrayOutputStream.close();
        } catch (Exception e) {
            log.error("FeeConfigurationApplicationImpl.generateFailRecord2Return has error", e);
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     *
     * @return
     */
    private List<FeeConfigExcelRowErrRecord> bulidFeeConfigExcelErrTile(){
        List<FeeConfigExcelRowErrRecord> feeConfigExcelRowErrRecords = new ArrayList<>();
        FeeConfigExcelRowErrRecord excelRowErrRecord = new FeeConfigExcelRowErrRecord();
        excelRowErrRecord.setProvinceId("京标省ID");
        excelRowErrRecord.setProvinceName("京标省名称");
        excelRowErrRecord.setCityId("京标市ID");
        excelRowErrRecord.setCityName("京标市名称");
        excelRowErrRecord.setCountyId("京标区县ID");
        excelRowErrRecord.setCountyName("京标区县名称");
        excelRowErrRecord.setTownId("京标乡镇ID");
        excelRowErrRecord.setTownName("京标乡镇名称");
        excelRowErrRecord.setOnSiteFee("上门费");
        excelRowErrRecord.setImmediately("立即预约上门加价");
        excelRowErrRecord.setHoliday("节假日加价");
        excelRowErrRecord.setNightDoorFee("夜间时段");
        excelRowErrRecord.setPeakServiceFee("高峰时段");
        excelRowErrRecord.setUpgrageAngelFee("服务人员升级费价格");
        excelRowErrRecord.setUpgrageSkuList("服务人员升级费sku");
        excelRowErrRecord.setIsPassed("上传状态标记");
        excelRowErrRecord.setErrMsg("错误信息");
        feeConfigExcelRowErrRecords.add(excelRowErrRecord);
        return feeConfigExcelRowErrRecords;
    }
    /**
     * 分页查询一口价商品
     *
     * @param fixedSkuConfigPageRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FeeConfigurationApplicationImpl.queryFixedSkuByPage")
    public PageDto<FixedSkuPageDto> queryFixedSkuByPage(FixedSkuConfigPageRequest fixedSkuConfigPageRequest) {
        //参数校验
        feeConfigurationCheckParam.queryFixedSkuByPage(fixedSkuConfigPageRequest);
        //入参转换
        FixedSkuConfig fixedSkuConfig = FeeConfigurationApplicationConverter.instance.queryPage2FixedSku(fixedSkuConfigPageRequest);
        //分页查询
        Page<FixedSkuConfig> page = fixedSkuConfigRepository.queryPage(fixedSkuConfig);
        //出参转换
        PageDto<FixedSkuPageDto> pageDto = new PageDto<>();
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        if (CollUtil.isNotEmpty(page.getRecords())) {
            pageDto.setList(page.getRecords().stream().map(FeeConfigurationApplicationConverter.instance::convertFixedSku2Dto).collect(Collectors.toList()));
        }
        return pageDto;
    }

    /**
     * 分页查询一口价商品
     *
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FeeConfigurationApplicationImpl.queryAllFixedSkuConfig")
    public Set<String> queryAllFixedSkuConfig() {
        //分页查询
        List<FixedSkuConfig> fixedSkuConfigList = fixedSkuConfigRepository.queryAllFixedSkuConfig();
        Set<String> skuList = new HashSet<>();
        //出参转换
        if (CollUtil.isNotEmpty(fixedSkuConfigList)) {
            skuList = fixedSkuConfigList.stream().map(FixedSkuConfig::getSkuId).collect(Collectors.toSet());
        }
        return skuList;
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FeeConfigurationApplicationImpl.deleteFixedSku")
    public Boolean deleteFixedSku(DeleteSkuCmd deleteSkuCmd) {
        //参数校验
        AssertUtils.nonNull(deleteSkuCmd, ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("deleteSkuCmd"));
        AssertUtils.nonNull(deleteSkuCmd.getId(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("商品id"));
        //查询是否存在
        FixedSkuConfig fixedSkuConfigExist = fixedSkuConfigRepository.find(FixedSkuConfigIdentifier.builder().id(deleteSkuCmd.getId()).build());
        if (Objects.isNull(fixedSkuConfigExist)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR.formatDescription("id不存在"));
        }
        //入参转换
        FixedSkuConfig fixedSkuConfig = FeeConfigurationApplicationConverter.instance.deleteCmd2FixedSku(deleteSkuCmd);
        fixedSkuConfigRepository.deleteById(fixedSkuConfig);
        //插入日志表
        saveOperateLog(deleteSkuCmd, String.valueOf(fixedSkuConfigExist.getSkuId()));
        return true;
    }

    @Override
    public List<FeeConfigDictDto> queryFeeConfigDict(QueryFeeConfigDictRequest queryFeeConfigDictRequest) {
        List<FeeConfigDictDto> feeConfigDictDtos = JSON.parseArray(duccConfig.getFeeConfigDictConfig(), FeeConfigDictDto.class);
        return Optional.of(feeConfigDictDtos).map(List::stream).orElseGet(Stream::empty).
                filter(feeConfigDictDto -> feeConfigDictDto.getFieldType().equals(queryFeeConfigDictRequest.getFieldType())).collect(Collectors.toList());
    }

    /**
     * 修改费项配置ducc和db
     *
     * @param uploadFeeConfigCmd
     * @return
     */
    public Boolean updateFeeConfig(UploadFeeConfigCmd uploadFeeConfigCmd, Map<String, List<FeeConfigExcelRowRecord>> feeConfigAllSheetMap, JdhFile jdhFile) {
        log.info("FeeConfigurationApplicationImpl.updateFeeConfig.uploadFeeConfigCmd={},feeConfigAllSheetMap={},jdhFile={}", JSON.toJSONString(uploadFeeConfigCmd), JSON.toJSONString(feeConfigAllSheetMap), JSON.toJSONString(jdhFile));
        //Step1.根据业务身份+销售渠道Id查询需要删除的redis主键
        FeeConfig feeConfig = feeConfigurationRepository.find(FeeConfigIdentifier.builder().id(uploadFeeConfigCmd.getId()).build());
        //Step3.更新db中的配置url和名称，并维护updateUser和time
        FeeConfig feeConfigUpdate = new FeeConfig();
        feeConfigUpdate.setFeeConfigUrl(jdhFile.getFilePath());
        feeConfigUpdate.setFeeConfigName(uploadFeeConfigCmd.getFeeConfigName());
        feeConfigUpdate.setUpdateUser(uploadFeeConfigCmd.getErp());
        feeConfigUpdate.setFeeConfigId(uploadFeeConfigCmd.getFeeConfigFileId());
        feeConfigUpdate.setId(uploadFeeConfigCmd.getId());
        feeConfigUpdate.setParseStatus(FeeConfigParseStatusEnum.PARSE_SUCC.getCode());
        int ret = feeConfigurationRepository.update(feeConfigUpdate);
        //step4.如果都成功，重新set：redis中的所有key value
        if (ret > 0) {
            feeConfigAllSheetMap.entrySet().stream().forEach(entry -> {
                //参数转换
                List<FeeConfigExcelRowRecord> feeConfigExcelRowRecords = entry.getValue();
                batchSaveOrUpdateFeeConfig(feeConfigExcelRowRecords,feeConfig);
            });
            //保存操作日志
            saveOperateLog(uploadFeeConfigCmd);
        }
        return true;
    }

    /**
     *
     * @param feeConfigExcelRowRecords
     * @param feeConfig
     * @return
     */
    public Boolean batchSaveOrUpdateFeeConfig(List<FeeConfigExcelRowRecord> feeConfigExcelRowRecords,FeeConfig feeConfig) {
        if(CollUtil.isNotEmpty(feeConfigExcelRowRecords)){
            Boolean exists = getTimePeriodAndHomeFeeConfig(feeConfig.getChannelId(),feeConfig.getServiceType());
            if(exists){
                batchUpateFeeConfig(feeConfigExcelRowRecords,feeConfig);
            }else{
                batchSaveFeeConfig(feeConfigExcelRowRecords,feeConfig);
                // 保存地区费项配置
                CompletableFuture.supplyAsync(() -> {
                    return  batchSaveRegionFeeConfig(feeConfigExcelRowRecords,feeConfig);
                },executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
            }
        }
        return true;
    }

    public void batchSaveFeeConfig2(List<FeeConfigExcelRowRecord> feeConfigExcelRowRecords,FeeConfig feeConfig) {
        for(FeeConfigExcelRowRecord feeConfigExcelRowRecord : feeConfigExcelRowRecords){
            String provinceId = feeConfigExcelRowRecord.getProvinceId();
            String cityId = feeConfigExcelRowRecord.getCityId();
            JdhAreaFeeConfigQuery jdhAreaFeeConfigQuery = new JdhAreaFeeConfigQuery();
            jdhAreaFeeConfigQuery.setProvinceCode(provinceId);
            jdhAreaFeeConfigQuery.setCityCode(cityId);
            jdhAreaFeeConfigQuery.setChannelId(feeConfig.getChannelId());
            jdhAreaFeeConfigQuery.setServiceType(Integer.parseInt(feeConfig.getServiceType()));
            List<JdhAreaFeeConfig> jdhAreaFeeConfigList = jdhAreaFeeConfigRepository.queryJdhAreaFeeConfigList(jdhAreaFeeConfigQuery);
            if(CollUtil.isNotEmpty(jdhAreaFeeConfigList)){
                String countyId = feeConfigExcelRowRecord.getCountyId();
                if(StringUtil.isNotEmpty(countyId)){
                    jdhAreaFeeConfigList = jdhAreaFeeConfigList.stream().filter(item -> countyId.equals(item.getCountyCode())).collect(Collectors.toList());
                }
                if(CollUtil.isNotEmpty(jdhAreaFeeConfigList)){
                    String townId = feeConfigExcelRowRecord.getTownId();
                    if(StringUtil.isNotEmpty(townId)){
                        jdhAreaFeeConfigList = jdhAreaFeeConfigList.stream().filter(item -> townId.equals(item.getTownCode())).collect(Collectors.toList());
                    }
                }
            }
        }
    }


    /**
     *
     * @param feeConfigExcelRowRecords
     * @param feeConfig
     * @return
     */
    public Boolean batchSaveFeeConfig(List<FeeConfigExcelRowRecord> feeConfigExcelRowRecords,FeeConfig feeConfig) {
        log.info("FeeConfigurationApplicationImpl -> batchSaveFeeConfig feeConfigExcelRowRecords={}, feeConfig={}", JSON.toJSONString(feeConfigExcelRowRecords), JSON.toJSONString(feeConfig));
        List<JdhAreaFeeConfig> jdhAreaFeeConfigList = new ArrayList<>();
        String sheetKey = feeConfig.getCacheKeyPrefix();
        try{
            for(FeeConfigExcelRowRecord feeConfigExcelRowRecord : feeConfigExcelRowRecords){
                JdhAreaFeeConfig jdhAreaFeeConfig = JdhFeeConfigPoConverter.INSTANCE.convertPo2SaveJdhAreaFeeConfig(feeConfigExcelRowRecord);
                jdhAreaFeeConfig.setAreaFeeConfigId(generateIdFactory.getId());
                jdhAreaFeeConfig.setFeeConfigId(feeConfig.getFeeConfigId());
                jdhAreaFeeConfig.setChannelId(feeConfig.getChannelId());
                jdhAreaFeeConfig.setServiceType(Integer.parseInt(feeConfig.getServiceType()));
                jdhAreaFeeConfigList.add(jdhAreaFeeConfig);
                HomeAndTimeFeeConfig homeAndTimeFeeConfig = new HomeAndTimeFeeConfig();
                homeAndTimeFeeConfig.setOnSiteFee(StringUtils.isNotBlank(feeConfigExcelRowRecord.getOnSiteFee()) ? new BigDecimal(feeConfigExcelRowRecord.getOnSiteFee()) : null);
                homeAndTimeFeeConfig.setImmediately(StringUtils.isNotBlank(feeConfigExcelRowRecord.getImmediately()) ? new BigDecimal(feeConfigExcelRowRecord.getImmediately()) : null);
                homeAndTimeFeeConfig.setHoliday(StringUtils.isNotBlank(feeConfigExcelRowRecord.getHoliday()) ? new BigDecimal(feeConfigExcelRowRecord.getHoliday()) : null);
                homeAndTimeFeeConfig.setNightDoorFee(StringUtils.isNotBlank(feeConfigExcelRowRecord.getNightDoorFee()) ? new BigDecimal(feeConfigExcelRowRecord.getNightDoorFee()) : null);
                homeAndTimeFeeConfig.setPeakServiceFee(StringUtils.isNotBlank(feeConfigExcelRowRecord.getPeakServiceFee()) ? new BigDecimal(feeConfigExcelRowRecord.getPeakServiceFee()) : null);
                homeAndTimeFeeConfig.setDesc(feeConfigExcelRowRecord.getKeyDesc());
                homeAndTimeFeeConfig.setAreaFeeConfigId(jdhAreaFeeConfig.getAreaFeeConfigId());
                jimClient.hSet(sheetKey, feeConfigExcelRowRecord.getKeyValue(), JSON.toJSONString(homeAndTimeFeeConfig));
            }
            jdhAreaFeeConfigRepository.batchSaveJdhAreaFeeConfig(jdhAreaFeeConfigList);
        }catch (Exception e){
            log.error("FeeConfigurationApplicationImpl -> batchSaveFeeConfig error e:", e);
            return false;
        }
        return true;
    }

    public Boolean batchSaveRegionFeeConfig(List<FeeConfigExcelRowRecord> feeConfigExcelRowRecords,FeeConfig feeConfig) {
        try {
            log.info("FeeConfigurationApplicationImpl batchSaveRegionFeeConfig feeConfigExcelRowRecords={}, feeConfig={}", JSON.toJSONString(feeConfigExcelRowRecords), JSON.toJSONString(feeConfig));
            for(FeeConfigExcelRowRecord feeConfigExcelRowRecord : feeConfigExcelRowRecords){
                JdhRegionFeeConfigQuery regionFeeConfigQuery = new JdhRegionFeeConfigQuery();
                regionFeeConfigQuery.setDestPrefix(feeConfig.getCacheKeyPrefix());
                regionFeeConfigQuery.setDestCode(feeConfigExcelRowRecord.getKeyValue());
                List<JdhRegionFeeConfig> regionFeeConfigList = jdhRegionFeeConfigRepository.findList(regionFeeConfigQuery);

                if (CollUtil.isEmpty(regionFeeConfigList)){
                    JdhRegionFeeConfig regionFeeConfig = JdhFeeConfigPoConverter.INSTANCE.convertPo2RegionFeeConfig(feeConfigExcelRowRecord);
                    regionFeeConfig.setRegionFeeConfigId(generateIdFactory.getId());
                    regionFeeConfig.setFeeConfigId(feeConfig.getFeeConfigId());
                    regionFeeConfig.setChannelId(feeConfig.getChannelId());
                    regionFeeConfig.setServiceType(Integer.parseInt(feeConfig.getServiceType()));
                    regionFeeConfig.setDestCode(feeConfigExcelRowRecord.getKeyValue());
                    regionFeeConfig.setDestName(feeConfigExcelRowRecord.getKeyDesc());
                    regionFeeConfig.setDestPrefix(feeConfig.getCacheKeyPrefix());

                    jdhRegionFeeConfigRepository.save(regionFeeConfig);
                }else {
                    JdhRegionFeeConfig regionFeeConfigDB = regionFeeConfigList.get(0);
                    JdhRegionFeeConfig updateRegionFeeConfig = JdhFeeConfigPoConverter.INSTANCE.convertPo2RegionFeeConfig(feeConfigExcelRowRecord);
                    updateRegionFeeConfig.setId(regionFeeConfigDB.getId());
                    updateRegionFeeConfig.setRegionFeeConfigId(regionFeeConfigDB.getRegionFeeConfigId());
                    updateRegionFeeConfig.setDestCode(feeConfigExcelRowRecord.getKeyValue());
                    updateRegionFeeConfig.setDestName(feeConfigExcelRowRecord.getKeyDesc());
                    updateRegionFeeConfig.setDestPrefix(feeConfig.getCacheKeyPrefix());

                    jdhRegionFeeConfigRepository.update(updateRegionFeeConfig);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("FeeConfigurationApplicationImpl batchSaveRegionFeeConfig error e", e);
            return false;
        }
    }

    /**
     *
     * @param feeConfigExcelRowRecords
     * @param feeConfig
     * @return
     */
    public Boolean batchUpateFeeConfig(List<FeeConfigExcelRowRecord> feeConfigExcelRowRecords,FeeConfig feeConfig) {
        log.info("FeeConfigurationApplicationImpl -> batchUpateFeeConfig feeConfigExcelRowRecords={}, feeConfig={}", JSON.toJSONString(feeConfigExcelRowRecords), JSON.toJSONString(feeConfig));
        List<FeeConfigExcelRowRecord> insertFeeConfigExcelRowRecords = new ArrayList<>();
        String cacheKeysPrefix = feeConfig.getChannelId() + "*" + feeConfig.getServiceType();
        List<JdhAreaFeeConfig> updateJdhAreaFeeConfigList = new ArrayList<>();
        for(FeeConfigExcelRowRecord feeConfigExcelRowRecord : feeConfigExcelRowRecords){
            String townId = StringUtil.isBlank(feeConfigExcelRowRecord.getTownId()) ? "" : feeConfigExcelRowRecord.getTownId();
            String countyId = StringUtil.isBlank(feeConfigExcelRowRecord.getCountyId()) ? "" : feeConfigExcelRowRecord.getCountyId();
            String cityId = StringUtil.isBlank(feeConfigExcelRowRecord.getCityId()) ? "" : feeConfigExcelRowRecord.getCityId();
            String provinceId = StringUtil.isBlank(feeConfigExcelRowRecord.getProvinceId()) ? "" : feeConfigExcelRowRecord.getProvinceId();
            String townFeeStr = jimClient.hGet(cacheKeysPrefix,townId);
            String countyFeeStr = jimClient.hGet(cacheKeysPrefix,countyId);
            String cityFeeStr = jimClient.hGet(cacheKeysPrefix,cityId);
            String provinceFeeStr = jimClient.hGet(cacheKeysPrefix,provinceId);
            HomeAndTimeFeeConfig homeAndTimeFeeConfig = null;
            if(StringUtil.isNotBlank(townFeeStr)){
                homeAndTimeFeeConfig = JSON.parseObject(townFeeStr, HomeAndTimeFeeConfig.class);
            }else if(StringUtil.isNotBlank(countyFeeStr)){
                homeAndTimeFeeConfig = JSON.parseObject(countyFeeStr, HomeAndTimeFeeConfig.class);
            }else if(StringUtil.isNotBlank(cityFeeStr)){
                homeAndTimeFeeConfig = JSON.parseObject(cityFeeStr, HomeAndTimeFeeConfig.class);
            }else if(StringUtil.isNotBlank(provinceFeeStr)){
                homeAndTimeFeeConfig = JSON.parseObject(provinceFeeStr, HomeAndTimeFeeConfig.class);
            }
            log.info("FeeConfigurationApplicationImpl -> batchUpateFeeConfig homeAndTimeFeeConfig={}", JSON.toJSONString(homeAndTimeFeeConfig));
            if(Objects.nonNull(homeAndTimeFeeConfig) && homeAndTimeFeeConfig.getAreaFeeConfigId() != null){
                JdhAreaFeeConfig jdhAreaFeeConfig = JdhFeeConfigPoConverter.INSTANCE.convertPo2SaveJdhAreaFeeConfig(feeConfigExcelRowRecord);
                jdhAreaFeeConfig.setAreaFeeConfigId(homeAndTimeFeeConfig.getAreaFeeConfigId());
                jdhAreaFeeConfig.setChannelId(feeConfig.getChannelId());
                jdhAreaFeeConfig.setServiceType(Integer.parseInt(feeConfig.getServiceType()));
                updateJdhAreaFeeConfigList.add(jdhAreaFeeConfig);
            }else{
                insertFeeConfigExcelRowRecords.add(feeConfigExcelRowRecord);
            }
        }
        updateAndSaveFeeConfig(insertFeeConfigExcelRowRecords,updateJdhAreaFeeConfigList,feeConfig);

        // 更新||保存地区费项配置
       CompletableFuture.supplyAsync(() -> {
           return  updateAndSaveRegionFeeConfig(insertFeeConfigExcelRowRecords,updateJdhAreaFeeConfigList,feeConfig);
        },executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
        return true;
    }

    /**
     *
     * @param channelId
     * @param serviceType
     * @return
     */
    private Boolean getTimePeriodAndHomeFeeConfig(String channelId, String serviceType) {
        //Step2.遍历redis中的所有keys
        String cacheKeysPrefix = channelId + "*" + serviceType;
        log.info("FeeConfigurationApplicationImpl#getTimePeriodAndHomeFeeConfig.cacheKeysPrefix={}", JSON.toJSONString(cacheKeysPrefix));
        Boolean exists = jimClient.exists(cacheKeysPrefix);
        return exists;
    }

    /**
     *
     * @param feeConfigExcelRowRecords
     * @param updateJdhAreaFeeConfigList
     * @return
     */
    public Boolean updateAndSaveFeeConfig(List<FeeConfigExcelRowRecord> feeConfigExcelRowRecords,List<JdhAreaFeeConfig> updateJdhAreaFeeConfigList,FeeConfig feeConfig) {
        log.info("FeeConfigurationApplicationImpl.updateAndSaveFeeConfig feeConfigExcelRowRecords={}, updateJdhAreaFeeConfigList={}", JSON.toJSONString(feeConfigExcelRowRecords), JSON.toJSONString(updateJdhAreaFeeConfigList));
        if(CollUtil.isNotEmpty(feeConfigExcelRowRecords)){
            batchSaveFeeConfig(feeConfigExcelRowRecords,feeConfig);
        }
        if(CollUtil.isNotEmpty(updateJdhAreaFeeConfigList)){
            for(JdhAreaFeeConfig jdhAreaFeeConfig : updateJdhAreaFeeConfigList){
                Integer result = jdhAreaFeeConfigRepository.update(jdhAreaFeeConfig);
                if(result == 0){
                    if(StringUtil.isNotBlank(jdhAreaFeeConfig.getProvinceCode())){
                        jdhAreaFeeConfigRepository.updateByCode(jdhAreaFeeConfig);
                    }else {
                        log.info("FeeConfigurationApplicationImpl.updateAndSaveFeeConfig.update fail={}", jdhAreaFeeConfig.getAreaFeeConfigId());
                    }
                }

                HomeAndTimeFeeConfig homeAndTimeFeeConfig = new HomeAndTimeFeeConfig();
                homeAndTimeFeeConfig.setOnSiteFee(StringUtils.isNotBlank(jdhAreaFeeConfig.getOnSiteFee()) ? new BigDecimal(jdhAreaFeeConfig.getOnSiteFee()) : null);
                homeAndTimeFeeConfig.setImmediately(StringUtils.isNotBlank(jdhAreaFeeConfig.getImmediatelyFee()) ? new BigDecimal(jdhAreaFeeConfig.getImmediatelyFee()) : null);
                homeAndTimeFeeConfig.setHoliday(StringUtils.isNotBlank(jdhAreaFeeConfig.getHolidayFee()) ? new BigDecimal(jdhAreaFeeConfig.getHolidayFee()) : null);
                homeAndTimeFeeConfig.setNightDoorFee(StringUtils.isNotBlank(jdhAreaFeeConfig.getNightDoorFee()) ? new BigDecimal(jdhAreaFeeConfig.getNightDoorFee()) : null);
                homeAndTimeFeeConfig.setPeakServiceFee(StringUtils.isNotBlank(jdhAreaFeeConfig.getPeakServiceFee()) ? new BigDecimal(jdhAreaFeeConfig.getPeakServiceFee()) : null);
                homeAndTimeFeeConfig.setDesc(jdhAreaFeeConfig.getKeyDesc());
                homeAndTimeFeeConfig.setAreaFeeConfigId(jdhAreaFeeConfig.getAreaFeeConfigId());
                jimClient.hSet(feeConfig.getCacheKeyPrefix(), jdhAreaFeeConfig.getKeyValue(), JSON.toJSONString(homeAndTimeFeeConfig));
            }
        }
        return true;
    }

    /**
     *
     * @param feeConfigExcelRowRecords
     * @param updateJdhAreaFeeConfigList
     * @return
     */
    public Boolean updateAndSaveRegionFeeConfig(List<FeeConfigExcelRowRecord> feeConfigExcelRowRecords,List<JdhAreaFeeConfig> updateJdhAreaFeeConfigList,FeeConfig feeConfig) {
        try {
            log.info("FeeConfigurationApplicationImpl updateAndSaveRegionFeeConfig feeConfigExcelRowRecords={}, updateJdhAreaFeeConfigList={}", JSON.toJSONString(feeConfigExcelRowRecords), JSON.toJSONString(updateJdhAreaFeeConfigList));
            if(CollUtil.isNotEmpty(feeConfigExcelRowRecords)){
                batchSaveRegionFeeConfig(feeConfigExcelRowRecords,feeConfig);
            }
            if(CollUtil.isNotEmpty(updateJdhAreaFeeConfigList)){
                for(JdhAreaFeeConfig jdhAreaFeeConfig : updateJdhAreaFeeConfigList){
                    JdhRegionFeeConfigQuery regionFeeConfigQuery = new JdhRegionFeeConfigQuery();
                    regionFeeConfigQuery.setDestPrefix(jdhAreaFeeConfig.getCacheKeyPrefix());
                    regionFeeConfigQuery.setDestCode(jdhAreaFeeConfig.getKeyValue());
                    List<JdhRegionFeeConfig> regionFeeConfigList = jdhRegionFeeConfigRepository.findList(regionFeeConfigQuery);
                    if (CollUtil.isEmpty(regionFeeConfigList)){
                        JdhRegionFeeConfig regionFeeConfig = JdhFeeConfigPoConverter.INSTANCE.convertPo2RegionFeeConfig(jdhAreaFeeConfig);
                        regionFeeConfig.setRegionFeeConfigId(generateIdFactory.getId());
                        regionFeeConfig.setFeeConfigId(feeConfig.getFeeConfigId());
                        regionFeeConfig.setChannelId(feeConfig.getChannelId());
                        regionFeeConfig.setServiceType(Integer.parseInt(feeConfig.getServiceType()));
                        regionFeeConfig.setDestCode(jdhAreaFeeConfig.getKeyValue());
                        regionFeeConfig.setDestName(jdhAreaFeeConfig.getKeyDesc());
                        regionFeeConfig.setDestPrefix(feeConfig.getCacheKeyPrefix());

                        jdhRegionFeeConfigRepository.save(regionFeeConfig);
                    }else {
                        JdhRegionFeeConfig regionFeeConfigDB = regionFeeConfigList.get(0);
                        JdhRegionFeeConfig updateRegionFeeConfig = JdhFeeConfigPoConverter.INSTANCE.convertPo2RegionFeeConfig(jdhAreaFeeConfig);
                        updateRegionFeeConfig.setId(regionFeeConfigDB.getId());
                        updateRegionFeeConfig.setRegionFeeConfigId(regionFeeConfigDB.getRegionFeeConfigId());
                        updateRegionFeeConfig.setDestCode(jdhAreaFeeConfig.getKeyValue());
                        updateRegionFeeConfig.setDestName(jdhAreaFeeConfig.getKeyDesc());
                        updateRegionFeeConfig.setDestPrefix(feeConfig.getCacheKeyPrefix());
                        jdhRegionFeeConfigRepository.update(updateRegionFeeConfig);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("FeeConfigurationApplicationImpl updateAndSaveRegionFeeConfig error e", e);
            return false;
        }
    }

    /**
     * 修改费项配置ducc和db
     *
     * @param uploadFeeConfigCmd
     * @return
     */
    public Boolean updateFailFeeConfig(UploadFeeConfigCmd uploadFeeConfigCmd,String filePath) {
        //Step3.更新db中的配置url和名称，并维护updateUser和time
        FeeConfig feeConfigUpdate = new FeeConfig();
        feeConfigUpdate.setFeeConfigUrl(filePath);
        feeConfigUpdate.setFeeConfigName("失败"+uploadFeeConfigCmd.getFeeConfigFileId());
        feeConfigUpdate.setFeeConfigId(uploadFeeConfigCmd.getFeeConfigFileId());
        feeConfigUpdate.setId(uploadFeeConfigCmd.getId());
        feeConfigUpdate.setParseStatus(FeeConfigParseStatusEnum.PARSE_FAIL.getCode());
        feeConfigurationRepository.update(feeConfigUpdate);
        return true;
    }

    /**
     * 向保存的实体中添加数据
     *
     * @param feeConfigExcelRowData
     * @param
     */
    private void addFeeConfigRowData(FeeConfigExcelRowData feeConfigExcelRowData, List<FeeConfigExcelRowRecord> feeConfigRowDatas, AnalysisContext context, AtomicBoolean allFlag) {
        AtomicBoolean flag = new AtomicBoolean(true);
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(feeConfigExcelRowData));
        log.info("UploadFeeConfigListener.checkRowDataValid.jsonObject={}", JSON.toJSONString(jsonObject));
        List<UploadDataDetailDto> uploadDataDetailDtos = jsonObject.keySet().stream().map(key -> {
            UploadDataDetailDto uploadDataDetailDto = new UploadDataDetailDto();
            uploadDataDetailDto.setKeyName(key);
            uploadDataDetailDto.setKeyValue(jsonObject.getString(key));
            log.info("UploadFeeConfigListener.checkRowDataValid.uploadDataDetailDto={}", JSON.toJSONString(uploadDataDetailDto));
            //空参数校验：上门费为空
            List<String> checkNotNull4Fee = JSON.parseArray(duccConfig.getUploadFeeConfigCheckNotNullCollection(), String.class);
            if (checkNotNull4Fee.contains(key) && StringUtils.isBlank(jsonObject.getString(key))) {
                uploadDataDetailDto.setErrorMessage("费项配置不能为空");
                flag.set(false);
                allFlag.set(false);
            }
            //负数参数校验：上门费为负数
            List<String> checkPositive = JSON.parseArray(duccConfig.getUploadFeeConfigCheckPositiveCollection(), String.class);
            if (checkPositive.contains(key)) {
                String value = jsonObject.getString(key);
                if(StringUtil.isNotBlank(value)){
                    if (!NumberUtil.isNumber(value)) {
                        uploadDataDetailDto.setErrorMessage("费项非法数字");
                        flag.set(false);
                        allFlag.set(false);
                    }else{
                        if (new BigDecimal(value).compareTo(BigDecimal.ZERO) < 0) {
                            uploadDataDetailDto.setErrorMessage("费项金额请配置正数，支持两位小数");
                            flag.set(false);
                            allFlag.set(false);
                        }else{
                            String pattern = "^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{1,2})?$";
                            if(!value.matches(pattern)){
                                uploadDataDetailDto.setErrorMessage("费项金额请配置正数，支持两位小数");
                                flag.set(false);
                                allFlag.set(false);
                            }
                        }
                    }
                }
            }
            //空参数校验：地址信息为空
            List<String> checkNotNull4Address = JSON.parseArray(duccConfig.getUploadAddressConfigCheckNotNullCollection(), String.class);
            if (checkNotNull4Address.contains(key) && StringUtils.isBlank(jsonObject.getString(key))) {
                uploadDataDetailDto.setErrorMessage("地址信息为空，至少填写一级地址的省ID和省名称");
                flag.set(false);
                allFlag.set(false);
            }

            if ("upgrageSkuList".equals(key)) {
                String value = jsonObject.getString(key);
                if(StringUtils.isNotBlank(value)){
                     String[] skuList = value.split("\n");
                     for(String skuId : skuList){
                         String pattern = "^(([0-9]{1}\\d*))";
                         if(!skuId.matches(pattern)){
                             uploadDataDetailDto.setErrorMessage("升级SKU非法字符");
                             flag.set(false);
                             allFlag.set(false);
                         }
                     }
                 }
            }

            return uploadDataDetailDto;
        }).collect(Collectors.toList());
        //**地域优先级从细—>粗，进行匹配*//*
        String[] locations = {feeConfigExcelRowData.getTownId() + "," + feeConfigExcelRowData.getTownName(),
                feeConfigExcelRowData.getCountyId() + "," + feeConfigExcelRowData.getCountyName(),
                feeConfigExcelRowData.getCityId() + "," + feeConfigExcelRowData.getCityName(),
                feeConfigExcelRowData.getProvinceId() + "," + feeConfigExcelRowData.getProvinceName()};
        FeeConfigExcelRowRecord feeConfigExcelRowRecord = JSON.parseObject(JSON.toJSONString(feeConfigExcelRowData), FeeConfigExcelRowRecord.class);
        feeConfigExcelRowRecord.setSheetName(context.readSheetHolder().getSheetName());
        feeConfigExcelRowRecord.setIsPassed(String.valueOf(flag.get()));
        if (!flag.get()) {
            StringBuffer errMsgBuffer = new StringBuffer();
            uploadDataDetailDtos.stream().forEach(uploadDataDetailDto -> {
                if (StringUtils.isNotBlank(uploadDataDetailDto.getErrorMessage())) {
                    errMsgBuffer.append(uploadDataDetailDto.getErrorMessage());
                }
            });
            feeConfigExcelRowRecord.setErrMsg(errMsgBuffer.toString());
        }
        for (String location : locations) {
            log.info("FeeConfigurationApplicationImpl.addFeeConfigRowData.feeConfigExcelRowData={},location={}", JSON.toJSONString(feeConfigExcelRowData), JSON.toJSONString(location));
            if (StringUtils.isNotBlank(location)) {
                String[] locationStr = location.split(",");
                if (!locationStr[0].equals("0") && !locationStr[0].equals("null")) {
                    feeConfigExcelRowRecord.setKeyDesc(locationStr[1]);
                    feeConfigExcelRowRecord.setKeyValue(locationStr[0]);
                    log.info("FeeConfigurationApplicationImpl.addFeeConfigRowData.feeConfigExcelRowRecord={}", JSON.toJSONString(feeConfigExcelRowRecord));
                    break;
                }
            }
        }
        feeConfigRowDatas.add(feeConfigExcelRowRecord);
    }

    /**
     * 同步地区费项配置数据
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FeeConfigurationApplicationImpl.syncRegionFeeConfig")
    public Boolean syncRegionFeeConfig(SyncRegionFeeConfigCmd cmd) {
        log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig cmd={}", JSON.toJSONString(cmd));
        // 省市区镇地址信息
        List<String> regionList;
        String regionKey = "JDH_O2O_INIT_REGION_DATA";
        String regionStr = jimClient.get(regionKey);
        if (StringUtils.isNotBlank(regionStr)){
            regionList = JSON.parseArray(regionStr, String.class);
            log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig region hit cache");
        }else {
            // 初始化省市区镇地址信息
            regionList = this.initRegionData();
            jimClient.set(regionKey, JSON.toJSONString(regionList));
        }
        log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig initRegionData regionCount={}, regionFirst={}", regionList.size(), JSON.toJSONString(regionList.get(0)));

        // 查询费项配置
        FeeConfig feeConfigQuery = new FeeConfig();
        List<FeeConfig> feeConfigs = feeConfigurationRepository.queryList(feeConfigQuery);
        log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig feeConfigs={}", JSON.toJSONString(feeConfigs));
        List<String> keyPrefixList = feeConfigs.stream().map(FeeConfig::getCacheKeyPrefix).distinct().collect(Collectors.toList());
        if (cmd != null && StringUtils.isNotBlank(cmd.getChannelId()) && cmd.getServiceType() != null){
            String cacheKeyPrefix = cmd.getChannelId()+"*"+cmd.getServiceType();
            keyPrefixList = keyPrefixList.stream().filter(cacheKeyPrefix::equals).collect(Collectors.toList());
        }
        log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig keyPrefixList={}", JSON.toJSONString(keyPrefixList));

        Map<String, FeeConfig> cacheKeyPrefixMap = feeConfigs.stream().collect(Collectors.toMap(FeeConfig::getCacheKeyPrefix, Function.identity(), (key1, key2) -> key2));
        List<JdhRegionFeeConfig> regionFeeConfigList = new ArrayList<>();
        for (String keyPrefix : keyPrefixList) {
            log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig keyPrefix={}", keyPrefix);
            JdhRegionFeeConfig cleanRegionFeeConfig = new JdhRegionFeeConfig();
            cleanRegionFeeConfig.setDestPrefix(keyPrefix);
            // 清除历史数据
            jdhRegionFeeConfigRepository.clean(cleanRegionFeeConfig);
            if (cmd != null && cmd.getJustClean() != null && cmd.getJustClean()){
                log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig justClean true");
                continue;
            }
            FeeConfig feeConfig = cacheKeyPrefixMap.get(keyPrefix);
            Map<String, String> regionMap = jimClient.hGetAll(keyPrefix);
            log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig hGetAll keyPrefix={},regionMapCount={}", keyPrefix, regionMap.size());
            int importRegionCount = 0;
            for (Map.Entry<String, String> entry : regionMap.entrySet()) {
                Integer destCode = Integer.valueOf(entry.getKey());
                HomeAndTimeFeeConfig homeAndTimeFeeConfig = JSON.parseObject(entry.getValue(), HomeAndTimeFeeConfig.class);
                //log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig destCode={},homeAndTimeFeeConfig={}", destCode, JSON.toJSONString(homeAndTimeFeeConfig));
                // 构建地区费项配置
                JdhRegionFeeConfig regionFeeConfig = this.buildCreateRegionFeeConfig(keyPrefix, feeConfig, destCode, homeAndTimeFeeConfig, regionList);
                //jdhRegionFeeConfigRepository.save(regionFeeConfig);
                regionFeeConfigList.add(regionFeeConfig);
                importRegionCount++;
            }
            log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig amount keyPrefix={},regionMapCount={},importRegionCount={}", keyPrefix, regionMap.size(), importRegionCount);
        }
        log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig regionFeeConfigListCount={}", regionFeeConfigList.size());

        // 一次插入的条数，也就是分批的list大小
        int pointsDataLimit = 500;
        int listSize= regionFeeConfigList.size();
        int maxSize = listSize - 1;
        List<JdhRegionFeeConfig> newList = new ArrayList<>();//新建一个载体list
        for (int i = 0; i < listSize; i++) {
            //分批次处理
            newList.add(regionFeeConfigList.get(i));//循环将数据填入载体list
            if (pointsDataLimit == newList.size() || i == maxSize) {  //载体list达到要求,进行批量操作
                //调用批量插入
                jdhRegionFeeConfigRepository.batchSaveRegionFeeConfig(newList);
                newList.clear();//每次批量操作后,清空载体list,等待下次的数据填入
            }
        }
        log.info("FeeConfigurationApplicationImpl syncRegionFeeConfig end");
        return true;
    }

    private JdhRegionFeeConfig buildCreateRegionFeeConfig(String keyPrefix, FeeConfig feeConfig, Integer destCode, HomeAndTimeFeeConfig homeAndTimeFeeConfig, List<String> regionList) {
        // 地区费项配置
        JdhRegionFeeConfig regionFeeConfig = JdhRegionFeeConfig.builder()
                .regionFeeConfigId(generateIdFactory.getId())
                .serviceType(Integer.valueOf(feeConfig.getServiceType()))
                .channelId(feeConfig.getChannelId())
                .destCode(String.valueOf(destCode))
                .destName(homeAndTimeFeeConfig.getDesc())
                .destPrefix(keyPrefix)
                .onSiteFee(homeAndTimeFeeConfig.getOnSiteFee() == null ? null : homeAndTimeFeeConfig.getOnSiteFee().toString())
                .immediatelyFee(homeAndTimeFeeConfig.getImmediately() == null ? null : homeAndTimeFeeConfig.getImmediately().toString())
                .holidayFee(homeAndTimeFeeConfig.getHoliday() == null ? null : homeAndTimeFeeConfig.getHoliday().toString())
                .nightDoorFee(homeAndTimeFeeConfig.getNightDoorFee() == null ? null : homeAndTimeFeeConfig.getNightDoorFee().toString())
                .peakServiceFee(homeAndTimeFeeConfig.getPeakServiceFee() == null ? null : homeAndTimeFeeConfig.getPeakServiceFee().toString())
                .createTime(new Date())
                .updateTime(new Date())
                .build();

        // 护士上门服务升级费
        Long areaFeeConfigId = homeAndTimeFeeConfig.getAreaFeeConfigId();
        if (areaFeeConfigId != null){
            JdhAreaFeeConfig areaFeeConfig = jdhAreaFeeConfigRepository.find(JdhAreaFeeConfigdentifier.builder().areaFeeConfigId(areaFeeConfigId).build());
            if (areaFeeConfig != null && StringUtils.isNotBlank(areaFeeConfig.getUpgrageAngelFee()) && StringUtils.isNotBlank(areaFeeConfig.getUpgrageSkuList())){
                regionFeeConfig.setUpgrageAngelFee(areaFeeConfig.getUpgrageAngelFee());
                regionFeeConfig.setUpgrageSkuList(areaFeeConfig.getUpgrageSkuList());
            }
        }

        // 匹配区域地址，如 13&&山东&&2@@1000&&济南市&&3@@40488&&历城区&&4@@54433&&遥墙街道&&5
        List<String> regionMatchList = this.matchRegion(destCode, regionList);
        //log.info("FeeConfigurationApplicationImpl buildCreateRegionFeeConfig matchRegion destCode={}, regionMatchList={}", destCode, JSON.toJSONString(regionMatchList));
        regionMatchList.forEach(regionMatch->{
            String[] regionMatchSplit = regionMatch.split("&&");
            String regionCode = regionMatchSplit[0];
            String regionName = regionMatchSplit[1];
            Integer regionLevel = Integer.valueOf(regionMatchSplit[2]);
            if (NumConstant.NUM_2.equals(regionLevel)){
                regionFeeConfig.setProvinceCode(regionCode);
                regionFeeConfig.setProvinceName(regionName);
            }else if (NumConstant.NUM_3.equals(regionLevel)){
                regionFeeConfig.setCityCode(regionCode);
                regionFeeConfig.setCityName(regionName);
            }else if (NumConstant.NUM_4.equals(regionLevel)){
                regionFeeConfig.setCountyCode(regionCode);
                regionFeeConfig.setCountyName(regionName);
            }else if (NumConstant.NUM_5.equals(regionLevel)){
                regionFeeConfig.setTownCode(regionCode);
                regionFeeConfig.setTownName(regionName);
            }
        });
        return regionFeeConfig;
    }

    /**
     * 导出地区费项配置数据
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FeeConfigurationApplicationImpl.exportRegionFeeConfig")
    public Boolean exportRegionFeeConfig(ExportRegionFeeConfigRequest request) {
        log.info("FeeConfigurationApplicationImpl exportRegionFeeConfig request={}", JSON.toJSONString(request));
        // 构建上下文
        Map<String, Object> ctx = new HashMap<>();
        ctx.put("query", request);
        ctx.put("scene", FileExportTypeEnum.REGION_FEE_CONFIG_EXPORT.getType());
        ctx.put("userPin","system");
        ctx.put("operationType", FileExportTypeEnum.REGION_FEE_CONFIG_EXPORT.getType());
        log.info("FeeConfigurationApplicationImpl exportRegionFeeConfig ctx={}", JSON.toJSONString(ctx));
        // 调用通用文件导入能力
        return fileManageApplication.export(ctx);
    }

    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.FeeConfigurationApplicationImpl.cleanExpireRegionFeeConfig")
    public Boolean cleanExpireRegionFeeConfig(CleanExpireRegionFeeConfigCmd cmd) {
        log.info("FeeConfigurationApplicationImpl cleanExpireRegionFeeConfig request={}", JSON.toJSONString(cmd));
        JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(Long.valueOf(cmd.getFeeConfigFileId())).build());
        log.info("FeeConfigurationApplicationImpl cleanExpireRegionFeeConfig jdhFile={}", JSON.toJSONString(jdhFile));
        AssertUtils.nonNull(jdhFile, SupportErrorCode.SUPPORT_FILE_UPLOAD_FILE_NOT_EXIST);

        List<FeeConfigExcelRowRecord> feeConfigExcelRowRecords = new ArrayList<>();
        AtomicBoolean flag = new AtomicBoolean(true);
        // 根据fileId 获取真实文件流
        InputStream fileInputStream = fileManageService.get(jdhFile.getFilePath());
        EasyExcel.read(fileInputStream, FeeConfigExcelRowData.class, new AnalysisEventListener<FeeConfigExcelRowData>() {
            @Override
            public void invoke(FeeConfigExcelRowData feeConfigExcelRowData, AnalysisContext context) {
                log.info("[批量费项配置数据] excel行数据{}", JSON.toJSONString(feeConfigExcelRowData));
                addFeeConfigRowData(feeConfigExcelRowData, feeConfigExcelRowRecords, context, flag);
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).doReadAll();

        log.info("FeeConfigurationApplicationImpl cleanExpireRegionFeeConfig feeConfigExcelRowRecordsCount={}", feeConfigExcelRowRecords.size());
        if (CollUtil.isEmpty(feeConfigExcelRowRecords)){
            return false;
        }
        List<String> destCodeList = feeConfigExcelRowRecords.stream().map(FeeConfigExcelRowRecord::getKeyValue).distinct().collect(Collectors.toList());
        log.info("FeeConfigurationApplicationImpl cleanExpireRegionFeeConfig destCodeCount={}", destCodeList.size());

        String cacheKeyPrefix = cmd.getChannelId()+"*"+cmd.getServiceType();
        Map<String, String> regionMap = jimClient.hGetAll(cacheKeyPrefix);
        log.info("FeeConfigurationApplicationImpl cleanExpireRegionFeeConfig regionMapCount={}", regionMap.size());
        int delCacheCount = 0;
        for (Map.Entry<String, String> entry : regionMap.entrySet()) {
            String destCode = entry.getKey();
            if (!destCodeList.contains(destCode)){
                // 清除缓存
                jimClient.hDel(cacheKeyPrefix, destCode);
                JdhRegionFeeConfig remRegionFeeConfig = new JdhRegionFeeConfig();
                remRegionFeeConfig.setDestPrefix(cacheKeyPrefix);
                remRegionFeeConfig.setDestCode(destCode);
                // 清除db
                jdhRegionFeeConfigRepository.remove(remRegionFeeConfig);
                delCacheCount++;
            }
        }
        log.info("FeeConfigurationApplicationImpl cleanExpireRegionFeeConfig totalCount={}, delCacheCount={}, existCacheCount={}", regionMap.size(), delCacheCount, destCodeList.size());
        return true;
    }

    /**
     * 初始化如：13&&山东&&2@@1000&&济南市&&3@@40488&&历城区&&4@@54433&&遥墙街道&&5
     * @return
     */
    private List<String> initRegionData(){
        List<String> regionList = new ArrayList<>();
        List<JDDistrictBo> provincesList = addressRpc.getProvinces();
        for (JDDistrictBo province : provincesList) {
            Integer provinceId = province.getDistrictCode();
            String provinceName = province.getDistrictName();
            Integer provinceLevel = province.getLevel();
            List<JDDistrictBo> cityList = addressRpc.getChildren(provinceId);
            for (JDDistrictBo city : cityList) {
                Integer cityId = city.getDistrictCode();
                String cityName = city.getDistrictName();
                Integer cityLevel = city.getLevel();
                List<JDDistrictBo> countyList = addressRpc.getChildren(cityId);
                for (JDDistrictBo county : countyList) {
                    Integer countyId = county.getDistrictCode();
                    String countyName = county.getDistrictName();
                    Integer countyLevel = county.getLevel();
                    List<JDDistrictBo> townList = addressRpc.getChildren(countyId);
                    if (CollectionUtils.isEmpty(townList)){
                        String region = provinceId+"&&"+provinceName+"&&"+provinceLevel +"@@"+ cityId+"&&"+cityName+"&&"+cityLevel+"@@"+countyId+"&&"+countyName+"&&"+countyLevel;
                        regionList.add(region);
                        continue;
                    }
                    for (JDDistrictBo town : townList) {
                        Integer townId = town.getDistrictCode();
                        String townName = town.getDistrictName();
                        Integer townLevel = town.getLevel();
                        String region = provinceId+"&&"+provinceName+"&&"+provinceLevel +"@@"+ cityId+"&&"+cityName+"&&"+cityLevel+"@@"+countyId+"&&"+countyName+"&&"+countyLevel+"@@"+townId+"&&"+townName+"&&"+townLevel;
                        regionList.add(region);
                    }
                }
            }
        }
        return regionList;
    }

    /**
     * 匹配区域地址，如 13&&山东&&2@@1000&&济南市&&3@@40488&&历城区&&4@@54433&&遥墙街道&&5
     * @param destCode
     * @param regionList
     * @return
     */
    private List<String> matchRegion(Integer destCode, List<String> regionList){
        List<String> regionMatchList = new ArrayList<>();
        for (String region : regionList) {
            String[] regionSplit = region.split("@@");
            for (String regionCodeName : regionSplit) {
                String[] regionCodeNameSplit = regionCodeName.split("&&");
                String regionCode = regionCodeNameSplit[0];
                int regionLevel = Integer.parseInt(regionCodeNameSplit[2]);
                if (String.valueOf(destCode).equals(regionCode)){
                    String[] regionMatchArr = Arrays.copyOfRange(regionSplit, 0, regionLevel-1);
                    regionMatchList = Arrays.asList(regionMatchArr);
                    return regionMatchList;
                }
            }
        }
        log.info("FeeConfigurationApplicationImpl matchRegion no match destCode={}, regionMatchList={}", destCode, JSON.toJSONString(regionMatchList));
        return regionMatchList;
    }
}
