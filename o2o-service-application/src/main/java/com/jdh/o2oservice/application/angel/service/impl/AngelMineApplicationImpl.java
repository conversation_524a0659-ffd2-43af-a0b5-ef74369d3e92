package com.jdh.o2oservice.application.angel.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.angel.convert.AngelApplicationConverter;
import com.jdh.o2oservice.application.angel.convert.AngelMineApplicationConverter;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.angel.service.AngelMineApplication;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleReadApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.core.domain.angel.context.UpdateServiceInfoContext;
import com.jdh.o2oservice.core.domain.angel.enums.AngelAuditProcessStatusEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelErrorCode;
import com.jdh.o2oservice.core.domain.angel.enums.AngelExtendKeyEnum;
import com.jdh.o2oservice.core.domain.angel.enums.ItemTypeEnum;
import com.jdh.o2oservice.core.domain.angel.model.*;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelExtendQuery;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.angel.service.AngelDomainService;
import com.jdh.o2oservice.export.angel.cmd.UpdateServiceInfoCmd;
import com.jdh.o2oservice.export.angel.dto.AngelDto;
import com.jdh.o2oservice.export.angel.dto.AngelMineDto;
import com.jdh.o2oservice.export.angel.dto.AngelMineSkillDto;
import com.jdh.o2oservice.export.angel.dto.AngelSkillGroupDto;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.angel.query.QueryServiceInfoRequest;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementMoneyDto;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: jdh-o2o-service
 * @description: 服务者 我的 Application Impl
 * @author: luxingchen3
 * @create: 2024-05-06 11:13
 **/
@Service
@Slf4j
public class AngelMineApplicationImpl implements AngelMineApplication {

    @Resource
    private AngelRepository angelRepository;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private AngelWorkApplication angelWorkApplication;

    @Resource
    private AngelApplication angelApplication;


    @Resource
    private JdServiceSettleReadApplication jdServiceSettleReadApplication;

    @Autowired
    private AngelDomainService angelDomainService;

    @Override
    public AngelMineDto queryMine(AngelRequest angelRequest) {
        if (Objects.isNull(angelRequest) || Objects.isNull(angelRequest.getAngelPin())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR,"服务者pin 不能为空");
        }
        // 服务者基本信息
        JdhAngelRepQuery jdhAngelRepQuery = JdhAngelRepQuery.builder()
                .angelPin(angelRequest.getAngelPin())
                .build();
        JdhAngel jdhAngel = angelRepository.queryByUniqueId(jdhAngelRepQuery);
        if(Objects.isNull(jdhAngel)){
            throw new BusinessException(AngelErrorCode.ANGEL_NOT_EXIST);
        }
        // 服务者通用信息
        String auditLink = duccConfig.getAngelMineDefaultMsgMap("auditLink");
        String auditModifyLink = duccConfig.getAngelMineDefaultMsgMap("auditModifyLink");
        String serviceTel = duccConfig.getAngelMineDefaultMsgMap("serviceTel");
        String headImgFallback = duccConfig.getAngelMineDefaultMsgMap("headImgFallback");
        String materialPackageLink = duccConfig.getAngelMineDefaultMsgMap("materialPackageLink");
        String walletLink =duccConfig.getAngelMineDefaultMsgMap("walletLink");
        String menuItem = duccConfig.getAngelMineDefaultMsgMap("menuItem");
        // 获取服务者黑名单
        Set<Long> angelBlackListSet = duccConfig.getAngelBlackListSet();
        // 服务者钱包
        BigDecimal toDaySettleAmount = BigDecimal.ZERO;
        BigDecimal totSettleAmount =  BigDecimal.ZERO;
        AngelSettleQuery angelSettleQuery = new AngelSettleQuery();
        if(!angelBlackListSet.contains(jdhAngel.getAngelId())){
            angelSettleQuery.setAngelId(jdhAngel.getAngelId());
            angelSettleQuery.setUserPin(jdhAngel.getAngelPin());
            try{
                AngelSettlementMoneyDto angelSettlementMoneyDto = jdServiceSettleReadApplication.queryAngelSettlementMoneyDto(angelSettleQuery);
                if(Objects.nonNull(angelSettlementMoneyDto)){
                    toDaySettleAmount = angelSettlementMoneyDto.getToDaySettleAmount();
                    totSettleAmount = angelSettlementMoneyDto.getTotSettleAmount();
                }
            }catch (BusinessException e){
                log.error("[AngelApplicationImpl.queryMine] error获取钱包信息异常 pin:{} e:", JSON.toJSONString(jdhAngel.getAngelPin()), e);
            }


        }
        List<Integer> needAuditModifyLinkList = Lists.newArrayList(AngelAuditProcessStatusEnum.AUDIT_REJECT.getCode(),AngelAuditProcessStatusEnum.AUDIT_GENERAL_REGISTER.getCode());

        // 组装结果
        List<AngelMineDto.MenuItem> menuItemList = JSON.parseArray(menuItem, AngelMineDto.MenuItem.class);

        //当护士已经绑定联盟ID时，替换好物推荐菜单链接
        Long angelUnionId = getAngelUnionId(jdhAngel);
        if(Objects.nonNull(angelUnionId)){
            Optional<AngelMineDto.MenuItem> optinal = menuItemList.stream().filter(s -> "cpsRecommend".equalsIgnoreCase(s.getKey())).findFirst();
            AngelMineDto.MenuItem item = optinal.orElse(null);
            if(Objects.nonNull(item)){
                item.setUrl(duccConfig.getAngelMineDefaultMsgMap("cpsRecommendLink"));
            }
        }

        AngelMineDto angelMineDto = AngelMineDto.builder()
                .angelId(jdhAngel.getAngelId())
                .angelPin(jdhAngel.getAngelPin())
                .angelName(jdhAngel.getAngelName())
                .headImg((jdhAngel.getHeadImg() != null) ? jdhAngel.getHeadImg():headImgFallback)
                .takeOrderStatus(jdhAngel.getTakeOrderStatus())
                .auditProcessStatus(jdhAngel.getAuditProcessStatus())
                .jobNature(jdhAngel.getJobNature())
                .auditLink(needAuditModifyLinkList.contains(jdhAngel.getAuditProcessStatus()) ? auditModifyLink :auditLink)
                .serviceTel(serviceTel)
                .materialPackageLink(materialPackageLink)
                .blackListFlag(angelBlackListSet.contains(jdhAngel.getAngelId()) ? 1:0) // 属于黑名单 为1 不属于为0
                .toDaySettleAmount(toDaySettleAmount)
                .totSettleAmount(totSettleAmount)
                .walletLink(walletLink)
                .menuItemList(menuItemList)
                .userServiceInfo(StringUtils.isNotEmpty(jdhAngel.getCountyCode())&&StringUtils.isNotEmpty(jdhAngel.getTwoDepartmentCode()))
                .alertMessage(menuItemList.get(0).getAlertMessage())
                .cpsInviteCode(getAngelInviteCode(jdhAngel))
                .angelUnionId(angelUnionId)
                .build();

        return angelMineDto;
    }

    /**
     * 查询护士邀请码
     * @param jdhAngel
     * @return
     */
    private Integer getAngelInviteCode(JdhAngel jdhAngel){
        JdhAngelExtendQuery query = JdhAngelExtendQuery.builder().angelId(jdhAngel.getAngelId()).extendKeyList(Arrays.asList(AngelExtendKeyEnum.CPS_INVITE_CODE.getFiledKey())).build();
        List<JdhAngelExtend> angelExtendList = angelRepository.findAngelExtendList(query);
        if(CollectionUtils.isEmpty(angelExtendList)){
            angelApplication.saveAngelInviteCode(jdhAngel.getAngelId());
            angelExtendList = angelRepository.findAngelExtendList(query);
        }
        if(CollectionUtils.isNotEmpty(angelExtendList) && Objects.nonNull(angelExtendList.get(0))){
            String inviteCodeStr = angelExtendList.get(0).getValue();
            if (Objects.nonNull(inviteCodeStr) && inviteCodeStr.matches("\\d+")){
                return Integer.parseInt(inviteCodeStr);
            }
        }
        return null;
    }

    /**
     * 查询护士绑定联盟ID
     * @param jdhAngel
     * @return
     */
    private Long getAngelUnionId(JdhAngel jdhAngel){
        JdhAngelExtendQuery query = JdhAngelExtendQuery.builder().angelId(jdhAngel.getAngelId()).extendKeyList(Arrays.asList(AngelExtendKeyEnum.CPS_JF_UNION_ID.getFiledKey())).build();
        List<JdhAngelExtend> angelExtendList = angelRepository.findAngelExtendList(query);
        if(CollectionUtils.isNotEmpty(angelExtendList) && Objects.nonNull(angelExtendList.get(0))){
            String inviteCodeStr = angelExtendList.get(0).getValue();
            if (Objects.nonNull(inviteCodeStr) && inviteCodeStr.matches("\\d+")){
                return Long.parseLong(inviteCodeStr);
            }
        }
        return null;
    }

    @Override
    public AngelMineSkillDto querySkill(AngelRequest angelRequest) {
        if (Objects.isNull(angelRequest) || Objects.isNull(angelRequest.getAngelPin())) {
            throw new ArgumentsException(BusinessErrorCode.ILLEGAL_ARG_ERROR,"服务者pin 不能为空");
        }
        log.info("AngelApplicationImpl -> queryAngelDetail angelRequest:{}", JSON.toJSONString(angelRequest));
        String serviceTel = duccConfig.getAngelMineDefaultMsgMap("serviceTel");
        JdhAngelRepQuery jdhAngelRepQuery = JdhAngelRepQuery.builder()
                .angelPin(angelRequest.getAngelPin())
                .build();
        JdhAngel jdhAngel = angelRepository.queryByUniqueId(jdhAngelRepQuery);
        if(Objects.isNull(jdhAngel)){
            //throw new BusinessException(AngelErrorCode.ANGEL_NOT_EXIST);
            return AngelMineSkillDto.builder()
                    .angelSkillGroupDtos(new ArrayList<AngelSkillGroupDto>())
                    .serviceTel(serviceTel)
                    .build();
        }
        jdhAngelRepQuery.setAngelId(jdhAngel.getAngelId());

        JdhAngel jdhAngelDetail = angelRepository.queryAngelDetail(jdhAngelRepQuery);
        if(Objects.isNull(jdhAngelDetail) || CollectionUtils.isEmpty(jdhAngelDetail.getJdhAngelSkillRelList())){
            return AngelMineSkillDto.builder()
                    .angelSkillGroupDtos(new ArrayList<AngelSkillGroupDto>())
                    .serviceTel(serviceTel)
                    .build();
        }
        List<JdhAngelSkillRel> jdhAngelSkillRelList = jdhAngelDetail.getJdhAngelSkillRelList();
        log.debug("AngelApplicationImpl -> queryAngelDetail jdhAngelSkillRelList:{}", JSON.toJSONString(jdhAngelSkillRelList));
        List<JdhAngelSkillAvail> jdhAngelSkillAvailList = new ArrayList<>();
        for (JdhAngelSkillRel jdhAngelSkillRel : jdhAngelSkillRelList) {
            JdhAngelSkillAvail jdhAngelSkillAvail = JdhAngelSkillAvail.builder()
                    .angelSkillCode(jdhAngelSkillRel.getAngelSkillCode())
                    .angelSkillName(jdhAngelSkillRel.getAngelSkillName())
                    .itemType(jdhAngelSkillRel.getItemType())
                    .status(1) // 开通状态的服务技能
                    .build();
            jdhAngelSkillAvailList.add(jdhAngelSkillAvail);
        }

        Map<Integer, List<JdhAngelSkillAvail>> groupedByItemType = jdhAngelSkillAvailList.stream()
                .collect(Collectors.groupingBy(JdhAngelSkillAvail::getItemType));

        // 将分组后的数据转换为 AngelSkillGroup 列表

        List<JdhAngelSkillGroup> jdhAngelSkillGroupList = groupedByItemType.entrySet().stream()
                .filter(entry -> ! ItemTypeEnum.SUPPLIER_HEALTH_EXAM.getCode().equals(entry.getKey())) //过滤
                .map(entry -> {
                    Integer itemType = entry.getKey();
                    String itemTypeDesc = ItemTypeEnum.getDescriptionByCode(entry.getKey());

                    List<JdhAngelSkillAvail> avails = entry.getValue();
                    return JdhAngelSkillGroup.builder()
                            .itemTypeDesc(itemTypeDesc)
                            .itemType(itemType)
                            .jdhAngelSkillAvailList(avails)
                            .build();
                })
                .collect(Collectors.toList());
        log.info("AngelApplicationImpl -> queryAngelDetail jdhAngelSkillGroupList:{}", JSON.toJSONString(jdhAngelSkillGroupList));
        List<AngelSkillGroupDto> angelSkillGroupDtos = AngelMineApplicationConverter.INS.convert2JdhAngelSkillGroupDtoList(jdhAngelSkillGroupList);

        return AngelMineSkillDto.builder()
                        .angelSkillGroupDtos(angelSkillGroupDtos)
                    .serviceTel(serviceTel).
                build();
    }

    /**
     * 护士端-我的-维护服务信息
     * @param updateServiceInfoCmd
     * @return
     */
    @Override
    public Boolean updateServiceInfo(UpdateServiceInfoCmd updateServiceInfoCmd) {
        UpdateServiceInfoContext updateServiceInfoContext = AngelMineApplicationConverter.INS.toUpdateServiceInfoContext(updateServiceInfoCmd);
        Boolean result = angelDomainService.updateServiceInfo(updateServiceInfoContext);
        return result;
    }

    @Override
    public AngelDto queryServiceInfo(QueryServiceInfoRequest queryServiceInfoRequest) {
        JdhAngelRepQuery jdhAngelRepQuery = JdhAngelRepQuery.builder()
                .angelPin(queryServiceInfoRequest.getAngelPin())
                .build();
        JdhAngel jdhAngel = angelRepository.queryByUniqueId(jdhAngelRepQuery);
        AngelDto angelDto = AngelMineApplicationConverter.INS.toAngelDto(jdhAngel);
        return angelDto;
    }


    private Date getRecentMonthDate(Date endDate, int amount) {
        Calendar calendar = Calendar.getInstance();
        if(endDate != null){
            calendar.setTime(endDate); // 设置起始日期
        }
        // 向前推一个月
        calendar.add(Calendar.MONTH, -1*amount);

        return calendar.getTime();
    }
}
