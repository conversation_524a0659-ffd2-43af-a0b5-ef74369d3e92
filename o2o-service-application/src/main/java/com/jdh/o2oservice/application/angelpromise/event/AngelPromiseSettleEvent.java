package com.jdh.o2oservice.application.angelpromise.event;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.EventConsumerRetryTemplate;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.trade.enums.TradeEventTypeEnum;
import com.jdh.o2oservice.core.domain.trade.event.OrderRefundAngelAountEventBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName:AngelPromiseSettleEvent
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/6/11 16:23
 * @Vserion: 1.0
 **/
@Component
@Slf4j
public class AngelPromiseSettleEvent {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    /**
     * 注册consumer
     */
    @PostConstruct
    public void registerEventConsumer() {

        /** 工单逆向护士结算价变更事件 */
        eventConsumerRegister.register(TradeEventTypeEnum.ORDER_REFUND_ANGEL_SETTLE_AMOUNT,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL_PROMISE, "angelWorkRefundAmount", this::angelWorkRefundAmount, Boolean.TRUE, EventConsumerRetryTemplate.defaultInstance()));

    }

    /**
     * 护士退款金额变更事件
     *
     * @param event
     */
    private void angelWorkRefundAmount(Event event) {
        log.info("[AngelPromiseSettleEvent.angelWorkRefundAmount],工单发送退款,结算价发生变更!event={}", JSON.toJSONString(event));
        OrderRefundAngelAountEventBody eventBody = JSON.parseObject(event.getBody(), OrderRefundAngelAountEventBody.class);
        if(Objects.isNull(eventBody) || Objects.isNull(eventBody.getSettleSubtractAmount())){
            log.error("[AngelPromiseSettleEvent.angelWorkRefundAmount],没有发送预估价变更!");
            return;
        }
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(eventBody.getPromiseId());
        List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
        if(CollectionUtils.isEmpty(angelWorkList)){
            log.error("[AngelPromiseSettleEvent.angelWorkRefundAmount],工单信息不存在!");
            return;
        }

        //预估收入扣减
        AngelWork angelWork = angelWorkList.get(0);
        if(Objects.isNull(angelWork.getAngelCharge()) || angelWork.getAngelCharge().compareTo(eventBody.getSettleSubtractAmount()) < 0){
            log.error("[AngelPromiseSettleEvent.angelWorkRefundAmount],扣减的服务费大于预估服务费,无法操作!");
            return;
        }
//        angelWork.addAngelChargeDetail(eventBody.getSettleSubtractAmount().negate(), new BigDecimal(0));
        angelWork.setAngelCharge(angelWork.getAngelCharge().subtract(eventBody.getSettleSubtractAmount()));
        int save = angelWorkRepository.save(angelWork);

        if(save <= CommonConstant.ZERO) {
            throw new BusinessException(AngelPromiseBizErrorCode.DEDUCT_AMOUNT_ERROR);
        }
    }

}
