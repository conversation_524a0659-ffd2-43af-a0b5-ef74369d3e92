package com.jdh.o2oservice.application.promise.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.promise.model.O2oPromiseIndex;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseIndexPageQuery;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.export.promise.dto.O2oPromiseIndexDto;
import com.jdh.o2oservice.export.promise.query.StoreProgramPromiseQuery;
import com.jdh.o2oservice.export.trade.cmd.JdOrderFullSaveCmd;

import java.util.List;

/**
 * PromiseTransformApplication
 *
 * <AUTHOR>
 * @date 2024/08/07
 */
public interface PromiseTransformApplication {

    /**
     * 将履约单的数据转存到es
     *
     * @param verticalBusiness
     * @param cmd
     * @return
     */
    boolean transformEs(JdhVerticalBusiness verticalBusiness, JdOrderFullSaveCmd cmd);

    /**
     * 查询履约单列表信息
     *
     * @param storeProgramPromiseQuery
     * @return
     */
    List<O2oPromiseIndexDto> queryList(StoreProgramPromiseQuery storeProgramPromiseQuery);

    /**
     * 分页查询履约单信息
     *
     * @param storeProgramPromiseQuery
     * @return
     */
    Page<O2oPromiseIndex> queryPage(StoreProgramPromiseQuery storeProgramPromiseQuery);
}
