package com.jdh.o2oservice.application.provider.convert;

import com.jd.health.xfyl.merchant.export.param.supplier.store.StoreInfoQueryParam;
import com.jdh.o2oservice.core.domain.provider.bo.StationAddressBo;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.context.AppointmentMigrationContext;
import com.jdh.o2oservice.core.domain.provider.context.ProviderStoreContext;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.query.ProviderStoreDetailQuery;
import com.jdh.o2oservice.core.domain.provider.query.QueryMerchantStoreListByParamQuery;
import com.jdh.o2oservice.export.laboratory.cmd.AddQuickMerchantStoreRequest;
import com.jdh.o2oservice.export.laboratory.cmd.AppointmentMigrationRequest;
import com.jdh.o2oservice.export.laboratory.cmd.UpdateQuickMerchantStoreRequest;
import com.jdh.o2oservice.export.laboratory.dto.QueryMerchantStoreDetailResponse;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreDetailByParamRequest;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreListByParamRequest;
import com.jdh.o2oservice.export.provider.cmd.JdhStationServiceItemRelCreateCmd;
import com.jdh.o2oservice.export.provider.cmd.JdhStationServiceItemRelDeleteCmd;
import com.jdh.o2oservice.export.provider.cmd.JdhStationServiceItemRelUpdateCmd;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.StationAddressDto;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import com.jdh.o2oservice.export.provider.query.StationAddressRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * ProviderStoreApplicationConverter
 *
 * <AUTHOR>
 * @date 2023/12/12
 */
@Mapper
public interface ProviderStoreApplicationConverter {

    /**
     * JdhVoucherDomainConverter
     */
    ProviderStoreApplicationConverter INSTANCE = Mappers.getMapper(ProviderStoreApplicationConverter.class);

    /**
     * 对象转换
     *
     * @param cmd cmd
     * @return bo
     */
    @Mapping(source = "samplingWay", target = "specimenWay")
    @Mapping(source = "simplePreserveDuration", target = "specimenPreserveDuration")
    @Mapping(source = "simpleNum", target = "specimenNum")
    @Mapping(source = "simplePreserveCondition", target = "specimenPreserveCondition")
    JdhStationServiceItemRel createCmdToModel(JdhStationServiceItemRelCreateCmd cmd);

    /**
     * 对象转换
     *
     * @param cmd cmd
     * @return bo
     */
    @Mapping(source = "samplingWay", target = "specimenWay")
    @Mapping(source = "simplePreserveDuration", target = "specimenPreserveDuration")
    @Mapping(source = "simpleNum", target = "specimenNum")
    @Mapping(source = "simplePreserveCondition", target = "specimenPreserveCondition")
    JdhStationServiceItemRel updateCmdToModel(JdhStationServiceItemRelUpdateCmd cmd);

    /**
     * 对象转换
     *
     * @param cmd cmd
     * @return bo
     */
    JdhStationServiceItemRel deleteCmdToModel(JdhStationServiceItemRelDeleteCmd cmd);

    /**
     * 对象转换
     *
     * @param request request
     * @return bo
     */
    JdhStationServiceItemRel requestToModel(JdhStationServiceItemRelRequest request);

    /**
     * 对象转换
     *
     * @param model model
     * @return bo
     */
    @Mapping(source = "specimenWay", target = "samplingWay")
    @Mapping(source = "specimenPreserveDuration", target = "simplePreserveDuration")
    @Mapping(source = "specimenNum", target = "simpleNum")
    @Mapping(source = "specimenPreserveCondition", target = "simplePreserveCondition")
    @Mapping(source = "specimenType", target = "sampleType")
    JdhStationServiceItemRelDto modelToDto(JdhStationServiceItemRel model);

    /**
     * 对象转换
     *
     * @param model model
     * @return bo
     */
    List<JdhStationServiceItemRelDto> modelToDtoList(List<JdhStationServiceItemRel> model);


    /**
     * 对象转换
     *
     * @param storeInfoBos
     * @return bo
     */
    List<StoreInfoDto> boToDtoList(List<StoreInfoBo> storeInfoBos);


    /**
     * 对象转换
     *
     * @param request
     * @return bo
     */
    StoreInfoQueryParam storeAddressReq(StationAddressRequest request);


    /**
     * 对象转换
     *
     * @param storeInfoBo
     * @return bo
     */
    StationAddressDto storeAddressBoToDto(StationAddressBo storeInfoBo);


    /**
     * 对象转换
     *
     * @param storeInfoBos
     * @return bo
     */
    List<StationAddressDto> storeAddressBoToDtoList(List<StationAddressBo> storeInfoBos);

    @Mapping(source = "lng",target = "storeLng")
    @Mapping(source = "lat",target = "storeLat")
    ProviderStoreContext toProviderStoreContext(AddQuickMerchantStoreRequest addQuickMerchantStoreRequest);

    ProviderStoreDetailQuery toProviderStoreDetailQuery(UpdateQuickMerchantStoreRequest updateQuickMerchantStoreRequest);

    @Mapping(source = "lng",target = "storeLng")
    @Mapping(source = "lat",target = "storeLat")
    ProviderStoreContext toProviderStoreContext(UpdateQuickMerchantStoreRequest updateQuickMerchantStoreRequest);

    ProviderStoreDetailQuery toProviderStoreDetailQuery(QueryMerchantStoreDetailByParamRequest queryMerchantStoreDetailByParamRequest);

    QueryMerchantStoreDetailResponse toQueryMerchantStoreDetailResponse(StoreInfoBo storeInfoBo);

    @Mapping(source = "status",target = "storeStatus")
    QueryMerchantStoreListByParamQuery toQueryMerchantStoreListByParamQuery(QueryMerchantStoreListByParamRequest queryMerchantStoreListByParamRequest);

    AppointmentMigrationContext toAppointmentMigrationContext(AppointmentMigrationRequest appointmentMigrationRequest);
}
