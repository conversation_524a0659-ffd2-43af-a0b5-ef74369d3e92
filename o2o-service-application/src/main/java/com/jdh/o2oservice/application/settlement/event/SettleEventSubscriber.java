package com.jdh.o2oservice.application.settlement.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.settlement.convert.SettlementApplicationConvert;
import com.jdh.o2oservice.application.settlement.handler.SettlementEbsUtil;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleApplication;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleReadApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.SendpayValueEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.EventConsumerRetryTemplate;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.common.enums.EbsSettleSplitTypeEnum;
import com.jdh.o2oservice.common.enums.EbsSettleTypeEnum;
import com.jdh.o2oservice.common.enums.HasAddedEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkHistoryRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkHistoryDbQuery;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkEventTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelWorkEventBody;
import com.jdh.o2oservice.core.domain.angelpromise.model.*;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.event.VoucherEventBody;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.settlement.bo.OrderAngelSettleDetailBo;
import com.jdh.o2oservice.core.domain.settlement.bo.OrderAngelSettleFeeBo;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.model.JdhAngelWorkSettleSnapshot;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementEbs;
import com.jdh.o2oservice.core.domain.settlement.repository.JdhAngelWorkSettleSnapshotRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.query.AngelWorkSettleSnapshotDBQuery;
import com.jdh.o2oservice.core.domain.trade.bo.OrderAngelRefundServiceBo;
import com.jdh.o2oservice.core.domain.trade.bo.OrderRefundDetailBo;
import com.jdh.o2oservice.core.domain.trade.bo.RefundSuccSettleDetailBo;
import com.jdh.o2oservice.core.domain.trade.enums.RefundTypeEnum;
import com.jdh.o2oservice.core.domain.trade.enums.TradeEventTypeEnum;
import com.jdh.o2oservice.core.domain.trade.event.OrderCompleteEventBody;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderRefundTask;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRefundTaskRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.export.medicalpromise.cmd.MedicalPromiseSettleStatusCmd;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.promise.cmd.VoucherExpireSettleCmd;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.settlement.SettlementEbsRequest;
import com.jdh.o2oservice.export.settlement.dto.AngelSettlementDto;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import com.jdh.o2oservice.export.trade.query.JdServiceSettleParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 结算领域事件订阅
 * <AUTHOR>
 * @date 2024-5-24 7:34 下午
 */
@Slf4j
@Service
public class SettleEventSubscriber {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;
    /**
     * redisLockUtil
     */
    @Resource
    private RedisLockUtil redisLockUtil;
    /**
     *
     */
    @Resource
    private JdServiceSettleReadApplication serviceSettleReadApplication;

    /**
     * 查询服务者结算价
     */
    @Resource
    private JdServiceSettleApplication jdServiceSettleApplication;
    /**
     * jdOrderRefundTaskRepository
     */
    @Resource
    private JdOrderRefundTaskRepository jdOrderRefundTaskRepository;
    /**
     * jdOrderApplication
     */
    @Resource
    private JdOrderApplication jdOrderApplication;

    /**
     * jdhAngelWorkSettleSnapshotRepository
     */
    @Autowired
    private JdhAngelWorkSettleSnapshotRepository jdhAngelWorkSettleSnapshotRepository;
    /**
     * angelWorkRepository
     */
    @Resource
    private AngelWorkRepository angelWorkRepository;

    /**
     * angelWorkHistoryRepository
     */
    @Resource
    private AngelWorkHistoryRepository angelWorkHistoryRepository;

    /**
     *
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     * 线程池
     */
    @Resource
    ExecutorPoolFactory executorPoolFactory;
    /**
     * promiseApplication
     */
    @Resource
    private PromiseApplication promiseApplication;
    /**
     * medicalPromiseApplication
     */
    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;

    @Resource
    private TradeApplication tradeApplication;
    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository jdhPromiseRepository;
    /**
     * jdhVoucherApplication
     */
    @Resource
    private VoucherApplication voucherApplication;



    /**
     * 注册事件使用者
     */
    @PostConstruct
    public void registerEventConsumer() {

        eventConsumerRegister.register(AngelWorkEventTypeEnum.ANGEL_WORK_EVENT_RECEIVED,
                WrapperEventConsumer.newInstance(DomainEnum.SETTLE_MENT, "dispatchAngelSettleAmount", this::dispatchAngelSettleAmount, Boolean.TRUE, Boolean.FALSE));

        // 人+sku纬度 服务完成
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_SKU_PATIENT_ALL_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.SETTLE_MENT,
                "serviceFinishForSettle", this::serviceFinishForSettle, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

//        // 人纬度 服务完成
//        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_PATIENT_ALL_GENERATE_REPORT, WrapperEventConsumer.newInstance(DomainEnum.SETTLE_MENT,
//                "serviceFinishForSettle", this::patientServiceFinishForSettle, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));
        // 护理完成
        eventConsumerRegister.register(MedPromiseEventTypeEnum.MED_PROMISE_PATIENT_SERVICE_COMPLETE, WrapperEventConsumer.newInstance(DomainEnum.SETTLE_MENT,
                "serviceFinishForSettle", this::serviceFinishForSettle, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        // 退款成功开始结算
        eventConsumerRegister.register(TradeEventTypeEnum.SELF_ORDER_SUCC_REFUND, WrapperEventConsumer.newInstance(DomainEnum.SETTLE_MENT,
                "selfRefundTaskSuccSettleProcessor", this::selfRefundTaskSuccSettleProcessor, Boolean.TRUE, Boolean.TRUE,EventConsumerRetryTemplate.exponentialRetryInstance(3,1000,2.0,30000)));

        // 订单完成
        eventConsumerRegister.register(TradeEventTypeEnum.SELF_ORDER_COMPLETE, WrapperEventConsumer.newInstance(DomainEnum.SETTLE_MENT,
                "selfOrderCompleteProcessor", this::selfOrderCompleteProcessor, Boolean.TRUE, Boolean.FALSE));

        // 服务单过期事件，推送实收ebs
        eventConsumerRegister.register(PromiseEventTypeEnum.VOUCHER_EXPIRE, WrapperEventConsumer.newInstance(DomainEnum.PROMISE,
                "autoAppointment", this::voucherExpire, Boolean.TRUE));
        // 无订单作废履约单，退款结算
        eventConsumerRegister.register(PromiseEventTypeEnum.PROMISE_INVALID, WrapperEventConsumer.newInstance(DomainEnum.SETTLE_MENT,
                "promiseInvalidSettleProcessor", this::promiseInvalidSettleProcessor, Boolean.TRUE, Boolean.TRUE));

        eventConsumerRegister.register(SettleEventTypeEnum.ANGEL_SETTLE_BY_WORK_SHIP,
                WrapperEventConsumer.newInstance(DomainEnum.SETTLE_MENT, "workTaskFinishToSetle", this::workTaskFinishToSetle, Boolean.FALSE, Boolean.FALSE,
                        EventConsumerRetryTemplate.exponentialRetryInstance(4, 1000, 2.0, 30000)));

    }


    /**
     * 处理服务工单状态
     *
     * @return boolean
     */
    public void workTaskFinishToSetle(Event event) {
        log.info("[SettleEventSubscriber.workTaskFinishToSetle],任务状态执行事件开始!event={}", JSON.toJSONString(event));
        String aggregateId = event.getAggregateId();
        if(StringUtils.isBlank(aggregateId)){
            log.error("[SettleEventSubscriber.workTaskFinishToSetle],任务状态执行事件失败!event={}", com.alibaba.fastjson.JSON.toJSONString(event));
            throw new BusinessException(AngelPromiseBizErrorCode.EVENT_INFO_ERROR);
        }
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.JDH_APPOINTMENT_SETTLE_LOCK_KEY, aggregateId);
        boolean isLock = redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.JDH_APPOINTMENT_SETTLE_LOCK_KEY.getExpireTime(), RedisKeyEnum.JDH_APPOINTMENT_SETTLE_LOCK_KEY.getExpireTimeUnit());
        if(!isLock){
            log.error("[SettleEventSubscriber.workTaskFinishToSetle],工单状态变更获取分布式锁失败!, event={}", JSON.toJSONString(event));
            return;
        }
        try{
            AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(Long.valueOf(aggregateId)));
            if(Objects.isNull(angelWork)){
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
            }
            taskDeliveryForSettle(angelWork);
        }finally {
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     *
     * @param angelWork
     */
    private void taskDeliveryForSettle(AngelWork angelWork) {
        Long promiseId = angelWork.getPromiseId();
        MedicalPromiseRequest medicalPromiseRequest = new MedicalPromiseRequest();
        medicalPromiseRequest.setPromiseId(promiseId);
        List<MedicalPromiseDTO> res = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseRequest);
        if(CollUtil.isEmpty(res)){
            log.info("SettleEventSubscriber -> serviceFinishForSettle promiseId:{}",angelWork.getPromiseId());
            return;
        }
        Map<Long,List<MedicalPromiseDTO>> map = res.stream().filter(item -> item.getSettleStatus() == 0).collect(Collectors.groupingBy(MedicalPromiseDTO::getServiceId));
        for (Map.Entry<Long, List<MedicalPromiseDTO>> entry: map.entrySet()) {
            MedicalPromiseEventBody body = new MedicalPromiseEventBody();
            body.setServiceId(String.valueOf(entry.getKey()));
            List<MedicalPromiseDTO> medicalPromiseDTOList = entry.getValue();
            medicalPromiseDTOList.forEach(medicalPromiseDTO -> {
                body.setPromisePatientId(medicalPromiseDTO.getPromisePatientId());
                body.setPromiseId(medicalPromiseDTO.getPromiseId());
                body.setMedicalPromiseId(medicalPromiseDTO.getMedicalPromiseId());
                body.setVerticalCode(medicalPromiseDTO.getVerticalCode());
                body.setServiceType(medicalPromiseDTO.getServiceType());
                body.setFinishState(false);
                serviceFinishForSettleOfBody(body);
            });
        }
    }

    /**
     * 服务单过期,记实收推送ebs
     *
     * @param event
     */
    private void voucherExpire(Event event) {
        log.info("[VoucherEventSubscriber -> voucherExpire],收到服务单过期事件!event={}", JSON.toJSONString(event));
        VoucherEventBody eventBody = JSON.parseObject(event.getBody(), VoucherEventBody.class);

        VoucherExpireSettleCmd settleCmd = new VoucherExpireSettleCmd();
        settleCmd.setVoucherId(event.getAggregateId());
        settleCmd.setSourceVoucherId(eventBody.getSourceVoucherId());
        settleCmd.setVoucherId(eventBody.getVoucherId());
        settleCmd.setSkuId(eventBody.getServiceId());
        settleCmd.setServiceId(Objects.nonNull(eventBody.getServiceId()) ? String.valueOf(eventBody.getServiceId()) : null);
        settleCmd.setExpireTime(eventBody.getExpireTime());
        voucherApplication.voucherExpireSettle(settleCmd);
    }

    /**
     * 订单完成
     * @param event
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.settlement.event.SettleEventSubscriber.selfOrderCompleteProcessor")
    private void selfOrderCompleteProcessor(Event event) {
        log.info("SettleEventSubscriber -> selfOrderCompleteProcessor event:{}", JSON.toJSONString(event));
        OrderCompleteEventBody body = JSON.parseObject(event.getBody(), OrderCompleteEventBody.class);
        Long orderId = body.getOrderId();
        log.info("SettleEventSubscriber -> selfOrderCompleteProcessor orderId:{}", JSON.toJSONString(orderId));
        //s1.查询订单详情
        JdOrder orderDetail = jdOrderRepository.findOrderDetail(JdOrder.builder().orderId(orderId).build());
        log.info("SettleEventSubscriber.handleAddItemMergeLogic.orderDetail={}",JSON.toJSONString(orderDetail));
        // 快递检测全部履约单完成才会拉完成，这里是履约单维度的拉完成，快递检测场景不处理
        if (orderDetail != null && CollUtil.isNotEmpty(orderDetail.getSendPayMap()) && orderDetail.getSendPayMap().containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()) && orderDetail.getSendPayMap().get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr()).equalsIgnoreCase(SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())) {
            return;
        }
        /**加项合管逻辑单独处理 由于配置的action是SPLIT_ORDER_QUERY_ORDER_DETAIL 所以当前拿到的orderId是主品的订单*/
        List<JdOrder> jdChildOrdersHasAdded = handleAddItemMergeLogic(orderDetail);
        log.info("SettleEventSubscriber -> selfOrderCompleteProcessor jdChildOrdersHasAdded:{}", JSON.toJSONString(jdChildOrdersHasAdded));
        if (CollUtil.isNotEmpty(jdChildOrdersHasAdded)) {
            return;
        }
        JdOrder jdOrder = JdOrder.builder().orderId(orderId).orderStatus(OrderStatusEnum.ORDER_COMPLETE.getStatus()).build();
        jdOrderRepository.updateOrderStatusByOrderId(jdOrder);

        reviseOrderFinishState(orderId);
    }

    /**
     * 无订单作废履约单，退款结算
     * @param event
     */
    private void promiseInvalidSettleProcessor(Event event) {
        log.info("SettleEventSubscriber.promiseInvalidSettleProcessor.promiseId={}",event.getAggregateId());
        String aggregateId = event.getAggregateId();
        if(StringUtil.isNotEmpty(aggregateId)){
            Long promiseId = Long.parseLong(aggregateId);
            JdhPromise snapshot = jdhPromiseRepository.find(new JdhPromiseIdentifier(promiseId));
            AngelWork angelWork = getAngelWork(promiseId);
            log.info("SettleEventSubscriber.promiseInvalidSettleProcessor.snapshot={}",snapshot);
            invalidVoucherOfSettle(snapshot,angelWork);
        }
    }

    /**
     * 加项合管逻辑单独处理 由于配置的action是SPLIT_ORDER_QUERY_ORDER_DETAIL 所以当前拿到的orderId可能是主品的订单
     * @param orderDetail
     * @return
     */
    private List<JdOrder> handleAddItemMergeLogic(JdOrder orderDetail) {
        Long orderId = orderDetail.getOrderId();
        log.info("SettleEventSubscriber.handleAddItemMergeLogic.orderId={}",orderId);
        //s2.如果当前订单满足：a.有父单 b.没有加项 则说明其为主品拆单后订单
        boolean isSplitMainOrder= orderDetail.getParentId().intValue() != 0 && HasAddedEnum.NOT_HAS_ADDED.getValue().equals(orderDetail.getHasAdded());
        log.info("SettleEventSubscriber.handleAddItemMergeLogic.isSplitMainOrder={}",isSplitMainOrder);
        if (isSplitMainOrder){
            //查询父单下的所有子单，将其中含有加项的子单过滤出来-拉完成（避免影响互医检验单的场景）
            List<JdOrder> jdOrders = jdOrderRepository.findOrderListByParentId(JdOrder.builder().parentId(orderDetail.getParentId()).build());
            log.info("SettleEventSubscriber.handleAddItemMergeLogic.jdOrders={}",jdOrders);
            List<JdOrder> jdOrdersHasAdded = Optional.ofNullable(jdOrders).map(List::stream).orElseGet(Stream::empty).filter(jdOrder -> HasAddedEnum.HAS_ADDED.getValue().equals(jdOrder.getHasAdded())).collect(Collectors.toList());
            log.info("SettleEventSubscriber.handleAddItemMergeLogic.jdOrdersHasAdded={}",jdOrdersHasAdded);
            Optional.ofNullable(jdOrdersHasAdded).map(List::stream).orElseGet(Stream::empty).forEach(jdOrderChild -> {
                JdOrder jdOrderChild2Complete = JdOrder.builder().orderId(jdOrderChild.getOrderId()).orderStatus(OrderStatusEnum.ORDER_COMPLETE.getStatus()).build();
                log.info("SettleEventSubscriber.handleAddItemMergeLogic.jdOrderChild2Complete={}",jdOrderChild2Complete);
                jdOrderRepository.updateOrderStatusByOrderId(jdOrderChild2Complete);
                reviseOrderFinishState(jdOrderChild2Complete.getOrderId());
            });
            //当前订单也要拉完成
            JdOrder jdOrder = JdOrder.builder().orderId(orderId).orderStatus(OrderStatusEnum.ORDER_COMPLETE.getStatus()).build();
            jdOrderRepository.updateOrderStatusByOrderId(jdOrder);
            log.info("SettleEventSubscriber.handleAddItemMergeLogic.jdOrder={}",jdOrder);
            reviseOrderFinishState(orderId);
            //互医检验单场景会是空
            return jdOrdersHasAdded;
        }
        return null;
    }

    /**
     *
     * @param orderId
     */
    private void reviseOrderFinishState(Long orderId) {
        tradeApplication.reviseOrderFinishState(orderId);
    }

    /**
     * 接单预估收入快照
     * @param event
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.settlement.event.SettleEventSubscriber.dispatchAngelSettleAmount")
    public void dispatchAngelSettleAmount(Event event){
        log.info("SettleEventSubscriber -> dispatchAngelSettleAmount event:{}",JSON.toJSONString(event));
        //预约相关触达，查询预约单信息
        AngelWorkEventBody body = getAngelWorkEventBody(event);
        Long orderId = body.getJdOrderId();
        String angelId = body.getAngelId();
        JdServiceSettleParam jdServiceSettleParam = new JdServiceSettleParam();
        jdServiceSettleParam.setAngelId(Long.parseLong(angelId));
        jdServiceSettleParam.setOrderId(orderId);
        jdServiceSettleParam.setWorkId(body.getWorkId());
        jdServiceSettleParam.setPromiseId(body.getPromiseId());
        jdServiceSettleParam.setSaveAngelsettleAmountSnapshot(Boolean.TRUE);
        jdServiceSettleParam.setDispatchMarkupPrice(body.getDispatchMarkupPrice());
        jdServiceSettleApplication.getOrderSettleAmount(jdServiceSettleParam);
    }

    /**
     * 获取工单
     *
     * @param event
     * @return
     */
    private AngelWorkEventBody getAngelWorkEventBody(Event event) {
        if (StringUtils.isBlank(event.getAggregateId())) {
            log.error("[SettleEventSubscriber.handleWorkReceive],事件参数异常!");
            throw new BusinessException(AngelPromiseBizErrorCode.EVENT_INFO_ERROR);
        }
        AngelWorkEventBody body = JSON.parseObject(event.getBody(), AngelWorkEventBody.class);
        Long orderId = body.getJdOrderId();
        String angelId = body.getAngelId();
        if(Objects.isNull(orderId) || StringUtil.isBlank(angelId)){
            AngelWork angelWork = angelWorkRepository.find(new AngelWorkIdentifier(Long.valueOf(event.getAggregateId())));
            if (Objects.isNull(angelWork)) {
                throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
            }
            if(StringUtil.isBlank(angelId)){
                body.setAngelId(angelWork.getAngelId());
            }
            if(Objects.isNull(orderId)){
                if(Objects.nonNull(angelWork.getJdOrderId())){
                    body.setJdOrderId(angelWork.getJdOrderId());
                }else{
                    PromiseDto promiseDto = getPromiseDto(angelWork.getPromiseId());
                    body.setJdOrderId(Long.parseLong(promiseDto.getSourceVoucherId()));
                }
            }
        }

        return body;
    }

    /**
     * 预约服务完成触发结算
     * @param event
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.settlement.event.SettleEventSubscriber.serviceFinishForSettle")
    public void serviceFinishForSettle(Event event){
        log.info("SettleEventSubscriber -> serviceFinishForSettle event:{}",JSON.toJSONString(event));
        //预约相关触达，查询预约单信息
        MedicalPromiseEventBody body = JSON.parseObject(event.getBody(), MedicalPromiseEventBody.class);
        serviceFinishForSettleOfBody(body);
    }

    /**
     * 预约服务完成触发结算
     * @param body
     */
    private void serviceFinishForSettleOfBody(MedicalPromiseEventBody body){
        //预约相关触达，查询预约单信息
        String lockKey = MessageFormat.format(RedisKeyEnum.JDH_APPOINTMENT_SETTLE_LOCK_KEY.getRedisKeyPrefix(), body.getPromiseId());
        boolean lockFlag = redisLockUtil.tryLockWithRetry(lockKey, UUID.fastUUID().toString(),
                RedisKeyEnum.JDH_APPOINTMENT_SETTLE_LOCK_KEY.getExpireTime(), 5,1000);
        if(!lockFlag){
            throw new BusinessException(BusinessErrorCode.SAVE_OPTION_DOING);
        }
        try{
            MedicalPromiseRequest medicalPromiseRequest = new MedicalPromiseRequest();
            medicalPromiseRequest.setMedicalPromiseId(body.getMedicalPromiseId());
            medicalPromiseRequest.setPromiseId(body.getPromiseId());
            medicalPromiseRequest.setServiceId(Long.parseLong(body.getServiceId()));
            MedicalPromiseDTO res = medicalPromiseApplication.queryMedicalPromise(medicalPromiseRequest);
            if(Objects.isNull(res)){
                log.info("SettleEventSubscriber -> serviceFinishForSettleOfBody promisePatientId:{}",body.getPromisePatientId());
                return;
            }
            body.setFlag(res.getFlag());
            body.setMedicalPromiseId(res.getMedicalPromiseId());
            Integer settleStatus = res.getSettleStatus();
            if(CommonConstant.ONE == settleStatus){
                log.info("SettleEventSubscriber -> serviceFinishForSettleOfBody repeat promisePatientId:{}",body.getPromisePatientId());
                jdServiceSettleApplication.orderFinishState(body);
                return;
            }
            jdServiceSettleApplication.angelServiceFinishSettleAndEbs(body);
        }finally{
            redisLockUtil.unLock(lockKey);
        }
    }

    /**
     * task 退款成功事件
     * @param event
     */
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.settlement.event.SettleEventSubscriber.selfRefundTaskSuccSettleProcessor")
    private void selfRefundTaskSuccSettleProcessor(Event event) {
        log.info("[SettleEventSubscriber->selfRefundTaskSuccSettleProcessor],eventBody={}", event.getBody());
        JSONObject jsonObject = JSON.parseObject(event.getBody());
        Long taskId = jsonObject.getLong("taskId");
        JdOrderRefundTask jdOrderRefundTask = JdOrderRefundTask.builder().build();
        jdOrderRefundTask.setTaskId(taskId);
        JdOrderRefundTask taskTemp = jdOrderRefundTaskRepository.findJdOrderRefundTask(jdOrderRefundTask);
        if(Objects.nonNull(taskTemp)){
            String lockKey = MessageFormat.format(RedisKeyEnum.JDH_APPOINTMENT_SETTLE_LOCK_KEY.getRedisKeyPrefix(), taskTemp.getPromiseId());
            boolean lockFlag = redisLockUtil.tryLockWithRetry(lockKey, UUID.fastUUID().toString(),
                    RedisKeyEnum.JDH_APPOINTMENT_SETTLE_LOCK_KEY.getExpireTime(), 5,1000);
            if(!lockFlag){
                throw new BusinessException(BusinessErrorCode.SAVE_OPTION_DOING);
            }
            try{
                this.refundSuccStartSettle(taskTemp);
            }finally{
                redisLockUtil.unLock(lockKey);
            }
        }
    }

    /**
     * 退款成功开始结算
     * @param jdOrderRefundTask
     */
    private void refundSuccStartSettle(JdOrderRefundTask jdOrderRefundTask){
        String refundDetail = jdOrderRefundTask.getRefundDetail();
        if(StringUtils.isNotBlank(refundDetail)){
            OrderRefundDetailBo orderRefundDetailBo = JsonUtil.parseObject(refundDetail,OrderRefundDetailBo.class);
            if(Objects.isNull(orderRefundDetailBo)) {
                log.error("[SettleEventSubscriber->refundSuccStartSettle],orderRefundDetailBo is null,orderId={}", jdOrderRefundTask.getOrderId());
                return;
            }
            List<Long> list = getPromiseIdList(jdOrderRefundTask);
            if(CollUtil.isNotEmpty(list)){
                for(Long promiseId : list){
                    jdOrderRefundTask.setPromiseId(promiseId);

                    if(StringUtil.isBlank(orderRefundDetailBo.getAngelId())){
                        AngelWorkSettleSnapshotDBQuery query = new AngelWorkSettleSnapshotDBQuery();
                        query.setPromiseId(jdOrderRefundTask.getPromiseId());
                        JdhAngelWorkSettleSnapshot settleSnapshot = jdhAngelWorkSettleSnapshotRepository.findAngelWorkSettleSnapshot(query);
                        if(Objects.isNull(settleSnapshot)) {
                            orderRefundDetailBo.setAngelId("12345");
                        }else{
                            orderRefundDetailBo.setAngelId(String.valueOf(settleSnapshot.getAngelId()));
                        }
                    }

                    List<AngelServiceFinishSettlementContext> angelServiceContextList = toAngelSettlementAndEbsDetail(jdOrderRefundTask,orderRefundDetailBo);
                    angelServiceContextList.forEach(angelServiceContext ->{
                        jdServiceSettleApplication.angelRefundSuccSettleAndEbs(angelServiceContext);
                    });
                }
            }
        }
    }

    /**
     *
     * @param jdOrderRefundTask
     * @return
     */
    private List<Long> getPromiseIdList(JdOrderRefundTask jdOrderRefundTask){
        List<Long> list;
        if(Objects.isNull(jdOrderRefundTask.getPromiseId())) {
            PromiseRepQuery query = new PromiseRepQuery();
            query.setSourceVoucherId(String.valueOf(jdOrderRefundTask.getOrderId()));
            List<JdhPromise> jdhPromiseList = jdhPromiseRepository.findList(query);
            list = CollectionUtils.isEmpty(jdhPromiseList) ? new ArrayList<>() : jdhPromiseList.stream().map(JdhPromise::getPromiseId).collect(Collectors.toList());
        }else {
            list = new ArrayList<>();
            list.add(jdOrderRefundTask.getPromiseId());
        }
        return list;
    }
    /**
     *
     * @param promiseId
     * @return
     */
    private PromiseDto getPromiseDto(Long promiseId){
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(promiseId);
        PromiseDto promiseDto = promiseApplication.findByPromiseId(promiseIdRequest);
        if (Objects.isNull(promiseDto))
            throw new BusinessException(AngelPromiseBizErrorCode.EVENT_INFO_ERROR);
        return promiseDto;
    }

    /**
     *
     * @param jdOrderRefundTask
     * @param orderRefundDetailBo
     * @return
     */
    private List<AngelServiceFinishSettlementContext> toAngelSettlementAndEbsDetail(JdOrderRefundTask jdOrderRefundTask,OrderRefundDetailBo orderRefundDetailBo){
        List<AngelServiceFinishSettlementContext> angelSettlementAndEbsDetailList = new ArrayList<>();
        // 检测单列表
        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseId(jdOrderRefundTask.getPromiseId());

        List<RefundSuccSettleDetailBo> promisePatientIdList;
        if(orderRefundDetailBo.getFreezeAndInvalid()){
            List<MedicalPromiseDTO> medicalPromises = getMedicalPromises(medicalPromiseListRequest);
            // 整单退补充收入对应的人
            promisePatientIdList = getPromisePatientIdList(medicalPromises,jdOrderRefundTask,orderRefundDetailBo);
        }else{
            medicalPromiseListRequest.setAllQuery(Boolean.TRUE);
            medicalPromiseListRequest.setFlag("0");
            List<MedicalPromiseDTO> medicalPromises = getMedicalPromises(medicalPromiseListRequest);
            // 整单退补充收入对应的人
            promisePatientIdList = getAddItemPromisePatientIdList(medicalPromises,jdOrderRefundTask,orderRefundDetailBo);
        }

        log.info("[SettleEventSubscriber->toAngelSettlementAndEbsDetail],promisePatientIdList={}", promisePatientIdList);
        List<OrderAngelRefundServiceBo> angelRefundServiceBoList = orderRefundDetailBo.getAngelRefundServiceBoList();
        Map<Long,OrderAngelRefundServiceBo> refundServiceBoMap = new HashMap<>();
        if(CollUtil.isNotEmpty(angelRefundServiceBoList)){
            refundServiceBoMap = angelRefundServiceBoList.stream().collect(Collectors.toMap(OrderAngelRefundServiceBo::getPromisePatientId, refundServiceBo -> refundServiceBo, (t, t2) -> t2));
        }
        for(int i =0; i< promisePatientIdList.size(); i++){
            RefundSuccSettleDetailBo refundSuccSettleDetailBo = promisePatientIdList.get(i);
            Long promisePatientId = refundSuccSettleDetailBo.getPromisePatientId();
            AngelServiceFinishSettlementContext context = SettlementApplicationConvert.INSTANCE.refundTaskConvertAngelServiceFinishSettlementContext(jdOrderRefundTask);
            OrderAngelSettleDetailBo orderAngelSettleDetailBo = new OrderAngelSettleDetailBo();
            context.setSettlementBusinessId(promisePatientId + "_" + jdOrderRefundTask.getServiceId());
            context.setPromisePatientId(promisePatientId);
            context.setAngelId(Long.parseLong(orderRefundDetailBo.getAngelId()));
            context.setServiceId(jdOrderRefundTask.getServiceId());
            context.setSkuRefundNum(orderRefundDetailBo.getSkuRefundNum());
            context.setAngelSettleService(orderRefundDetailBo.getAngelSettleService());
            context.setFreezeStatus(orderRefundDetailBo.getFreezeStatus());

            if(refundSuccSettleDetailBo.getAngelSettleBack()){
                context.setAngelSettleBack(true);
                orderAngelSettleDetailBo.setRefundBackSkuAmount(orderRefundDetailBo.getSkuInComeBackAmount());
                orderAngelSettleDetailBo.setRefundBackFeeAmount(orderRefundDetailBo.getFeeInComeBackAmount());
                context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
                angelSettlementAndEbsDetailList.add(context);
                log.info("[SettleEventSubscriber->toAngelSettlementAndEbsDetail],angelSettlementAndEbsDetailList={}", angelSettlementAndEbsDetailList);
                return angelSettlementAndEbsDetailList;
            }else{
                if(CollUtil.isNotEmpty(angelRefundServiceBoList)){
                    OrderAngelRefundServiceBo refundServiceBo = refundServiceBoMap.get(promisePatientId);
                    if(Objects.nonNull(refundServiceBo)){
                        OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
                        orderAngelSettleFeeBo.setOrderId(jdOrderRefundTask.getOrderId());
                        orderAngelSettleFeeBo.setServiceId(jdOrderRefundTask.getServiceId());
                        orderAngelSettleFeeBo.setItemInComeAmount(refundServiceBo.getItemInComeAmount());
                        List<OrderAngelSettleFeeBo> itemInComeAmountList = new ArrayList<>();
                        itemInComeAmountList.add(orderAngelSettleFeeBo);
                        orderAngelSettleDetailBo.setItemInComeAmountList(itemInComeAmountList);
                    }
                }
                BigDecimal feeAmount = BigDecimal.ZERO;
                if(i == promisePatientIdList.size() - 1){
                    // 工单最后一单
                    if(orderRefundDetailBo.getVoucherLastService()){
                        context.setVoucherLastService(Boolean.TRUE);
                        // ext收入与ext支出
                        List<OrderAngelSettleFeeBo> feeInComeAmountList = dealParentTaskRefundDetail(jdOrderRefundTask);
                        orderAngelSettleDetailBo.setFeeInComeAmountList(feeInComeAmountList);
                    }
                    if(jdOrderRefundTask.getLastRefundTask() == CommonConstant.ONE && Objects.nonNull(context.getAngelFeeServiceAmount())){
                        feeAmount = context.getAngelFeeServiceAmount();
                        orderAngelSettleDetailBo.getItemOutComeAmountList().add(feeAmount);
                    }
                }
            }

            context.setAngelSettleFee(orderRefundDetailBo.getAngelSettleFee());
            context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
            angelSettlementAndEbsDetailList.add(context);
        }
        log.info("[SettleEventSubscriber->toAngelSettlementAndEbsDetail],angelSettlementAndEbsDetailList={}", angelSettlementAndEbsDetailList);
        return angelSettlementAndEbsDetailList;
    }



    /**
     * 整单退：人与收入金额合并
     * @param jdOrderRefundTask
     * @return
     */
    private List<RefundSuccSettleDetailBo> getPromisePatientIdList(List<MedicalPromiseDTO> medicalPromises, JdOrderRefundTask jdOrderRefundTask, OrderRefundDetailBo orderRefundDetailBo){
        List<Long> promisePatientIdList = orderRefundDetailBo.getPromisePatientIdList();

        List<MedicalPromiseDTO> medicalPromisesServiceList = medicalPromises.stream().filter(medical -> jdOrderRefundTask.getServiceId().equals(medical.getServiceId().toString())).collect(Collectors.toList());

        if(RefundTypeEnum.ORDER_REFUND.getType().equals(jdOrderRefundTask.getRefundType())){
            promisePatientIdList = medicalPromisesServiceList.stream().map(MedicalPromiseDTO::getPromisePatientId).collect(Collectors.toList());
            promisePatientIdList = promisePatientIdList.stream().distinct().collect(Collectors.toList());
            this.dealAngelRefundServiceBoList(promisePatientIdList,orderRefundDetailBo);
        }

        List<Long> finalPromisePatientIdList = promisePatientIdList;
        List<MedicalPromiseDTO> medicalPromisesSeleList = medicalPromisesServiceList.stream().filter(medical ->
                finalPromisePatientIdList.contains(medical.getPromisePatientId())).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(medicalPromisesSeleList)){
            MedicalPromiseSettleStatusCmd medicalPromiseSettleStatusCmd = new MedicalPromiseSettleStatusCmd();
            medicalPromiseSettleStatusCmd.setMedicalPromiseIds(medicalPromisesSeleList.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList()));
            medicalPromiseSettleStatusCmd.setSettleStatus(CommonConstant.ONE);
            medicalPromiseApplication.updateSettleSatus(medicalPromiseSettleStatusCmd);
        }

        List<MedicalPromiseDTO> medicalPromisesOther = medicalPromisesServiceList.stream().filter(medical -> !finalPromisePatientIdList.contains(medical.getPromisePatientId())
                && medical.getSettleStatus().equals(CommonConstant.ZERO)).collect(Collectors.toList());
        if(CollUtil.isEmpty(medicalPromisesOther)){
            orderRefundDetailBo.setOrderLastService(Boolean.TRUE);
        }
        medicalPromises = medicalPromises.stream().filter(medical -> !jdOrderRefundTask.getServiceId().equals(medical.getServiceId().toString())
                && medical.getSettleStatus().equals(CommonConstant.ZERO)).collect(Collectors.toList());
        if(CollUtil.isEmpty(medicalPromises) && CollUtil.isEmpty(medicalPromisesOther)){
            orderRefundDetailBo.setVoucherLastService(Boolean.TRUE);
        }

        List<Long> completedList = medicalPromisesServiceList.stream().filter(medical -> finalPromisePatientIdList.contains(medical.getPromisePatientId()) &&
                MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(medical.getStatus())).map(MedicalPromiseDTO::getPromisePatientId).collect(Collectors.toList());

        List<RefundSuccSettleDetailBo> refundSuccSettleDetailBoList = new ArrayList<>();
        for(Long promisePatientId : promisePatientIdList){
            RefundSuccSettleDetailBo refundSuccSettleDetailBo = new RefundSuccSettleDetailBo();
            refundSuccSettleDetailBo.setPromisePatientId(promisePatientId);
            if(CollUtil.isNotEmpty(completedList) && completedList.contains(promisePatientId)){
                AngelSettleQuery angelSettleQuery = new AngelSettleQuery();
                angelSettleQuery.setPromiseId(jdOrderRefundTask.getPromiseId());
                if(Objects.isNull(jdOrderRefundTask.getPromiseId())){
                    angelSettleQuery.setOrderId(jdOrderRefundTask.getOrderId());
                }
                angelSettleQuery.setQuerySettleDetail(Boolean.FALSE);
                AngelSettlementDto res = serviceSettleReadApplication.querySettlement(angelSettleQuery);
                if(Objects.nonNull(res)){
                    refundSuccSettleDetailBo.setAngelSettleBack(Boolean.TRUE);
                }else{
                    String appointmentId = promisePatientId + "_" + jdOrderRefundTask.getOrderId();
                    String preId = SettlementEbsUtil.appendSplitMultiKey(CommonConstant.CHARACTER_UNDERLINE, appointmentId, EbsSettleSplitTypeEnum.SKU_FEE.getType(), EbsSettleTypeEnum.INCOME.getType().toString());
                    SettlementEbsRequest settlementEbsRequest = new SettlementEbsRequest();
                    settlementEbsRequest.setPreId(preId);
                    List<JdhSettlementEbs> list = serviceSettleReadApplication.querySettlementEbs(settlementEbsRequest);
                    if(CollUtil.isNotEmpty(list)){
                        refundSuccSettleDetailBo.setAngelSettleBack(Boolean.TRUE);
                    }
                }
            }
            refundSuccSettleDetailBoList.add(refundSuccSettleDetailBo);
        }
        return refundSuccSettleDetailBoList;
    }

    /**
     * 整单退：人与收入金额合并
     * @param jdOrderRefundTask
     * @return
     */
    private List<RefundSuccSettleDetailBo> getAddItemPromisePatientIdList(List<MedicalPromiseDTO> medicalPromises, JdOrderRefundTask jdOrderRefundTask, OrderRefundDetailBo orderRefundDetailBo){
        List<Long> promisePatientIdList = orderRefundDetailBo.getPromisePatientIdList();

        List<MedicalPromiseDTO> medicalPromisesServiceList = medicalPromises.stream().filter(medical -> jdOrderRefundTask.getServiceId().equals(medical.getServiceId().toString())).collect(Collectors.toList());

        if(RefundTypeEnum.ORDER_REFUND.getType().equals(jdOrderRefundTask.getRefundType())){
            promisePatientIdList = medicalPromisesServiceList.stream().map(MedicalPromiseDTO::getPromisePatientId).collect(Collectors.toList());
            promisePatientIdList = promisePatientIdList.stream().distinct().collect(Collectors.toList());
            this.dealAngelRefundServiceBoList(promisePatientIdList,orderRefundDetailBo);
        }

        List<Long> finalPromisePatientIdList = promisePatientIdList;
        List<MedicalPromiseDTO> medicalPromisesSeleList = medicalPromisesServiceList.stream().filter(medical ->
                finalPromisePatientIdList.contains(medical.getPromisePatientId())).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(medicalPromisesSeleList)){
            MedicalPromiseSettleStatusCmd medicalPromiseSettleStatusCmd = new MedicalPromiseSettleStatusCmd();
            medicalPromiseSettleStatusCmd.setMedicalPromiseIds(medicalPromisesSeleList.stream().map(MedicalPromiseDTO::getMedicalPromiseId).collect(Collectors.toList()));
            medicalPromiseSettleStatusCmd.setSettleStatus(CommonConstant.ONE);
            medicalPromiseApplication.updateSettleSatus(medicalPromiseSettleStatusCmd);
        }

        List<RefundSuccSettleDetailBo> refundSuccSettleDetailBoList = new ArrayList<>();
        for(Long promisePatientId : promisePatientIdList){
            RefundSuccSettleDetailBo refundSuccSettleDetailBo = new RefundSuccSettleDetailBo();
            refundSuccSettleDetailBo.setPromisePatientId(promisePatientId);
            AngelSettleQuery angelSettleQuery = new AngelSettleQuery();
            angelSettleQuery.setPromiseId(jdOrderRefundTask.getPromiseId());
            if(Objects.isNull(jdOrderRefundTask.getPromiseId())){
                angelSettleQuery.setOrderId(jdOrderRefundTask.getOrderId());
            }
            angelSettleQuery.setQuerySettleDetail(Boolean.FALSE);
            AngelSettlementDto res = serviceSettleReadApplication.querySettlement(angelSettleQuery);
            if(Objects.nonNull(res)){
                refundSuccSettleDetailBo.setAngelSettleBack(Boolean.TRUE);
            }else{
                String appointmentId = promisePatientId + "_" + jdOrderRefundTask.getOrderId();
                String preId = SettlementEbsUtil.appendSplitMultiKey(CommonConstant.CHARACTER_UNDERLINE, appointmentId, EbsSettleSplitTypeEnum.SKU_FEE.getType(), EbsSettleTypeEnum.INCOME.getType().toString());
                SettlementEbsRequest settlementEbsRequest = new SettlementEbsRequest();
                settlementEbsRequest.setPreId(preId);
                List<JdhSettlementEbs> list = serviceSettleReadApplication.querySettlementEbs(settlementEbsRequest);
                if(CollUtil.isNotEmpty(list)){
                    refundSuccSettleDetailBo.setAngelSettleBack(Boolean.TRUE);
                }
            }
            refundSuccSettleDetailBoList.add(refundSuccSettleDetailBo);
        }
        return refundSuccSettleDetailBoList;
    }

    /**
     *
     * @param medicalPromiseListRequest
     * @return
     */
    private List<MedicalPromiseDTO> getMedicalPromises(MedicalPromiseListRequest medicalPromiseListRequest){
        List<MedicalPromiseDTO> medicalPromises = medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
        return medicalPromises;
    }

    /**
     * 收入金额与人合并
     * @param promisePatientIdList
     * @param orderRefundDetailBo
     */
    private void dealAngelRefundServiceBoList(List<Long> promisePatientIdList,OrderRefundDetailBo orderRefundDetailBo){
        List<BigDecimal> itemInComeAmountList = orderRefundDetailBo.getItemInComeAmountList();
        if(CollUtil.isNotEmpty(itemInComeAmountList)){
            List<OrderAngelRefundServiceBo> angelRefundServiceBoList = new ArrayList<>();
            for(int k = 0; k<itemInComeAmountList.size(); k++){
                if(k > promisePatientIdList.size() - 1){
                    break;
                }
                OrderAngelRefundServiceBo orderAngelRefundServiceBo = new OrderAngelRefundServiceBo();
                orderAngelRefundServiceBo.setPromisePatientId(promisePatientIdList.get(k));
                orderAngelRefundServiceBo.setItemInComeAmount(itemInComeAmountList.get(k));
                angelRefundServiceBoList.add(orderAngelRefundServiceBo);
            }
            orderRefundDetailBo.setAngelRefundServiceBoList(angelRefundServiceBoList);
        }
    }

    /**
     *
     * @param jdOrderRefundTask
     * @return
     */
    private List<OrderAngelSettleFeeBo> dealParentTaskRefundDetail(JdOrderRefundTask jdOrderRefundTask){
        Long parentId = jdOrderRefundTask.getParentId();
        if(Objects.isNull(parentId)){
            Long orderId = jdOrderRefundTask.getOrderId();
            JdOrder jdOrder = jdOrderApplication.queryJdOrderByOrderId(orderId);
            parentId = jdOrder.getParentId();
        }

        List<OrderAngelSettleFeeBo> feeInComeAmountList = new ArrayList<>();
        if(Objects.nonNull(parentId) && parentId > 0){
            JdOrderRefundTask taskQuery = JdOrderRefundTask.builder().build();
            taskQuery.setParentId(parentId);
            taskQuery.setPromiseId(jdOrderRefundTask.getPromiseId());
            List<JdOrderRefundTask> taskList = jdOrderRefundTaskRepository.findJdOrderRefundTaskList(taskQuery);
            for(JdOrderRefundTask task : taskList){
                OrderAngelSettleFeeBo orderAngelSettleFeeBo = getOrderAngelSettleFeeBo(task);
                if(Objects.nonNull(orderAngelSettleFeeBo)){
                    feeInComeAmountList.add(orderAngelSettleFeeBo);
                }
            }
        }else{
            OrderAngelSettleFeeBo orderAngelSettleFeeBo = getOrderAngelSettleFeeBo(jdOrderRefundTask);
            if(Objects.nonNull(orderAngelSettleFeeBo)){
                feeInComeAmountList.add(orderAngelSettleFeeBo);
            }
        }
        return feeInComeAmountList;
    }

    /**
     *
     * @param jdOrderRefundTask
     * @return
     */
    private OrderAngelSettleFeeBo getOrderAngelSettleFeeBo(JdOrderRefundTask jdOrderRefundTask){
        String refundDetail = jdOrderRefundTask.getRefundDetail();
        if(StringUtils.isNotBlank(refundDetail)){
            OrderRefundDetailBo orderRefundDetailBo = JsonUtil.parseObject(refundDetail,OrderRefundDetailBo.class);
            BigDecimal feeInComeAmount = orderRefundDetailBo.getFeeInComeAmount();
            if(Objects.nonNull(feeInComeAmount) && feeInComeAmount.compareTo(BigDecimal.ZERO) >0){
                OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
                orderAngelSettleFeeBo.setFeeInComeAmount(feeInComeAmount);
                orderAngelSettleFeeBo.setOrderId(jdOrderRefundTask.getOrderId());
                return orderAngelSettleFeeBo;
            }
        }
        return null;
    }



    /**
     *
     * @param promiseId
     * @return
     */
    private AngelWork getAngelWork(Long promiseId){
        log.info("SettleEventSubscriber.getAngelWork.promiseId={}",promiseId);
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        //promiseId
        angelWorkDBQuery.setPromiseId(promiseId);
        angelWorkDBQuery.setNotInWorkStatusList(Arrays.asList(AngelWorkStatusEnum.CANCEL.getType()));
        AngelWork angelWork = angelWorkRepository.findAngelWork(angelWorkDBQuery);
        if(Objects.nonNull(angelWork) && AngelWorkStatusEnum.InvalidStatus().contains(angelWork.getWorkStatus())){
            List<AngelWorkHistory> workHistories = angelWorkHistoryRepository.findList(AngelWorkHistoryDbQuery.builder().workId(angelWork.getWorkId()).afterStatus(angelWork.getWorkStatus()).build());
            Integer beforeStatus = workHistories.get(0).getBeforeStatus();
            angelWork.setWorkStatus(beforeStatus);
        }
        log.info("SettleEventSubscriber.getAngelWork.angelWork={}",JsonUtil.toJSONString(angelWork));
        return angelWork;
    }

    /**
     *
     * @param promise
     * @param angelWork
     */
    private void invalidVoucherOfSettle(JdhPromise promise, AngelWork angelWork){
        if(Objects.isNull(promise) || Objects.isNull(angelWork)){
            log.info("SettleEventSubscriber -> invalidVoucherOfSettle promise or angelWork is null");
            return;
        }
        String verticalCode = promise.getVerticalCode();
        if(StringUtil.isBlank(verticalCode)){
            log.info("SettleEventSubscriber -> invalidVoucherOfSettle verticalCode is null");
            return;
        }
        String noOrderInvalidSettleConfig = duccConfig.getNoOrderInvalidSettleConfig();
        log.info("SettleEventSubscriber -> invalidVoucherOfSettle noOrderInvalidSettleConfig={}",noOrderInvalidSettleConfig);
        if(noOrderInvalidSettleConfig.contains(verticalCode)){
            log.info("SettleEventSubscriber -> noOrderInvalidSettleConfig promise={}",promise);
            CompletableFuture.runAsync(() -> syncSendAngelSettle(promise,angelWork),
                    executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT));
        }
    }

    /**
     *
     * @param promise
     * @param angelWork
     */
    private void syncSendAngelSettle(JdhPromise promise,AngelWork angelWork){
        log.info("SettleEventSubscriber -> syncSendAngelSettle promiseId={}",promise.getPromiseId());
        Long promiseId = promise.getPromiseId();
        AngelSettleQuery angelSettleQuery = new AngelSettleQuery();
        angelSettleQuery.setPromiseId(promiseId);
        angelSettleQuery.setQuerySettleDetail(Boolean.FALSE);
        AngelSettlementDto res = serviceSettleReadApplication.querySettlement(angelSettleQuery);
        if(Objects.nonNull(res)){
            log.info("SettleEventSubscriber -> syncSendAngelSettle repeat promiseId={}",promise.getPromiseId());
            return;
        }
        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseId(promiseId);
        // 检测单列表
        List<MedicalPromiseDTO> medicalPromises = getMedicalPromises(medicalPromiseListRequest);
        if(CollUtil.isEmpty(medicalPromises)){
            log.error("SettleEventSubscriber -> syncSendAngelSettle medicalPromises is null,promiseId={}",promiseId);
            return;
        }
        Map<Long,List<MedicalPromiseDTO>> medicalPromisesMap = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromiseDTO::getPromisePatientId));
        int count = 0;
        for(Map.Entry<Long,List<MedicalPromiseDTO>> entry : medicalPromisesMap.entrySet()){
            List<MedicalPromiseDTO> list = entry.getValue();
            Long serviceId = list.get(0).getServiceId();
            List<Long> medicalPromiseIdList = list.stream().map(MedicalPromiseDTO::getPromisePatientId).distinct().collect(Collectors.toList());
            Long promisePatientId = entry.getKey();
            AngelServiceFinishSettlementContext context = new AngelServiceFinishSettlementContext();
            context.setVerticalCode(promise.getVerticalCode());
            context.setServiceType(promise.getServiceType());
            context.setUserPin(context.getUserPin());
            context.setPromiseId(promiseId);
            context.setVoucherId(context.getVoucherId());
            context.setOrderId(StringUtil.isNotBlank(promise.getSourceVoucherId()) ? Long.parseLong(promise.getSourceVoucherId()) : 0L);
            context.setSourceVoucherId(promise.getSourceVoucherId());
            context.setSettlementBusinessId(promisePatientId + "_" + serviceId);
            context.setPromisePatientId(promisePatientId);
            context.setAngelId(Long.parseLong(angelWork.getAngelId()));
            context.setServiceId(String.valueOf(serviceId));
            context.setSkuRefundNum(medicalPromiseIdList.size());
            context.setAngelSettleService(Boolean.TRUE);
            count ++;
            context.setVoucherLastService(count == medicalPromisesMap.size());
            context.setFreezeStatus(angelWork.getWorkStatus());
            context.setPaymentTime(promise.getCreateTime());
            OrderAngelSettleDetailBo orderAngelSettleDetailBo = new OrderAngelSettleDetailBo();
            context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
            log.info("SettleEventSubscriber -> syncSendAngelSettle context={}",JsonUtil.toJSONString(context));
            jdServiceSettleApplication.invalidVoucherSettleAndEbs(context);
        }
    }
}
