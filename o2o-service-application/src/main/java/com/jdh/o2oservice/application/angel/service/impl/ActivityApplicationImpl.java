package com.jdh.o2oservice.application.angel.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.angel.convert.ActivityConfigConverter;
import com.jdh.o2oservice.application.angel.service.ActivityApplication;
import com.jdh.o2oservice.application.angel.service.ActivityConvertDelegate;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.*;
import com.jdh.o2oservice.core.domain.angel.model.ActivityDetailGroupCount;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelRecruitmentActivity;
import com.jdh.o2oservice.core.domain.angel.model.ext.ActivityRewardProgress;
import com.jdh.o2oservice.core.domain.angel.model.ext.JdhAngelRecruitmentActivityConfig;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.*;
import com.jdh.o2oservice.core.domain.support.basic.enums.ActivityConfigStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.ActivityRewardStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.model.CredentialNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.domain.angel.core.ext.context.ActivityConfigBaseContext;
import com.jdh.o2oservice.core.domain.angel.repository.db.ActivityRepository;
import com.jdh.o2oservice.core.domain.angel.service.AngelActivityDomainService;
import com.jdh.o2oservice.domain.angel.core.ext.context.AngelActivityBaseContext;
import com.jdh.o2oservice.domain.angel.core.ext.model.JdhAbstractActivityConfig;
import com.jdh.o2oservice.domain.angel.core.ext.model.JdhAbstractAngelActivity;
import com.jdh.o2oservice.domain.angel.core.ext.model.JdhActivityConfigIdentifier;
import com.jdh.o2oservice.domain.angel.core.ext.model.JdhAngelActivityIdentifier;
import com.jdh.o2oservice.export.angel.cmd.ActivityConfigCmd;
import com.jdh.o2oservice.export.angel.cmd.AngelActivityCmd;
import com.jdh.o2oservice.export.angel.cmd.JdhActivityConfigAngelRecruitmentRule;
import com.jdh.o2oservice.export.angel.dto.*;
import com.jdh.o2oservice.export.angel.query.ActivityConfigRequest;
import com.jdh.o2oservice.export.angel.query.AngelActivityRecruitmentRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName ActivityApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/11/19 21:01
 **/
@Service
@Slf4j
public class ActivityApplicationImpl implements ActivityApplication {

    /**
     *
     */
    @Resource
    private AngelActivityDomainService angelActivityDomainService;

    /**
     *
     */
    @Resource
    private ActivityConvertDelegate activityConvertDelegate;

    /**
     *
     */
    @Resource
    private AngelRepository angelRepository;

    /**
     *
     */
    @Resource
    private ActivityRepository activityRepository;

    /**
     *
     */
    @Resource
    private FileManageApplication fileManageApplication;

    /**
     * 创建活动配置
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.angel.service.impl.ActivityApplicationImpl.createActivityConfig")
    public Boolean createActivityConfig(ActivityConfigCmd cmd) {
        if (Objects.isNull(cmd) || Objects.isNull(cmd.getActivityConfigType())) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        ActivityConfigBaseContext context = activityConvertDelegate.convertConfigCmd2Context(cmd);
        return angelActivityDomainService.createActivity(context);
    }

    /**
     * 查询活动配置基础信息
     * @param request
     * @return
     */
    @Override
    public ActivityConfigDto queryActivityConfig(ActivityConfigRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getActivityConfigId())) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        JdhAbstractActivityConfig activityConfig = activityRepository.findActivityConfig(JdhActivityConfigIdentifier.builder().activityConfigId(request.getActivityConfigId()).build());
        if (Objects.isNull(activityConfig)) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        return activityConvertDelegate.convertAggregate2Dto(activityConfig);
    }

    /**
     * 同步护士活动进展
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.angel.service.impl.ActivityApplicationImpl.syncAngelActivityProgress")
    public Boolean syncAngelActivityProgress(AngelActivityCmd cmd) {
        if (Objects.isNull(cmd) || Objects.isNull(cmd.getActivityConfigType())) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }

        AngelActivityBaseContext context = activityConvertDelegate.convertActivityCmd2Context(cmd);
        return angelActivityDomainService.syncAngelActivityProgress(context);
    }

    /**
     * 查询护拉护活动配置基础信息
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public ActivityConfigAngelRecruitmentDto queryAngelRecruitmentActivityConfig(ActivityConfigRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getPageNum()) || Objects.isNull(request.getPageSize())) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        List<JdhAbstractActivityConfig> activityConfigList = activityRepository.findActivityConfigList(JdhActivityConfigRepQuery.builder().activityConfigType(ActivityConfigTypeEnum.ANGEL_RECRUITMENT.getType()).build());
        if (CollectionUtils.isEmpty(activityConfigList)) {
            return null;
        }
        //同时只有一个相同类型的活动在进行
        JdhAngelRecruitmentActivityConfig activityConfig = Convert.convert(JdhAngelRecruitmentActivityConfig.class, activityConfigList.get(0));
        //查询当前护士信息
        JdhAngel angel = angelRepository.queryByUniqueId(JdhAngelRepQuery.builder().angelPin(request.getUserPin()).build());
        return convertActivityConfigAngelRecruitmentDto(request, activityConfig, angel);
    }

    /**
     * 护拉护活动受邀配置基础信息
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public ActivityConfigBeInviteRecruitmentDto queryBeInviteRecruitmentActivityConfig(ActivityConfigRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getAngelId()) || Objects.isNull(request.getActivityConfigId())) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        //查询活动信息
        JdhAbstractActivityConfig activityConfig = activityRepository.findActivityConfig(JdhActivityConfigIdentifier.builder().activityConfigId(request.getActivityConfigId()).build());
        JdhAngelRecruitmentActivityConfig recruitmentActivityConfig = Convert.convert(JdhAngelRecruitmentActivityConfig.class, activityConfig);
        //查询邀请护士信息
        JdhAngel angel = angelRepository.queryAngelDetail(JdhAngelRepQuery.builder().angelId(request.getAngelId()).build());
        ActivityConfigBeInviteRecruitmentDto dto = ActivityConfigConverter.INS.convertActivityConfigBeInviteRecruitmentDto(recruitmentActivityConfig, angel);
        //查询此护士已经邀请的审核通过的护士数量，达到1000后，受邀医生点击邀请医生链接后，在受邀页面点击【立即入驻】按钮，弹窗提示

        //累计邀请数据
        List<ActivityDetailGroupCount> groupCountYearTotalList = activityRepository.findActivityDetailGroupCount(ActivityDetailGroupCountQuery.builder().activityConfigId(dto.getActivityConfigId())
                .column("reward_status")
                .activityConfigType(dto.getActivityConfigType()).rewardStatus(ActivityRewardStatusEnum.REWARD_ACHIEVE.getStatus()).angelId(request.getAngelId()).build());
        Long eligibleInviteNum = 0L;
        for (ActivityDetailGroupCount groupCountTotal : groupCountYearTotalList) {
            Integer rewardStatus = Convert.convert(Integer.class, groupCountTotal.getGroupKeyValue());
            if (Objects.equals(rewardStatus, ActivityRewardStatusEnum.REWARD_ACHIEVE.getStatus()) && Objects.nonNull(groupCountTotal.getCount())) {
                eligibleInviteNum = groupCountTotal.getCount();
            }
        }

        dto.setInviteLimit(eligibleInviteNum.intValue() >= recruitmentActivityConfig.getActivityConfigRule().getAllowRepeatNum());
        return dto;
    }

    /**
     * 查询护士活动信息
     *
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm
    public AngelActivityRecruitmentDto queryRecruitmentAngelActivity(AngelActivityRecruitmentRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getAngelActivityId())) {
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        JdhAbstractAngelActivity abstractAngelActivity = activityRepository.find(JdhAngelActivityIdentifier.builder().angelActivityId(request.getAngelActivityId()).build());
        AngelActivityRecruitmentDto dto = activityConvertDelegate.convertAggregate2Dto(abstractAngelActivity);
        JdhActivityConfigAngelRecruitmentRule activityConfigRule = dto.getActivityConfigRule();
        Long acceptAngelId = activityConfigRule.getAcceptAngelId();
        JdhAngel angel = angelRepository.queryByUniqueId(JdhAngelRepQuery.builder().angelId(acceptAngelId).searchDel(true).build());
        if (Objects.nonNull(angel)) {
            activityConfigRule.setAcceptAngelName(new UserName(angel.getAngelName()).mask());
            activityConfigRule.setAcceptAngelId(angel.getAngelId());
            activityConfigRule.setAcceptAngelInstitutionCode(CollectionUtils.isNotEmpty(angel.getJdhAngelProfessionRelList()) ? angel.getJdhAngelProfessionRelList().get(0).getInstitutionCode() : null);
            activityConfigRule.setAcceptAngelInstitutionName(CollectionUtils.isNotEmpty(angel.getJdhAngelProfessionRelList()) ? angel.getJdhAngelProfessionRelList().get(0).getInstitutionName() : null);
            activityConfigRule.setAcceptAngelOneDepartmentCode(angel.getOneDepartmentCode());
            activityConfigRule.setAcceptAngelOneDepartmentName(angel.getOneDepartmentName());
            activityConfigRule.setAcceptAngelTwoDepartmentCode(angel.getTwoDepartmentCode());
            activityConfigRule.setAcceptAngelTwoDepartmentName(angel.getTwoDepartmentName());
        }
        return dto;
    }

    /**
     * 运营端查询护拉护数据
     * @param request
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.angel.service.impl.ActivityApplicationImpl.queryAngelRecruitmentActivity4Man")
    public PageDto<AngelRecruitmentActivityManDto> queryAngelRecruitmentActivity4Man(AngelActivityRecruitmentRequest request) {
        //入参包含邀请护士pin、邀请护士ID、邀请护士姓名，查询邀请护士ID列表
        List<JdhAngel> inviteAngelList = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getInviteAngelId()) || StringUtils.isNotBlank(request.getInviteAngelPin()) || StringUtils.isNotBlank(request.getInviteAngelName())) {
            JdhAngelRepQuery.JdhAngelRepQueryBuilder builder = JdhAngelRepQuery.builder();
            if (StringUtils.isNotBlank(request.getInviteAngelId())) {
                builder.angelId(Long.valueOf(request.getInviteAngelId()));
            }
            if (StringUtils.isNotBlank(request.getInviteAngelPin())) {
                builder.angelPin(request.getInviteAngelPin());
            }
            if (StringUtils.isNotBlank(request.getInviteAngelName())) {
                builder.angelName(request.getInviteAngelName());
            }
            JdhAngelRepQuery angelRepPageQuery = builder.build();
            inviteAngelList = angelRepository.findList(angelRepPageQuery);
            if (CollectionUtils.isEmpty(inviteAngelList)) {
                return PageDto.getEmptyPage();
            }
        }
        //入参包含受邀护士pin、受邀护士ID、受邀护士名称、受邀护士职称、受邀护士医院、受邀护士资质审核状态、首次入驻时间-开始时间、首次入驻时间-结束时间、审核时间-开始时间、审核时间-结束时间，查询受邀护士ID列表
        List<JdhAngel> beInviteAngelList = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getAngelId()) || StringUtils.isNotBlank(request.getAngelPin()) || StringUtils.isNotBlank(request.getAngelName())
                || StringUtils.isNotBlank(request.getProfessionTitleCode()) || StringUtils.isNotBlank(request.getInstitutionName()) || Objects.nonNull(request.getAuditProcessStatus())
                || Objects.nonNull(request.getRegisterStartTime()) || Objects.nonNull(request.getRegisterEndTime()) || Objects.nonNull(request.getAuditProcessDateStartTime()) || Objects.nonNull(request.getAuditProcessDateEndTime())) {
            JdhAngelRepQuery.JdhAngelRepQueryBuilder builder = JdhAngelRepQuery.builder();
            if (StringUtils.isNotBlank(request.getAngelId())) {
                builder.angelId(Long.valueOf(request.getAngelId()));
            }
            if (StringUtils.isNotBlank(request.getAngelPin())) {
                builder.angelPin(request.getAngelPin());
            }
            if (StringUtils.isNotBlank(request.getAngelName())) {
                builder.angelName(request.getAngelName());
            }
            if (StringUtils.isNotBlank(request.getProfessionTitleCode())) {
                builder.professionTitleCode(request.getProfessionTitleCode());
            }
            if (StringUtils.isNotBlank(request.getInstitutionName())) {
                builder.institutionName(request.getInstitutionName());
            }
            if (Objects.nonNull(request.getAuditProcessStatus())) {
                builder.auditProcessStatus(request.getAuditProcessStatus());
            }
            if (Objects.nonNull(request.getRegisterStartTime())) {
                builder.registerStartTime(request.getRegisterStartTime());
            }
            if (Objects.nonNull(request.getRegisterEndTime())) {
                builder.registerEndTime(request.getRegisterEndTime());
            }
            if (Objects.nonNull(request.getAuditProcessDateStartTime())) {
                builder.auditProcessDateStartTime(request.getAuditProcessDateStartTime());
            }
            if (Objects.nonNull(request.getAuditProcessDateEndTime())) {
                builder.auditProcessDateEndTime(request.getAuditProcessDateEndTime());
            }
            JdhAngelRepQuery angelRepPageQuery = builder.build();
            beInviteAngelList = angelRepository.findList(angelRepPageQuery);
            if (CollectionUtils.isEmpty(beInviteAngelList)) {
                return PageDto.getEmptyPage();
            }
        }
        //邀请护士ID列表 + 受邀护士ID列表 + 是否已奖励查询护士活动信息
        JdhAngelActivityRepQuery.JdhAngelActivityRepQueryBuilder builder = JdhAngelActivityRepQuery.builder();
        if (CollectionUtils.isNotEmpty(inviteAngelList)) {
            builder.angelIdList(inviteAngelList.stream().map(JdhAngel::getAngelId).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(beInviteAngelList)) {
            builder.applySql("JSON_EXTRACT(activity_rule_progress, '$.acceptAngelId') in (" + Joiner.on(",").join(beInviteAngelList.stream().map(JdhAngel::getAngelId).distinct().collect(Collectors.toList())) + ")");
        }
        if (Objects.nonNull(request.getRewardStatus())) {
            builder.rewardStatus(request.getRewardStatus());
        }
        if (Objects.nonNull(request.getPageNum())) {
            builder.pageNum(request.getPageNum());
        }
        if (Objects.nonNull(request.getPageSize())) {
            builder.pageSize(request.getPageSize());
        }
        if (Objects.nonNull(request.getCreateStartTime())) {
            builder.createStartTime(request.getCreateStartTime());
        }
        if (Objects.nonNull(request.getCreateEndTime())) {
            builder.createEndTime(request.getCreateEndTime());
        }
        JdhAngelActivityRepQuery activityRepQuery = builder.build();
        Page<JdhAbstractAngelActivity> page = activityRepository.findPage(activityRepQuery);
        return convertAngelRecruitmentActivityManDto(page);
    }

    /**
     * 导出护拉护数据
     * @param request
     * @return
     */
    @Override
    public Boolean exportAngelRecruitmentActivity(AngelActivityRecruitmentRequest request) {
        //入参包含邀请护士pin、邀请护士ID、邀请护士姓名，查询邀请护士ID列表
        List<JdhAngel> inviteAngelList = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getInviteAngelId()) || StringUtils.isNotBlank(request.getInviteAngelPin()) || StringUtils.isNotBlank(request.getInviteAngelName())) {
            JdhAngelRepQuery.JdhAngelRepQueryBuilder builder = JdhAngelRepQuery.builder();
            if (StringUtils.isNotBlank(request.getInviteAngelId())) {
                builder.angelId(Long.valueOf(request.getInviteAngelId()));
            }
            if (StringUtils.isNotBlank(request.getInviteAngelPin())) {
                builder.angelPin(request.getInviteAngelPin());
            }
            if (StringUtils.isNotBlank(request.getInviteAngelName())) {
                builder.angelName(request.getInviteAngelName());
            }
            JdhAngelRepQuery angelRepPageQuery = builder.build();
            inviteAngelList = angelRepository.findList(angelRepPageQuery);
            if (CollectionUtils.isEmpty(inviteAngelList)) {
                throw new BusinessException(ActivityErrorCode.EXPORT_ACTIVITY_DATA_ZERO) ;
            }
        }
        //入参包含受邀护士pin、受邀护士ID、受邀护士名称、受邀护士职称、受邀护士医院、受邀护士资质审核状态、首次入驻时间-开始时间、首次入驻时间-结束时间、审核时间-开始时间、审核时间-结束时间，查询受邀护士ID列表
        List<JdhAngel> beInviteAngelList = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getAngelId()) || StringUtils.isNotBlank(request.getAngelPin()) || StringUtils.isNotBlank(request.getAngelName())
                || StringUtils.isNotBlank(request.getProfessionTitleCode()) || StringUtils.isNotBlank(request.getInstitutionName()) || Objects.nonNull(request.getAuditProcessStatus())
                || Objects.nonNull(request.getRegisterStartTime()) || Objects.nonNull(request.getRegisterEndTime()) || Objects.nonNull(request.getAuditProcessDateStartTime()) || Objects.nonNull(request.getAuditProcessDateEndTime())) {
            JdhAngelRepQuery.JdhAngelRepQueryBuilder builder = JdhAngelRepQuery.builder();
            if (StringUtils.isNotBlank(request.getAngelId())) {
                builder.angelId(Long.valueOf(request.getAngelId()));
            }
            if (StringUtils.isNotBlank(request.getAngelPin())) {
                builder.angelPin(request.getAngelPin());
            }
            if (StringUtils.isNotBlank(request.getAngelName())) {
                builder.angelName(request.getAngelName());
            }
            if (StringUtils.isNotBlank(request.getProfessionTitleCode())) {
                builder.professionTitleCode(request.getProfessionTitleCode());
            }
            if (StringUtils.isNotBlank(request.getInstitutionName())) {
                builder.institutionName(request.getInstitutionName());
            }
            if (Objects.nonNull(request.getAuditProcessStatus())) {
                builder.auditProcessStatus(request.getAuditProcessStatus());
            }
            if (Objects.nonNull(request.getRegisterStartTime())) {
                builder.registerStartTime(request.getRegisterStartTime());
            }
            if (Objects.nonNull(request.getRegisterEndTime())) {
                builder.registerEndTime(request.getRegisterEndTime());
            }
            if (Objects.nonNull(request.getAuditProcessDateStartTime())) {
                builder.auditProcessDateStartTime(request.getAuditProcessDateStartTime());
            }
            if (Objects.nonNull(request.getAuditProcessDateEndTime())) {
                builder.auditProcessDateEndTime(request.getAuditProcessDateEndTime());
            }
            JdhAngelRepQuery angelRepPageQuery = builder.build();
            beInviteAngelList = angelRepository.findList(angelRepPageQuery);
            if (CollectionUtils.isEmpty(beInviteAngelList)) {
                throw new BusinessException(ActivityErrorCode.EXPORT_ACTIVITY_DATA_ZERO) ;
            }
        }
        //邀请护士ID列表 + 受邀护士ID列表 + 是否已奖励查询护士活动信息
        JdhAngelActivityRepQuery.JdhAngelActivityRepQueryBuilder builder = JdhAngelActivityRepQuery.builder();
        if (CollectionUtils.isNotEmpty(inviteAngelList)) {
            builder.angelIdList(inviteAngelList.stream().map(JdhAngel::getAngelId).distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(beInviteAngelList)) {
            builder.applySql("JSON_EXTRACT(activity_rule_progress, '$.acceptAngelId') in (" + Joiner.on(",").join(beInviteAngelList.stream().map(JdhAngel::getAngelId).distinct().collect(Collectors.toList())) + ")");
        }
        if (Objects.nonNull(request.getRewardStatus())) {
            builder.rewardStatus(request.getRewardStatus());
        }
        if (Objects.nonNull(request.getCreateStartTime())) {
            builder.createStartTime(request.getCreateStartTime());
        }
        if (Objects.nonNull(request.getCreateEndTime())) {
            builder.createEndTime(request.getCreateEndTime());
        }
        JdhAngelActivityRepQuery activityRepQuery = builder.build();
        Integer count = activityRepository.findCount(activityRepQuery);
        if(Objects.nonNull(count) && count > FileExportTypeEnum.ACTIVITY_ANGEL_RECRUITMENT_EXPORT.getMaxCount()){
            throw new BusinessException(ActivityErrorCode.EXPORT_ACTIVITY_DATA_OUT);
        }
        //1、构建上下文
        Map<String, Object> ctx = new HashMap<>();
        ctx.put("query", activityRepQuery);
        ctx.put("scene", FileExportTypeEnum.ACTIVITY_ANGEL_RECRUITMENT_EXPORT.getType());
        ctx.put("userPin",request.getUserPin());
        ctx.put("operationType", FileExportTypeEnum.ACTIVITY_ANGEL_RECRUITMENT_EXPORT.getType());
        //2、调用通用文件导入能力
        return fileManageApplication.export(ctx);
    }

    /**
     *
     * @param page
     * @return
     */
    private PageDto<AngelRecruitmentActivityManDto> convertAngelRecruitmentActivityManDto(Page<JdhAbstractAngelActivity> page) {
        if (Objects.isNull(page)) {
            return null;
        }
        List<JdhAbstractAngelActivity> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageDto.getEmptyPage();
        }
        List<JdhAngelRecruitmentActivity> list = Convert.toList(JdhAngelRecruitmentActivity.class, records);
        Set<Long> angelSet = list.stream().map(JdhAbstractAngelActivity::getAngelId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> beInviteAngelSet = list.stream().map(recruitmentActivity -> recruitmentActivity.getActivityRuleProgress().getAcceptAngelId()).filter(Objects::nonNull).collect(Collectors.toSet());
        angelSet.addAll(beInviteAngelSet);
        List<Long> angelIdList = Lists.newArrayList(angelSet);

        JdhAngelRepPageQuery angelRepPageQuery = JdhAngelRepPageQuery.builder().angelIdList(angelIdList).build();
        angelRepPageQuery.setPageNum(1);
        angelRepPageQuery.setPageSize(100000);
        angelRepPageQuery.setSearchDel(true);
        Page<JdhAngel> jdhAngelPage = angelRepository.page(angelRepPageQuery);
        Map<Long, JdhAngel> id2Angel = new HashMap<>();
        if (Objects.nonNull(jdhAngelPage)) {
            id2Angel = jdhAngelPage.getRecords().stream().collect(Collectors.toMap(JdhAngel::getAngelId, angel -> angel));
        };

        List<AngelRecruitmentActivityManDto> dtoList = new ArrayList<>();
        for (JdhAngelRecruitmentActivity recruitmentActivity : list) {
            JdhAngel angel = id2Angel.get(recruitmentActivity.getAngelId());
            JdhAngel beInviteAngel = id2Angel.get(recruitmentActivity.getActivityRuleProgress().getAcceptAngelId());
            AngelRecruitmentActivityManDto dto = new AngelRecruitmentActivityManDto();
            dto.setAngelId(String.valueOf(beInviteAngel.getAngelId()));
            dto.setAngelPin(beInviteAngel.getAngelPin());
            dto.setAngelName(new UserName(beInviteAngel.getAngelName()).mask());
            dto.setAngelHeadImg(beInviteAngel.getHeadImg());
            dto.setInviteAngelId(String.valueOf(recruitmentActivity.getAngelId()));
            dto.setInviteAngelPin(angel.getAngelPin());
            dto.setInviteAngelName(new UserName(angel.getAngelName()).mask());
            dto.setProfessionTitleCode(CollectionUtils.isNotEmpty(beInviteAngel.getJdhAngelProfessionRelList()) ? beInviteAngel.getJdhAngelProfessionRelList().get(0).getProfessionTitleCode() : null);
            dto.setProfessionTitleName(CollectionUtils.isNotEmpty(beInviteAngel.getJdhAngelProfessionRelList()) ? beInviteAngel.getJdhAngelProfessionRelList().get(0).getProfessionTitleName() : null);
            dto.setInstitutionName(CollectionUtils.isNotEmpty(beInviteAngel.getJdhAngelProfessionRelList()) ? beInviteAngel.getJdhAngelProfessionRelList().get(0).getInstitutionName() : null);
            dto.setAuditProcessStatus(beInviteAngel.getAuditProcessStatus());
            dto.setAuditProcessStatusDesc(AngelAuditProcessStatusEnum.getDescByCode(beInviteAngel.getAuditProcessStatus()));
            dto.setRewardStatus(recruitmentActivity.getRewardStatus());
            dto.setRewardStatusDesc(ActivityRewardStatusEnum.getDescByStatus(recruitmentActivity.getRewardStatus()));
            List<ActivityRewardProgress> cashRewardList = Optional.ofNullable(recruitmentActivity.getActivityRewardProgress()).orElse(new ArrayList<>()).stream().filter(activityRewardProgress -> Objects.equals(activityRewardProgress.getConfigRewardType(), ActivityConfigRewardTypeEnum.CASH.getType())).collect(Collectors.toList());
            dto.setRewardValue(CollectionUtils.isNotEmpty(cashRewardList) ?
                    cashRewardList.stream().map(ActivityRewardProgress::getConfigRewardValue).filter(StringUtils::isNotBlank).map(BigDecimal::new).reduce(BigDecimal.ZERO, BigDecimal::add).toPlainString() : "");
            dto.setRegisterTime(DateUtil.formatDateTime(recruitmentActivity.getCreateTime()));
            dto.setAuditProcessDateTime(beInviteAngel.getAuditProcessDate());
            dtoList.add(dto);
        }
        PageDto<AngelRecruitmentActivityManDto> resultPage = new PageDto<>();
        resultPage.setList(dtoList);
        resultPage.setTotalPage(page.getPages());
        resultPage.setPageNum(page.getCurrent());
        resultPage.setPageSize(page.getSize());
        resultPage.setTotalCount(page.getTotal());
        return resultPage;
    }

    /**
     *
     * @param activityConfig
     * @param angel
     * @return
     */
    private ActivityConfigAngelRecruitmentDto convertActivityConfigAngelRecruitmentDto(ActivityConfigRequest request, JdhAngelRecruitmentActivityConfig activityConfig, JdhAngel angel) {
        //组装活动基础配置信息
        ActivityConfigAngelRecruitmentDto dto = ActivityConfigConverter.INS.convertActivityConfig2Dto(activityConfig);
        dto.setInvitedUrl(Objects.nonNull(dto) && Objects.nonNull(dto.getActivityConfigRule()) ? dto.getActivityConfigRule().getInvitedUrl() : "");
        dto.setBeInvitedUrl(Objects.nonNull(dto) && Objects.nonNull(dto.getActivityConfigRule()) ? String.format(dto.getActivityConfigRule().getBeInvitedUrl(), angel.getAngelId(), activityConfig.getActivityConfigId()) : "");
        //组装护士信息
        dto.setAngelId(String.valueOf(angel.getAngelId()));
        dto.setAngelName(new UserName(angel.getAngelName()).mask());
        dto.setProfessionTitleCode(CollectionUtils.isNotEmpty(angel.getJdhAngelProfessionRelList()) ? angel.getJdhAngelProfessionRelList().get(0).getProfessionTitleCode() : null);
        dto.setProfessionTitleName(CollectionUtils.isNotEmpty(angel.getJdhAngelProfessionRelList()) ? angel.getJdhAngelProfessionRelList().get(0).getProfessionTitleName() : null);
        dto.setInstitutionName(CollectionUtils.isNotEmpty(angel.getJdhAngelProfessionRelList()) ? angel.getJdhAngelProfessionRelList().get(0).getInstitutionName() : null);
        dto.setAngelHeadImg(angel.getHeadImg());
        dto.setCanShare(Objects.nonNull(angel) && Objects.nonNull(angel.getNethpDocId()) && Objects.equals(AngelAuditProcessStatusEnum.AUDIT_PASS.getCode(), angel.getAuditProcessStatus()));
        //组装邀请数据总览
        AngelActivityOverviewDto overviewDto = new AngelActivityOverviewDto();
        dto.setOverview(overviewDto);

        //本月邀请数据
        DateTime beginOfMonth = DateUtil.beginOfMonth(new Date());
        DateTime nextMonth = DateUtil.offsetMonth(beginOfMonth, 1);
        List<ActivityDetailGroupCount> groupCountCurrentMonthList = activityRepository.findActivityDetailGroupCount(ActivityDetailGroupCountQuery.builder().activityConfigId(dto.getActivityConfigId())
                .column("reward_status")
                .angelId(angel.getAngelId())
                .activityConfigType(dto.getActivityConfigType()).createStartTime(beginOfMonth).createEndTime(nextMonth).build());
        Long currentMonthInviteNum = 0L;
        Long currentMonthEligibleInviteNum = 0L;
        for (ActivityDetailGroupCount groupCountCurrentMonth : groupCountCurrentMonthList) {
            Integer rewardStatus = Convert.convert(Integer.class, groupCountCurrentMonth.getGroupKeyValue());
            if (Objects.equals(rewardStatus, ActivityRewardStatusEnum.REWARD_ACHIEVE.getStatus())) {
                currentMonthEligibleInviteNum += groupCountCurrentMonth.getCount();
            }
            currentMonthInviteNum += groupCountCurrentMonth.getCount();
        }
        overviewDto.setCurrentMonthEligibleNum(currentMonthEligibleInviteNum.intValue());
        overviewDto.setCurrentMonthInviteNum(currentMonthInviteNum.intValue());
        //本月获得奖金
        //查询所有已发放奖励的数据
        List<JdhAbstractAngelActivity> list = activityRepository.findList(JdhAngelActivityRepQuery.builder()
                .rewardStatus(ActivityRewardStatusEnum.REWARD_ACHIEVE.getStatus()).activityConfigId(dto.getActivityConfigId()).activityConfigType(dto.getActivityConfigType())
                .angelId(angel.getAngelId()).createStartTime(beginOfMonth).createEndTime(nextMonth).build());
        list = CollectionUtils.isEmpty(list) ? new ArrayList<>() : list;
        BigDecimal activityDetailGroupSum = list.stream().map(angelActivity -> {
            JdhAngelRecruitmentActivity activity = Convert.convert(JdhAngelRecruitmentActivity.class, angelActivity);
            List<ActivityRewardProgress> cashRewardList = Optional.ofNullable(activity.getActivityRewardProgress()).orElse(new ArrayList<>()).stream().filter(activityRewardProgress -> Objects.equals(activityRewardProgress.getConfigRewardType(), ActivityConfigRewardTypeEnum.CASH.getType())).collect(Collectors.toList());
            return CollectionUtils.isEmpty(cashRewardList) ? BigDecimal.ZERO : cashRewardList.stream().map(activityRewardProgress -> new BigDecimal(activityRewardProgress.getConfigRewardValue())).reduce(BigDecimal.ZERO, BigDecimal::add);
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        overviewDto.setCurrentMonthReceivedReward(activityDetailGroupSum);


        //累计邀请数据
        DateTime beginOfYear = DateUtil.beginOfYear(new Date());
        DateTime nextYear = DateUtil.offset(beginOfYear, DateField.YEAR,1);
        List<ActivityDetailGroupCount> groupCountYearTotalList = activityRepository.findActivityDetailGroupCount(ActivityDetailGroupCountQuery.builder().activityConfigId(dto.getActivityConfigId())
                .column("reward_status")
                .angelId(angel.getAngelId())
                .activityConfigType(dto.getActivityConfigType()).build());
        Long totalInviteNum = 0L;
        Long totalEligibleInviteNum = 0L;
        for (ActivityDetailGroupCount groupCountTotal : groupCountYearTotalList) {
            Integer rewardStatus = Convert.convert(Integer.class, groupCountTotal.getGroupKeyValue());
            if (Objects.equals(rewardStatus, ActivityRewardStatusEnum.REWARD_ACHIEVE.getStatus()) && Objects.nonNull(groupCountTotal.getCount())) {
                totalEligibleInviteNum += groupCountTotal.getCount();
            }
            totalInviteNum += groupCountTotal.getCount();
        }
        overviewDto.setTotalMonthEligibleNum(totalEligibleInviteNum.intValue());
        overviewDto.setTotalMonthInviteNum(totalInviteNum.intValue());
        //累计获得奖金
        List<JdhAbstractAngelActivity> totalList = activityRepository.findList(JdhAngelActivityRepQuery.builder()
                .rewardStatus(ActivityRewardStatusEnum.REWARD_ACHIEVE.getStatus()).activityConfigId(dto.getActivityConfigId()).activityConfigType(dto.getActivityConfigType())
                .angelId(angel.getAngelId()).build());
        totalList = CollectionUtils.isEmpty(totalList) ? new ArrayList<>() : totalList;
        BigDecimal activityDetailGroupTotalSum = totalList.stream().map(angelActivity -> {
            JdhAngelRecruitmentActivity activity = Convert.convert(JdhAngelRecruitmentActivity.class, angelActivity);
            List<ActivityRewardProgress> cashRewardList = Optional.ofNullable(activity.getActivityRewardProgress()).orElse(new ArrayList<>()).stream().filter(activityRewardProgress -> Objects.equals(activityRewardProgress.getConfigRewardType(), ActivityConfigRewardTypeEnum.CASH.getType())).collect(Collectors.toList());
            return CollectionUtils.isEmpty(cashRewardList) ? BigDecimal.ZERO : cashRewardList.stream().map(activityRewardProgress -> new BigDecimal(activityRewardProgress.getConfigRewardValue())).reduce(BigDecimal.ZERO, BigDecimal::add);
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        overviewDto.setTotalMonthReceivedReward(activityDetailGroupTotalSum);

        //邀请护士明细列表
        AngelActivityRecruitmentRequest recruitmentRequest = new AngelActivityRecruitmentRequest();
        recruitmentRequest.setInviteAngelId(String.valueOf(angel.getAngelId()));
        recruitmentRequest.setCreateStartTime(beginOfYear);
        recruitmentRequest.setCreateEndTime(nextYear);
        recruitmentRequest.setPageNum(request.getPageNum());
        recruitmentRequest.setPageSize(request.getPageSize());
        PageDto<AngelRecruitmentActivityManDto> pageDto = queryAngelRecruitmentActivity4Man(recruitmentRequest);
        overviewDto.setInviteAngels(pageDto);
        //修改审核描述
        if (Objects.nonNull(pageDto) && CollectionUtils.isNotEmpty(pageDto.getList())) {
            pageDto.getList().forEach(angelActivity -> {
                if (Objects.equals(angelActivity.getAuditProcessStatus(), AngelAuditProcessStatusEnum.AUDIT_PASS.getCode()) && Objects.equals(angelActivity.getRewardStatus(), ActivityRewardStatusEnum.REWARD_WAIT.getStatus())) {
                    angelActivity.setAuditProcessStatus(AngelActivityAuditProcessStatusEnum.NO_SETTLEMENT.getCode());
                    angelActivity.setAuditProcessStatusDesc(AngelActivityAuditProcessStatusEnum.NO_SETTLEMENT.getDesc());
                } else {
                    angelActivity.setAuditProcessStatusDesc(AngelActivityAuditProcessStatusEnum.getDescByCode(angelActivity.getAuditProcessStatus()));
                }
            });
        }
        return dto;
    }
}