package com.jdh.o2oservice.application.settlement.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.cjgexpress.utils.StringUtils;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.angel.service.ActivityApplication;
import com.jdh.o2oservice.application.angel.service.AngelApplication;
import com.jdh.o2oservice.application.settlement.convert.JdServiceSettleConvert;
import com.jdh.o2oservice.application.settlement.service.JdServiceSettleReadApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementQueryContext;
import com.jdh.o2oservice.core.domain.settlement.context.AngelSettlementUpdateContext;
import com.jdh.o2oservice.core.domain.settlement.enums.*;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlement;
import com.jdh.o2oservice.core.domain.settlement.model.AngelSettlementDetail;
import com.jdh.o2oservice.core.domain.settlement.model.JdhSettlementEbs;
import com.jdh.o2oservice.core.domain.settlement.repository.AngelSettlementRepository;
import com.jdh.o2oservice.core.domain.settlement.repository.db.SettlementEbsRepository;
import com.jdh.o2oservice.core.domain.settlement.rpc.HySettleRpc;
import com.jdh.o2oservice.core.domain.settlement.vo.AngelCashOutVo;
import com.jdh.o2oservice.core.domain.settlement.vo.BankCardDetailVo;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawAccountVo;
import com.jdh.o2oservice.core.domain.settlement.vo.WithdrawDetailVo;
import com.jdh.o2oservice.export.angel.cmd.JdhActivityConfigAngelRecruitmentRule;
import com.jdh.o2oservice.export.angel.dto.ActivityConfigAngelRecruitmentDto;
import com.jdh.o2oservice.export.angel.dto.AngelActivityRecruitmentDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelActivityRecruitmentRequest;
import com.jdh.o2oservice.export.angel.query.AngelRequest;
import com.jdh.o2oservice.export.settlement.SettlementEbsRequest;
import com.jdh.o2oservice.export.settlement.cmd.AngelCashOutCmd;
import com.jdh.o2oservice.export.settlement.cmd.JdSettlementStatusCmd;
import com.jdh.o2oservice.export.settlement.dto.*;
import com.jdh.o2oservice.export.settlement.query.AngelSettleQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.swing.text.html.Option;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

import static com.jdh.o2oservice.base.constatnt.DateConstant.YMDHMS;
import static com.jdh.o2oservice.base.util.TimeFormat.LONG_PATTERN_LINE;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 2:36 下午
 * @Description:
 */
@Service
@Slf4j
public class JdServiceSettleReadApplicationImpl implements JdServiceSettleReadApplication {
    /**
     *
     */
    @Autowired
    private AngelSettlementRepository angelSettlementRepository;
    /**
     *
     */
    @Autowired
    private HySettleRpc hySettleRpc;
    /**
     *
     */
    @Autowired
    private SettlementEbsRepository settlementEbsRepository;

    /**
     * jimRedisService
     */
    @Resource
    private Cluster jimClient;
    /**
     *
     */
    @Autowired
    private AngelApplication angelApplication;
    /**
     *
     */
    @Autowired
    private  DuccConfig duccConfig;

    @Autowired
    private ActivityApplication activityApplication;

    /**
     *
     * @param query
     * @return
     */
    @Override
    public PageDto<AngelSettlementDto> querySettlementPage(AngelSettleQuery query) {
        log.info("JdServiceSettleReadApplicationImpl querySettlementPage query={}", JSON.toJSONString(query));
        if (StringUtils.isBlank(query.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        query.setAngelId(jdhAngelDto.getAngelId());
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        /**查聚合，不查询初始化状态*/
        if (SettleTypeEnum.INCOME.getType().equals(query.getSettlementType())){
            queryContext.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
        }
        Page<AngelSettlement> res = angelSettlementRepository.querySettlementPage(queryContext);
        return JdServiceSettleConvert.ins.entity2AngelSettlementDtoPage(res);
    }

    /**
     *
     * @param query
     * @return
     */
    @Override
    public AngelSettlementDto querySettlementDetailList(AngelSettleQuery query) {
        log.info("JdServiceSettleReadApplicationImpl querySettlementDetailList query={}", JSON.toJSONString(query));
        if (StringUtils.isBlank(query.getUserPin()) || Objects.isNull(query.getSettleId())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        List<AngelSettlement> settlementList = angelSettlementRepository.querySettlementList(queryContext);
        if (CollectionUtils.isEmpty(settlementList)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelSettlementDto settlementDto = JdServiceSettleConvert.ins.entity2AngelSettlementDto(settlementList.get(0));
        //支出详情查互医
        if (query.getQuerySettleDetail()) {
            if (Objects.equals(settlementDto.getSettlementType(), SettleTypeEnum.EXPEND.getType())) {
                WithdrawDetailVo detailVo = hySettleRpc.queryWithdrawRecordDetail(settlementDto.getSettlementNo(),query.getUserPin(),null);
                JdServiceSettleConvert.ins.packWithdrawDetailVo(settlementDto, detailVo);
            } else {
                AngelSettlement angelSettlement=settlementList.get(0);
                Date dataCleansingTimestamp = DateUtil.parse(duccConfig.getDataCleansingTimestamp(),YMDHMS);
                //如果可以查到promiseId，或者查询的是已结算的状态，则不为itemType=3or4的情况，还用settleId进行查询
                if (Objects.nonNull(angelSettlement.getPromiseId())&& angelSettlement.getCreateTime().after(dataCleansingTimestamp)) {
                    /**第二次查明细，只查询初始状态，且需要用settleId去置换promiseId查询*/
                    queryContext.setPromiseId(settlementList.get(0).getPromiseId());
                    queryContext.setSettleStatus(SettleStatusEnum.INIT.getType());
                    /**不用settleId查询*/
                    queryContext.setSettleId(null);
                }
                List<AngelSettlement> detailList = angelSettlementRepository.querySettlementList(queryContext);
                settlementDto.setDetailList(JdServiceSettleConvert.ins.entityAngelSettlementDtos(detailList));
                /**feeName映射*/
                Optional.ofNullable(settlementDto.getDetailList()).map(List::stream).orElseGet(Stream::empty).forEach(angelSettlementDetailDto->{
                    angelSettlementDetailDto.setFeeName(AngelSettleItemTypeEnum.getSettleTypeDescByType(angelSettlementDetailDto.getItemType()));
                });
            }
        }

        // 护士邀请活动佣金
        if (AngelSettleItemTypeEnum.ACTIVITY.getType().equals(settlementDto.getItemType())){
            AngelActivityRecruitmentRequest angelActivityRequest = new AngelActivityRecruitmentRequest();
            angelActivityRequest.setAngelActivityId(settlementDto.getItemSourceId());
            AngelActivityRecruitmentDto activityConfigAngelDto = activityApplication.queryRecruitmentAngelActivity(angelActivityRequest);
            log.info("querySettlementDetailList angelActivityRequest={}, activityConfigAngelDto={}", JSON.toJSONString(angelActivityRequest), JSON.toJSONString(activityConfigAngelDto));
            if (activityConfigAngelDto != null && activityConfigAngelDto.getActivityConfigRule() != null){
                settlementDto.setActivityConfigRule(JSON.parseObject(JSON.toJSONString(activityConfigAngelDto.getActivityConfigRule()), ActivityConfigAngelRecruitmentRuleDto.class));
            }
        }

        JdServiceSettleConvert.ins.packDetailMap(settlementDto);
        return settlementDto;
    }

    /**
     * 查询账单明细
     *
     * @param query
     * @return
     */
    @Override
    public AngelSettlementDto querySettlement(AngelSettleQuery query) {
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        List<AngelSettlement> settlementList = angelSettlementRepository.querySettlementList(queryContext);
        if (CollectionUtils.isEmpty(settlementList)) {
            return null;
        }
        AngelSettlementDto settlementDto = JdServiceSettleConvert.ins.entity2AngelSettlementDto(settlementList.get(0));
        if (query.getQuerySettleDetail()) {
            List<AngelSettlementDetail> detailList = angelSettlementRepository.querySettlementDetailList(queryContext);
            settlementDto.setDetailList(JdServiceSettleConvert.ins.entityAngelSettlementDetailDtos(detailList));
        }

        return settlementDto;
    }

    /**
     *
     * @param query
     * @return
     */
    @Override
    public Map<String, BigDecimal> querySettleTotal(AngelSettleQuery query) {
        if (StringUtils.isBlank(query.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        Map<String, BigDecimal> result = new HashMap<>();
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        query.setAngelId(jdhAngelDto.getAngelId());
        AngelSettlementQueryContext incomeQuery = JdServiceSettleConvert.ins.query2Context(query);
        incomeQuery.setSettlementType(SettleTypeEnum.INCOME.getType());
        incomeQuery.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
        BigDecimal incomeTot = angelSettlementRepository.querySettlementAmountTot(incomeQuery);
        result.put("income", incomeTot);

        AngelSettlementQueryContext expendQuery = JdServiceSettleConvert.ins.query2Context(query);
        expendQuery.setSettlementType(SettleTypeEnum.EXPEND.getType());
        BigDecimal expendTot = angelSettlementRepository.querySettlementAmountTot(expendQuery);
        result.put("expend", expendTot);
        //近一年
        AngelSettlementQueryContext yearQuery = JdServiceSettleConvert.ins.getTotAngelSettlementQueryContext(query);
        BigDecimal yearSettlementTot = angelSettlementRepository.querySettlementAmountTot(yearQuery);
        result.put("year", yearSettlementTot);

        return result;
    }

    /**
     *
     * @param query
     * @return
     */
    @Override
    public AngelSettlementMoneyDto queryAngelSettlementMoneyDto(AngelSettleQuery query) {
        if (StringUtils.isBlank(query.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        query.setAngelId(jdhAngelDto.getAngelId());
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        WithdrawAccountVo accountVo = hySettleRpc.queryWithdrawAccount(queryContext);
        AngelSettlementMoneyDto res = JdServiceSettleConvert.ins.vo2AngelSettlementMoneyDto(accountVo);

        queryContext.setSettlementType(SettleTypeEnum.INCOME.getType());
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND,0);
        queryContext.setSettleTimeStart(calendar.getTime());
        calendar.add(Calendar.DAY_OF_MONTH, 1);

        queryContext.setSettleTimeEnd(calendar.getTime());
        queryContext.setSettleStatusList(Arrays.asList(SettleStatusEnum.FREEZE.getType(),SettleStatusEnum.SETTLED.getType()));
        BigDecimal incomeTot = angelSettlementRepository.querySettlementAmountTot(queryContext);
        res.setToDaySettleAmount(incomeTot);
        return res;
    }

    /**
     *
     * @param query
     * @return
     */
    @Override
    public AngelBankDto queryBindBank(AngelSettleQuery query) {
        if (StringUtils.isBlank(query.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(query.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        query.setAngelId(jdhAngelDto.getAngelId());
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(query);
        BankCardDetailVo detailVo = hySettleRpc.queryBankCardDetail(queryContext);
        return JdServiceSettleConvert.ins.vo2AngelBankDto(detailVo);
    }

    /**
     *
     * @param cashOutCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitCashOut(AngelCashOutCmd cashOutCmd) {
        if (StringUtils.isBlank(cashOutCmd.getUserPin())) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        AngelRequest angelRequest = new AngelRequest();
        angelRequest.setAngelPin(cashOutCmd.getUserPin());
        JdhAngelDto jdhAngelDto = angelApplication.queryByAngelInfo(angelRequest);
        if (Objects.isNull(jdhAngelDto)) {
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        cashOutCmd.setAngelId(jdhAngelDto.getAngelId());
        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.REPEAT_CHECK_PRE, cashOutCmd.getUserPin());
        try {
            if (jimClient.exists(redisKey)) {
                throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_REPEAT);
            }
            // 加缓存 分布式锁
            jimClient.setEx(redisKey, "1", 1, TimeUnit.SECONDS);
            //提现重试
            if (Objects.nonNull(cashOutCmd.getSettlementNo())) {
                return retrySubmitCashOut(cashOutCmd);
            } else {
                AngelSettlement angelSettlement = JdServiceSettleConvert.ins.packAngelSettlement(cashOutCmd);
                angelSettlement.setCashTime(new Date());
                Long settleId = angelSettlementRepository.save(angelSettlement);
                if (Objects.isNull(settleId)) {
                    throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
                }
                AngelCashOutVo angelCashOutVo = JdServiceSettleConvert.ins.cmd2AngelCashOutVo(cashOutCmd);
                Long settlementNo = hySettleRpc.withdraw(angelCashOutVo);
                if (Objects.isNull(settlementNo)) {
                    throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
                }
                //成功更新互医结算id
                angelSettlement.setSettlementNo(settlementNo);
                // 更新 状态为处理中
                angelSettlement.setSettleId(settleId);
                angelSettlement.setCashStatus(CashStatusEnum.CASHING.getType());
                Long updateId = angelSettlementRepository.save(angelSettlement);
                if (Objects.isNull(updateId)) {
                    throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
                }
                return settleId;
            }
        }catch(ArgumentsException e) {
            log.error("JdServiceSettleReadApplicationImpl.submitCashOut ArgumentsException={}",e);
            throw e;
        }catch(SystemException e) {
            log.error("JdServiceSettleReadApplicationImpl.submitCashOut SystemException={}",e);
            throw e;
        }catch(BusinessException e) {
            log.error("JdServiceSettleReadApplicationImpl.submitCashOut BusinessException={}",e);
            throw e;
        }catch(Exception e) {
            log.error("JdServiceSettleReadApplicationImpl.submitCashOut e={}",e);
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
        }finally{
            jimClient.del(redisKey);
        }
    }

    /**
     * 更新提现结果
     *
     * @param angelSettlementDto
     * @return
     */
    @Override
    public Boolean updateCashOutResult(AngelSettlementDto angelSettlementDto) {
        AngelSettlement angelSettlement = JdServiceSettleConvert.ins.dto2AngelSettlement(angelSettlementDto);
        Long id = angelSettlementRepository.save(angelSettlement);
        if (Objects.isNull(id)) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_RESULT_UPDATE_FAILED);
        }
        if (CollectionUtils.isNotEmpty(angelSettlementDto.getDetailList())) {
            List<AngelSettlementDetail> detailList = JdServiceSettleConvert.ins.dto2AngelSettlementDetails(angelSettlementDto.getDetailList());
            angelSettlementRepository.batchSaveAngelSettlementAndDetail(null, detailList);
        }
        return Boolean.TRUE;
    }

    /**
     * 查询护士结算信息
     *
     * @param angelSettlQuery
     * @return
     */
    @Override
    public PageDto<AngelSettlementDto> querySettlementPageBySettleTime(AngelSettleQuery angelSettlQuery) {
        AngelSettlementQueryContext queryContext = JdServiceSettleConvert.ins.query2Context(angelSettlQuery);
        Page<AngelSettlement> res = angelSettlementRepository.querySettlementPage(queryContext);
        return JdServiceSettleConvert.ins.entity2AngelSettlementDtoPage(res);
    }

    /**
     * @param jdSettlementStatusCmd
     */
    @Override
    public Integer updateSettleStatusBySettleIdList(JdSettlementStatusCmd jdSettlementStatusCmd) {
        AngelSettlementUpdateContext angelSettlementUpdateContext = JdServiceSettleConvert.ins.query2UpdateContext(jdSettlementStatusCmd);
        return angelSettlementRepository.updateSettleStatusBySettleIdList(angelSettlementUpdateContext);
    }

    /**
     * 查询ebs收入
     * @param settlementEbsRequest
     * @return
     */
    @Override
    public List<JdhSettlementEbs> querySettlementEbs(SettlementEbsRequest settlementEbsRequest) {
        JdhSettlementEbs jdhSettlementEbs = new JdhSettlementEbs();
        jdhSettlementEbs.setPreId(settlementEbsRequest.getPreId());
        List<JdhSettlementEbs> list = settlementEbsRepository.queryJdhSettlementEbsList(jdhSettlementEbs);
        return list;
    }

    /**
     * 提现失败重试
     *
     * @param cashOutCmd
     */
    private Long retrySubmitCashOut(AngelCashOutCmd cashOutCmd) {
        AngelSettlementQueryContext queryContext = new AngelSettlementQueryContext();
        queryContext.setAngelId(cashOutCmd.getAngelId());
        queryContext.setSettlementNo(cashOutCmd.getSettlementNo());
        queryContext.setSettlementType(SettleTypeEnum.EXPEND.getType());
        List<AngelSettlement> cashDetails = angelSettlementRepository.querySettlementList(queryContext);
        if (CollectionUtils.isEmpty(cashDetails)) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_NOT_EXISTS);
        }
        AngelSettlement cashDetail = cashDetails.get(0);
        if (!Objects.equals(cashDetail.getCashStatus(), CashStatusEnum.CASH_FAIL.getType())) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_NOT);
        }
        //更新提现时间
        cashDetail.setCashTime(new Date());
        cashDetail.setUpdateUser(cashOutCmd.getUserPin());
        Long setId = angelSettlementRepository.save(cashDetail);
        if (Objects.isNull(setId)) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
        }
        //互医重试
        Boolean tryRes = hySettleRpc.retryWithdraw(cashOutCmd.getSettlementNo());
        if (!tryRes) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
        }
        cashDetail.setCashStatus(CashStatusEnum.CASHING.getType());
        setId = angelSettlementRepository.save(cashDetail);
        if (Objects.isNull(setId)) {
            throw new BusinessException(SettleErrorCode.ANGEL_SUBMIT_CASH_FAILED);
        }
        return setId;
    }

}
