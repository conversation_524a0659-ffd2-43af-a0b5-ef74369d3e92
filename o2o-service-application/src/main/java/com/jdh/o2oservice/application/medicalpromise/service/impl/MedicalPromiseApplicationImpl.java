package com.jdh.o2oservice.application.medicalpromise.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angel.service.StationApplication;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.medicalpromise.convert.MedicalPromiseConvert;
import com.jdh.o2oservice.application.medicalpromise.service.MedPromiseExtApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceIndicatorApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.product.service.ProductSpecimenCodeApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.promise.service.PromiseApplication;
import com.jdh.o2oservice.application.provider.event.ProviderPromiseEventSubscriber;
import com.jdh.o2oservice.application.provider.service.ProviderApplication;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.application.report.service.MedicalReportApplication;
import com.jdh.o2oservice.application.settlement.context.SettlementEbsContext;
import com.jdh.o2oservice.application.settlement.service.SettlementEbsApplication;
import com.jdh.o2oservice.application.support.service.DictApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.application.support.service.PdfApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.base.annotation.AlarmPolicy;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CacheConstant;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.QuickCheckStatusMappingConfig;
import com.jdh.o2oservice.base.ducc.model.ReportIndicatorNormalRangeConfig;
import com.jdh.o2oservice.base.ducc.model.ReportNormalRangeStationConfig;
import com.jdh.o2oservice.base.ducc.model.report.*;
import com.jdh.o2oservice.base.enums.*;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.*;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.AngelStationModeTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.InventoryChannelEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.medpromise.model.*;
import com.jdh.o2oservice.core.domain.medpromise.model.*;
import com.jdh.o2oservice.core.domain.medpromise.query.MedPromiseHistoryQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedPromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.product.model.JdhStationIndicatorRel;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceIndicatorRepository;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.QuickCheckPushInfoBO;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportIndicatorRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.support.basic.model.BaseDomainErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.model.Birthday;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DirectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.DirectionRequestParam;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.report.bo.MedicalReportQueryBO;
import com.jdh.o2oservice.core.domain.report.bo.SyncReportToCenterResBo;
import com.jdh.o2oservice.core.domain.support.ship.enums.JdLogisticsEventTypeEnum;
import com.jdh.o2oservice.export.medicalpromise.cmd.PlanDetailCmd;
import com.jdh.o2oservice.core.domain.medpromise.bo.QuickCheckLabQueryStatusBo;
import com.jdh.o2oservice.core.domain.medpromise.context.*;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedicalPromiseCallbackResultEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.*;
import com.jdh.o2oservice.core.domain.medpromise.factory.StoreDispatchRuleFactory;
import com.jdh.o2oservice.core.domain.medpromise.model.*;
import com.jdh.o2oservice.core.domain.medpromise.query.MedPromiseHistoryQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseEsQuery;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.StoreDispatchRule;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedPromiseHistoryRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.es.JdMedicalPromiseEsRepository;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.medpromise.service.MedicalPromiseDomainService;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuIdentifier;
import com.jdh.o2oservice.core.domain.product.model.JdhStationIndicatorRel;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceIndicatorRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseExtend;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.QuickCheckPushInfoBO;
import com.jdh.o2oservice.core.domain.report.bo.MedicalReportQueryBO;
import com.jdh.o2oservice.core.domain.report.bo.SyncReportToCenterResBo;
import com.jdh.o2oservice.core.domain.report.model.MedicalReport;
import com.jdh.o2oservice.core.domain.report.model.ReportCenterReport;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportIndicatorRepository;
import com.jdh.o2oservice.core.domain.report.repository.db.MedicalReportRepository;
import com.jdh.o2oservice.core.domain.report.service.ReportCenterDomainService;
import com.jdh.o2oservice.core.domain.support.basic.model.BaseDomainErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DirectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.DirectionRequestParam;
import com.jdh.o2oservice.core.domain.support.file.context.PutFileResult;
import com.jdh.o2oservice.core.domain.support.file.enums.FileExportTypeEnum;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.core.domain.support.pdf.bo.IndicatorPdfBo;
import com.jdh.o2oservice.core.domain.support.pdf.bo.MedicalPromisePdfBo;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.enums.ServiceHomeTypeEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.export.angel.cmd.ReduceInventoryCmd;
import com.jdh.o2oservice.export.angel.dto.SkuAngelStationDto;
import com.jdh.o2oservice.export.angel.dto.SkuAngelStationResultDto;
import com.jdh.o2oservice.export.angel.dto.StationDto;
import com.jdh.o2oservice.export.angel.query.AddressDetail;
import com.jdh.o2oservice.export.angel.query.QuerySkuAngelStationRequest;
import com.jdh.o2oservice.export.medicalpromise.cmd.*;
import com.jdh.o2oservice.export.medicalpromise.dto.*;
import com.jdh.o2oservice.export.medicalpromise.query.*;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.dto.ServiceIndicatorDto;
import com.jdh.o2oservice.export.product.dto.ServiceItemDto;
import com.jdh.o2oservice.export.product.query.JdhIndicatorExactQuery;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.product.query.ServiceItemQuery;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.PromiseStationDto;
import com.jdh.o2oservice.export.promise.dto.VoucherDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.PromiseListRequest;
import com.jdh.o2oservice.export.promise.query.VoucherIdRequest;
import com.jdh.o2oservice.export.provider.dto.ExportDataDTO;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import com.jdh.o2oservice.export.provider.query.StoreInfoRequest;
import com.jdh.o2oservice.export.report.cmd.MedicalReportSaveCmd;
import com.jdh.o2oservice.export.report.cmd.UpdateMedPromiseStationCmd;
import com.jdh.o2oservice.export.report.dto.*;
import com.jdh.o2oservice.export.support.command.CreatePdfCmd;
import com.jdh.o2oservice.export.support.dto.DictInfoDto;
import com.jdh.o2oservice.export.support.dto.PdfCreateDto;
import com.jdh.o2oservice.export.support.query.DictRequest;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhMedicalPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhMedicalPromisePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Description: 履约检测单应用层impl
 * @Author: wangpengfei144
 * @Date: 2024/4/15
 */
@Service
@Slf4j
public class MedicalPromiseApplicationImpl implements MedicalPromiseApplication {

    /**
     * 履约检测单domain层
     */
    @Autowired
    private MedicalPromiseDomainService medicalPromiseDomainService;

    /**
     * 顺序规则factory
     */
    @Autowired
    private StoreDispatchRuleFactory storeDispatchRuleFactory;

    /**
     * 检测单仓储层
     */
    @Autowired
    private MedicalPromiseRepository medicalPromiseRepository;

    @Resource
    private JdhMedicalPromisePoMapper jdhMedicalPromisePoMapper;

    /**
     * 履约应用层
     */
    @Autowired
    private PromiseApplication promiseApplication;

    /**
     * DUCC
     */
    @Autowired
    private DuccConfig duccConfig;

    /**
     * jdhSkuRepository
     */
    @Autowired
    private JdhSkuRepository jdhSkuRepository;

    /**
     * jimRedisService
     */
    @Resource
    private Cluster jimClient;

    /**
     * 报告medicalReportApplication
     */
    @Autowired
    private MedicalReportApplication medicalReportApplication;
    /**
     * 商品耗材条码
     */
    @Autowired
    private ProductSpecimenCodeApplication productSpecimenCodeApplication;

    /**
     * 供应商门店
     */
    @Autowired
    private ProviderStoreApplication providerStoreApplication;

    /**
     * 商品
     */
    @Resource
    private ProductApplication productApplication;

    /**
     * 地址服务
     */
    @Autowired
    private AddressRpc addressRpc;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * 文件管理服务
     */
    @Autowired
    private FileManageService fileManageService;

    /**
     * redisLockUtil
     */
    @Resource
    private RedisLockUtil redisLockUtil;

    /**
     * 指标
     */
    @Autowired
    private ProductServiceIndicatorApplication productServiceIndicatorApplication;

    /**
     * promiseRepository
     */
    @Autowired
    private PromiseRepository promiseRepository;
    /**
     *
     */
    @Autowired
    private VerticalBusinessRepository businessRepository;

    /**
     * 结算推送ebs
     */
    @Autowired
    private SettlementEbsApplication settlementEbsApplication;

    /**
     * 服务站应用层
     */
    @Autowired
    private StationApplication stationApplication;

    /**
     * es
     */
    @Autowired
    private JdMedicalPromiseEsRepository jdMedicalPromiseEsRepository;

    /**
     * jimRedisService
     */
    @Resource
    private Cluster cluster;

    /**
     * 表达式编译缓存
     */
    private static Map<String, Expression> APPOINT_STATUS_EXPRESSION_MAP = Maps.newHashMap();

    /**
     * 供应商
     */
    @Autowired
    private ProviderApplication providerApplication;

    /**
     * pdfApplication
     */
    @Autowired
    private PdfApplication pdfApplication;

    /**
     * productServiceItemApplication
     */
    @Autowired
    private ProductServiceItemApplication productServiceItemApplication;

    /**
     * generateIdFactory
     */
    @Autowired
    private GenerateIdFactory generateIdFactory;

    @Autowired
    private VoucherApplication voucherApplication;

    @Autowired
    private JdOrderApplication jdOrderApplication;

    @Autowired
    private FileManageApplication fileManageApplication;

    /**
     * 线程池工厂
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * medPromiseExtApplication
     */
    @Autowired
    private MedPromiseExtApplication medPromiseExtApplication;

    @Autowired
    private ReportCenterDomainService reportCenterDomainService;

    /**
     * JdhFileRepository在类中的功能是：提供对JdhFile实体的持久化操作和查询服务。
     */
    @Autowired
    private JdhFileRepository jdhFileRepository;

    @Autowired
    private MedicalReportIndicatorRepository medicalReportIndicatorRepository;

    @Autowired
    private MedicalReportRepository medicalReportRepository;

    /**
     * dongDongRobotRpc
     */
    @Resource
    private DongDongRobotRpc dongDongRobotRpc;


    private static Map<String,Pattern> patternCache = Maps.newHashMap();

    @Autowired
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * 商品指标配置
     */
    @Resource
    JdhServiceIndicatorRepository jdhServiceIndicatorRepository;

    @Resource
    private MedPromiseHistoryRepository medPromiseHistoryRepository;

    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "pdfToJpgProvider")
    private MessageProducer pdfToJpgProvider;

    @Value("${topics.report.deal}")
    private String pdfToJpgTopic;

    @Autowired
    private ProviderPromiseEventSubscriber providerPromiseEventSubscriber;

    /**
     * 距离路径规划
     */
    @Resource
    private DirectionServiceRpc directionServiceRpc;
    @Autowired
    private DictApplication dictApplication;

    /**
     * 批量创建检测单
     * @param batchCreateCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedicalPromiseApplicationImpl.createMedicalPromise")
    public Boolean createMedicalPromise(MedicalPromiseCreateCmd batchCreateCmd) {
        MedicalPromiseCreateContext context = MedicalPromiseConvert.INSTANCE.cmd2CreateContext(batchCreateCmd);
        //查询商品信息
        Set<Long> serviceIdList = batchCreateCmd.getPatientList().stream()
                .flatMap(patient -> patient.getServiceItems().stream())
                .map(MedPromiseCmdServiceItem::getServiceId).collect(Collectors.toSet());
        Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder().skuIdList(serviceIdList).querySkuCoreData(true).queryServiceItem(true).build());
        context.setJdhSkuDtoMap(MedicalPromiseConvert.INSTANCE.jdhSku2MedicalPromiseSku(jdhSkuDtoMap));
        context.initVertical();
        //查项目信息

        if (MapUtils.isNotEmpty(jdhSkuDtoMap) && CollectionUtils.isNotEmpty(context.getPatientList())){
            List<JdhSkuDto> skuDtos = new ArrayList<>(jdhSkuDtoMap.values());
            Map<Long,Integer> serviceItemToShowType = Maps.newHashMap();
            for (JdhSkuDto jdhSkuDto : skuDtos) {
                if (CollectionUtils.isNotEmpty(jdhSkuDto.getServiceItemList())){
                    for (ServiceItemDto serviceItem : jdhSkuDto.getServiceItemList()) {
                        serviceItemToShowType.put(serviceItem.getItemId(),serviceItem.getReportShowType());
                    }
                }
            }

            for(MedicalPromiseAppointmentPatient patient : context.getPatientList()){
                for(MedicalPromiseServiceItem serviceItem : patient.getServiceItems()){
                    serviceItem.setReportShowType(serviceItemToShowType.get(serviceItem.getItemId()));
                }
            }
        }

        Boolean result = medicalPromiseDomainService.createMedicalPromise(context);
        //发送检测单创建事件
        if (result && CollectionUtils.isNotEmpty(context.getMedicalPromiseList())) {
            context.getMedicalPromiseList().forEach(medicalPromise -> {
                eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_CREATE,
                        MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).status(medicalPromise.getStatus()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).build()));
            });
        }
        return true;
    }

    /**
     * 实验室派发
     *
     * @param medicalPromiseDispatchCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm
    public Boolean storeDisPatch(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd) {

        // 护理类不需要派发实验室
        if (ServiceTypeEnum.CARE.getServiceType().equalsIgnoreCase(medicalPromiseDispatchCmd.getServiceType())) {
            return Boolean.TRUE;
        }
        //加锁,同一个履约单派发实验室
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STORE_DISPATCH_LOCK_KEY, medicalPromiseDispatchCmd.getPromiseId());
        String uuid = UUID.randomUUID().toString();
        if (!redisLockUtil.tryLock(lockKey, uuid, RedisKeyEnum.STORE_DISPATCH_LOCK_KEY.getExpireTime(), RedisKeyEnum.STORE_DISPATCH_LOCK_KEY.getExpireTimeUnit())) {
            log.error("[MedicalPromiseApplicationImpl.invalidMedicalPromiseBatch],实验室履约单派发加锁失败!");
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }

        try {
            //校验参数
            checkDispatchParam(medicalPromiseDispatchCmd);
            //1.根据promiseId获取履约单
            PromiseDto jdhPromise = getJdhPromise(medicalPromiseDispatchCmd);
            //2.根据promiseId和受检人IDList获取检测单
            List<MedicalPromise> medicalPromises = this.getValidMedicalPromises(medicalPromiseDispatchCmd);
            Set<Long> serviceId = medicalPromises.stream().map(p->Long.valueOf(p.getServiceId())).collect(Collectors.toSet());
            List<JdhSku> jdhSkuList = jdhSkuRepository.queryMultiSku(serviceId.stream().map(t-> JdhSku.builder().skuId(t).build()).collect(Collectors.toList()));
            Set<Long> collect = jdhSkuList.stream().filter(p -> !Objects.equals(CommonConstant.ONE, p.getSkuType())).map(JdhSku::getSkuId).collect(Collectors.toSet());
            medicalPromiseDispatchCmd.setServiceIdSet(collect);
            if (StringUtils.isBlank(medicalPromiseDispatchCmd.getScheduleDay()) || StringUtils.isBlank(medicalPromiseDispatchCmd.getBookTimeSpan())){
                String scheduleDay = DateUtil.format(jdhPromise.getAppointmentTime().getAppointmentStartTime(), CommonConstant.YMD);
                String startTime = DateUtil.format(jdhPromise.getAppointmentTime().getAppointmentStartTime(), CommonConstant.HM);
                String endTime = DateUtil.format(jdhPromise.getAppointmentTime().getAppointmentEndTime(), CommonConstant.HM);
                medicalPromiseDispatchCmd.setScheduleDay(scheduleDay);
                medicalPromiseDispatchCmd.setBookTimeSpan(startTime+"-"+endTime);
            }

            String msg = "订单号："+jdhPromise.getSourceVoucherId();
            medicalPromiseDispatchCmd.setMsg(msg);
            //3.根据skuNo获取排序规则
            List<StoreDispatchRule> storeDispatchRules = this.getStoreDispatchRules(jdhPromise, medicalPromises);
            //6.获取businessMode
            JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(jdhPromise.getVerticalCode());
            medicalPromiseDispatchCmd.setBusinessModeCode(jdhVerticalBusiness.getBusinessModeCode());
            //4.获取用户地址命中服务站对应的实验室列表
            Long orderId = Objects.nonNull(medicalPromiseDispatchCmd.getOrderId()) ? medicalPromiseDispatchCmd.getOrderId() : Long.valueOf(jdhPromise.getSourceVoucherId());
            medicalPromiseDispatchCmd.setOrderId(orderId);
            Map<String,List<SkuAngelStationDto>> stationToAngelStation = Maps.newHashMap();
            //非快检模式,不会走服务站逻辑
            Set<String> existStation = getExistStationSet(medicalPromiseDispatchCmd,stationToAngelStation);
            //5.根据检测项目获取实验室列表
            List<JdhStationServiceItemRelContext> jdhStationServiceItemRelContexts = this.getJdhStationServiceItemRelContexts(medicalPromiseDispatchCmd, medicalPromises,existStation);

            if (CollectionUtils.isEmpty(jdhStationServiceItemRelContexts)){
                sendDongDongMsg("分派实验室失败",medicalPromiseDispatchCmd.getMsg()+",无项目");
                throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATION_GROUP_NULL);
            }

            //7.获取context
            MedicalPromiseDispatchContext medicalPromiseDispatchContext = this.getMedicalPromiseDispatchContext(medicalPromiseDispatchCmd, jdhPromise, medicalPromises, storeDispatchRules, jdhStationServiceItemRelContexts);
            if(CollectionUtils.isNotEmpty(jdhSkuList.get(0).getHighQualityStoreId())){
                //非快检模式,配置了定向实验室,走定向
                medicalPromiseDispatchContext.setHighQualityStoreId(jdhSkuList.get(0).getHighQualityStoreId().get(0));
            }
            //8.计算最佳实验室组，返回的 key：站点ID，value：站点要做的项目
            List<Map<String, Set<String>>> maps = medicalPromiseDomainService.storeDispatch(medicalPromiseDispatchContext);
            if (CollectionUtils.isNotEmpty(maps)){
                //派发实验室
                disPatchStation(medicalPromises, jdhStationServiceItemRelContexts, maps ,stationToAngelStation,medicalPromiseDispatchContext);
                log.info("MedicalPromiseApplicationImpl->storeDisPatch,medicalPromises={}",JsonUtil.toJSONString(medicalPromises));

                //合管
                Map<String, List<String>> mergeMedicalPromiseConfig = duccConfig.getMergeMedicalPromiseConfig();
                if (MapUtils.isNotEmpty(mergeMedicalPromiseConfig)){
                    merge(medicalPromises, mergeMedicalPromiseConfig);
                }
                //批量更新
                medicalPromises.forEach(medicalPromise -> {
                    if (medicalPromiseRepository.save(medicalPromise)<=0){
                        log.info("MedicalPromiseApplicationImpl->storeDisPatch,saveError,medicalPromise={}",JsonUtil.toJSONString(medicalPromise));
                        throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATUS_CHANGE_ERROR);
                    }
                    //保存检测单派发历史
                    executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->this.saveMedPromiseHistory(medicalPromise));

                    // 实验室派发事件
                    eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_LAB_DISTRIBUTE,
                            MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).build()));
                    log.info("MedicalPromiseApplicationImpl storeDisPatch MED_PROMISE_LAB_DISTRIBUTE");
                });
//                medicalPromiseRepository.updateStoreDispatch(medicalPromises);
                return Boolean.TRUE;
            }
            //如果派送不成功抛异常，因为实验室派送是必要环节
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATION_GROUP_NULL);
        }finally {
            redisLockUtil.unLock(lockKey, uuid);
        }


    }

    /**
     * 保存检测单派发历史
     * @param medicalPromise
     */
    private void saveMedPromiseHistory(MedicalPromise medicalPromise){
        MedPromiseHistory medPromiseHistory = new MedPromiseHistory();
        medPromiseHistory.setEventCode("暂无");
        medPromiseHistory.setEventDesc("暂无");
        medPromiseHistory.setAfterStatus(medicalPromise.getStatus());
        medPromiseHistory.setAfterStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()));
        medPromiseHistory.setBeforeStatus(medicalPromise.getStatus());
        medPromiseHistory.setBeforeStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(medicalPromise.getStatus()));
        medPromiseHistory.setProviderId(medicalPromise.getProviderId());
        medPromiseHistory.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        medPromiseHistory.setExtend(JsonUtil.toJSONString(medicalPromise));
        medPromiseHistory.setVersion(medicalPromise.getVersion());
        medPromiseHistory.setDesc("分派实验室");
        medPromiseHistory.setOperator("storeDisPatch");

        medPromiseHistoryRepository.save(medPromiseHistory);
    }

    /**
     * 合管
     * @param medicalPromises
     * @param mergeMedicalPromiseConfig
     */
    private void merge(List<MedicalPromise> medicalPromises, Map<String, List<String>> mergeMedicalPromiseConfig) {
        Set<Long> mergeItemIds = mergeMedicalPromiseConfig.keySet().stream().map(Long::valueOf).collect(Collectors.toSet());
        List<ServiceItemDto> serviceItemDtos = productServiceItemApplication.queryServiceItemList(ServiceItemQuery.builder().itemIds(mergeItemIds).build());
        Map<Long, String> itemIdToName = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(serviceItemDtos)){
            itemIdToName = serviceItemDtos.stream().collect(Collectors.toMap(ServiceItemDto::getItemId, ServiceItemDto::getItemName));
        }

        // 按照value的size进行排序
        LinkedHashMap<String, List<String>> sortedMap = mergeMedicalPromiseConfig.entrySet().stream()
                .sorted(Map.Entry.comparingByValue(Comparator.comparingInt(list -> -list.size())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        //以人分组
        Map<Long, List<MedicalPromise>> pidToList = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));
        //遍历
        for (Map.Entry<Long,List<MedicalPromise>> entry : pidToList.entrySet()){
            List<MedicalPromise> medicalPromiseList =entry.getValue();
            //以station分组
            Map<String, List<MedicalPromise>> stationToList = medicalPromiseList.stream().collect(Collectors.groupingBy(MedicalPromise::getStationId));
            Map<Long, String> finalItemIdToName = itemIdToName;
            stationToList.forEach((stationId, mps)->{
                //合管
                List<String> serviceItemList = mps.stream().map(MedicalPromise::getServiceItemId).collect(Collectors.toList());
                Map<String, List<String>> mergeMedicalPromiseBestConfig = getMergeMedicalPromiseBestConfig(sortedMap, serviceItemList);
                if (MapUtils.isNotEmpty(mergeMedicalPromiseBestConfig)){
                    mergeMedicalPromiseBestConfig.forEach((k,v)->{
                        Long mergeId = generateIdFactory.getId();
                        MedicalPromise medicalPromise = mps.get(0);
                        Set<String> mpSet = mps.stream().map(MedicalPromise::getServiceId).collect(Collectors.toSet());
                        VoucherDto jdhVoucher = voucherApplication.findByVoucherId(VoucherIdRequest.builder().voucherId(medicalPromise.getVoucherId()).build());
                        JdOrder order = jdOrderApplication.queryJdOrderAndItemExt(Long.valueOf(jdhVoucher.getSourceVoucherId()));
                        List<JdOrderItem> jdOrderItemList = order.getJdOrderItemList();
                        JdOrderItem jdOrderItem = jdOrderItemList.stream().filter(p -> mpSet.contains(String.valueOf(p.getSkuId())) && Objects.equals(CommonConstant.ZERO,p.getIsAdded())).findFirst().orElse(null);

                        List<Long> mergeMpIds = mps.stream().filter(p -> v.contains(p.getServiceItemId())).map(MedicalPromise::getMedicalPromiseId).collect(Collectors.toList());
                        medicalPromiseRepository.deleteByMedicalPromiseIds(mergeMpIds,mergeId);
                        medicalPromises.removeIf(p->mergeMpIds.contains(p.getMedicalPromiseId()));
                        MedicalPromise transMergeMp = new MedicalPromise();
                        transMergeMp.setPromiseId(medicalPromise.getPromiseId());
                        transMergeMp.setPromisePatientId(medicalPromise.getPromisePatientId());
                        transMergeMp.setStatus(medicalPromise.getStatus());
                        transMergeMp.setAngelStationId(medicalPromise.getAngelStationId());
                        transMergeMp.setProviderId(medicalPromise.getProviderId());
                        transMergeMp.setStationId(medicalPromise.getStationId());
                        transMergeMp.setStationName(medicalPromise.getStationName());
                        transMergeMp.setStationAddress(medicalPromise.getStationAddress());
                        transMergeMp.setStationPhone(medicalPromise.getStationPhone());
                        transMergeMp.setServiceItemName(finalItemIdToName.get(Long.valueOf(k)));
                        transMergeMp.setServiceItemId(k);
                        transMergeMp.setMedicalPromiseId(mergeId);
                        transMergeMp.setVerticalCode(medicalPromise.getVerticalCode());
                        transMergeMp.setUserPin(medicalPromise.getUserPin());
                        transMergeMp.setServiceType(medicalPromise.getServiceType());
                        transMergeMp.setVoucherId(medicalPromise.getVoucherId());
                        if (Objects.nonNull(jdOrderItem)){
                            transMergeMp.setServiceId(String.valueOf(jdOrderItem.getSkuId()));
                        }else {
                            transMergeMp.setServiceId(medicalPromise.getServiceId());
                        }
                        //加项订单
                        transMergeMp.setFlag(MedicalPromiseFlagEnum.ADD_SERVICE_ITEM.getFlag());
                        medicalPromises.add(transMergeMp);
                    });
                }
            });
        }
    }


    /**
     * 根据条件分页查询检测单
     *
     * @param medicalPromiseListRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedicalPromiseApplicationImpl.queryMedicalPromisePage")
    public PageDto<MedicalPromiseDTO> queryMedicalPromisePage(MedicalPromiseListRequest medicalPromiseListRequest) {
        MedicalPromiseListQuery query = MedicalPromiseConvert.INSTANCE.convert(medicalPromiseListRequest);
        PageDto<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromisePage(query);
        return MedicalPromiseConvert.INSTANCE.convert(medicalPromises);
    }

    /**
     * 检测单状态回传
     *
     * @param medicalPromiseCallbackCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedicalPromiseApplicationImpl.medicalPromiseCallBack")
    public MedicalPromiseCallbackResultDTO medicalPromiseCallBack(MedicalPromiseCallbackCmd medicalPromiseCallbackCmd) {
        //结果类型
        String resultCode = medicalPromiseCallbackCmd.getResultType()+medicalPromiseCallbackCmd.getCode();
        //查找检测单
        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(medicalPromiseCallbackCmd.getMedicalPromiseId()).build());
        if (Objects.isNull(medicalPromise)){
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_NULL);
        }
        if (Objects.equals(MedicalPromiseCallbackResultEnum.ARRIVED_STORE.getResultCode(),resultCode)){
            eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SUBJECTIVE_STATION_RECEIVE,
                    MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).status(medicalPromise.getStatus()).outerId(medicalPromiseCallbackCmd.getAppointmentNo()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).build()));
        }

        MedicalPromiseCallbackResultDTO medicalPromiseCallbackResultDTO = new MedicalPromiseCallbackResultDTO();
        medicalPromiseCallbackResultDTO.setResult(Boolean.TRUE);
        return medicalPromiseCallbackResultDTO;
    }

    /**
     * 根据条件查询检测单
     *
     * @param medicalPromiseListRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedicalPromiseApplicationImpl.queryMedicalPromiseList")
    public List<MedicalPromiseDTO> queryMedicalPromiseList(MedicalPromiseListRequest medicalPromiseListRequest) {
        MedicalPromiseListQuery query = MedicalPromiseConvert.INSTANCE.convert(medicalPromiseListRequest);
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(query);
        log.info("[MedicalPromiseApplicationImpl.queryMedicalPromiseList],medicalPromises={}", JSON.toJSONString(medicalPromises));

        List<MedicalPromiseDTO> res = MedicalPromiseConvert.INSTANCE.convert(medicalPromises);
        //组装项目详情
        packServiceItem(medicalPromiseListRequest, medicalPromises, res);
        //组装受检人详情
        packPatientInfo(medicalPromiseListRequest, res);
        //组装报告详情
        packShowType(medicalPromiseListRequest, res);
        return res;
    }


    /**
     * 查询履约检测单绑定情况
     *
     * @param medicalPromiseBindRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedicalPromiseApplicationImpl.queryMedicalPromiseBindCondition")
    public MedicalPromiseBindDTO queryMedicalPromiseBindCondition(MedicalPromiseBindRequest medicalPromiseBindRequest) {
        //校验参数
        AssertUtils.nonNull(medicalPromiseBindRequest.getPromiseId(),MedPromiseErrorCode.PROMISE_ID_PARAM_NULL);
        //查询检测单
        MedicalPromiseListQuery query = MedicalPromiseConvert.INSTANCE.convert(medicalPromiseBindRequest);
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(query);
        //结果
        MedicalPromiseBindDTO res = new MedicalPromiseBindDTO();
        res.setPromiseId(medicalPromiseBindRequest.getPromiseId());
        List<MedicalPromisePatientDTO> medicalPromisePatientDTOS = Lists.newArrayList();
        //根据人分堆
        Map<Long, List<MedicalPromise>> pidToListMap = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));
        pidToListMap.forEach((k,v)->{
            MedicalPromisePatientDTO medicalPromisePatientDTO = new MedicalPromisePatientDTO();
            medicalPromisePatientDTO.setPromisePatientId(k);
            medicalPromisePatientDTO.setMedicalPromiseConditionDTOList(MedicalPromiseConvert.INSTANCE.convertCondition(v));
            medicalPromisePatientDTOS.add(medicalPromisePatientDTO);
        });
        res.setMedicalPromisePatientDTOList(medicalPromisePatientDTOS);
        return res;
    }

    /**
     * 批量绑定样本条码
     * @param cmd c
     * @return r
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedicalPromiseApplicationImpl.batchBindSpecimenCode")
    public Boolean batchBindSpecimenCode(MedicalPromiseBindSpecimenCodeCmd cmd) {
        MedicalPromiseBatchBindSpecimenCodeContext context = MedicalPromiseConvert.INSTANCE.cmd2MedicalPromiseBindContext(cmd);

//        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(cmd.getVerticalCode());
        List<String> specimenCodeList = new ArrayList<>();
        Set<String> specimenCodeSet = Sets.newHashSet();
        for (MedicalPromiseSpecimenCode specimenCode : context.getSpecimenCodeList()) {
            // 明确忽略的不校验条码规则，否则都走校验
            if (!Boolean.FALSE.equals(context.getCheckCodeRules())) {
                String upperCase = specimenCode.getSpecimenCode();
                //验重
                if (!specimenCodeSet.add(upperCase)){
                    throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_SPECIMEN_CODE_ILLEGAL);
                }
                //jd开头
                String format = duccConfig.getJdRandomFormat();
                if (!upperCase.matches(format)){
                    log.info("条码格式不正确,upperCase={}",upperCase);
                    throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_SPECIMEN_CODE_ILLEGAL);
                }
                if (!duccConfig.getSpecimenCodeLength().contains(upperCase.length())){
                    log.info("条码格式不正确,upperCase={}",upperCase);
                    throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_SPECIMEN_CODE_ILLEGAL);
                }

                //护士上门校验规则
                if (!productSpecimenCodeApplication.checkSpecimenCode(specimenCode.getSpecimenCode())) {
                    throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_SPECIMEN_CODE_ILLEGAL);
                }
            }
            specimenCodeList.add(specimenCode.getSpecimenCode());
        }
        List<MedicalPromise> existPromiseList = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().specimenCodeList(specimenCodeList).build());
        if (CollectionUtils.isNotEmpty(existPromiseList)) {
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_SPECIMEN_CODE_EXIST);
        }
        Boolean result = medicalPromiseDomainService.batchBindSpecimenCode(context);
        //发送检测单创建事件
        if (result && CollectionUtils.isNotEmpty(context.getMedicalPromiseList())) {
            List<MedicalPromise> snapShotMedicalPromiseList = context.getSnapShotMedicalPromiseList();
            Map<Long, Integer> snapIdToStatus = snapShotMedicalPromiseList.stream().collect(Collectors.toMap(MedicalPromise::getMedicalPromiseId, MedicalPromise::getStatus));
            context.getMedicalPromiseList().forEach(medicalPromise -> {
                eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_BIND_SPECIMEN_CODE,
                        MedicalPromiseEventBody.builder()
                                .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                                .status(medicalPromise.getStatus())
                                .beforeStatus(snapIdToStatus.get(medicalPromise.getMedicalPromiseId()))
                                .specimenCode(medicalPromise.getSpecimenCode())
                                .verticalCode(medicalPromise.getVerticalCode())
                                .serviceType(medicalPromise.getServiceType())
                                .promiseId(medicalPromise.getPromiseId())
                                .build()));
            });
        }
        return result;
    }

    /**
     * 推送实验室报告
     * @param medicalPromiseReportCmd
     * @return
     */
    @Override
    @LogAndAlarm(alarmPolicy = {
            @AlarmPolicy(type = AlarmPolicyTypeEnum.UMP),
            @AlarmPolicy(methodName = "实验室推送报告", keyword = "medicalPromiseId", type = AlarmPolicyTypeEnum.DONGDONG_ROBOT)
    })
    public MedicalPromisePushReportDTO pushMedicalPromiseReport(MedicalPromiseReportCmd medicalPromiseReportCmd) {
        MedicalPromiseRepQuery repQuery = new MedicalPromiseRepQuery();
        repQuery.setMedicalPromiseId(medicalPromiseReportCmd.getMedicalPromiseId());
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(repQuery);
        if (Objects.isNull(medicalPromise)){
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_NULL);
        }
        medicalPromiseReportCmd.setReportTime(new Date());
        medicalPromiseReportCmd.setStationId(medicalPromise.getStationId());
        medicalPromiseReportCmd.setPromisePatientId(medicalPromise.getPromisePatientId());

        //如果pdf链接不为空
        if (StringUtils.isNotBlank(medicalPromiseReportCmd.getReportUrl())) {
            //校验域名白名单
            if (CollectionUtils.isNotEmpty(duccConfig.getUrlBlankSet())){
                checkUrl(medicalPromiseReportCmd);
            }
            String fileName = medicalPromiseReportCmd.getMedicalPromiseId() + "_" + DateUtil.format(new Date(), CommonConstant.YMDHMSSS2) + CommonConstant.FILE_PDF;
            PutFileResult putFileResult = fileManageService.transferPut(fileName, medicalPromiseReportCmd.getReportUrl(), FileManageServiceImpl.FolderPathEnum.REPORT, ContentTypeEnum.PDF.getValue());
            medicalPromiseReportCmd.setReportUrl(putFileResult.getFilePath());
        }else if (Objects.nonNull(medicalPromiseReportCmd.getFileId())){
            JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(medicalPromiseReportCmd.getFileId()).build());
            if (Objects.nonNull(jdhFile)) {
                medicalPromiseReportCmd.setReportUrl(jdhFile.getFilePath());
            }
        }

        //如果结构化字符串不为空
        if (StringUtils.isNotBlank(medicalPromiseReportCmd.getJdStructReportStr())) {
            List<MedicalPromiseReportVerifyRequest> reportResultList = new ArrayList<>();
            MedicalPromiseReportVerifyRequest medicalPromiseReportVerifyRequest = new MedicalPromiseReportVerifyRequest();
            medicalPromiseReportVerifyRequest.setJdStructReportStr(medicalPromiseReportCmd.getJdStructReportStr());
            medicalPromiseReportVerifyRequest.setMedicalPromiseId(medicalPromiseReportCmd.getMedicalPromiseId());
            reportResultList.add(medicalPromiseReportVerifyRequest);
            QuickStructReportVerifyResultDTO quickStructReportVerifyResultDTO = verifyStructQuickReport(reportResultList);
            // 无法忽略的错误抛异常
            if (quickStructReportVerifyResultDTO != null && Boolean.FALSE.equals(quickStructReportVerifyResultDTO.getVerifyResult()) && Boolean.FALSE.equals(quickStructReportVerifyResultDTO.getVerifyFailCanSkip())) {
                throw new BusinessException(new DynamicErrorCode(MedPromiseErrorCode.REPORT_VERIFY_FAIL.getCode(), quickStructReportVerifyResultDTO.getVerifyFailMsg()));
            }
            // 异步执行报告阳性数量预警
            String structReportStr = medicalPromiseReportCmd.getStructReportStr();
            CompletableFuture.runAsync(() -> sendReportWarnMsg(structReportStr, medicalPromise), executorPoolFactory.get(ThreadPoolConfigEnum.ALARM_POOL));
            //上传结构化报告
            StructQuickReportContentDTO structQuickReportContentDTO = getStructQuickReportContentDTO(medicalPromiseReportCmd);
            if (Objects.nonNull(structQuickReportContentDTO.getExtendInfo())){
                medicalPromiseReportCmd.setManufacturerNumber(structQuickReportContentDTO.getExtendInfo().getManufacturerNumber());
                medicalPromiseReportCmd.setSnCode(structQuickReportContentDTO.getExtendInfo().getSnCode());
            }
            checkReportDetail(structQuickReportContentDTO,medicalPromise);
            log.info("MedicalPromiseApplicationImpl->getStructQuickReportContentDTO,structQuickReportContentDTO={}",JsonUtil.toJSONString(structQuickReportContentDTO));
            String fileName = CommonConstant.STRUCT_REPORT_PRE + medicalPromiseReportCmd.getMedicalPromiseId() + "_" + DateUtil.format(new Date(), CommonConstant.YMDHMSSS2) + CommonConstant.FILE_JSON;
            ByteArrayInputStream inputStream = new ByteArrayInputStream(JsonUtil.toJSONString(structQuickReportContentDTO).getBytes(StandardCharsets.UTF_8));
            PutFileResult putFileResult = fileManageService.put(fileName, inputStream, FileManageServiceImpl.FolderPathEnum.REPORT, null,Boolean.FALSE);
            medicalPromiseReportCmd.setJdStructReportStr(putFileResult.getFilePath());

            //如果pdf为空，但结构化不为空，则根据结构化生成pdf
            if (StringUtils.isBlank(medicalPromiseReportCmd.getReportUrl())){
                try {
                    CreatePdfCmd createPdfCmd = new CreatePdfCmd();
                    createPdfCmd.setTemplate("pdfCreateSelfTest");
                    String pdfFileName = medicalPromise.getMedicalPromiseId() +"_"+DateUtil.format(new Date(),CommonConstant.YMDHMSSS2)+".pdf";
                    createPdfCmd.setPdfName(pdfFileName);
                    List<StructQuickReportResultDTO> reportResult = structQuickReportContentDTO.getReportResult();
                    List<StructQuickReportResultIndicatorDTO> indicators = reportResult.get(0).getIndicators();
                    List<IndicatorPdfBo> indicatorPdfBos = Lists.newArrayList();
                    MedicalPromisePdfBo medicalPromisePdfBo = new MedicalPromisePdfBo();
                    medicalPromisePdfBo.setCheckTime(DateUtil.format(medicalPromise.getCheckTime(),CommonConstant.YMDHMS));
                    medicalPromisePdfBo.setServiceItemName(medicalPromise.getServiceItemName());
                    medicalPromisePdfBo.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
                    PromiseDto byPromiseId = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(medicalPromise.getPromiseId()).build());
                    if (Objects.nonNull(byPromiseId) ){
                        if (CollectionUtils.isNotEmpty(byPromiseId.getPatients())){
                            List<PromisePatientDto> patients = byPromiseId.getPatients();
                            PromisePatientDto promisePatientDto = patients.stream().filter(p -> Objects.equals(p.getPromisePatientId(), medicalPromise.getPromisePatientId())).findFirst().orElse(null);
                            if (Objects.nonNull(promisePatientDto)){
                                medicalPromisePdfBo.setUserName(Objects.nonNull(promisePatientDto.getUserName()) ? promisePatientDto.getUserName().getName() : null);
                                medicalPromisePdfBo.setUserGenderStr(GenderEnum.getReportTextOfType(promisePatientDto.getGender()));
                                medicalPromisePdfBo.setAge(Objects.nonNull(promisePatientDto.getBirthday()) ? promisePatientDto.getBirthday().getAge() : null);
                            }
                        }
                    }
                    medicalPromisePdfBo.setSpecimenCode(medicalPromise.getSpecimenCode());
                    medicalPromisePdfBo.setStationName(medicalPromise.getStationName());
                    medicalPromisePdfBo.setReportTime(DateUtil.format(medicalPromiseReportCmd.getReportTime(),CommonConstant.YMDHMS));
                    Map<String, String> stationTestCheck = duccConfig.getStationTestCheckUserConfig().get(medicalPromiseReportCmd.getStationId());
                    medicalPromisePdfBo.setTestUser(MapUtils.isNotEmpty(stationTestCheck) ? stationTestCheck.get("testUser") : null);
                    medicalPromisePdfBo.setCheckUser(MapUtils.isNotEmpty(stationTestCheck) ? stationTestCheck.get("checkUser") : null);
                    MedPromiseHistoryQuery medPromiseHistoryQuery = new MedPromiseHistoryQuery();
                    medPromiseHistoryQuery.setMedicalPromiseId(medicalPromiseReportCmd.getMedicalPromiseId());
                    medPromiseHistoryQuery.setAfterStatus(2);
                    List<MedPromiseHistory> medPromiseHistories = medPromiseHistoryRepository.queryMedPromiseHistoryList(medPromiseHistoryQuery);
                    if (CollectionUtils.isNotEmpty(medPromiseHistories)){
                        medPromiseHistories.sort((order1,order2)->order2.getCreateTime().compareTo(order1.getCreateTime()));
                        medicalPromisePdfBo.setSampleTime(DateUtil.format(medPromiseHistories.get(0).getCreateTime(),CommonConstant.YMDHMS));
                    }
                    for (StructQuickReportResultIndicatorDTO indicatorDTO : indicators){
                        IndicatorPdfBo indicatorPdfBo = new IndicatorPdfBo();
                        indicatorPdfBo.setIndicatorName(indicatorDTO.getIndicatorName());
                        indicatorPdfBo.setUnit(indicatorDTO.getUnit());
                        indicatorPdfBo.setAbnormalType(indicatorDTO.getAbnormalType());
                        indicatorPdfBo.setAdvice(indicatorDTO.getAdvice());
                        indicatorPdfBo.setValue(indicatorDTO.getValue());
                        indicatorPdfBo.setNormalRangeValue(indicatorDTO.getNormalRangeValue());
                        indicatorPdfBo.setValueDescription(indicatorDTO.getValueDescription());
                        indicatorPdfBo.setCtValue(indicatorDTO.getCtValue());
                        indicatorPdfBo.setReferenceRangeValue(indicatorDTO.getReferenceRangeValue());
                        indicatorPdfBos.add(indicatorPdfBo);
                        medicalPromisePdfBo.setIndicatorPdfBos(indicatorPdfBos);
                    }
                    medicalPromisePdfBo.setSpecimenCode(medicalPromise.getSpecimenCode());
                    createPdfCmd.setPdfInfo(JsonUtil.toJSONString(medicalPromisePdfBo));
                    createPdfCmd.setUploadToOss(Boolean.TRUE);
                    JSONObject configObj = JSON.parseObject(duccConfig.getReportPdfConfig());
                    if (configObj.getBoolean("pdfCreateSelfTestV2Switch")){
                        createPdfCmd.setTemplate("pdfCreateSelfTestV2");
                    }
                    PdfCreateDto pdf = pdfApplication.createPdf(createPdfCmd);
                    if (pdf.getResult()){
                        medicalPromiseReportCmd.setReportUrl(pdf.getOssPath());
                    }
                }catch (Exception e){
                    log.info("createPdfError,medicalPromiseId = {}",String.valueOf(medicalPromiseReportCmd.getMedicalPromiseId()),e);
                }

            }
        }

        //如果原始结构化报告不为空
        if (StringUtils.isNotBlank(medicalPromiseReportCmd.getStructReportStr())) {
            //上传结构化报告
            String fileName = CommonConstant.ORI_STRUCT_REPORT_PRE + medicalPromiseReportCmd.getMedicalPromiseId() + "_" + DateUtil.format(new Date(), CommonConstant.YMDHMSSS2) + CommonConstant.FILE_JSON;
            ByteArrayInputStream inputStream = new ByteArrayInputStream(medicalPromiseReportCmd.getStructReportStr().getBytes(StandardCharsets.UTF_8));
            PutFileResult putFileResult = fileManageService.put(fileName, inputStream, FileManageServiceImpl.FolderPathEnum.REPORT, null,Boolean.FALSE);
            medicalPromiseReportCmd.setStructReportStr(putFileResult.getFilePath());
        }

//        AssertUtils.hasText(medicalPromiseReportCmd.getReportUrl(),MedPromiseErrorCode.REPORT_URL_PARAM_NULL);
        MedicalPromiseReportEventBody medicalPromiseReportEventBody = MedicalPromiseConvert.INSTANCE.convert(medicalPromiseReportCmd);
        eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_PUSH_REPORT, medicalPromiseReportEventBody));
        MedicalPromisePushReportDTO medicalPromisePushReportDTO = new MedicalPromisePushReportDTO();
        medicalPromisePushReportDTO.setRes(Boolean.TRUE);
        return medicalPromisePushReportDTO;
    }



    /**
     * 检查快速报告的详细信息是否符合配置要求。
     * @param structQuickReportContentDTO 快速报告内容DTO
     * @param medicalPromise 医疗承诺对象
     */
    private void checkReportDetail(StructQuickReportContentDTO structQuickReportContentDTO,MedicalPromise medicalPromise){

        Set<String> checkReportDetailItemIdSet = duccConfig.getCheckReportDetailItemIdSet();
        if (CollectionUtils.isEmpty(checkReportDetailItemIdSet)) {
            return;
        }
        if (!checkReportDetailItemIdSet.contains(medicalPromise.getServiceItemId())) {
            return;
        }
        Map<String, List<String>> stationCheckBlankItem = duccConfig.getStationCheckBlankItem();
        if (MapUtils.isNotEmpty(stationCheckBlankItem)) {
            if (stationCheckBlankItem.containsKey(medicalPromise.getStationId())){
                List<String> blankItemIds = stationCheckBlankItem.get(medicalPromise.getStationId());
                if (blankItemIds.contains(medicalPromise.getServiceItemId())){
                    return;
                }
            }
        }

        Map<String, String> indicatorCheckConfig = duccConfig.getIndicatorCheckConfig();
        if (MapUtils.isEmpty(indicatorCheckConfig)) {
            return;
        }
        List<StructQuickReportResultDTO> reportResult = structQuickReportContentDTO.getReportResult();
        //新模版
        structQuickReportContentDTO.setNewTemplate(Boolean.TRUE);
        for (StructQuickReportResultDTO reportResultDTO : reportResult) {
            List<StructQuickReportResultIndicatorDTO> indicators = reportResultDTO.getIndicators();
            for (StructQuickReportResultIndicatorDTO indicatorDTO : indicators) {

                String indicatorCheck = indicatorCheckConfig.get(indicatorDTO.getIndicatorName());
                if (StringUtils.isBlank(indicatorCheck)) {
                    indicatorDTO.setTemplateType(IndicatorTemplateEnum.BREAK_EVEN.getTemplateType());
                    continue;
                }
                IndicatorDetailCheckConfig indicatorDetailCheckConfig = JsonUtil.parseObject(indicatorCheck, IndicatorDetailCheckConfig.class);
                String unitExpression = indicatorDetailCheckConfig.getUnitExpression();
                String valueExpression = indicatorDetailCheckConfig.getValueExpression();
                String abnormalTypeExpression = indicatorDetailCheckConfig.getAbnormalTypeExpression();
                //模版类型
                indicatorDTO.setTemplateType(indicatorDetailCheckConfig.getShowTemplate());
                if (StringUtils.equals(IndicatorTemplateEnum.CT.getTemplateType(),indicatorDTO.getTemplateType()) && StringUtils.equals("0",indicatorDTO.getAbnormalType())){
                    indicatorDTO.setTemplateType(IndicatorTemplateEnum.CONTENT.getTemplateType());
                    continue;
                }
                if (StringUtils.isNotBlank(unitExpression)) {
                    String unit = StringUtils.isNotBlank(indicatorDTO.getUnit()) ? indicatorDTO.getUnit() : "";
                    Pattern pattern = patternCache.get(unitExpression);
                    pattern = Objects.nonNull(pattern) ? pattern : Pattern.compile(unitExpression);
                    if (!pattern.matcher(unit).find()) {
                        log.info("checkReportDetail->unitExpression->unit={},unitExpression={}",unit,unitExpression);
                        indicatorDTO.setTemplateType(IndicatorTemplateEnum.BREAK_EVEN.getTemplateType());
                        continue;
                    };

                }

                if (StringUtils.isNotBlank(valueExpression)) {
                    String value = StringUtils.isNotBlank(indicatorDTO.getValue()) ? indicatorDTO.getValue() : "";
                    Pattern pattern = patternCache.get(valueExpression);
                    pattern = Objects.nonNull(pattern) ? pattern : Pattern.compile(valueExpression);
                    if (!pattern.matcher(value).find()){
                        log.info("checkReportDetail->valueExpression={},value={}",valueExpression,value);
                        indicatorDTO.setTemplateType(IndicatorTemplateEnum.BREAK_EVEN.getTemplateType());
                        continue;
                    };
                }

                if (StringUtils.isNotBlank(abnormalTypeExpression)) {
                    String abnormalType = StringUtils.isNotBlank(indicatorDTO.getAbnormalType()) ? indicatorDTO.getAbnormalType() : "";
                    Pattern pattern = patternCache.get(abnormalTypeExpression);
                    pattern = Objects.nonNull(pattern) ? pattern : Pattern.compile(abnormalTypeExpression);
                    if (!pattern.matcher(abnormalType).find()) {
                        log.info("checkReportDetail->abnormalTypeExpression={},abnormalType={}",abnormalTypeExpression,abnormalType);
                        indicatorDTO.setTemplateType(IndicatorTemplateEnum.BREAK_EVEN.getTemplateType());
                        continue;
                    };
                }
                //范围标准化
                //如果是数值型
                if (StringUtils.equals(NormalRangeValueEnum.NUMBER.getType(),indicatorDetailCheckConfig.getNormalRangeValueType())){
                    indicatorDTO.setRangeValueDTOS(convertExpression(indicatorDTO,indicatorDetailCheckConfig.getShowTemplate()));
                }
            }

        }



    }



    /**
     * 检测单状态更新，冻结or解冻，不支持单据重复冻结
     *
     * @param medicalPromiseStatusCmd m
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedicalPromiseApplicationImpl.freezeMedicalPromiseStatusBatch")
    public List<MedicalPromiseDTO> freezeMedicalPromiseStatusBatch(MedicalPromiseStatusCmd medicalPromiseStatusCmd) {


        //加锁,同一个履约单冻结保证顺序
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.MEDICAL_PROMISE_FREEZE_LOCK_KEY, medicalPromiseStatusCmd.getPromiseId());
        String uuid = UUID.randomUUID().toString();
        if (!redisLockUtil.tryLock(lockKey, uuid, RedisKeyEnum.MEDICAL_PROMISE_FREEZE_LOCK_KEY.getExpireTime(), RedisKeyEnum.MEDICAL_PROMISE_FREEZE_LOCK_KEY.getExpireTimeUnit())) {
            log.error("[MedicalPromiseApplicationImpl.freezeMedicalPromiseStatusBatch],履约检测单冻结加锁失败!");
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }

        try {
            //查询是否有检测单
            MedicalPromiseListQuery convert = MedicalPromiseConvert.INSTANCE.convert(medicalPromiseStatusCmd);
            log.info("MedicalPromiseApplicationImpl->freezeMedicalPromiseStatusBatch,convert={}",JsonUtil.toJSONString(convert));

            List<MedicalPromise> medicalPromises = medicalPromiseRepository.listByPatientAndService(convert);
            log.info("MedicalPromiseApplicationImpl->freezeMedicalPromiseStatusBatch,medicalPromises={}",JsonUtil.toJSONString(medicalPromises));
            medicalPromises.removeIf(p->Objects.equals(CommonConstant.ZERO,p.getYn()) && Objects.isNull(p.getMergeMedicalId()));
            log.info("MedicalPromiseApplicationImpl->freezeMedicalPromiseStatusBatch,medicalPromisesAfter={}",JsonUtil.toJSONString(medicalPromises));
            if (CollectionUtils.isEmpty(medicalPromises)){
                throw new SystemException(SystemErrorCode.UNKNOWN_ERROR);
            }

            List<Long> mergeIds = medicalPromises.stream().filter(p -> Objects.equals(CommonConstant.ZERO, p.getYn())).map(MedicalPromise::getMergeMedicalId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mergeIds)){
                List<MedicalPromise> mergePromiseList = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().medicalPromiseIds(mergeIds).build());
//                medicalPromises.removeIf(p->Objects.equals(CommonConstant.ZERO,p.getYn()));
                medicalPromises.addAll(mergePromiseList);
            }

            // 如果已经被冻结，不支持重复冻结
            List<MedicalPromiseStatusInfoCmd> patients = medicalPromiseStatusCmd.getMedicalPromiseStatusInfoCmds();
            Map<Long, Set<Long>> patientService = Maps.newHashMap();
            patients.forEach(e -> {
                Set<Long> serviceIds = patientService.computeIfAbsent(e.getPromisePatientId(), a->Sets.newHashSet());
                serviceIds.add(e.getServiceId());
            });

            medicalPromises.forEach(m->{
                if (m.isFreeze() && patientService.containsKey(m.getPromisePatientId()) && !StringUtils.equals(CommonConstant.ONE_STR,m.getFlag())){
                    Set<Long> service = patientService.get(m.getPromisePatientId());
                    if (service.contains(Long.valueOf(m.getServiceId()))){
                        throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_FREEZE_NO_SUPPORT);
                    }
                }
            });
            //冻结前
            List<MedicalPromise> beforeMedicalPromises = medicalPromiseRepository.queryMedicalPromiseList(
                    MedicalPromiseListQuery.builder().promiseId(medicalPromiseStatusCmd.getPromiseId()).invalid(Boolean.FALSE).allQuery(Boolean.TRUE).build()
            );
            //设置状态
            medicalPromises.forEach(p->p.setFreeze(medicalPromiseStatusCmd.getFreeze()));
            //更新检测单
            boolean res = medicalPromiseRepository.updateFreezeBatch(medicalPromises)>0;

            //冻结后
            List<MedicalPromise> afterMedicalPromises = medicalPromiseRepository.queryMedicalPromiseList(
                    MedicalPromiseListQuery.builder().promiseId(medicalPromiseStatusCmd.getPromiseId())
                            .invalid(Boolean.FALSE)
                            .allQuery(Boolean.TRUE)
                            .build()
            );

            if (res){
                //发送冻结事件
                medicalPromises.forEach(medicalPromise->{
                    eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_FREEZE,
                            MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).status(medicalPromise.getStatus()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).build()));
                });
                //发送人纬度冻结事件
                publishPpidFreezeEvent(beforeMedicalPromises,afterMedicalPromises);
            }

            return MedicalPromiseConvert.INSTANCE.convert(afterMedicalPromises);
        }finally {
            redisLockUtil.unLock(lockKey, uuid);
        }

    }

    /**
     * 检测单批量作废，返回剩余有效的检测单
     * @param medicalPromiseBatchInvalidCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedicalPromiseApplicationImpl.invalidMedicalPromiseBatch")
    public List<MedicalPromiseDTO> invalidMedicalPromiseBatch(MedicalPromiseBatchInvalidCmd medicalPromiseBatchInvalidCmd) {


        //加锁,同一个履约单作废保证顺序
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.MEDICAL_PROMISE_LOCK_KEY, medicalPromiseBatchInvalidCmd.getPromiseId());
        String uuid = UUID.randomUUID().toString();
        if (!redisLockUtil.tryLockWithRetry(lockKey, uuid, RedisKeyEnum.MEDICAL_PROMISE_LOCK_KEY.getExpireTime(), RedisKeyEnum.MEDICAL_PROMISE_LOCK_KEY.getExpireTimeUnit(),1000,5)) {
            log.error("[MedicalPromiseApplicationImpl.invalidMedicalPromiseBatch],履约检测单作废加锁失败!");
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }

        try {

            MedicalPromiseBatchInvalidContext context = MedicalPromiseConvert.INSTANCE.batchInvalidCmd2MedicalPromiseBatchInvalidContext(medicalPromiseBatchInvalidCmd);

            //作废前状态
            List<MedicalPromise> beforeMedicalPromises = medicalPromiseRepository.queryMedicalPromiseList(
                    MedicalPromiseListQuery.builder().allQuery(Boolean.TRUE).promiseId(medicalPromiseBatchInvalidCmd.getPromiseId()).build()
            );
            //作废
            Boolean result = medicalPromiseDomainService.invalidMedicalPromiseBatch(context);
            //作废后状态
            List<MedicalPromise> afterMedicalPromises = medicalPromiseRepository.queryMedicalPromiseList(
                    MedicalPromiseListQuery.builder().allQuery(Boolean.TRUE).promiseId(medicalPromiseBatchInvalidCmd.getPromiseId()).build()
            );

            //发送检测单作废事件
            if (result && CollectionUtils.isNotEmpty(context.getMedicalPromiseList())) {
                Map<Long, Integer> idToBeforeStatus = beforeMedicalPromises.stream().collect(Collectors.toMap(MedicalPromise::getMedicalPromiseId, MedicalPromise::getStatus));
                context.getMedicalPromiseList().forEach(medicalPromise -> {
                    eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_INVALID,
                            MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).beforeStatus(idToBeforeStatus.get(medicalPromise.getMedicalPromiseId())).status(medicalPromise.getStatus()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).build()));
                });

                //发送人纬度的作废
                publishPpidInvalidFreezeEvent(beforeMedicalPromises, afterMedicalPromises, MedicalPromiseStatusEnum.INVALID);
                //发送人+sku纬度作废事件
                publishPpidServiceInvalidEvent(beforeMedicalPromises, afterMedicalPromises);
                //判断作废后是否报告全部已出
                eventCoordinator.publish(EventFactory.newDefaultEvent(afterMedicalPromises.get(0), MedPromiseEventTypeEnum.MED_PROMISE_JUDGE_ALL_GENERATE,
                        MedicalPromiseEventBody.builder().promiseId(medicalPromiseBatchInvalidCmd.getPromiseId()).build()));

            }

            afterMedicalPromises.removeIf(p->Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(),p.getStatus()));
            return MedicalPromiseConvert.INSTANCE.convert(afterMedicalPromises);
        } finally {
            redisLockUtil.unLock(lockKey, uuid);
        }

    }



    /**
     * 提交检测单
     * @param medicalPromiseSubmitCmd m
     * @return r
     */
    @Override
    @LogAndAlarm(alarmPolicy = {
            @AlarmPolicy(type = AlarmPolicyTypeEnum.UMP),
            @AlarmPolicy(methodName = "提交实验室检测信息", keyword = "medicalPromiseId", type = AlarmPolicyTypeEnum.DONGDONG_ROBOT)
    })
    public Boolean submitMedicalPromiseToStation(MedicalPromiseSubmitCmd medicalPromiseSubmitCmd) {
        //查询检测单数据
        MedicalPromise snapshot = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(medicalPromiseSubmitCmd.getMedicalPromiseId()).build());
        if (Objects.isNull(snapshot)) {
            log.error("MedicalPromiseApplicationImpl -> submitMedicalPromiseToStation error, medicalPromise is null, medicalPromiseSubmitCmd={}", JSON.toJSONString(medicalPromiseSubmitCmd));
            return false;
        }
        //TODO shiyanshibaimingdan
        if (duccConfig.getQuickCheckStationIdWhiteList().contains(snapshot.getStationId())){
            log.info("MedicalPromiseApplicationImpl submitMedicalPromiseToStation stationId no match");
            return false;
        }

        //查询履约单受检人信息
        PromiseDto jdhPromise = getJdhPromise(snapshot.getPromiseId());
        List<PromisePatientDto> patients = jdhPromise.getPatients();
        Map<Long, PromisePatientDto> patientToDto = patients.stream().collect(Collectors.toMap(PromisePatientDto::getPromisePatientId, p -> p));
        PromisePatientDto promisePatientDto = patientToDto.get(snapshot.getPromisePatientId());
        if (Objects.isNull(promisePatientDto)) {
            log.error("MedicalPromiseApplicationImpl -> submitMedicalPromiseToStation error, promisePatientDto is null, promisePatientId={}", snapshot.getPromisePatientId());
            return false;
        }
        MedicalPromiseSubmitContext context = MedicalPromiseConvert.INSTANCE.convertMedicalPromiseSubmitContext(snapshot, promisePatientDto);
        context.init(snapshot, MedPromiseEventTypeEnum.MED_PROMISE_SUBMIT);
        Boolean result = medicalPromiseDomainService.submitMedicalPromiseToStation(context);
        eventCoordinator.publish(EventFactory.newDefaultEvent(context.getMedicalPromise(), MedPromiseEventTypeEnum.MED_PROMISE_SUBMIT,
                    MedicalPromiseEventBody.builder().medicalPromiseId(context.getMedicalPromise().getMedicalPromiseId()).beforeStatus(snapshot.getStatus()).status(context.getMedicalPromise().getStatus()).specimenCode(context.getMedicalPromise().getSpecimenCode()).verticalCode(context.getMedicalPromise().getVerticalCode()).serviceType(context.getMedicalPromise().getServiceType()).build()));

        return result;
    }

    /**
     * 直接调用三方API商家,提交预约信息
     * @param directCallMedicalPromiseSubmitCmd m
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean directCallMedicalPromiseToStation(DirectCallMedicalPromiseSubmitCmd directCallMedicalPromiseSubmitCmd) {
        //查询检测单数据
        MedicalPromise snapshot = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(directCallMedicalPromiseSubmitCmd.getMedicalPromiseId()).build());
        if (Objects.isNull(snapshot)) {
            log.error("MedicalPromiseApplicationImpl -> submitMedicalPromiseToStation error, medicalPromise is null, medicalPromiseSubmitCmd={}", JSON.toJSONString(directCallMedicalPromiseSubmitCmd));
            return false;
        }
        //查询履约单受检人信息
        PromiseDto jdhPromise = getJdhPromise(snapshot.getPromiseId());
        List<PromisePatientDto> patients = jdhPromise.getPatients();
        Map<Long, PromisePatientDto> patientToDto = patients.stream().collect(Collectors.toMap(PromisePatientDto::getPromisePatientId, p -> p));
        PromisePatientDto promisePatientDto = patientToDto.get(snapshot.getPromisePatientId());
        if (Objects.isNull(promisePatientDto)) {
            log.error("MedicalPromiseApplicationImpl -> submitMedicalPromiseToStation error, promisePatientDto is null, promisePatientId={}", snapshot.getPromisePatientId());
            return false;
        }
        MedicalPromiseSubmitContext context = MedicalPromiseConvert.INSTANCE.convertMedicalPromiseSubmitContext(snapshot, promisePatientDto);
        context.init(snapshot, MedPromiseEventTypeEnum.MED_PROMISE_SUBMIT);
        context.setDirectCall(directCallMedicalPromiseSubmitCmd.getDirectCall());
        Boolean result = medicalPromiseDomainService.submitMedicalPromiseToStation(context);
        return result;
    }

    /**
     * 提交检测单
     *
     * @param batchMedicalPromiseSubmitCmd m
     * @return r
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedicalPromiseApplicationImpl.batchSubmitMedicalPromiseToStation")
    public Boolean batchSubmitMedicalPromiseToStation(BatchMedicalPromiseSubmitCmd batchMedicalPromiseSubmitCmd) {

        //获取履约单
        getJdhPromise(batchMedicalPromiseSubmitCmd.getPromiseId());
        //1.查询有效检测单
        List<MedicalPromise> medicalPromises = getValidMedicalPromises(batchMedicalPromiseSubmitCmd);
        //发送提交信息
        medicalPromises.forEach(medicalPromise -> {
            eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SUBJECTIVE_SUBMIT,
                    MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).status(medicalPromise.getStatus()).specimenCode(medicalPromise.getSpecimenCode()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).build()));
        });
        return Boolean.TRUE;
    }



    /**
     * 手动创建检测单
     *
     * @param medicalPromiseHandCmd
     * @return
     */
    @Override
    public Boolean handCreateMedicalPromise(MedicalPromiseHandCmd medicalPromiseHandCmd) {
        MedicalPromise convert = MedicalPromiseConvert.INSTANCE.convert(medicalPromiseHandCmd);
        int save = medicalPromiseRepository.save(convert);
        return save>0;
    }

    /**
     * 查询检测单信息
     *
     * @param medicalPromiseRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.medicalpromise.service.impl.MedicalPromiseApplicationImpl.queryMedicalPromise")
    public MedicalPromiseDTO queryMedicalPromise(MedicalPromiseRequest medicalPromiseRequest) {
        MedicalPromiseRepQuery convert = MedicalPromiseConvert.INSTANCE.convert(medicalPromiseRequest);
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(convert);
        if (Objects.isNull(medicalPromise)) {
            return null;
        }
        MedicalPromiseDTO res = MedicalPromiseConvert.INSTANCE.convert(medicalPromise);
        packServiceItem(medicalPromiseRequest,res);
        packPatientInfo(medicalPromiseRequest, medicalPromise, res);
        packEtaInfo(medicalPromiseRequest,medicalPromise,res);

        return res;
    }

    /**
     *
     * @param medicalPromiseRequest
     * @return
     */
    @Override
    public List<MedicalPromiseDTO> queryMedicalPromiseList(MedicalPromiseRequest medicalPromiseRequest) {
        MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
        medicalPromiseListQuery.setPromiseId(medicalPromiseRequest.getPromiseId());
        medicalPromiseListQuery.setPromisePatientId(medicalPromiseRequest.getPromisePatientId());
        medicalPromiseListQuery.setOrderBy("asc");
        List<MedicalPromise> medicalPromiseList = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        return MedicalPromiseConvert.INSTANCE.convert(medicalPromiseList);
    }
    private Boolean verifyQuickCheck(MedicalPromise medicalPromise){
        if (!duccConfig.getQuickCheckStationIdWhiteList().contains(medicalPromise.getStationId())){
            log.info("ProviderPromiseEventSubscriber verifyQuickCheck stationId no match");
            return false;
        }
        JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(medicalPromise.getVerticalCode());
        if (!Arrays.asList(BusinessModeEnum.SELF_TEST.getCode(), BusinessModeEnum.ANGEL_TEST.getCode()).contains(jdhVerticalBusiness.getBusinessModeCode())){
            log.info("ProviderPromiseEventSubscriber verifyQuickCheck businessModeCode no match");
            return false;
        }
        return true;
    }



    /**
     * 查询履约单下报告情况
     *
     * @param promiseId
     * @return
     */
    @Override
    public MedicalPromiseReportCheckDTO queryReportStatus(Long promiseId) {
        MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
        medicalPromiseListQuery.setPromiseId(promiseId);
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        log.info("MedicalPromiseApplicationImpl->queryReportStatus,medicalPromises={}",JsonUtil.toJSONString(medicalPromises));
        medicalPromises.removeIf(p->Objects.equals(CommonConstant.ONE,p.getFreeze()) || Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(),p.getStatus()));
        MedicalPromiseReportCheckDTO medicalPromiseReportCheckDTO = new MedicalPromiseReportCheckDTO();

        if (CollectionUtils.isEmpty(medicalPromises)){
            //没有检测单
            medicalPromiseReportCheckDTO.setReportAllCreate(Boolean.FALSE);
            return medicalPromiseReportCheckDTO;
        }
        log.info("MedicalPromiseApplicationImpl->queryReportStatus,medicalPromisesAfter={}",JsonUtil.toJSONString(medicalPromises));
        List<MedicalPromiseReportStatusDTO> medicalPromiseReportStatusDTOS = medicalPromises.stream().map(p -> {
            MedicalPromiseReportStatusDTO medicalPromiseReportStatusDTO = new MedicalPromiseReportStatusDTO();
            if (!Objects.equals(CommonConstant.ONE, p.getReportStatus())) {
                medicalPromiseReportCheckDTO.setReportAllCreate(Boolean.FALSE);
            }
            medicalPromiseReportStatusDTO.setMedicalPromiseId(p.getMedicalPromiseId());
            medicalPromiseReportStatusDTO.setReportStatus(p.getReportStatus());
            return medicalPromiseReportStatusDTO;
        }).collect(Collectors.toList());
        medicalPromiseReportCheckDTO.setMedicalPromiseReportStatusDTOS(medicalPromiseReportStatusDTOS);
        if (Objects.isNull(medicalPromiseReportCheckDTO.getReportAllCreate())){
            medicalPromiseReportCheckDTO.setReportAllCreate(Boolean.TRUE);
        }
        return medicalPromiseReportCheckDTO;
    }

    /**
     * 推送检测单结构化报告
     *
     * @param medicalPromiseReportCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogAndAlarm(alarmPolicy = {
            @AlarmPolicy(type = AlarmPolicyTypeEnum.UMP),
            @AlarmPolicy(methodName = "保存报告", keyword = "medicalPromiseId", type = AlarmPolicyTypeEnum.DONGDONG_ROBOT)
    })
    public MedicalPromisePushReportDTO pushMedicalPromiseReportInfo(MedicalPromiseReportCmd medicalPromiseReportCmd) {
        log.info("[MedicalPromiseApplicationImpl.pushMedicalPromiseReportInfo],medicalPromiseReportCmd={}",JsonUtil.toJSONString(medicalPromiseReportCmd));

        //查询检测单
        MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(medicalPromiseReportCmd.getMedicalPromiseId()).build());
        //加锁,同一个履约单下报告顺序出，因为会发送首次和末次出报告事件
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.PUSH_REPORT_LOCK_KEY, medicalPromise.getPromiseId());
        String uuid = UUID.randomUUID().toString();
        if (!redisLockUtil.tryLock(lockKey, uuid, RedisKeyEnum.PUSH_REPORT_LOCK_KEY.getExpireTime(), RedisKeyEnum.PUSH_REPORT_LOCK_KEY.getExpireTimeUnit())) {
            log.error("[MedicalPromiseApplicationImpl.pushMedicalPromiseReportInfo],推送报告加锁失败!");
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }

        try {

            //组装报告信息
            MedicalReportSaveCmd medicalReportSaveCmd = getMedicalReportSaveCmd(medicalPromiseReportCmd, medicalPromise);

            //同步报告中心
            this.syncMedicalReportToReportCenter(medicalReportSaveCmd);

            log.info("[MedicalPromiseApplicationImpl.pushMedicalPromiseReportInfo],medicalReportSaveCmd={}",JsonUtil.toJSONString(medicalReportSaveCmd));

            //保存报告
            SaveMedicalReportResultDTO save = medicalReportApplication.save(medicalReportSaveCmd);
            medicalReportSaveCmd.setReportId(save.getReportId());
            //组装检测单状态context
            MedicalPromiseReportContext medicalPromiseReportContext = getMedicalPromiseReportContext(medicalPromise);
            medicalPromiseReportContext.setReportTime(medicalReportSaveCmd.getReportTime());
            //修改检测单状态
            Boolean res = medicalPromiseDomainService.pushMedicalPromiseReport(medicalPromiseReportContext);
            //组装返回信息
            MedicalPromisePushReportDTO medicalPromisePushReportDTO = new MedicalPromisePushReportDTO();
            medicalPromisePushReportDTO.setReportId(save.getReportId());
            medicalPromisePushReportDTO.setRes(res);


            //发送检测单报告已出事件
            eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_GENERATE_REPORT,
                    MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).beforeStatus(medicalPromiseReportContext.getSnapshot().getStatus()).status(medicalPromiseReportContext.getMedicalPromise().getStatus()).specimenCode(medicalPromise.getSpecimenCode()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).build()));
            //发送人+sku全部报告已出
            sendPpidServiceReport(medicalPromise, medicalPromiseReportContext);
            //发送人纬度全部报告已出
//            sendPpidReport(medicalPromise, medicalPromiseReportContext);
            //如果无效则不发 直接返回
            if (Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(), medicalPromise.getStatus()) || Objects.equals(CommonConstant.ONE, medicalPromise.getFreeze())) {
                return medicalPromisePushReportDTO;
            }
            //判断报告是否首次已出
            JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(medicalPromise.getPromiseId()).build());
            sendFirstGenerateReport(medicalPromise, promise);
            //判断后是否报告全部已出
            eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_JUDGE_ALL_GENERATE,
                    MedicalPromiseEventBody.builder().promiseId(medicalPromise.getPromiseId()).build()));

            if (duccConfig.getPdfToJpgMqSwitch()){
                Message message = new Message(pdfToJpgTopic,JsonUtil.toJSONString(medicalReportSaveCmd));
                pdfToJpgProvider.send(message);
            }
            Event publishEvent = EventFactory.newDelayEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SMS, MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).promiseId(medicalPromise.getPromiseId()).build(),30L);
            eventCoordinator.publish(publishEvent);


            return medicalPromisePushReportDTO;
        }catch (JMQException e){
            log.info("sendMessage,error",e);
        }catch (Exception e){
            jimClient.del(CacheConstant.MEDICAL_PROMISE_ID_REPORT_ID_PREFIX + String.valueOf(medicalPromiseReportCmd.getMedicalPromiseId()));
            throw e;
        }catch (Throwable e){
            jimClient.del(CacheConstant.MEDICAL_PROMISE_ID_REPORT_ID_PREFIX + String.valueOf(medicalPromiseReportCmd.getMedicalPromiseId()));
            throw e;
        } finally{
            redisLockUtil.unLock(lockKey, uuid);
        }
        return null;
    }

    /**
     * 同步报告中心，将报告传给报告中心侧，同时返回报告reportid写入数据库
     * @param medicalReportSaveCmd
     */
    @Override
    public Boolean syncMedicalReportToReportCenter(MedicalReportSaveCmd medicalReportSaveCmd) {
        try {
            if(log.isInfoEnabled()) {
                log.info("syncMedicalReportToReportCenter,medicalReportSaveCmd = {} ", medicalReportSaveCmd);
            }
            ReportCenterReport reportCenterReport = MedicalPromiseConvert.INSTANCE.medicalReportSaveCmd2ReportCenterReport(medicalReportSaveCmd);

            //设置档案id
            PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
            promiseIdRequest.setPromiseId(medicalReportSaveCmd.getPromiseId());
            PromiseDto promiseDto = promiseApplication.findPromiseByPromiseId(promiseIdRequest);
            reportCenterReport.setPatientId(promiseDto.getPatients().get(0).getPatientId());

            SyncReportToCenterResBo syncReportToCenterResBo = reportCenterDomainService.syncMedicalReportToReportCenter(reportCenterReport);
            log.info("syncMedicalReportToReportCenter,syncReportToCenterResBo = {} ", syncReportToCenterResBo);
            if (StringUtils.equals("0000",syncReportToCenterResBo.getCode())){
                if(StringUtils.isNotBlank(syncReportToCenterResBo.getReportCenterId())) {
                    medicalReportSaveCmd.setReportCenterId(syncReportToCenterResBo.getReportCenterId());
                    return true;
                }
            }else if (StringUtils.equals("400015",syncReportToCenterResBo.getCode())){
                //1.查询同个promise下的相同md5的检测单

                medicalReportSaveCmd.setReportCenterId(syncReportToCenterResBo.getReportCenterId());

                List<MedicalReport> medicalReports = medicalReportRepository.selectByCondition(MedicalReportQueryBO.builder()
                        .promiseId(medicalReportSaveCmd.getPromiseId())
                        .fileMd5(medicalReportSaveCmd.getFileMd5()).build()
                );
                if (CollectionUtils.isNotEmpty(medicalReports)) {

                    medicalReports.removeIf(p -> StringUtil.isBlank(p.getFileMd5()));
                    if (CollectionUtils.isEmpty(medicalReports)){
                        return true;
                    }
                    List<Long> ids = medicalReports.stream().map(MedicalReport::getMedicalPromiseId).collect(Collectors.toList());
                    List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().medicalPromiseIds(ids).build());

                    if (CollectionUtils.isNotEmpty(medicalPromises)) {
                        String name = medicalPromises.stream().map(MedicalPromise::getServiceItemName).collect(Collectors.joining("+"));
                        reportCenterReport.setServiceItemName(name);
                        reportCenterReport.setReportCenterId(syncReportToCenterResBo.getReportCenterId());
                        reportCenterDomainService.syncMedicalReportToReportCenter(reportCenterReport);
                        log.info("syncMedicalReportToReportCenter,reSyncReportToCenterResBo = {} ", syncReportToCenterResBo);
                    }else {
                        return true;
                    }

                }else {
                    return true;
                }


            }



        } catch (Throwable e) {
            log.error("syncMedicalReportToReportCenter err.", e);
        }
        return false;
    }

    /**
     * 检查是否存在进行中的检测单
     *
     * @param receiverId
     * @param patientIdSet
     * @return
     */
    @Override
    public Boolean checkProgressMedPromise(String receiverId, Set<String> patientIdSet) {
        if(StringUtils.isBlank(receiverId) || CollectionUtils.isEmpty(patientIdSet)) {
            log.error("[MedicalPromiseApplicationImpl -> checkProgressMedPromise],参数不正确!");
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
        medicalPromiseListQuery.setStationId(receiverId);
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        Map<Long, List<MedicalPromise>> medicalPromiseGroup = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));

        Iterator<Map.Entry<Long, List<MedicalPromise>>> iterator = medicalPromiseGroup.entrySet().iterator();
        while(iterator.hasNext()) {
            Map.Entry<Long, List<MedicalPromise>> next = iterator.next();
            if (patientIdSet.contains(String.valueOf(next.getKey()))) {
                continue;
            }
            for (MedicalPromise medicalPromise : next.getValue()) {
                if(Objects.equals(medicalPromise.getStatus(), MedicalPromiseStatusEnum.INVALID.getStatus()) ||
                        medicalPromise.isFreeze()) {
                    continue;
                }
                log.error("[AngelWorkApplicationImpl -> angelWorkInvalid],存在进行中检测单!medicalPromise={}", com.alibaba.fastjson.JSON.toJSONString(medicalPromise));
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 校验结构化报告
     *
     * @param reportResult reportResult
     * @return dto dto
     */
    @Override
    public QuickStructReportVerifyResultDTO verifyStructQuickReport(List<MedicalPromiseReportVerifyRequest> reportResult) {
        log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport], reportResult={}", JSON.toJSONString(reportResult));
        QuickStructReportVerifyResultDTO quickStructReportVerifyResultDTO = new QuickStructReportVerifyResultDTO();
        quickStructReportVerifyResultDTO.setVerifyResult(true);
        quickStructReportVerifyResultDTO.setVerifyFailCanSkip(true);
        List<VerifyReportRootConfig> list = JSON.parseArray(JSON.toJSONString(duccConfig.getVerifyReportRootConfig()), VerifyReportRootConfig.class);
        // 未配置项目数据,直接返回成功
        if (CollUtil.isEmpty(list)) {
            return quickStructReportVerifyResultDTO;
        }
        Map<String, VerifyReportBaseRootConfig> verifyReportRootConfigMapping = new HashMap<>();
        for (VerifyReportRootConfig verifyReportRootConfig: list) {
            for (String serviceItemId : verifyReportRootConfig.getServiceItemId()) {
                verifyReportRootConfigMapping.put(serviceItemId, verifyReportRootConfig);
            }
        }
        // 未配置项目数据,直接返回成功
        if (CollUtil.isEmpty(verifyReportRootConfigMapping)) {
            return quickStructReportVerifyResultDTO;
        }
        if (CollUtil.isEmpty(reportResult)) {
            log.error("[MedicalPromiseApplicationImpl -> verifyStructQuickReport],参数不正确!");
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        int originNum = reportResult.size();
        int filterNum = (int) reportResult.stream().filter(s -> StringUtils.isNotBlank(s.getJdStructReportStr())).count();
        if (originNum != filterNum) {
            log.error("[MedicalPromiseApplicationImpl -> verifyStructQuickReport],结构化报告为空!");
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }
        Map<String, List<QuickStructReportVerifyResultDTO>> errorGroup = new HashMap<>();
        List<String> errorMsg = new ArrayList<>();
        for (MedicalPromiseReportVerifyRequest reportVerifyRequest : reportResult) {
            MedicalPromiseReportCmd medicalPromiseReportCmd = new MedicalPromiseReportCmd();
            medicalPromiseReportCmd.setJdStructReportStr(reportVerifyRequest.getJdStructReportStr());
            medicalPromiseReportCmd.setMedicalPromiseId(reportVerifyRequest.getMedicalPromiseId());
            StructQuickReportContentDTO structQuickReportContentDTO = getStructQuickReportContentDTO(medicalPromiseReportCmd);
            for (StructQuickReportResultDTO  structQuickReportResultDTO : structQuickReportContentDTO.getReportResult()) {
                MedicalPromiseRepQuery repQuery = new MedicalPromiseRepQuery();
                if (medicalPromiseReportCmd.getMedicalPromiseId() != null) {
                    repQuery.setMedicalPromiseId(medicalPromiseReportCmd.getMedicalPromiseId());
                } else {
                    repQuery.setSpecimenCode(structQuickReportResultDTO.getSampleBarcode());
                }
                MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(repQuery);
                if(medicalPromise == null) {
                    codeNotExist(quickStructReportVerifyResultDTO, structQuickReportResultDTO, errorMsg, errorGroup);
                    continue;
                }
                if (!verifyReportRootConfigMapping.containsKey(medicalPromise.getServiceItemId())) {
                    log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport], 项目{}不再配置名单,忽略", medicalPromise.getServiceItemId());
                    continue;
                }
                VerifyReportBaseRootConfig verifyReportRootConfig = JSONUtil.toBean(JSONUtil.toJsonStr(verifyReportRootConfigMapping.get(medicalPromise.getServiceItemId())), VerifyReportBaseRootConfig.class);
                log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport], verifyReportRootConfig={}", JSON.toJSONString(verifyReportRootConfig));
                if (!Boolean.TRUE.equals(verifyReportRootConfig.getConfigSwitch())) {
                    log.info("[MedicalPromiseApplicationImpl -> verifyReportRootConfig],总开关关闭!");
                    continue;
                }
                // 校验结果与项目配置指标数量是否一致
                if (verifyReportRootConfig.getContrastServiceItemIndicatorNum() != null && Boolean.TRUE.equals(verifyReportRootConfig.getContrastServiceItemIndicatorNum().getConfigSwitch())) {
                    contrastServiceItemIndicatorNum(verifyReportRootConfig, medicalPromise, quickStructReportVerifyResultDTO, structQuickReportResultDTO, errorMsg, errorGroup);
                }
                log.info("[MedicalPromiseApplicationImpl -> structQuickReportResultDTO.getIndicators], 指标及类型={}", JSON.toJSONString(structQuickReportResultDTO.getIndicators()));
                Map<String, String> indicatorNameTypeMap = structQuickReportResultDTO.getIndicators().stream().collect(Collectors.toMap(StructQuickReportResultIndicatorDTO::getIndicatorName, StructQuickReportResultIndicatorDTO::getAbnormalType, (v1, v2) -> v2));
                log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport], 指标及类型={}", JSON.toJSONString(indicatorNameTypeMap));
                // 阳性指标冲突校验
                if (CollUtil.isNotEmpty(indicatorNameTypeMap) && CollUtil.isNotEmpty(verifyReportRootConfig.getIndicatorConflict())) {
                    indicatorConflict(verifyReportRootConfig, indicatorNameTypeMap, quickStructReportVerifyResultDTO, structQuickReportResultDTO, errorMsg, errorGroup);
                }
                // 指标异常数量校验
                if (verifyReportRootConfig.getAnomalyIndicatorNum() != null && Boolean.TRUE.equals(verifyReportRootConfig.getAnomalyIndicatorNum().getConfigSwitch())) {
                    anomalyIndicatorNum(verifyReportRootConfig, indicatorNameTypeMap, quickStructReportVerifyResultDTO, structQuickReportResultDTO, errorMsg, errorGroup);
                }
            }
        }
        if (CollUtil.isNotEmpty(errorMsg)) {
            quickStructReportVerifyResultDTO.setVerifyFailMsg(Joiner.on(",").join(errorMsg));
        }
        quickStructReportVerifyResultDTO.setErrorGroup(errorGroup);
        return quickStructReportVerifyResultDTO;
    }

    /**
     * 更新检测单实验室数据
     * @param updateMedPromiseStationCmd
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean updateMedicalPromiseStation(UpdateMedPromiseStationCmd updateMedPromiseStationCmd) {
        UpdateMedicalPromiseStation updateMedicalPromiseStation = JSON.parseObject(JSON.toJSONString(updateMedPromiseStationCmd),UpdateMedicalPromiseStation.class);
        return medicalPromiseRepository.updateMedicalPromiseStation(updateMedicalPromiseStation);
    }

    @Override
    public Integer listMedicalPromiseBySpecimenCode(List<String> specimenCodes) {
        if (CollectionUtils.isEmpty(specimenCodes)){
            return 0;
        }
        LambdaQueryWrapper<JdhMedicalPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(JdhMedicalPromisePo::getSpecimenCode, specimenCodes);
        queryWrapper.eq(JdhMedicalPromisePo::getYn, YnStatusEnum.YES.getCode());
        return jdhMedicalPromisePoMapper.selectCount(queryWrapper);
    }

    /**
     * 分页查询检测单综合数据
     *
     * @param labQueryMedPromisePageRequest
     * @return
     */
    @Override
    public MedicalPromiseFullDTO labQueryMedicalPromiseDetail(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest) {
        MedicalPromiseEsQuery medicalPromiseEsQuery = buildQuery(labQueryMedPromisePageRequest);
        log.info("MedicalPromiseApplicationImpl->labQueryMedicalPromiseDetail labQueryPage,start");
        MedicalPromiseFull medicalPromiseFullPageDto = jdMedicalPromiseEsRepository.labQueryDetail(medicalPromiseEsQuery);
        log.info("MedicalPromiseApplicationImpl->labQueryMedicalPromiseDetail labQueryPage,end");

        MedicalPromiseFullDTO medicalPromiseFullDTOPageDto = MedicalPromiseConvert.INSTANCE.convertFull(medicalPromiseFullPageDto);
        if (medicalPromiseFullDTOPageDto != null) {
            List<MedicalPromiseFullDTO> res = buildDto(com.google.common.collect.Lists.newArrayList(medicalPromiseFullPageDto), medicalPromiseEsQuery, labQueryMedPromisePageRequest);
            return res.get(0);
        }
        return null;
    }

    /**
     * 解析获取结构化报告
     *
     * @return
     */
    @Override
    public StructQuickReportContentDTO getStructQuickReportFromSourceOss(Long medicalPromiseId) {
        AssertUtils.nonNull(medicalPromiseId, "检验单id为空");

        com.jdh.o2oservice.export.report.query.MedicalPromiseRequest medicalPromiseRequest = new com.jdh.o2oservice.export.report.query.MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(medicalPromiseId);
        MedicalReportDTO medicalReportDTO = medicalReportApplication.getByMedicalPromiseId(medicalPromiseRequest);
        if (medicalReportDTO == null) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.DB_DATA_NOT_EXIST.getCode(), "报告未生成或数据不存在"));
        }
        if (StringUtils.isBlank(medicalReportDTO.getSourceOss())) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.DB_DATA_NOT_EXIST.getCode(), "报告原始文件为空"));
        }
        MedicalPromiseReportCmd medicalPromiseReportCmd = new MedicalPromiseReportCmd();
        medicalPromiseReportCmd.setJdStructReportStr(getSourceReportStr(medicalReportDTO.getSourceOss()));
        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(medicalPromiseId).build());
        medicalPromiseReportCmd.setStationId(medicalPromise.getStationId());
        return getStructQuickReportContentDTO(medicalPromiseReportCmd);
    }

    /**
     * 从原始oss解析并重新生成结构化报告
     *
     * @param medicalPromiseId
     * @return
     */
    @Override
    public String anewStructQuickReportFromSourceOss(Long medicalPromiseId) {
        AssertUtils.nonNull(medicalPromiseId, "检验单id为空");
        com.jdh.o2oservice.export.report.query.MedicalPromiseRequest medicalPromiseRequest = new com.jdh.o2oservice.export.report.query.MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(medicalPromiseId);
        MedicalReportDTO medicalReportDTO = medicalReportApplication.getByMedicalPromiseId(medicalPromiseRequest);if (medicalReportDTO == null) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.DB_DATA_NOT_EXIST.getCode(), "报告未生成或数据不存在"));
        }
        if (StringUtils.isBlank(medicalReportDTO.getSourceOss())) {
            throw new BusinessException(new DynamicErrorCode(SystemErrorCode.DB_DATA_NOT_EXIST.getCode(), "报告原始文件为空"));
        }
        MedicalPromiseReportCmd medicalPromiseReportCmd = new MedicalPromiseReportCmd();
        medicalPromiseReportCmd.setJdStructReportStr(getSourceReportStr(medicalReportDTO.getSourceOss()));
        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(medicalPromiseId).build());
        medicalPromiseReportCmd.setStationId(medicalPromise.getStationId());
        StructQuickReportContentDTO structQuickReportContentDTO = getStructQuickReportContentDTO(medicalPromiseReportCmd);
        String fileName = StringUtils.isNotBlank(medicalReportDTO.getStructReportOss()) ? medicalReportDTO.getStructReportOss() : CommonConstant.STRUCT_REPORT_PRE + medicalPromiseReportCmd.getMedicalPromiseId() + "_" + DateUtil.format(new Date(), CommonConstant.YMDHMSSS2) + CommonConstant.FILE_JSON;
        ByteArrayInputStream inputStream = new ByteArrayInputStream(JsonUtil.toJSONString(structQuickReportContentDTO).getBytes(StandardCharsets.UTF_8));
        PutFileResult putFileResult = fileManageService.put(fileName, inputStream, FileManageServiceImpl.FolderPathEnum.REPORT, null,Boolean.FALSE);
        MedicalReport medicalReport = new MedicalReport();
        medicalReport.setMedicalPromiseId(medicalPromiseId);
        medicalReport.setStructReportOss(putFileResult.getFilePath());
        medicalReportRepository.updateByCondition(medicalReport);
        return fileManageService.getPublicUrl(putFileResult.getFilePath(), true, DateUtil.offsetDay(new Date(), 1));
    }

    /**
     * 更新检测单时效信息
     *
     * @param medicalPromiseEtaCmd medicalPromiseEtaCmd
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateMedicalPromiseEta(UpdateMedicalPromiseEtaCmd medicalPromiseEtaCmd) {
        if(Objects.isNull(medicalPromiseEtaCmd) || Objects.isNull(medicalPromiseEtaCmd.getMedPromiseId())){
            throw new BusinessException(MedPromiseErrorCode.PARAM_NULL);
        }
        //如果要更新的4个数据都为空，不更新
        if(Objects.isNull(medicalPromiseEtaCmd.getTestingTimeOutDate())
                && Objects.isNull(medicalPromiseEtaCmd.getTestingTimeOutStatus())
                && Objects.isNull(medicalPromiseEtaCmd.getWaitingTestTimeOutStatus())
                && Objects.isNull(medicalPromiseEtaCmd.getWaitingTestTimeOutDate())){
            throw new BusinessException(MedPromiseErrorCode.PARAM_NULL);
        }

        //1、根据检测单id，反查
        MedicalPromise medicalPromise = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(medicalPromiseEtaCmd.getMedPromiseId()).build());
        log.info("MedicalPromiseApplicationImpl -> updateMedicalPromiseEta medicalPromise:{}",JSON.toJSONString(medicalPromise));
        if(Objects.isNull(medicalPromise)){
            throw new BusinessException(MedPromiseErrorCode.MED_PROMISE_NOT_EXIT);
        }


        List<MedPromiseEventTypeEnum> publishEventTypeList = new ArrayList<>();
        //如果待检测超时时间更新
        if(Objects.nonNull(medicalPromiseEtaCmd.getWaitingTestTimeOutDate())){
            medicalPromise.setWaitingTestTimeOutDate(medicalPromiseEtaCmd.getWaitingTestTimeOutDate());
            publishEventTypeList.add(MedPromiseEventTypeEnum.MED_PROMISE_WAITING_TESTING_ETA_UPDATE);
        }

        //如果是触发了待检测超时
        if(NumConstant.NUM_1.equals(medicalPromiseEtaCmd.getWaitingTestTimeOutStatus())){
            medicalPromise.setWaitingTestTimeOutStatus(medicalPromiseEtaCmd.getWaitingTestTimeOutStatus());
            publishEventTypeList.add(MedPromiseEventTypeEnum.MED_PROMISE_WAITING_TESTING_TIMEOUT);
        }

        //如果检测超时时间更新
        if(Objects.nonNull(medicalPromiseEtaCmd.getTestingTimeOutDate())){
            medicalPromise.setTestingTimeOutDate(medicalPromiseEtaCmd.getTestingTimeOutDate());
            publishEventTypeList.add(MedPromiseEventTypeEnum.MED_PROMISE_WAITING_TESTING_ETA_UPDATE);
        }

        //如果是触发了检测超时
        if(NumConstant.NUM_1.equals(medicalPromiseEtaCmd.getTestingTimeOutStatus())){
            medicalPromise.setTestingTimeOutStatus(medicalPromiseEtaCmd.getTestingTimeOutStatus());
            publishEventTypeList.add(MedPromiseEventTypeEnum.MED_PROMISE_TESTING_TIMEOUT);
        }

        medicalPromiseRepository.save(medicalPromise);

        if(CollUtil.isNotEmpty(publishEventTypeList)){
            MedicalPromiseEventBody medicalPromiseEventBody = MedicalPromiseEventBody.builder()
                    .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                    .status(medicalPromise.getStatus())
                    .specimenCode(medicalPromise.getSpecimenCode())
                    .verticalCode(medicalPromise.getVerticalCode())
                    .serviceType(medicalPromise.getServiceType())
                    .testingTimeOutDate(medicalPromise.getTestingTimeOutDate())
                    .waitingTestTimeOutDate(medicalPromise.getWaitingTestTimeOutDate())
                    .testingTimeOutStatus(medicalPromise.getTestingTimeOutStatus())
                    .waitingTestTimeOutStatus(medicalPromise.getWaitingTestTimeOutStatus()).build();
            publishEventTypeList.forEach(event -> eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, event, medicalPromiseEventBody)));
        }
        return Boolean.TRUE;
    }

    /**
     * 到检
     *
     * @param medPromiseCallbackStatusCmd
     */
    @Override
    public Boolean medicalPromiseCheck(MedPromiseCallbackStatusCmd medPromiseCallbackStatusCmd) {
        log.info("MedicalPromiseApplicationImpl->medicalPromiseCheck,medPromiseCallbackStatusCmd={}",JsonUtil.toJSONString(medPromiseCallbackStatusCmd));

        //lock
        String uuid = UUID.randomUUID().toString();
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.MEDICAL_PROMISE_CHECK_KEY,String.valueOf(medPromiseCallbackStatusCmd.getMedicalPromiseId()));
        boolean lock = redisLockUtil.tryLock(lockKey, uuid, RedisKeyEnum.MEDICAL_PROMISE_CHECK_KEY.getExpireTime(), RedisKeyEnum.MEDICAL_PROMISE_CHECK_KEY.getExpireTimeUnit());
        if (!lock){
            log.info("MedicalPromiseApplicationImpl->medicalPromiseCheck,加锁失败,medicalPromiseId={}",medPromiseCallbackStatusCmd.getMedicalPromiseId().toString());
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }

        try {
            MedicalPromiseCallbackContext medicalPromiseCallbackContext = new MedicalPromiseCallbackContext();
            medicalPromiseCallbackContext.setMedicalPromiseId(medPromiseCallbackStatusCmd.getMedicalPromiseId());
            medicalPromiseCallbackContext.setOuterId(medPromiseCallbackStatusCmd.getOuterId());
            Boolean res = medicalPromiseDomainService.medicalPromiseCheck(medicalPromiseCallbackContext);
            MedicalPromise medicalPromise = medicalPromiseCallbackContext.getMedicalPromise();
            if (res){
                if (StringUtils.isEmpty(medicalPromise.getSerialNum())){
                    //补充顺序
                    String serialNum = generalSerialNum(medicalPromise);
                    log.info("MedicalPromiseApplicationImpl->medicalPromiseCheck,serialNum={}",serialNum);
                    MedicalPromise updateSerialNum = new MedicalPromise();
                    updateSerialNum.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
                    updateSerialNum.setSerialNum(serialNum);
                    medicalPromiseRepository.updateSerialNum(updateSerialNum);
                }
                //发送事件
                eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_STATION_RECEIVE,
                        MedicalPromiseEventBody.builder().medicalPromiseId(medicalPromise.getMedicalPromiseId()).beforeStatus(medicalPromiseCallbackContext.getSnapshot().getStatus()).status(medicalPromiseCallbackContext.getMedicalPromise().getStatus()).specimenCode(medicalPromise.getSpecimenCode()).verticalCode(medicalPromise.getVerticalCode()).serviceType(medicalPromise.getServiceType()).promiseId(medicalPromise.getPromiseId()).build()));
            }
            return res;
        }finally {
            redisLockUtil.unLock(lockKey,uuid);
        }
    }

    /**
     * 获取序号
     * @param medicalPromise
     * @return
     */
    private String generalSerialNum(MedicalPromise medicalPromise){
        if (StringUtils.isNotBlank(medicalPromise.getSerialNum())){
            return medicalPromise.getSerialNum();
        }
        String checkDate = DateUtil.format(medicalPromise.getCheckTime(), CommonConstant.YMD);
        String stationSerialNumKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.MEDICAL_PROMISE_SERIAL_KEY,medicalPromise.getStationId(),checkDate);
        Long incr = cluster.incr(stationSerialNumKey);
        cluster.expireAt(stationSerialNumKey,DateUtil.endOfDay(medicalPromise.getCheckTime()));
        return String.valueOf(incr);

    }

    /**
     * 获取序号
     * @param medicalPromiseSerialCmd
     * @return
     */
    @Override
    public String generalSerialNum(MedicalPromiseSerialCmd medicalPromiseSerialCmd){
        MedicalPromise medicalPromise = new MedicalPromise();
        BeanUtil.copyProperties(medicalPromiseSerialCmd,medicalPromise);
        return generalSerialNum(medicalPromise);
    }

    /**
     * @param cmd
     * @return
     */
    @Override
    @LogAndAlarm(alarmPolicy = {
            @AlarmPolicy(type = AlarmPolicyTypeEnum.UMP),
            @AlarmPolicy(methodName = "实验室结算推送天算", keyword = "medicalPromiseId", type = AlarmPolicyTypeEnum.DONGDONG_ROBOT)
    })
    public Boolean sendSettlementMessage(MedPromiseSettlementCmd cmd) {
        String tsHistoryKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STATION_SETTLEMENT_TS_REDIS_KEY, cmd.getAppointmentId());
        String redis = jimClient.get(tsHistoryKey);
        if (StringUtils.isNotBlank(redis)){
            return Boolean.TRUE;
        }
        //加锁,同一个履约单下报告顺序出，因为会发送首次和末次出报告事件
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STATION_SETTLEMENT_LOCK_KEY, cmd.getAppointmentId());
        String uuid = UUID.randomUUID().toString();
        if (!redisLockUtil.tryLock(lockKey, uuid, RedisKeyEnum.STATION_SETTLEMENT_LOCK_KEY.getExpireTime(), RedisKeyEnum.STATION_SETTLEMENT_LOCK_KEY.getExpireTimeUnit())) {
            log.error("[MedicalPromiseApplicationImpl.sendSettlementMessage],实验室结算加锁失败!");
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }

        try {
            //查询检测单
            MedicalPromiseListQuery query = new MedicalPromiseListQuery();
            query.setPromiseId(cmd.getPromiseId());
            query.setPromisePatientIdList(Lists.newArrayList(cmd.getPromisePatientId()));
            query.setServiceId(cmd.getServiceId());
            query.setReportStatus(CommonConstant.ONE);
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(query);
            if (CollectionUtils.isEmpty(medicalPromises)){
                return Boolean.FALSE;
            }

            //查询履约单
            JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(cmd.getPromiseId()).build());

            Date orderStartTime = null;

            if (StringUtil.equals("jzyyHomeTest",promise.getVerticalCode())){
                orderStartTime = promise.getCreateTime();
            }else {
                //查询订单
                JdOrder jdOrder = jdOrderApplication.queryJdOrderByOrderId(Long.valueOf(promise.getSourceVoucherId()));
                if (Objects.isNull(jdOrder)){
                    log.info("[MedicalPromiseApplicationImpl.sendSettlementMessage],通过订单号查询医送订单为空!");
                    throw new BusinessException(BaseDomainErrorCode.TIAN_SUAN_PUSH_ERROR);
                }
                orderStartTime= jdOrder.getCreateTime();
            }


            //查询项目结算价
            List<Long> itemList = medicalPromises.stream().map(p->Long.valueOf(p.getServiceItemId())).collect(Collectors.toList());
            List<JdhStationServiceItemRelDto> jdhStationServiceItemRelDto = providerStoreApplication.queryStationServiceItemRelList(
                    JdhStationServiceItemRelRequest.builder().serviceItemIds(itemList).queryStationDetail(Boolean.TRUE).build()
            );

            //key:station_item,value:Obj
            Map<String, JdhStationServiceItemRelDto> stationItemToObj = jdhStationServiceItemRelDto.stream().collect(Collectors.toMap(p -> p.getStationId() + "_" + p.getServiceItemId(), o -> o));
            List<MedPromiseSettlementContext> medPromiseSettlementContexts  = Lists.newArrayList();
            for (MedicalPromise mp : medicalPromises){
                JdhStationServiceItemRelDto jdhStationServiceItemRel = stationItemToObj.get(mp.getStationId() + "_" + mp.getServiceItemId());
                if (Objects.isNull(jdhStationServiceItemRel)){
                    return Boolean.FALSE;
                }

                Date verifyTime = Objects.nonNull(mp.getReportTime()) ? mp.getReportTime() : cmd.getCompleteTime();
                MedPromiseSettlementContext medPromiseSettlementContext = MedPromiseSettlementContext.builder()
                        .settlementPrice(jdhStationServiceItemRel.getSettlementPrice())
                        .userPin(mp.getUserPin())
                        .medicalPromiseId(mp.getMedicalPromiseId())
                        .channelRuleCode(jdhStationServiceItemRel.getChannelRuleCode())
                        .providerId(mp.getProviderId())
                        .orderStartTime(orderStartTime)
                        //TODO
                        .verifyTime(verifyTime)
                        .medicalPromise(mp)
                        .actualPrice(jdhStationServiceItemRel.getSettlementPrice())
                        .build();
                medPromiseSettlementContexts.add(medPromiseSettlementContext);

            }

            Boolean res;
            List<Integer> isTests = new ArrayList<>();
            medicalPromises.forEach(m->{
                Integer isTest = duccConfig.getTestSpecimenCodeConfig().isTest(m.getStationId(),m.getSpecimenCode())?1:0;
                isTests.add(isTest);
            });
            if(CollectionUtils.isNotEmpty(isTests)&&isTests.contains(YnStatusEnum.YES.getCode())){
                log.info("测试单,不发送天算 medicalPromises={}",JSON.toJSONString(medicalPromises));
                res = true;
            }else{
                res = medicalPromiseDomainService.sendSettlementMessage(medPromiseSettlementContexts);
            }
            if (res){

                List<String> black = duccConfig.getTianSuanSettlementStationBlack();

                medPromiseSettlementContexts.forEach(p->{

                    if (!black.contains(p.getMedicalPromise().getStationId())){
                        //发送天算结算完成事件
                        MedicalPromise medicalPromise = p.getMedicalPromise();
                        eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SETTLEMENT_COMPLETE,
                                MedPromiseSettlementEventBody.builder()
                                        .promiseId(medicalPromise.getPromiseId())
                                        .medicalPromiseId(p.getMedicalPromiseId())
                                        .completeTime(p.getVerifyTime())
                                        .promisePatientId(medicalPromise.getPromisePatientId())
                                        .serviceId(medicalPromise.getServiceId())
                                        .settlementPrice(p.getSettlementPrice())
                                        .build()));
                    }


                });
                jimClient.setEx(tsHistoryKey,cmd.getAppointmentId(),CommonConstant.FIFTEEN, TimeUnit.DAYS);
            }
            return res;
        }finally {
            redisLockUtil.unLock(lockKey, uuid);
        }

    }

    /**
     * 预约单人纬度完成事件->触发实验室结算
     *
     * @param cmd
     * @return
     */
    @Override
        @LogAndAlarm(alarmPolicy = {
            @AlarmPolicy(type = AlarmPolicyTypeEnum.UMP),
            @AlarmPolicy(methodName = "实验室结算推送天算", keyword = "medicalPromiseId", type = AlarmPolicyTypeEnum.DONGDONG_ROBOT)
    })
    public Boolean sendPatientSettlementMessage(MedPromiseSettlementCmd cmd) {
        String tsHistoryKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STATION_PATIENT_SETTLEMENT_TS_REDIS_KEY, cmd.getAppointmentId());
        String redis = jimClient.get(tsHistoryKey);
        if (StringUtils.isNotBlank(redis)){
            return Boolean.TRUE;
        }
        //加锁,同一个履约单下报告顺序出，因为会发送首次和末次出报告事件
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STATION_PATIENT_SETTLEMENT_TS_REDIS_KEY, cmd.getAppointmentId());
        String uuid = UUID.randomUUID().toString();
        if (!redisLockUtil.tryLock(lockKey, uuid, RedisKeyEnum.STATION_PATIENT_SETTLEMENT_TS_REDIS_KEY.getExpireTime(), RedisKeyEnum.STATION_SETTLEMENT_LOCK_KEY.getExpireTimeUnit())) {
            log.error("[MedicalPromiseApplicationImpl.sendPatientSettlementMessage],实验室结算加锁失败!");
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }

        try {
            //查询检测单
            MedicalPromiseListQuery query = new MedicalPromiseListQuery();
            query.setPromiseId(cmd.getPromiseId());
            query.setPromisePatientIdList(Lists.newArrayList(cmd.getPromisePatientId()));
            List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(query);
            if (CollectionUtils.isEmpty(medicalPromises)){
                return Boolean.FALSE;
            }


            //查询项目结算价
            List<Long> itemList = medicalPromises.stream().map(p->Long.valueOf(p.getServiceItemId())).collect(Collectors.toList());
            List<JdhStationServiceItemRelDto> jdhStationServiceItemRelDto = providerStoreApplication.queryStationServiceItemRelList(
                    JdhStationServiceItemRelRequest.builder().serviceItemIds(itemList).queryStationDetail(Boolean.TRUE).build()
            );

            //key:station_item,value:Obj
            Map<String, JdhStationServiceItemRelDto> stationItemToObj = jdhStationServiceItemRelDto.stream().collect(Collectors.toMap(p -> p.getStationId() + "_" + p.getServiceItemId(), o -> o));
            List<MedPromiseSettlementContext> medPromiseSettlementContexts  = Lists.newArrayList();
            for (MedicalPromise mp : medicalPromises){
                JdhStationServiceItemRelDto jdhStationServiceItemRel = stationItemToObj.get(mp.getStationId() + "_" + mp.getServiceItemId());
                if (Objects.isNull(jdhStationServiceItemRel)){
                    log.error("[[MedicalPromiseApplicationImpl.sendPatientSettlementMessage],存在无法检测的项目!]");
                    return Boolean.FALSE;
                }
                MedPromiseSettlementContext medPromiseSettlementContext = MedPromiseSettlementContext.builder()
                        .settlementPrice(jdhStationServiceItemRel.getSettlementPrice())
                        .userPin(mp.getUserPin())
                        .medicalPromiseId(mp.getMedicalPromiseId())
                        .channelRuleCode(jdhStationServiceItemRel.getChannelRuleCode())
                        .providerId(mp.getProviderId())
                        .orderStartTime(cmd.getCompleteTime())
                        //TODO
                        .verifyTime(cmd.getCompleteTime())
                        .medicalPromise(mp)
                        .actualPrice(jdhStationServiceItemRel.getSettlementPrice())
                        .build();
                medPromiseSettlementContexts.add(medPromiseSettlementContext);

            }

            Boolean res = medicalPromiseDomainService.sendSettlementMessage(medPromiseSettlementContexts);
            if (res){
                medPromiseSettlementContexts.forEach(p->{
                    //发送天算结算完成事件
                    MedicalPromise medicalPromise = p.getMedicalPromise();
                    eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SETTLEMENT_COMPLETE,
                            MedPromiseSettlementEventBody.builder()
                                    .promiseId(medicalPromise.getPromiseId())
                                    .medicalPromiseId(p.getMedicalPromiseId())
                                    .completeTime(p.getVerifyTime())
                                    .promisePatientId(medicalPromise.getPromisePatientId())
                                    .serviceId(medicalPromise.getServiceId())
                                    .settlementPrice(p.getSettlementPrice())
                                    .build()));
                });
                jimClient.setEx(tsHistoryKey,cmd.getAppointmentId(),CommonConstant.FIFTEEN, TimeUnit.DAYS);
            }
            return res;
        }finally {
            redisLockUtil.unLock(lockKey, uuid);
        }
    }

    /**
     * 履约单报告全部已出
     *
     * @param medicalPromiseAllGenerateCmd
     * @return
     */
    @Override
    public Boolean medicalPromiseAllGenerate(MedicalPromiseAllGenerateCmd medicalPromiseAllGenerateCmd) {

        //加锁,同一个履约单下报告顺序出，因为会发送首次和末次出报告事件
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.MEDICAL_PROMISE_ALL_GENERATE_REPORT_KEY, medicalPromiseAllGenerateCmd.getPromiseId());
        String uuid = UUID.randomUUID().toString();
        if (!redisLockUtil.tryLock(lockKey, uuid, RedisKeyEnum.MEDICAL_PROMISE_ALL_GENERATE_REPORT_KEY.getExpireTime(), RedisKeyEnum.MEDICAL_PROMISE_ALL_GENERATE_REPORT_KEY.getExpireTimeUnit())) {
            log.error("[MedicalPromiseApplicationImpl.medicalPromiseAllGenerate],判断推送全部报告已出失败!");
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }


        try {
            //如果发过全部已出报告消息，则跳过
            JdhPromise promise = promiseRepository.findPromise(PromiseRepQuery.builder().promiseId(medicalPromiseAllGenerateCmd.getPromiseId()).build());
            List<JdhPromiseExtend> promiseExtends = promise.getPromiseExtends();
            if (CollectionUtils.isNotEmpty(promiseExtends)){
                JdhPromiseExtend jdhPromiseExtend = promiseExtends.stream().filter(p -> StringUtils.equals(PromiseExtendKeyEnum.LAST_GENERATE_REPORT.getFiledKey(), p.getAttribute())).findFirst().orElse(null);
                if (Objects.nonNull(jdhPromiseExtend)){
                    return Boolean.TRUE;
                }
            }

            //履约单下所有检测单
            List<MedicalPromise> all = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(medicalPromiseAllGenerateCmd.getPromiseId()).build());
            //如果有完成的报告，并且所有的报告都是终态，则说明履约单已完成
            List<Integer> status = all.stream().map(MedicalPromise::getStatus).collect(Collectors.toList());
            log.info("[MedicalPromiseApplicationImpl.medicalPromiseAllGenerate],status={}",JsonUtil.toJSONString(status));
            if (status.contains(MedicalPromiseStatusEnum.COMPLETED.getStatus())){
                status.removeIf(p->Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(),p) || Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(),p));
                log.info("[MedicalPromiseApplicationImpl.medicalPromiseAllGenerate],afterStatus={}",JsonUtil.toJSONString(status));
                //如果为空，则说明都是最终态了
                if (CollectionUtils.isEmpty(status)){
                    promise.refreshExtend(PromiseExtendKeyEnum.LAST_GENERATE_REPORT,CommonConstant.ONE_STR);
                    promiseRepository.save(promise);
                    eventCoordinator.publish(EventFactory.newDefaultEvent(all.get(0), MedPromiseEventTypeEnum.MED_PROMISE_ALL_COMPLETE,
                            MedicalPromiseAggregateEventBody.builder().voucherId(all.get(0).getVoucherId()).promiseId(all.get(0).getPromiseId()).build()));
                    log.info("[MedicalPromiseApplicationImpl.medicalPromiseAllGenerate],报告全部已出,promiseId={}",all.get(0).getPromiseId());
                }
            }
        }finally {
            redisLockUtil.unLock(lockKey, uuid);
        }

        return Boolean.TRUE;
    }

    /**
     * 实验室结算推ebs
     * @return
     */
    @Override
    @LogAndAlarm(alarmPolicy = {
            @AlarmPolicy(type = AlarmPolicyTypeEnum.UMP),
            @AlarmPolicy(methodName = "实验室结算推送EBS", keyword = "medicalPromiseId", type = AlarmPolicyTypeEnum.DONGDONG_ROBOT)
    })
    public Boolean settlementToEbs(MedPromiseToEbsCmd medPromiseToEbsCmd) {

        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STATION_SETTLEMENT_EBS_REDIS_KEY, medPromiseToEbsCmd.getMedicalPromiseId());
        String existEbs = jimClient.get(redisKey);
        if (StringUtils.isNotBlank(existEbs)){
            return Boolean.TRUE;
        }
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STATION_SETTLEMENT_EBS_LOCK_KEY, medPromiseToEbsCmd.getMedicalPromiseId());
        String uuid = UUID.randomUUID().toString();
        if (!redisLockUtil.tryLock(lockKey, uuid, RedisKeyEnum.STATION_SETTLEMENT_EBS_LOCK_KEY.getExpireTime(), RedisKeyEnum.STATION_SETTLEMENT_EBS_LOCK_KEY.getExpireTimeUnit())) {
            log.error("[MedicalPromiseApplicationImpl.settlementToEbs],实验室结算推送EBS加锁失败!");
            throw new BusinessException(MedPromiseErrorCode.DOING_WAIT_MOMENT);
        }

        try {
            //1.查询检测单信息
            MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(medPromiseToEbsCmd.getMedicalPromiseId()).build());
            JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(medicalPromise.getVerticalCode());
            SettlementEbsContext settlementEbsContext = new SettlementEbsContext();
            settlementEbsContext.setBusinessModeEnum(BusinessModeEnum.getEnumByCode(jdhVerticalBusiness.getBusinessModeCode()));
            settlementEbsContext.setEbsSettleTypeEnum(EbsSettleTypeEnum.EXPEND);
            settlementEbsContext.setEbsSettleSplitTypeEnum(EbsSettleSplitTypeEnum.SERVICE_ITEM_FEE);
            settlementEbsContext.setEbsSettleMainBodyTypeEnum(EbsSettleMainBodyTypeEnum.STATION);
            settlementEbsContext.setExtBusinessModel(medPromiseToEbsCmd);
            Boolean res = settlementEbsApplication.sendToEbs(settlementEbsContext);
            if (res){
                jimClient.setEx(redisKey,"exist",RedisKeyEnum.STATION_SETTLEMENT_EBS_REDIS_KEY.getExpireTime(),RedisKeyEnum.STATION_SETTLEMENT_EBS_REDIS_KEY.getExpireTimeUnit());
            }
            return res;
        }finally {
            redisLockUtil.unLock(lockKey, uuid);
        }
    }

    /**
     * 服务者检测单完成
     *
     * @param medicalPromiseStatusCmd
     * @return
     */
    @Override
    @LogAndAlarm
    @Transactional(rollbackFor = Exception.class)
    public Boolean angelMedicalPromiseFinish(MedicalPromiseStatusCmd medicalPromiseStatusCmd) {
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(MedicalPromiseListQuery.builder().promiseId(medicalPromiseStatusCmd.getPromiseId()).build());
        if (CollectionUtils.isEmpty(medicalPromises)){
            return Boolean.FALSE;
        }
        medicalPromises.removeIf(p->Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(),p.getStatus()) || Objects.equals(CommonConstant.ONE,p.getFreeze()));
        if (CollectionUtils.isEmpty(medicalPromises)){
            return Boolean.FALSE;
        }
        MedicalPromise medicalPromise = medicalPromises.get(0);
        JdhVerticalBusiness jdhVerticalBusiness = businessRepository.find(medicalPromise.getVerticalCode());
        if (!StringUtils.equals(BusinessModeEnum.ANGEL_CARE.getCode(),jdhVerticalBusiness.getBusinessModeCode())){
            return Boolean.TRUE;
        }
        MedicalPromiseBatchFinishContext medicalPromiseBatchFinishContext = new MedicalPromiseBatchFinishContext();
        medicalPromiseBatchFinishContext.setSnapMedicalPromises(medicalPromises);
        medicalPromiseDomainService.medicalPromiseFinish(medicalPromiseBatchFinishContext);
        List<MedicalPromise> medicalPromiseRes = medicalPromiseBatchFinishContext.getMedicalPromises();


        Map<Long, MedicalPromise> beforeStatusMap = medicalPromises.stream().collect(Collectors.toMap(MedicalPromise::getMedicalPromiseId, p -> p));
        medicalPromiseRes.forEach(p->{
            MedicalPromiseAggregateEventBody medicalPromiseAggregateEventBody = new MedicalPromiseAggregateEventBody();
            medicalPromiseAggregateEventBody.setPromiseId(p.getPromiseId());
            MedicalPromiseCheckEventBody build = MedicalPromiseCheckEventBody.builder().medicalPromiseId(p.getMedicalPromiseId()).status(beforeStatusMap.get(p.getMedicalPromiseId()).getStatus()).build();
            medicalPromiseAggregateEventBody.setMedicalPromiseCheckEventBodies(Lists.newArrayList(build));
            eventCoordinator.publish(EventFactory.newDefaultEvent(p, MedPromiseEventTypeEnum.MED_PROMISE_COMPLETE,medicalPromiseAggregateEventBody));
        });
        Map<String, List<MedicalPromise>> beforePpidServiceToList = medicalPromises.stream().collect(Collectors.groupingBy(p -> p.getPromisePatientId() + "_" + p.getServiceId()));
        Map<String, List<MedicalPromise>> afterPpidServiceToList = medicalPromiseRes.stream().collect(Collectors.groupingBy(p -> p.getPromisePatientId() + "_" + p.getServiceId()));
        beforePpidServiceToList.forEach((ppidService,beforeList)->{
            List<MedicalPromise> afterList = afterPpidServiceToList.get(ppidService);
            MedicalPromise beforeCompleted = beforeList.stream().filter(p -> Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(), p.getStatus())).findFirst().orElse(null);
            MedicalPromise afterNotCompleted = afterList.stream().filter(p -> !Objects.equals(MedicalPromiseStatusEnum.COMPLETED.getStatus(), p.getStatus())).findFirst().orElse(null);
            if (Objects.isNull(beforeCompleted) && Objects.isNull(afterNotCompleted)){
                MedicalPromise mp = afterList.get(0);
                MedicalPromiseEventBody eventBody = MedicalPromiseConvert.INSTANCE.medicalPromise2MedicalPromiseEventBody(mp);
                eventBody.setAppointmentId(mp.getPromisePatientId() + "_" + mp.getServiceId());
                eventBody.setSourceVoucherId(promiseApplication.findSourceVoucherIdByPromiseId(mp.getPromiseId()));
                eventCoordinator.publish(EventFactory.newDefaultEvent(mp, MedPromiseEventTypeEnum.MED_PROMISE_PATIENT_SERVICE_COMPLETE,eventBody));
            }
        });
        //判断后是否报告全部已出
        eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_JUDGE_ALL_GENERATE,
                MedicalPromiseEventBody.builder().promiseId(medicalPromiseStatusCmd.getPromiseId()).build()));
        return Boolean.TRUE;
    }

    /**
     * 更新结算状态
     *
     * @param medicalPromiseSettleStatusCmd
     * @return
     */
    @Override
    public Integer updateSettleSatus(MedicalPromiseSettleStatusCmd medicalPromiseSettleStatusCmd) {
        MedicalPromise medicalPromise = MedicalPromise.builder().build();
        medicalPromise.setServiceId(medicalPromiseSettleStatusCmd.getServiceId());
        medicalPromise.setPromisePatientId(medicalPromiseSettleStatusCmd.getPromisePatientId());
        medicalPromise.setSettleStatus(medicalPromiseSettleStatusCmd.getSettleStatus());
        medicalPromise.setMedicalPromiseIds(medicalPromiseSettleStatusCmd.getMedicalPromiseIds());
        return medicalPromiseRepository.updateSettleSatus(medicalPromise);
    }

    /**
     * 导出门店下检测单
     *
     * @param labQueryMedPromisePageRequest
     * @return
     */
    @Override
    public ExportDataDTO exportMedicalPromiseList(LabQueryMedPromisePageRequest labQueryMedPromisePageRequest) {

        //1、构建上下文
        Map<String, Object> ctx = cn.hutool.core.bean.BeanUtil.beanToMap(labQueryMedPromisePageRequest);
        ctx.put("scene", FileExportTypeEnum.STATION_APPOINT_LIST_EXPORT.getType());
        ctx.put("userPin",labQueryMedPromisePageRequest.getUserPin());
        ctx.put("operationType", FileExportTypeEnum.STATION_APPOINT_LIST_EXPORT.getType());
        fileManageApplication.export(ctx);
        //2、调用通用文件导入能力
        return null;
    }

    /**
     * 重置报告上传状态
     * @param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetReportStatus(ResetReportStatusCmd cmd) {
        ValidateParamUtil.validThrow(cmd);
        MedicalPromise medicalPromise = medicalPromiseRepository.find(new MedicalPromiseIdentifier(cmd.getMedicalPromiseId()));

        // 重置报告状态
        medicalPromise.resetReportStatus();
        medicalPromiseRepository.save(medicalPromise);

        //删除报告指标
        MedicalReport medicalReport = medicalReportRepository.getByMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        if (Objects.nonNull(medicalReport)){
            medicalReportIndicatorRepository.deleteMedicalReportIndicators(String.valueOf(medicalReport.getId()));
        }


        // 发布事件
        MedicalPromiseEventBody eventBody = MedicalPromiseEventBody.builder()
                .promiseId(medicalPromise.getPromiseId())
                .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                .beforeStatus(medicalPromise.getStatus())
                .status(medicalPromise.getStatus())
                .build();
        eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.RESET_REPORT_STATUS,eventBody));
    }

    /**
     * 重置条码
     * @param cmd
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetSpecimenCode(ResetSpecimenCodeCmd cmd) {
        ValidateParamUtil.validThrow(cmd);
        // 条码业务校验
        productSpecimenCodeApplication.checkSpecimenCode(cmd.getSpecimenCode());

        MedicalPromise snapshot = medicalPromiseRepository.find(new MedicalPromiseIdentifier(cmd.getMedicalPromiseId()));
        MedicalPromise medicalPromise= snapshot.copyInstance();

        // 重置条码
        medicalPromise.resetSpecimenCode(cmd.getSpecimenCode());
        medicalPromiseRepository.save(medicalPromise);
        BatchMedicalPromiseSubmitCmd batchMedicalPromiseSubmitCmd = new BatchMedicalPromiseSubmitCmd();
        batchMedicalPromiseSubmitCmd.setPromiseId(snapshot.getPromiseId());
        batchSubmitMedicalPromiseToStation(batchMedicalPromiseSubmitCmd);

        // 事件发布
        MedicalPromiseEventBody eventBody = MedicalPromiseEventBody.builder()
                .promiseId(medicalPromise.getPromiseId())
                .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                .beforeStatus(medicalPromise.getStatus())
                .status(medicalPromise.getStatus())
                .oldSpecimenCode(snapshot.getSpecimenCode())
                .freshSpecimenCode(cmd.getSpecimenCode())
                .build();
        eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.RESET_SPECIMEN_CODE,eventBody));
    }


    /**
     *
     * @param request
     * @return
     */
    private MedicalPromiseEsQuery buildQuery(LabQueryMedPromisePageRequest request){
        MedicalPromiseEsQuery medicalPromiseEsQuery = new MedicalPromiseEsQuery();
        medicalPromiseEsQuery.setProviderId(request.getProviderId());
        medicalPromiseEsQuery.setMedicalPromiseId(request.getMedicalPromiseId());
        Date appointDate;
        if (StringUtils.isNotBlank(request.getAppointmentDate())) {
            appointDate = DateUtil.parse(request.getAppointmentDate());
//            appointDate = TimeUtils.timeStrToDate(request.getAppointmentDate(), TimeFormat.SHORT_PATTERN_LINE);
        } else {
            appointDate = new Date();
        }
        if (request.getAppointmentStartTime() != null) {
            medicalPromiseEsQuery.setAppointmentStartTime(request.getAppointmentStartTime());
        } else {
            medicalPromiseEsQuery.setAppointmentStartTime(TimeUtils.getStartTime(appointDate));
        }
        if (request.getAppointmentEndTime() != null) {
            medicalPromiseEsQuery.setAppointmentEndTime(request.getAppointmentEndTime());
        } else {
            medicalPromiseEsQuery.setAppointmentEndTime(TimeUtils.getEndTime(appointDate));
        }
        medicalPromiseEsQuery.setPageNum(request.getPageNum());
        medicalPromiseEsQuery.setPageSize(request.getPageSize());
        medicalPromiseEsQuery.setLaboratoryStationId(request.getStationId());
        medicalPromiseEsQuery.setSpecimenCode(request.getSpecimenCode());
        medicalPromiseEsQuery.setServiceItemId(request.getServiceItemId());
        medicalPromiseEsQuery.setServiceItemName(request.getServiceItemName());
        Map<String, Object> maps = duccConfig.getQuickCheckQueryStoreStatus();
        log.info("MedicalPromiseApplicationImpl->labQueryMedicalPromisePage maps={}", JSONUtil.toJsonStr(maps));
        List<Integer> statusList = new ArrayList<>();
        List<Integer> shipStatusList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(maps)) {
            String statusListKey = "statusList";
            if (maps.containsKey(statusListKey)) {
                statusList = JSONUtil.toList(JSONUtil.toJsonStr(maps.get(statusListKey)), Integer.class);
            }
            if (CollUtil.isNotEmpty(statusList)) {
                medicalPromiseEsQuery.setStatusList(statusList);
            }
            String shipStatusListKey = "shipStatusList";
            if (maps.containsKey(shipStatusListKey)) {
                shipStatusList = JSONUtil.toList(JSONUtil.toJsonStr(maps.get(shipStatusListKey)), Integer.class);
            }
            if (CollUtil.isNotEmpty(shipStatusList)) {
                medicalPromiseEsQuery.setShipStatusList(shipStatusList.stream().map(Object::toString).collect(Collectors.toList()));
            }
            if (request.getCompositeStatus() != null && maps.containsKey("compositeStatusRule")) {
                String compositeStatusRule = JSONUtil.toJsonStr(maps.get("compositeStatusRule"));
                log.info("MedicalPromiseApplicationImpl->labQueryMedicalPromisePage compositeStatusRule={}", compositeStatusRule);
                TypeReference<Map<String, QuickCheckLabQueryStatusBo>> typeReference = new TypeReference<Map<String, QuickCheckLabQueryStatusBo>>() {};
                Map<String, QuickCheckLabQueryStatusBo> quickCheckLabQueryStatus = JSONUtil.toBean(compositeStatusRule, typeReference, true);
                if (CollUtil.isNotEmpty(quickCheckLabQueryStatus) && quickCheckLabQueryStatus.get(request.getCompositeStatus().toString()) != null) {
                    QuickCheckLabQueryStatusBo checkLabQueryStatus = quickCheckLabQueryStatus.get(request.getCompositeStatus().toString());
                    log.info("MedicalPromiseApplicationImpl->labQueryMedicalPromisePage checkLabQueryStatus={}", JSONUtil.toJsonStr(checkLabQueryStatus));
                    medicalPromiseEsQuery.setStatusList(checkLabQueryStatus.getOrderStatusList());
                    medicalPromiseEsQuery.setShipStatusList(checkLabQueryStatus.getSubmitOrderStatusList());
                    medicalPromiseEsQuery.setCheckStatus(checkLabQueryStatus.getCheckStatus());
                    medicalPromiseEsQuery.setReportStatus(checkLabQueryStatus.getReportStatus());
                }
            }
            medicalPromiseEsQuery.setReportTimeOutStatus(request.getReportTimeOutStatus());
            if (maps.containsKey("reportTimeOut")) {
                Long reportTimeOut = Long.parseLong(maps.get("reportTimeOut").toString());
                medicalPromiseEsQuery.setReportTimeOut(reportTimeOut);
            }
            if (maps.containsKey("reportTimeOutDesc")) {
                medicalPromiseEsQuery.setReportTimeOutStatusName(maps.get("reportTimeOutDesc").toString());
            }
            if (maps.containsKey("reportNotTimeOutDesc")) {
                medicalPromiseEsQuery.setReportNotTimeOutStatusName(maps.get("reportNotTimeOutDesc").toString());
            }
            medicalPromiseEsQuery.setReportCheckTimeOutStatus(request.getReportCheckTimeOutStatus());
            if (maps.containsKey("reportCheckTimeOut")) {
                Long reportCheckTimeOut = Long.parseLong(maps.get("reportCheckTimeOut").toString());
                medicalPromiseEsQuery.setReportCheckTimeOut(reportCheckTimeOut);
            }
            if (maps.containsKey("reportCheckTimeOutDesc")) {
                medicalPromiseEsQuery.setReportCheckTimeOutStatusName(maps.get("reportCheckTimeOutDesc").toString());
            }
            if (maps.containsKey("reportCheckNotTimeOutDesc")) {
                medicalPromiseEsQuery.setReportCheckNotTimeOutStatusName(maps.get("reportCheckNotTimeOutDesc").toString());
            }
        }
        return medicalPromiseEsQuery;
    }
    /**
     * 分页查询检测单综合数据
     *
     * @param request
     * @return
     */
    @Override
    public PageDto<MedicalPromiseFullDTO> labQueryMedicalPromisePage(LabQueryMedPromisePageRequest request) {

        MedicalPromiseEsQuery medicalPromiseEsQuery = buildQuery(request);
        log.info("MedicalPromiseApplicationImpl->labQueryMedicalPromisePage labQueryPage,start");
        PageDto<MedicalPromiseFull> medicalPromiseFullPageDto = jdMedicalPromiseEsRepository.labQueryPage(medicalPromiseEsQuery);
        log.info("MedicalPromiseApplicationImpl->labQueryMedicalPromisePage labQueryPage,end");

        PageDto<MedicalPromiseFullDTO> medicalPromiseFullDTOPageDto = MedicalPromiseConvert.INSTANCE.convertFull(medicalPromiseFullPageDto);
        if (Objects.nonNull(medicalPromiseFullPageDto) && CollectionUtil.isNotEmpty(medicalPromiseFullPageDto.getList())) {
            log.info("MedicalPromiseApplicationImpl->labQueryMedicalPromisePage labQueryPage,size={}",medicalPromiseFullPageDto.getList().size());
            List<MedicalPromiseFullDTO> res = buildDto(medicalPromiseFullPageDto.getList(), medicalPromiseEsQuery, request);
            log.info("MedicalPromiseApplicationImpl->labQueryMedicalPromisePage list,end");

            medicalPromiseFullDTOPageDto.setList(res);

        }

        return medicalPromiseFullDTOPageDto;
    }

    private List<MedicalPromiseFullDTO> buildDto(List<MedicalPromiseFull> list, MedicalPromiseEsQuery medicalPromiseEsQuery, LabQueryMedPromisePageRequest request) {
        List<MedicalPromiseFullDTO> res = Lists.newArrayList();
        Map<String, String> jdlMessage = new HashMap<>();
        try {
            List<String> promiseList = list.stream().filter(s -> ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode().equalsIgnoreCase(s.getVerticalCode())).map(MedicalPromiseFull::getPromiseId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(promiseList)) {
                List<String> cacheValues = jimClient.mGet(promiseList.stream().map(s -> RedisKeyEnum.getRedisKey(RedisKeyEnum.STORE_QUERY_MEDICAL_LIST_SHIP_INFO, s)).toArray(String[]::new));
                int index = 0;
                for (String s : promiseList) {
                    String shipMessage = cacheValues.get(index);
                    jdlMessage.put(s, shipMessage);
                    index++;
                }
            }
        } catch (Exception e) {
            log.error("MedicalPromiseApplicationImpl->labQueryMedicalPromisePage", e);
        }

        for (MedicalPromiseFull medicalPromiseFull : list){

            MedicalPromiseFullDTO medicalPromiseFullDTO = new MedicalPromiseFullDTO();

            medicalPromiseFullDTO.setMedicalPromiseId(Long.valueOf(medicalPromiseFull.getMedicalPromiseId()));
            medicalPromiseFullDTO.setServiceItemName(medicalPromiseFull.getServiceItemName());
            medicalPromiseFullDTO.setServiceItemId(medicalPromiseFull.getServiceItemId());
            medicalPromiseFullDTO.setServiceId(Long.valueOf(medicalPromiseFull.getOrderId()));
            medicalPromiseFullDTO.setSerialNum(medicalPromiseFull.getSerialNum());
            //TODO
            medicalPromiseFullDTO.setDeliveryStoreTime(DateUtil.format(medicalPromiseFull.getDeliveryStoreTime(),CommonConstant.YMDHMS));
            medicalPromiseFullDTO.setCheckTime(DateUtil.format(medicalPromiseFull.getCheckTime(),CommonConstant.YMDHMS));
            medicalPromiseFullDTO.setReportTime(DateUtil.format(medicalPromiseFull.getReportTime(),CommonConstant.YMDHMS));
            medicalPromiseFullDTO.setSpecimenCode(medicalPromiseFull.getSpecimenCode());
            medicalPromiseFullDTO.setProviderId(StringUtils.isNotBlank(medicalPromiseFull.getProviderId()) ? Long.valueOf(medicalPromiseFull.getProviderId()) : null);
            medicalPromiseFullDTO.setShipStatus(medicalPromiseFull.getShipStatus());
            medicalPromiseFullDTO.setAppointmentStartTime(DateUtil.format(medicalPromiseFull.getAppointmentStartTime(),CommonConstant.YMDHMS));
            medicalPromiseFullDTO.setAppointmentEndTime(DateUtil.format(medicalPromiseFull.getAppointmentEndTime(),CommonConstant.YMDHMS));
            medicalPromiseFullDTO.setMedicalPromiseStatus(Integer.valueOf(medicalPromiseFull.getMedicalPromiseStatus()));
            medicalPromiseFullDTO.setReportStatus(medicalPromiseFull.getReportStatus());


            Integer status = medicalPromiseFullDTO.getMedicalPromiseStatus();
            Integer reportStatus = medicalPromiseFullDTO.getReportStatus();
            Integer shipStatus = medicalPromiseFullDTO.getShipStatus();

            Map<String, String> quickCheckStatusConfig = duccConfig.getQuickCheckStatusConfig();
            //未到达实验室
            Set<Integer> notArriveStatusSet = Sets.newHashSet(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),MedicalPromiseStatusEnum.COMPLETED.getStatus(),MedicalPromiseStatusEnum.INVALID.getStatus());
            //已出报告
            if (Objects.equals(CommonConstant.ONE,reportStatus)){
                medicalPromiseFullDTO.setCompositeStatus(CommonConstant.SIX);
            }else if (Objects.equals(AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus(),shipStatus) && !notArriveStatusSet.contains(status)){
                //送检中
                medicalPromiseFullDTO.setCompositeStatus(CommonConstant.FOUR);
            }else if (Objects.equals(AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus(),shipStatus) && !notArriveStatusSet.contains(status)) {
                //已送达待收样
                medicalPromiseFullDTO.setCompositeStatus(CommonConstant.NUMBER_FOURTY_FIVE);
            }else if (Objects.equals(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus(),status)) {
                //检测中
                medicalPromiseFullDTO.setCompositeStatus(CommonConstant.FIVE);
            }else if (Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(),status)){
                //已退款
                medicalPromiseFullDTO.setCompositeStatus(CommonConstant.NUMBER_MINUS_ONE);
            }
            log.info("MedicalPromiseApplicationImpl->medicalPromiseFullDTOPageDto compositeStatus={}", JsonUtil.toJSONString(medicalPromiseFullDTO.getCompositeStatus()));

            if (Objects.nonNull(medicalPromiseFullDTO.getCompositeStatus())){
                String s = quickCheckStatusConfig.get(String.valueOf(medicalPromiseFullDTO.getCompositeStatus()));
                Map<String,String> statusDescMap = JsonUtil.parseObject(s, Map.class);
                medicalPromiseFullDTO.setCompositeStatusName(statusDescMap.get("compositeStatusName"));
                medicalPromiseFullDTO.setCompositeStatusDesc(statusDescMap.get("compositeStatusDesc"));
            }
            if (CollUtil.isNotEmpty(jdlMessage) && medicalPromiseFull.getPromiseId() != null && jdlMessage.containsKey(medicalPromiseFull.getPromiseId()) && StringUtils.isNotBlank(jdlMessage.get(medicalPromiseFull.getPromiseId()))) {
                medicalPromiseFullDTO.setCompositeStatusName(medicalPromiseFullDTO.getCompositeStatusName() == null ? "" : medicalPromiseFullDTO.getCompositeStatusName() + "(" + jdlMessage.get(medicalPromiseFull.getPromiseId()) + ")");
            }

            medicalPromiseFullDTO.setServiceItemName(medicalPromiseFull.getServiceItemName());
            medicalPromiseFullDTO.setReportTimeOutStatus(CommonConstant.ZERO);
            if (medicalPromiseFull.getCheckTime() != null && medicalPromiseEsQuery.getReportTimeOut() != null) {
                if (medicalPromiseFull.getReportTime() != null) {
                    medicalPromiseFullDTO.setReportTimeOutStatus((medicalPromiseFull.getReportTime().getTime() - medicalPromiseFull.getCheckTime().getTime()) / 1000 >= medicalPromiseFull.getInspectDuration() ? 1 : 0);
                    medicalPromiseFullDTO.setReportTimeOutRemain(0L);
                } else {
                    Date now = new Date();
                    long remain = medicalPromiseFull.getInspectDuration() - ((now.getTime() - medicalPromiseFull.getCheckTime().getTime()) / 1000);
                    medicalPromiseFullDTO.setReportTimeOutStatus(remain < 0 ? 1 : 0);
                    medicalPromiseFullDTO.setReportTimeOutRemain(remain < 0 ? 0 : remain);
                }
            }

            if (medicalPromiseFullDTO.getReportTimeOutStatus() == 0) {
                medicalPromiseFullDTO.setReportTimeOutStatusName(medicalPromiseEsQuery.getReportNotTimeOutStatusName());
            } else if (medicalPromiseFullDTO.getReportTimeOutStatus() == 1) {
                medicalPromiseFullDTO.setReportTimeOutStatusName(medicalPromiseEsQuery.getReportTimeOutStatusName());
            }

            // 报告-送达时间 间隔超时
            medicalPromiseFullDTO.setReportCheckTimeOutStatus(0);
            if (medicalPromiseFull.getDeliveryStoreTime() != null && medicalPromiseFull.getInspectDuration() != null) {
                if (medicalPromiseFull.getReportTime() != null) {
                    medicalPromiseFullDTO.setReportCheckTimeOutStatus((medicalPromiseFull.getReportTime().getTime() - medicalPromiseFull.getDeliveryStoreTime().getTime()) / 1000 >= medicalPromiseFull.getInspectDuration() ? 1 : 0);
                    medicalPromiseFullDTO.setReportCheckTimeOutRemain(0L);
                } else {
                    Date now = new Date();
                    long remain = medicalPromiseFull.getInspectDuration() - ((now.getTime() - medicalPromiseFull.getDeliveryStoreTime().getTime()) / 1000);
                    medicalPromiseFullDTO.setReportCheckTimeOutStatus(remain < 0 ? 1 : 0);
                    medicalPromiseFullDTO.setReportCheckTimeOutRemain(remain < 0 ? 0 : remain);
                }
            }

            if (medicalPromiseFullDTO.getReportCheckTimeOutStatus() == 0) {
                medicalPromiseFullDTO.setReportCheckTimeOutStatusName(medicalPromiseEsQuery.getReportCheckNotTimeOutStatusName());
            } else if (medicalPromiseFullDTO.getReportCheckTimeOutStatus() == 1) {
                medicalPromiseFullDTO.setReportCheckTimeOutStatusName(medicalPromiseEsQuery.getReportCheckTimeOutStatusName());
            }
            if (medicalPromiseFull.getDeliveryStoreTime() != null) {
                medicalPromiseFullDTO.setDeliveryStoreTime(TimeUtils.dateTimeToStr(medicalPromiseFull.getDeliveryStoreTime()));
            }

            // 已退款不展示是否超时
            if (MedicalPromiseStatusEnum.INVALID.getStatus().equals(Integer.valueOf(medicalPromiseFull.getMedicalPromiseStatus()))) {
                medicalPromiseFullDTO.setReportTimeOutStatus(null);
                medicalPromiseFullDTO.setReportTimeOutStatusName(null);
                medicalPromiseFullDTO.setReportTimeOutRemain(null);
                medicalPromiseFullDTO.setReportCheckTimeOutStatus(null);
                medicalPromiseFullDTO.setReportCheckTimeOutStatusName(null);
                medicalPromiseFullDTO.setReportCheckTimeOutRemain(null);
            }

            medicalPromiseFullDTO.setName(new UserName(medicalPromiseFull.getUserName()).maskPersonal());
            medicalPromiseFullDTO.setGender(StringUtils.isBlank(medicalPromiseFull.getUserGender()) ? null : Integer.parseInt(medicalPromiseFull.getUserGender()));
            medicalPromiseFullDTO.setGenderDesc(StringUtils.isBlank(medicalPromiseFull.getUserGender()) ? null : GenderEnum.getDescOfType(Integer.parseInt(medicalPromiseFull.getUserGender())));
            medicalPromiseFullDTO.setAge(Birthday.parseAge(medicalPromiseFull.getBirthday()));

            if (Boolean.TRUE.equals(request.getQueryEncryptionName())) {
                medicalPromiseFullDTO.setEncryptionName(RSACode.encryptByPublicKey(RandomStringUtils.randomAlphanumeric(6) + medicalPromiseFull.getUserName()));
            }

            res.add(medicalPromiseFullDTO);
        }
        return res;
    }


    /**
     * 查询检测单
     * @param medicalPromiseDispatchCmd
     * @return
     */
    private List<MedicalPromise> getValidMedicalPromises(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd) {
        MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
        medicalPromiseListQuery.setPromiseId(medicalPromiseDispatchCmd.getPromiseId());
        medicalPromiseListQuery.setPromisePatientIdList(medicalPromiseDispatchCmd.getPromisePatientIdList());
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        log.info("MedicalPromiseApplicationImpl->getValidMedicalPromises,medicalPromises={}",JsonUtil.toJSONString(medicalPromises));
        if (CollectionUtils.isEmpty(medicalPromises)){
            // 没有检测单信息 抛异常
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_MATCH_STATUS_LIST_NULL);
        }
        //去除不符合派发状态的检测单
        Set<Integer> dispatchBlackStatus = MedicalPromiseStatusEnum.getDispatchBlackStatus();
        medicalPromises.removeIf(p->Objects.equals(CommonConstant.ONE,p.getFreeze()) || dispatchBlackStatus.contains(p.getStatus()));
        log.info("MedicalPromiseApplicationImpl->getValidMedicalPromises,medicalPromisesAfter={}",JsonUtil.toJSONString(medicalPromises));
        if (CollectionUtils.isEmpty(medicalPromises)){
            //没有符合的，抛异常
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_MATCH_STATUS_LIST_NULL);
        }
        return medicalPromises;
    }

    /**
     * 查询履约单
     * @param medicalPromiseDispatchCmd
     * @return
     */
    private PromiseDto getJdhPromise(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd) {
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(medicalPromiseDispatchCmd.getPromiseId());
        PromiseDto jdhPromise = promiseApplication.findByPromiseId(promiseIdRequest);
        log.info("MedicalPromiseApplicationImpl->getJdhPromise,jdhPromise={}",JsonUtil.toJSONString(jdhPromise));
        if (Objects.isNull(jdhPromise) || CollectionUtils.isEmpty(jdhPromise.getPatients())){
            //没有履约信息或者受检人信息
            throw new BusinessException(MedPromiseErrorCode.PROMISE_MEDICAL_PROMISE_NULL);
        }
        //校验状态,是否冻结
        if (Objects.equals(CommonConstant.ONE,jdhPromise.getFreeze())){
            // 冻结 抛异常
            throw new BusinessException(MedPromiseErrorCode.PROMISE_MEDICAL_PROMISE_FREEZE);
        }
        return jdhPromise;
    }

    /**
     * 查询履约单
     * @param promiseId
     * @return
     */
    private PromiseDto getJdhPromise(Long promiseId) {
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(promiseId);
        PromiseDto jdhPromise = promiseApplication.findByPromiseId(promiseIdRequest);
        if (Objects.isNull(jdhPromise) || CollectionUtils.isEmpty(jdhPromise.getPatients())){
            //没有履约信息或者受检人信息
            throw new BusinessException(MedPromiseErrorCode.PROMISE_MEDICAL_PROMISE_NULL);
        }
        return jdhPromise;
    }

    /**
     * 获取派送实验室顺序规则
     * @param jdhPromise
     * @param medicalPromises
     * @return
     */
    private List<StoreDispatchRule> getStoreDispatchRules(PromiseDto jdhPromise, List<MedicalPromise> medicalPromises) {
        List<StoreDispatchRule> storeDispatchRules = Lists.newArrayList();
        log.info("MedicalPromiseApplicationImpl->getStoreDispatchRules,jdhPromise={},medicalPromises={}",JsonUtil.toJSONString(jdhPromise),JsonUtil.toJSONString(medicalPromises));
        //互医场景下(或者家医场景下)，护士上门检测，实验室排序规则是确定的，整单>距离>成本
        if ((StringUtils.equals("nhHomeTest", jdhPromise.getVerticalCode())&&StringUtils.equals(ServiceTypeEnum.TEST.getServiceType(), jdhPromise.getServiceType()))
                || (StringUtils.equals("fhhHomeTest", jdhPromise.getVerticalCode())&&StringUtils.equals(ServiceTypeEnum.TEST.getServiceType(), jdhPromise.getServiceType()))
        ){
            //整单
            storeDispatchRules.add(storeDispatchRuleFactory.createDispatchRuleProcessor(StoreDispatchRuleFactory.createRouteKey(CommonConstant.DISPATCH_ROUTE_KEY, CommonConstant.ONE)));
            //距离
            storeDispatchRules.add(storeDispatchRuleFactory.createDispatchRuleProcessor(StoreDispatchRuleFactory.createRouteKey(CommonConstant.DISPATCH_ROUTE_KEY, CommonConstant.TWO)));
            //成本
            storeDispatchRules.add(storeDispatchRuleFactory.createDispatchRuleProcessor(StoreDispatchRuleFactory.createRouteKey(CommonConstant.DISPATCH_ROUTE_KEY, CommonConstant.THREE)));
        }else {
            //根据skuNo获取排序，非互医情况下，一个履约单只有一个sku
            String serviceId = medicalPromises.get(0).getServiceId();
            //获取排序规则
            JdhSku jdhSku = jdhSkuRepository.find(JdhSkuIdentifier.builder().skuId(Long.valueOf(serviceId)).build());
            log.info("MedicalPromiseApplicationImpl->getStoreDispatchRules,jdhSku={}",JsonUtil.toJSONString(jdhSku));
            if (Objects.isNull(jdhSku)){
                throw new BusinessException(MedPromiseErrorCode.DISPATCH_LOGIC_NULL);
            }
            String stationAssignType = jdhSku.getStationAssignType();
            if (StringUtils.isBlank(stationAssignType)){
                throw new BusinessException(MedPromiseErrorCode.DISPATCH_LOGIC_NULL);
            }
            List<Integer> stationAssignTypeList = JsonUtil.parseArray(stationAssignType, Integer.class);
            for (Integer type : stationAssignTypeList){
                storeDispatchRules.add(storeDispatchRuleFactory.createDispatchRuleProcessor(StoreDispatchRuleFactory.createRouteKey(CommonConstant.DISPATCH_ROUTE_KEY, type)));
            }
        }
        return storeDispatchRules;
    }


    /**
     * 根据检测项目获取实验室列表
     * @param medicalPromiseDispatchCmd
     * @param medicalPromises
     * @return
     */
    private List<JdhStationServiceItemRelContext> getJdhStationServiceItemRelContexts(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd, List<MedicalPromise> medicalPromises,Set<String> existStation) {
        JdhStationServiceItemRelRequest jdhStationServiceItemRelRequest = new JdhStationServiceItemRelRequest();
        //查询门店详情
        jdhStationServiceItemRelRequest.setQueryStationDetail(Boolean.TRUE);
        //非快检模式,不受城市限制,全国查询符合条件的实验室
        if(!BusinessModeEnum.SELF_TEST_TRANSPORT.getCode().equals(medicalPromiseDispatchCmd.getBusinessModeCode())){
            //是否只查本市的实验室，直辖市按一级地址下筛选
            if (duccConfig.getDispatchStationLimitCity()){
                Integer cityId = medicalPromiseDispatchCmd.getCityId();
                Integer provinceId = medicalPromiseDispatchCmd.getProvinceId();
                if (Objects.isNull(cityId) || Objects.isNull(provinceId)){
                    BaseAddressBo jdAddressFromAddress = addressRpc.getJDAddressFromAddress(medicalPromiseDispatchCmd.getStartAddress().trim());
                    cityId = jdAddressFromAddress.getCityCode();
                    provinceId = jdAddressFromAddress.getProvinceCode();
                }
                //直辖市按一级地址筛选
                if (duccConfig.getMunicipalFirstLevelSet().contains(provinceId)){
                    jdhStationServiceItemRelRequest.setProvinceId(provinceId);
                }else {
                    //其余按二级地址筛选
                    jdhStationServiceItemRelRequest.setCityId(cityId);
                }
            }
        }
        List<Long> serviceItemIds = medicalPromises.stream().map(p -> Long.valueOf(p.getServiceItemId())).distinct().collect(Collectors.toList());
        jdhStationServiceItemRelRequest.setServiceItemIds(serviceItemIds);
        jdhStationServiceItemRelRequest.setStationIdSet(existStation);
        //查询符合检测项目和命中实验室id的 门店项目信息列表
        List<JdhStationServiceItemRelDto> jdhStationServiceItemRelDtos = providerStoreApplication.queryStationServiceItemRelList(jdhStationServiceItemRelRequest);
        log.info("MedicalPromiseApplicationImpl->getJdhStationServiceItemRelContexts,jdhStationServiceItemRelDtos={}",JsonUtil.toJSONString(jdhStationServiceItemRelDtos));
        if (CollectionUtils.isEmpty(jdhStationServiceItemRelDtos)){
            return Lists.newArrayList();
        }

        //聚合,key:stationId,value:JdhStationServiceItemRelContext
        Map<String,JdhStationServiceItemRelContext> contextMap = Maps.newHashMap();
        for (JdhStationServiceItemRelDto jdhStationServiceItemRelDto : jdhStationServiceItemRelDtos) {
            JdhStationServiceItemRelContext jdhStationServiceItemRelContext = contextMap.getOrDefault(jdhStationServiceItemRelDto.getStationId(), MedicalPromiseConvert.INSTANCE.convertRel(jdhStationServiceItemRelDto));
            //计算距离
            if (Objects.isNull(jdhStationServiceItemRelContext.getDistance())){
                double distance = getDistance(medicalPromiseDispatchCmd, jdhStationServiceItemRelDto);
                jdhStationServiceItemRelContext.setDistance(new BigDecimal(distance));
            }
            JdhServiceItemContext jdhServiceItemContext = MedicalPromiseConvert.INSTANCE.convert(jdhStationServiceItemRelDto);
            if (CollectionUtils.isEmpty(jdhStationServiceItemRelContext.getJdhServiceItemContexts())){
                jdhStationServiceItemRelContext.setJdhServiceItemContexts(Lists.newArrayList(jdhServiceItemContext));
            }else {
                jdhStationServiceItemRelContext.getJdhServiceItemContexts().add(jdhServiceItemContext);
            }

            contextMap.put(jdhStationServiceItemRelDto.getStationId(), jdhStationServiceItemRelContext);
        }
        log.info("MedicalPromiseApplicationImpl->getJdhStationServiceItemRelContexts,contextMap={}",JsonUtil.toJSONString(contextMap));

        return Lists.newArrayList(contextMap.values());
    }

    /**
     * 暂时抽出来，统计qps
     * @param medicalPromiseDispatchCmd
     * @param jdhStationServiceItemRelDto
     * @return
     */
    @LogAndAlarm
    public  double getDistance(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd, JdhStationServiceItemRelDto jdhStationServiceItemRelDto) {

        String redisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.STATION_USER_DISTANCE_KEY, jdhStationServiceItemRelDto.getStationAddr(), medicalPromiseDispatchCmd.getStartAddress());
        String cacheValue = jimClient.get(redisKey);
        if (StringUtils.isNotBlank(cacheValue)){
            return Double.parseDouble(cacheValue);
        }
        try {
            DirectionResultBO directionResult = directionServiceRpc.getDirectionResult(DirectionRequestParam.builder()
                    .fromLocation(String.format("%s,%s", medicalPromiseDispatchCmd.getLatitude(), medicalPromiseDispatchCmd.getLongitude()))
                    .toLocation(String.format("%s,%s", jdhStationServiceItemRelDto.getStationLat(), jdhStationServiceItemRelDto.getStationLng()))
                    .travelMode(DirectionServiceRpc.TravelMode.BICYCLING).build());
            if (Objects.nonNull(directionResult)) {
                jimClient.setEx(redisKey,String.valueOf(directionResult.getDistance()),RedisKeyEnum.STATION_USER_DISTANCE_KEY.getExpireTime(),RedisKeyEnum.STATION_USER_DISTANCE_KEY.getExpireTimeUnit());
                return directionResult.getDistance();
            }
        }catch (Exception e){
            log.info("getDistance->getDirectionResult,error",e);
        }
        return PositionUtil.getDistance(Double.parseDouble(jdhStationServiceItemRelDto.getStationLng()), Double.parseDouble(jdhStationServiceItemRelDto.getStationLat()), medicalPromiseDispatchCmd.getLongitude().doubleValue(), medicalPromiseDispatchCmd.getLatitude().doubleValue());
    }


    /**
     * 获取context
     * @param medicalPromiseDispatchCmd
     * @param jdhPromise
     * @param medicalPromises
     * @param storeDispatchRules
     * @param jdhStationServiceItemRelContexts
     * @return
     */
    private  MedicalPromiseDispatchContext getMedicalPromiseDispatchContext(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd, PromiseDto jdhPromise, List<MedicalPromise> medicalPromises, List<StoreDispatchRule> storeDispatchRules, List<JdhStationServiceItemRelContext> jdhStationServiceItemRelContexts) {
        MedicalPromiseDispatchContext medicalPromiseDispatchContext = new MedicalPromiseDispatchContext();
        medicalPromiseDispatchContext.setMedicalPromises(medicalPromises);
        medicalPromiseDispatchContext.setStoreDispatchRules(storeDispatchRules);
        medicalPromiseDispatchContext.setPromiseId(medicalPromiseDispatchCmd.getPromiseId());
        medicalPromiseDispatchContext.setServiceType(jdhPromise.getServiceType());
        medicalPromiseDispatchContext.setVerticalCode(jdhPromise.getVerticalCode());
        medicalPromiseDispatchContext.setBusinessModeCode(medicalPromiseDispatchCmd.getBusinessModeCode());
        medicalPromiseDispatchContext.setAppointmentStartTime(jdhPromise.getAppointmentTime().getAppointmentStartTime());
        medicalPromiseDispatchContext.setAppointmentEndTime(jdhPromise.getAppointmentTime().getAppointmentEndTime());
        medicalPromiseDispatchContext.setJdhStationServiceItemRelContexts(jdhStationServiceItemRelContexts);
        medicalPromiseDispatchContext.setOrderId(Long.valueOf(jdhPromise.getSourceVoucherId()));

        medicalPromiseDispatchContext.setScheduleDay(medicalPromiseDispatchCmd.getScheduleDay());
        medicalPromiseDispatchContext.setBookTimeSpan(medicalPromiseDispatchCmd.getBookTimeSpan());
        medicalPromiseDispatchContext.setStartAddress(medicalPromiseDispatchCmd.getStartAddress());

        return medicalPromiseDispatchContext;
    }

    /**
     * 分发实验室
     * @param medicalPromises 医疗承诺列表
     * @param jdhStationServiceItemRelContexts 站点服务项关联上下文列表
     * @param maps 字符串映射列表
     * @throws NullPointerException 如果参数为null
     */
    private  void disPatchStation(List<MedicalPromise> medicalPromises, List<JdhStationServiceItemRelContext> jdhStationServiceItemRelContexts, List<Map<String, Set<String>>> maps,Map<String,List<SkuAngelStationDto>> stationToAngel,MedicalPromiseDispatchContext medicalPromiseDispatchContext) {
        //获取第一个(如果最佳只有一个，则选择最佳；如果最佳的有多个，选第一个)
        Map<String, Set<String>>stringListMap = maps.get(0);
        //key:stationId,value:JdhStationServiceItemRelContext
        Map<String, JdhStationServiceItemRelContext> stationIdToObj = jdhStationServiceItemRelContexts.stream().collect(Collectors.toMap(JdhStationServiceItemRelContext::getStationId, p -> p));
        Map<String,String> itemToStation = Maps.newHashMap();
        for (Map.Entry<String,Set<String>> entry : stringListMap.entrySet()){
            String stationId = entry.getKey();
            for (String item : entry.getValue()){
                itemToStation.putIfAbsent(item,stationId);
            }
        }
        Map<String, Map<String, String>> labMigration = duccConfig.getLabMigration();
        Map<String,String> stationIdToAngel = Maps.newHashMap();
        Date appointmentStartTime = medicalPromiseDispatchContext.getAppointmentStartTime();
        Date appointmentEndTime = medicalPromiseDispatchContext.getAppointmentEndTime();
        AtomicReference<String> targetStation = new AtomicReference<>("");

        for (MedicalPromise medicalPromise : medicalPromises){
            String stationId = itemToStation.get(medicalPromise.getServiceItemId());

            //自检测呼叫的是达达骑手，需要派服务站
            if (StringUtils.equals(medicalPromiseDispatchContext.getBusinessModeCode(),BusinessModeEnum.SELF_TEST.getCode())){
                //派服务站
                if (stationIdToAngel.containsKey(stationId)){
                    medicalPromise.setAngelStationId(stationIdToAngel.get(stationId));
                }else {
                    List<SkuAngelStationDto> skuAngelStationDtos = stationToAngel.get(stationId);
                    //骑手
                    if (StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(),medicalPromiseDispatchContext.getBusinessModeCode())){
                        skuAngelStationDtos.removeIf(p->!p.getHavePreemption() && Objects.isNull(p.getSkuInventoryDto()));
                        if (CollectionUtils.isEmpty(skuAngelStationDtos)){
                            throw new BusinessException(MedPromiseErrorCode.MEDICAL_REDUCE_INVENTORY);
                        }
                        skuAngelStationDtos.removeIf(p->!p.getHavePreemption() && (Objects.isNull(p.getSkuInventoryDto().getInventoryNum()) || p.getSkuInventoryDto().getInventoryNum()<=0));
                        if (CollectionUtils.isEmpty(skuAngelStationDtos)){
                            throw new BusinessException(MedPromiseErrorCode.MEDICAL_REDUCE_INVENTORY);
                        }
                    }

                    skuAngelStationDtos.sort(Comparator.comparing(p->p.getSkuInventoryDto().getInventoryNum()));
                    medicalPromise.setAngelStationId(String.valueOf(skuAngelStationDtos.get(skuAngelStationDtos.size()-1).getAngelStationId()));
                    stationIdToAngel.put(stationId,String.valueOf(skuAngelStationDtos.get(skuAngelStationDtos.size()-1).getAngelStationId()));
                }
            }


            if (labMigration.containsKey(medicalPromise.getStationId())){
                Map<String, String> stringStringMap = labMigration.get(medicalPromise.getStationId());
                stringStringMap.forEach((hours,sId)->{
                    String[] range = hours.split("~");
                    String startDateStr = range[0].trim();
                    String endDateStr = range[1].trim();

                    Date startDate = DateUtil.parse(startDateStr);
                    Date endDate = DateUtil.parse(endDateStr);
                    if (DateUtil.isIn(appointmentStartTime,startDate,endDate) && DateUtil.isIn(appointmentEndTime,startDate,endDate)){
                        targetStation.set(sId);
                    }
                });
            }
            String target = targetStation.get();
            //设置实验室具体信息
            packStationInfo(stationIdToObj, medicalPromise, stationId, target);

        }
        //如果需要占库存,此时是自检测，只会有一个实验室
        if (StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(),medicalPromiseDispatchContext.getBusinessModeCode())){
            String angelStationId = Lists.newArrayList(stationIdToAngel.values()).get(0);
            ReduceInventoryCmd reduceInventoryCmd = new ReduceInventoryCmd();
            reduceInventoryCmd.setAngelStationId(Long.valueOf(angelStationId));
            reduceInventoryCmd.setPin(medicalPromises.get(0).getUserPin());
            reduceInventoryCmd.setBusinessId(String.valueOf(medicalPromiseDispatchContext.getOrderId()));
            reduceInventoryCmd.setBusinessType(CommonConstant.ONE);
            reduceInventoryCmd.setScheduleDay(medicalPromiseDispatchContext.getScheduleDay());
            reduceInventoryCmd.setScheduleTime(medicalPromiseDispatchContext.getBookTimeSpan());
            reduceInventoryCmd.setInventoryChannelNo(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
            Boolean res = stationApplication.reduceInventory(reduceInventoryCmd);
            if (!res){
                throw new BusinessException(MedPromiseErrorCode.MEDICAL_REDUCE_INVENTORY);
            }
        }


        //异步记录派发方案
        executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
            log.info("MedicalPromiseApplicationImpl->maps={}",JsonUtil.toJSONString(maps));
            List<String> serviceItemId = medicalPromises.stream().map(MedicalPromise::getServiceItemId).collect(Collectors.toList());
            saveDispatchPlans(serviceItemId, maps, medicalPromiseDispatchContext, stationIdToObj);
        });

    }

    /**
     * 记录选中方案
     * @param serviceItemId
     * @param maps
     * @param medicalPromiseDispatchContext
     * @param stationIdToObj
     */
    private  void saveDispatchPlans(List<String> serviceItemId , List<Map<String, Set<String>>> maps, MedicalPromiseDispatchContext medicalPromiseDispatchContext, Map<String, JdhStationServiceItemRelContext> stationIdToObj) {
        List<StationDispatchPlanCmd> stationDispatchPlanCmds = Lists.newArrayList();

        log.info("MedicalPromiseApplicationImpl->saveDispatchPlans,medicalPromiseDispatchContext={}",JsonUtil.toJSONString(medicalPromiseDispatchContext));

        // 如果只有一个方案，则直接返回
        Map<String, Set<String>> selectPlan = maps.get(0);
        StationDispatchPlanCmd stationDispatchPlanCmd = new StationDispatchPlanCmd();
        stationDispatchPlanCmd.setPromiseId(medicalPromiseDispatchContext.getPromiseId());
        stationDispatchPlanCmd.setPlanId(1);
        stationDispatchPlanCmd.setSelected(CommonConstant.ONE);
        List<PlanDetailCmd> planDetailCmds = Lists.newArrayList();
        selectPlan.forEach((stationId,serviceitemSet)->{
            PlanDetailCmd planDetailCmd = new PlanDetailCmd();
            planDetailCmd.setStartAddress(medicalPromiseDispatchContext.getStartAddress());
            JdhStationServiceItemRelContext jdhStationServiceItemRelContext = stationIdToObj.get(stationId);
            planDetailCmd.setStationName(jdhStationServiceItemRelContext.getStationName());
            planDetailCmd.setStationAddress(jdhStationServiceItemRelContext.getStationAddr());
            //结算价
            //实验室可检测项目
            List<JdhServiceItemContext> jdhServiceItemContexts = jdhStationServiceItemRelContext.getJdhServiceItemContexts();
            Map<String, BigDecimal> serviceItemToSettle = jdhServiceItemContexts.stream().collect(Collectors.toMap(JdhServiceItemContext::getServiceItemId, JdhServiceItemContext::getSettlementPrice));
            BigDecimal settleAmount = new BigDecimal(CommonConstant.ZERO);
            for (String medicalPromise : serviceItemId) {
                if (serviceitemSet.contains(medicalPromise)){
                    settleAmount = settleAmount.add(serviceItemToSettle.get(medicalPromise));
                }
            }
            planDetailCmd.setSettlementAmount(settleAmount);
            //距离
            planDetailCmd.setDistance(jdhStationServiceItemRelContext.getDistance().setScale(2, RoundingMode.HALF_UP).toPlainString());
            planDetailCmd.setDetail(JsonUtil.toJSONString(serviceitemSet));
            planDetailCmds.add(planDetailCmd);

        });

        stationDispatchPlanCmd.setPlanDetailCmds(planDetailCmds);
        stationDispatchPlanCmds.add(stationDispatchPlanCmd);
        //保存方案
        medPromiseExtApplication.batchSaveDispatchPlan(stationDispatchPlanCmds);

    }


    /**
     * 检查调度参数
     * @param medicalPromiseDispatchCmd 医疗承诺调度命令
     * @throws MedPromiseErrorCode 当承诺ID参数为空时抛出承诺ID参数为空错误
     */
    private void checkDispatchParam(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd) {
        AssertUtils.nonNull(medicalPromiseDispatchCmd.getPromiseId(),MedPromiseErrorCode.PROMISE_ID_PARAM_NULL);
        if (Objects.isNull(medicalPromiseDispatchCmd.getLatitude()) || Objects.isNull(medicalPromiseDispatchCmd.getLongitude())){
            GisPointBo lngLatByAddress = addressRpc.getLngLatByAddress(medicalPromiseDispatchCmd.getStartAddress());
            medicalPromiseDispatchCmd.setLatitude(lngLatByAddress.getLatitude());
            medicalPromiseDispatchCmd.setLongitude(lngLatByAddress.getLongitude());
        }
    }

    /**
     * 获取有效的检测单
     * @param batchMedicalPromiseSubmitCmd
     * @return
     */
    private List<MedicalPromise> getValidMedicalPromises(BatchMedicalPromiseSubmitCmd batchMedicalPromiseSubmitCmd) {
        MedicalPromiseListQuery medicalPromiseListQuery = MedicalPromiseConvert.INSTANCE.convertSubmit(batchMedicalPromiseSubmitCmd);
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        //过滤不可提交状态的检测单
        Set<Integer> dispatchBlackStatus = MedicalPromiseStatusEnum.getDispatchBlackStatus();
        medicalPromises.removeIf(p->Objects.equals(CommonConstant.ONE,p.getFreeze()) || dispatchBlackStatus.contains(p.getStatus()));
        if (CollectionUtils.isEmpty(medicalPromises)){
            //没有可提交的检测单
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_SUBMIT_STATUS_LIST_NULL);
        }
        return medicalPromises;
    }

    /**
     * 组装报告信息
     * @param medicalPromiseReportCmd
     * @param medicalPromise
     * @return
     */
    private MedicalReportSaveCmd getMedicalReportSaveCmd(MedicalPromiseReportCmd medicalPromiseReportCmd, MedicalPromise medicalPromise) {
        MedicalReportSaveCmd medicalReportSaveCmd = new MedicalReportSaveCmd();
        medicalReportSaveCmd.setMedicalPromiseId(medicalPromiseReportCmd.getMedicalPromiseId());
        medicalReportSaveCmd.setUserPin(medicalPromise.getUserPin());
        medicalReportSaveCmd.setChannelNo(medicalPromiseReportCmd.getChannelType());
        medicalReportSaveCmd.setSourceOss(medicalPromiseReportCmd.getStructReportStr());
        medicalReportSaveCmd.setStructReportOss(medicalPromiseReportCmd.getJdStructReportStr());
        medicalReportSaveCmd.setReportOss(medicalPromiseReportCmd.getReportUrl());
        medicalReportSaveCmd.setPromiseId(medicalPromise.getPromiseId());
        if (StringUtil.isNotBlank(medicalPromiseReportCmd.getReportUrl()) && duccConfig.getPdfToJpgSwitch()){
            packReportImg(medicalPromiseReportCmd, medicalReportSaveCmd);
        }
        Date examinationTime = Objects.nonNull(medicalPromise.getCheckTime()) ? medicalPromise.getCheckTime() : new Date();
        medicalReportSaveCmd.setExaminationTime(DateUtil.format(examinationTime,CommonConstant.YMD));
        if (Objects.isNull(medicalPromise.getReportTime())){
            Date reportTime = Objects.nonNull(medicalPromiseReportCmd.getReportTime()) ? medicalPromiseReportCmd.getReportTime() : new Date();
            medicalReportSaveCmd.setReportTime(reportTime);
        }
        com.jdh.o2oservice.export.report.query.MedicalPromiseRequest medicalPromiseRequest = new com.jdh.o2oservice.export.report.query.MedicalPromiseRequest();
        medicalPromiseRequest.setMedicalPromiseId(medicalPromiseReportCmd.getMedicalPromiseId());
        MedicalReportDTO byMedicalPromiseId = medicalReportApplication.getByMedicalPromiseId(medicalPromiseRequest);
        if (Objects.isNull(byMedicalPromiseId)){
            PromiseDto byPromiseId = getPromiseDto(medicalPromise);
            List<PromisePatientDto> patients = byPromiseId.getPatients();
            PromisePatientDto promisePatientDto = patients.stream().filter(p -> Objects.equals(medicalPromise.getPromisePatientId(), p.getPromisePatientId())).findFirst().orElse(null);
            if (Objects.nonNull(promisePatientDto)){
                medicalReportSaveCmd.setPatientId(promisePatientDto.getPatientId());
                medicalReportSaveCmd.setPatientName(Objects.nonNull(promisePatientDto.getUserName())?promisePatientDto.getUserName().getName():null);
            }
        }else {
            medicalReportSaveCmd.setReportCenterId(byMedicalPromiseId.getReportCenterId());
        }
        medicalReportSaveCmd.setMedicalType(Objects.nonNull(medicalPromiseReportCmd.getMedicalType()) ? medicalPromiseReportCmd.getMedicalType() : CommonConstant.ONE);
        medicalReportSaveCmd.setReportType(medicalPromiseReportCmd.getReportType());
        medicalReportSaveCmd.setReportStatus(Objects.nonNull(medicalPromiseReportCmd.getReportStatus()) ? medicalPromiseReportCmd.getReportStatus() : CommonConstant.ONE);
        medicalReportSaveCmd.setManufacturerNumber(medicalPromiseReportCmd.getManufacturerNumber());
        medicalReportSaveCmd.setSnCode(medicalPromiseReportCmd.getSnCode());

        //设置FileMd5
        ObjectMetadata objectMetadata = fileManageService.getObjectMetadata(medicalReportSaveCmd.getReportOss());
        if(log.isInfoEnabled()) {
            log.info("syncMedicalReportToReportCenter,objectMetadata = {} ", JSON.toJSONString(objectMetadata));
        }

        medicalReportSaveCmd.setFileMd5(objectMetadata.getETag());
        medicalReportSaveCmd.setServiceItemName(medicalPromise.getServiceItemName());
        medicalReportSaveCmd.setServiceItemId(medicalPromise.getServiceItemId());
        return medicalReportSaveCmd;
    }

    /**
     *
     * @param medicalPromise
     * @return
     */
    private PromiseDto getPromiseDto(MedicalPromise medicalPromise) {
        PromiseIdRequest promiseIdRequest = new PromiseIdRequest();
        promiseIdRequest.setPromiseId(medicalPromise.getPromiseId());
        return promiseApplication.findByPromiseId(promiseIdRequest);
    }


    /**
     *
     * @param medicalPromise
     * @return
     */
    private static MedicalPromiseReportContext getMedicalPromiseReportContext(MedicalPromise medicalPromise) {
        log.error("[MedicalPromiseApplicationImpl.getMedicalPromiseReportContext],medicalPromise={}",JsonUtil.toJSONString(medicalPromise));
        MedicalPromiseReportContext medicalPromiseReportContext = new MedicalPromiseReportContext();
        medicalPromiseReportContext.setVerticalCode(medicalPromise.getVerticalCode());
        medicalPromiseReportContext.setServiceType(medicalPromise.getServiceType());
        medicalPromiseReportContext.setMedicalPromiseId(medicalPromise.getMedicalPromiseId());
        medicalPromiseReportContext.init(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_GENERATE_REPORT);
        return medicalPromiseReportContext;
    }

    /**
     * 下载结构化报告字符串
     *
     * @param jssUrl
     * @return
     */
    private String getStructReportStr(String jssUrl) {
        if (StringUtils.isBlank(jssUrl)) {
            return null;
        }
        log.info("MedicalReportApplicationImpl.getStructReportStr -> jssUrl={}", jssUrl);
        InputStream inputStream = fileManageService.get(jssUrl);
        String result = new BufferedReader(new InputStreamReader(inputStream))
                .lines().collect(Collectors.joining("\n"));
        log.info("MedicalReportApplicationImpl.getStructReportStr -> result={}", result);
        return result;
    }


    /**
     * 组装报告信息
     * @param medicalPromiseReportCmd
     * @return
     */
    private StructQuickReportContentDTO getStructQuickReportContentDTO(MedicalPromiseReportCmd medicalPromiseReportCmd) {
        StructQuickReportContentDTO structQuickReportContentDTO = JsonUtil.parseObject(medicalPromiseReportCmd.getJdStructReportStr(), StructQuickReportContentDTO.class);

        List<StructQuickReportResultDTO> reportResultList = structQuickReportContentDTO.getReportResult();
        Set<String> indicatorName = Sets.newHashSet();
        Map<String, String> indicatorNameMap = duccConfig.getIndicatorNameMap();
        Set<String> oldKnightSkuIndicatorValue = duccConfig.getOldKnightSkuIndicatorValue();
        Map<String, String> indicatorAbnoramlDesc = duccConfig.getIndicatorAbnoramlDesc();
        Map<String, ReportNormalRangeStationConfig> reportNormalRangeStationConfigMaps = duccConfig.getReportNormalRangeStationConfig();
        for (StructQuickReportResultDTO structQuickReportResultDTO : reportResultList) {
            List<StructQuickReportResultIndicatorDTO> indicators = structQuickReportResultDTO.getIndicators();
            for (StructQuickReportResultIndicatorDTO indicatorDTO : indicators) {
                // 必须先保存ct值
                setCtValue(indicatorDTO);
                indicatorDTO.setReferenceRangeValue(indicatorDTO.getNormalRangeValue());
                String mappedIndicatorName = indicatorNameMap.get(indicatorDTO.getIndicatorName());
                indicatorName.add(StringUtils.isNotBlank(mappedIndicatorName) ? mappedIndicatorName : indicatorDTO.getIndicatorName());
                indicatorDTO.setIndicatorName(StringUtils.isNotBlank(mappedIndicatorName) ? mappedIndicatorName : indicatorDTO.getIndicatorName());
                if (oldKnightSkuIndicatorValue.contains(indicatorDTO.getIndicatorName())){
                    String s = indicatorAbnoramlDesc.get(indicatorDTO.getAbnormalType());
                    indicatorDTO.setValue(StringUtils.isNotBlank(s) ? s : indicatorDTO.getValue());
                    indicatorDTO.setUnit(null);
                    indicatorDTO.setNormalRangeValue(StringUtils.isNotBlank(s) ? "阴性" : indicatorDTO.getNormalRangeValue());
                }
                // 实验室维度兜底正常范围，逻辑中使用了标准名称获取配置，逻辑位置调整需要注意
                setNormalRangeValue(medicalPromiseReportCmd, indicatorDTO, reportNormalRangeStationConfigMaps);
            }
        }

        //查询指标列表
        //目前体检报告没有结构化报告，默认查询指标类型为检测类
        log.info("MedicalPromiseApplicationImpl->getStructQuickReportContentDTO,indicatorName={}",JsonUtil.toJSONString(indicatorName));
        List<ServiceIndicatorDto> serviceIndicatorDtos = productServiceIndicatorApplication.queryIndicatorExactList(JdhIndicatorExactQuery.builder().indicatorName(indicatorName).indicatorType(CommonConstant.TWO).build());
        log.info("MedicalPromiseApplicationImpl->getStructQuickReportContentDTO,serviceIndicatorDtos={}",JsonUtil.toJSONString(serviceIndicatorDtos));

        if (CollectionUtils.isNotEmpty(serviceIndicatorDtos)){
            Map<String, ServiceIndicatorDto> indicatorNameToObj = serviceIndicatorDtos.stream()
                    .collect(Collectors.toMap(
                            ServiceIndicatorDto::getIndicatorName,
                            Function.identity(),
                            (existing, replacement) -> existing));
            List<StructQuickReportResultDTO> reportResult = structQuickReportContentDTO.getReportResult();
            for (StructQuickReportResultDTO structQuickReportResultDTO : reportResult){
                if (CollectionUtils.isNotEmpty(structQuickReportResultDTO.getIndicators())){
                    for (StructQuickReportResultIndicatorDTO quickReportResultIndicatorDTO : structQuickReportResultDTO.getIndicators()){
                        ServiceIndicatorDto serviceIndicatorDto = indicatorNameToObj.get(quickReportResultIndicatorDTO.getIndicatorName());
                        if (Objects.nonNull(serviceIndicatorDto)){
                            quickReportResultIndicatorDTO.setIndicatorNo(serviceIndicatorDto.getIndicatorId());
                            quickReportResultIndicatorDTO.setTags(serviceIndicatorDto.getTags());
                        }else {
                            quickReportResultIndicatorDTO.setIndicatorNo(null);
                        }
                        // 通过ct值计算浓度，必传参数实验室id+指标id
                        calculateIndicatorConcentration(medicalPromiseReportCmd, quickReportResultIndicatorDTO);
                    }
                }
            }
        }
        return structQuickReportContentDTO;
    }

    /**
     * 发送以人纬度作废/冻结事件
     * @param beforeMedicalPromises
     * @param medicalPromises
     */
    private void publishPpidInvalidFreezeEvent(List<MedicalPromise> beforeMedicalPromises, List<MedicalPromise> medicalPromises, MedicalPromiseStatusEnum medicalPromiseStatusEnum) {
        //作废/冻结前以人分堆
        Map<Long, List<MedicalPromise>> beforePpidToObject = beforeMedicalPromises.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));
        //作废/冻结后以人分堆
        Map<Long, List<MedicalPromise>> afterPpidToObject = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));
        beforePpidToObject.forEach((ppid,beforeMedPromises)->{
            List<MedicalPromise> afterMedPromises = afterPpidToObject.get(ppid);
            //作废/冻结前是否有没作废/冻结的,过滤没作废/冻结的
            MedicalPromise beforeNotInvalid = beforeMedPromises.stream().filter(p -> !Objects.equals(medicalPromiseStatusEnum.getStatus(), p.getStatus())).findFirst().orElse(null);
            MedicalPromise afterNotInvalid = afterMedPromises.stream().filter(p -> !Objects.equals(medicalPromiseStatusEnum.getStatus(), p.getStatus())).findFirst().orElse(null);
            //如果之前有没作废/冻结的，之后都作废/冻结了，发送人纬度作废/冻结事件
            if (Objects.nonNull(beforeNotInvalid) && Objects.isNull(afterNotInvalid)){
                //发送人纬度作废/冻结事件
                eventCoordinator.publish(EventFactory.newDefaultEvent(beforeNotInvalid, MedPromiseEventTypeEnum.MED_PROMISE_PATIENT_INVALID,
                        MedicalPromiseEventBody.builder().promisePatientId(ppid).promiseId(beforeNotInvalid.getPromiseId()).build()));
            }
        });
    }

    /**
     * 发送以人纬度冻结事件
     * @param beforeMedicalPromises
     * @param medicalPromises
     */
    private void publishPpidFreezeEvent(List<MedicalPromise> beforeMedicalPromises, List<MedicalPromise> medicalPromises) {
        //冻结前以人分堆
        Map<Long, List<MedicalPromise>> beforePpidToObject = beforeMedicalPromises.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));
        //冻结后以人分堆
        Map<Long, List<MedicalPromise>> afterPpidToObject = medicalPromises.stream().collect(Collectors.groupingBy(MedicalPromise::getPromisePatientId));
        beforePpidToObject.forEach((ppid,beforeMedPromises)->{
            List<MedicalPromise> afterMedPromises = afterPpidToObject.get(ppid);
            //冻结前是否有没冻结的,过滤没冻结的
            MedicalPromise beforeNotInvalid = beforeMedPromises.stream().filter(p -> !Objects.equals(CommonConstant.ONE, p.getFreeze())).findFirst().orElse(null);
            MedicalPromise afterNotInvalid = afterMedPromises.stream().filter(p -> !Objects.equals(CommonConstant.ONE, p.getFreeze())).findFirst().orElse(null);
            //如果之前有没冻结的，之后都冻结了，发送人纬度冻结事件
            if (Objects.nonNull(beforeNotInvalid) && Objects.isNull(afterNotInvalid)){
                //发送人纬度作废/冻结事件
                eventCoordinator.publish(EventFactory.newDefaultEvent(beforeNotInvalid, MedPromiseEventTypeEnum.MED_PROMISE_PATIENT_FREEZE,
                        MedicalPromiseEventBody.builder().promisePatientId(ppid).promiseId(beforeNotInvalid.getPromiseId()).build()));
            }
        });
    }

    /**
     * 发送人+sku纬度作废事件
     * @param beforeMedicalPromises
     * @param medicalPromises
     */
    private void publishPpidServiceInvalidEvent(List<MedicalPromise> beforeMedicalPromises, List<MedicalPromise> medicalPromises) {
        Map<String, List<MedicalPromise>> ppidServiceToListBefore = beforeMedicalPromises.stream().collect(Collectors.groupingBy(p -> p.getPromisePatientId() + "_" + p.getServiceId()));
        Map<String, List<MedicalPromise>> ppidServiceToListAfter = medicalPromises.stream().collect(Collectors.groupingBy(p -> p.getPromisePatientId() + "_" + p.getServiceId()));
        ppidServiceToListBefore.forEach((ppidService,before)->{
            List<MedicalPromise> after = ppidServiceToListAfter.get(ppidService);
            //如果作废前未作废，作废后已作废，则发送作废事件
            MedicalPromise notInvalidBefore = before.stream().filter(p -> !Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(), p.getStatus())).findFirst().orElse(null);
            MedicalPromise notInvalidAfter= after.stream().filter(p -> !Objects.equals(MedicalPromiseStatusEnum.INVALID.getStatus(), p.getStatus())).findFirst().orElse(null);
            after.sort(Comparator.comparing(MedicalPromise::getMedicalPromiseId));
            if (Objects.nonNull(notInvalidBefore) && Objects.isNull(notInvalidAfter)){
                MedicalPromise aggregate = after.get(0);
                eventCoordinator.publish(EventFactory.newDefaultEvent(aggregate, MedPromiseEventTypeEnum.MED_PROMISE_SKU_PATIENT_INVALID,
                        MedicalPromiseEventBody.builder()
                                .medicalPromiseId(aggregate.getMedicalPromiseId())
                                .status(aggregate.getStatus())
                                .verticalCode(aggregate.getVerticalCode())
                                .serviceType(aggregate.getServiceType())
                                .promisePatientId(aggregate.getPromisePatientId())
                                .serviceId(aggregate.getServiceId())
                                .promiseId(aggregate.getPromiseId())
                                .appointmentId(aggregate.getPromisePatientId()+"_"+aggregate.getServiceId())
                                .build()
                ));
            }
        });
    }

    /**
     * 发送人纬度全部报告已出
     *
     * @param medicalPromise
     * @param medicalPromiseReportContext
     */
    private void sendPpidReport(MedicalPromise medicalPromise, MedicalPromiseReportContext medicalPromiseReportContext) {
        log.info("MedicalPromiseApplicationImpl->sendPpidReport,medicalPromise={}",JsonUtil.toJSONString(medicalPromise));
        log.info("MedicalPromiseApplicationImpl->sendPpidReport,medicalPromiseReportContext={}",JsonUtil.toJSONString(medicalPromiseReportContext));
        MedicalPromiseListQuery query = new MedicalPromiseListQuery();
        query.setPromiseId(medicalPromise.getPromiseId());
        query.setInvalid(Boolean.TRUE);
        query.setPromisePatientIdList(Lists.newArrayList(medicalPromise.getPromisePatientId()));
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(query);
        log.info("MedicalPromiseApplicationImpl->sendPpidReport,medicalPromises={}",JsonUtil.toJSONString(medicalPromises));
        if (Objects.equals(medicalPromiseReportContext.getSnapshot().getReportStatus(),CommonConstant.ZERO)){
            //过滤没有出报告的
            MedicalPromise notReport = medicalPromises.stream().filter(p -> Objects.equals(p.getReportStatus(), CommonConstant.ZERO)).findFirst().orElse(null);
            medicalPromises.sort(Comparator.comparing(MedicalPromise::getMedicalPromiseId));
            MedicalPromise first = medicalPromises.get(0);
            if (Objects.isNull(notReport)){
                MedicalPromiseEventBody eventBody = MedicalPromiseConvert.INSTANCE.medicalPromise2MedicalPromiseEventBody(first);
                eventBody.setAppointmentId(String.valueOf(first.getPromisePatientId()));
                eventBody.setSourceVoucherId(promiseApplication.findSourceVoucherIdByPromiseId(first.getPromiseId()));
                eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_PATIENT_ALL_GENERATE_REPORT,eventBody));
            }
        }
    }

    /**
     * 人+sku纬度已出报告
     * @param medicalPromise
     * @param medicalPromiseReportContext
     */
    private void sendPpidServiceReport(MedicalPromise medicalPromise, MedicalPromiseReportContext medicalPromiseReportContext) {
        log.info("MedicalPromiseApplicationImpl->sendPpidServiceReport,medicalPromise={}",JsonUtil.toJSONString(medicalPromise));
        log.info("MedicalPromiseApplicationImpl->sendPpidServiceReport,medicalPromiseReportContext={}",JsonUtil.toJSONString(medicalPromiseReportContext));
        MedicalPromiseListQuery query = new MedicalPromiseListQuery();
        query.setPromiseId(medicalPromise.getPromiseId());
        query.setInvalid(Boolean.TRUE);
        query.setPromisePatientIdList(Lists.newArrayList(medicalPromise.getPromisePatientId()));
        query.setServiceId(medicalPromise.getServiceId());
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(query);
        log.info("MedicalPromiseApplicationImpl->sendPpidServiceReport,medicalPromises={}",JsonUtil.toJSONString(medicalPromises));
        if (Objects.equals(medicalPromiseReportContext.getSnapshot().getReportStatus(),CommonConstant.ZERO)){
            //过滤没有出报告的
            MedicalPromise notReport = medicalPromises.stream().filter(p -> Objects.equals(p.getReportStatus(), CommonConstant.ZERO)).findFirst().orElse(null);
            medicalPromises.sort(Comparator.comparing(MedicalPromise::getMedicalPromiseId));
            MedicalPromise first = medicalPromises.get(0);
            if (Objects.isNull(notReport)){
                MedicalPromiseEventBody eventBody = MedicalPromiseConvert.INSTANCE.medicalPromise2MedicalPromiseEventBody(first);
                eventBody.setAppointmentId(first.getPromisePatientId()+"_"+first.getServiceId());
                eventBody.setSourceVoucherId(promiseApplication.findSourceVoucherIdByPromiseId(first.getPromiseId()));
                eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_SKU_PATIENT_ALL_GENERATE_REPORT,eventBody));
            }
        }
    }

    /**
     * 首次出报告
     * @param medicalPromise
     * @param promise
     */
    private void sendFirstGenerateReport(MedicalPromise medicalPromise, JdhPromise promise) {
        List<JdhPromiseExtend> promiseExtends = promise.getPromiseExtends();
        if (CollectionUtils.isNotEmpty(promiseExtends)){
            JdhPromiseExtend jdhPromiseExtend = promiseExtends.stream().filter(p -> StringUtils.equals(PromiseExtendKeyEnum.FIRST_GENERATE_REPORT.getFiledKey(), p.getAttribute())).findFirst().orElse(null);
            if (Objects.isNull(jdhPromiseExtend)){
                promise.refreshExtend(PromiseExtendKeyEnum.FIRST_GENERATE_REPORT,CommonConstant.ONE_STR);
                promiseRepository.save(promise);
                eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_FIRST_GENERATE_REPORT,
                        MedicalPromiseAggregateEventBody.builder().promiseId(medicalPromise.getPromiseId()).build()));
            }
        }else {
            promise.refreshExtend(PromiseExtendKeyEnum.FIRST_GENERATE_REPORT,CommonConstant.ONE_STR);
            promiseRepository.save(promise);
            eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_FIRST_GENERATE_REPORT,
                    MedicalPromiseAggregateEventBody.builder().promiseId(medicalPromise.getPromiseId()).build()));
        }
    }

    /**
     * 组装项目详情
     * @param medicalPromiseListRequest
     * @param medicalPromises
     * @param res
     */
    private void packServiceItem(MedicalPromiseListRequest medicalPromiseListRequest, List<MedicalPromise> medicalPromises, List<MedicalPromiseDTO> res) {
        if (CollectionUtils.isNotEmpty(res) && Boolean.TRUE.equals(medicalPromiseListRequest.getItemDetail())){
            List<Long> itemList = medicalPromises.stream().map(MedicalPromise::getServiceItemId).filter(Objects::nonNull).map(Long::parseLong).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemList)){
                return;
            }
            List<JdhStationServiceItemRelDto> jdhStationServiceItemRelDtos = providerStoreApplication.queryStationServiceItemRelList(JdhStationServiceItemRelRequest.builder().serviceItemIds(itemList).build());
            if (CollectionUtils.isEmpty(jdhStationServiceItemRelDtos)){
                return;
            }
            Map<Long, List<JdhStationServiceItemRelDto>> itemToStation = jdhStationServiceItemRelDtos.stream().collect(Collectors.groupingBy(JdhStationServiceItemRelDto::getServiceItemId));
            for (MedicalPromiseDTO medicalPromise : res){
                JdhStationServiceItemRelDto jdhStationServiceItemRelDto = null;
                if (StringUtils.isNotBlank(medicalPromise.getServiceItemId()) && itemToStation.containsKey(Long.valueOf(medicalPromise.getServiceItemId()))){
                    if (StringUtils.isNotBlank(medicalPromise.getStationId())){
                        jdhStationServiceItemRelDto = itemToStation.get(Long.valueOf(medicalPromise.getServiceItemId())).stream().filter(p -> StringUtils.equals(p.getStationId(), medicalPromise.getStationId())).findFirst().orElse(null);
                    }
                }
                if (Objects.nonNull(jdhStationServiceItemRelDto)){
                    medicalPromise.setTestDuration(jdhStationServiceItemRelDto.getTestDuration());
                    medicalPromise.setSamplingWay(jdhStationServiceItemRelDto.getSamplingWay());
                    medicalPromise.setSampleType(jdhStationServiceItemRelDto.getSampleType());
                }
            }
        }
    }

    /**
     * 组装受检测人详情
     * @param medicalPromiseListRequest
     * @param res
     */
    private void packPatientInfo(MedicalPromiseListRequest medicalPromiseListRequest, List<MedicalPromiseDTO> res) {
        if (CollectionUtils.isNotEmpty(res) && Boolean.TRUE.equals(medicalPromiseListRequest.getPatientDetail())){
            Set<Long> ppidList = res.stream().map(MedicalPromiseDTO::getPromisePatientId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<Long> voucherIdList = res.stream().map(MedicalPromiseDTO::getVoucherId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ppidList)){

                List<PromiseDto> promiseList = promiseApplication.findByPromiseList(PromiseListRequest.builder().voucherIds(voucherIdList).build());
                Map<Long, PromiseDto> promiseDtoMap = promiseList.stream().collect(Collectors.toMap(PromiseDto::getPromiseId, Function.identity(), (o, n) -> o));

                List<PromisePatientDto> promisePatientDtos = new ArrayList<>();
                promiseList.forEach(ele -> promisePatientDtos.addAll(ele.getPatients()));
                Map<Long, PromisePatientDto> ppidToPatient = promisePatientDtos.stream().collect(Collectors.toMap(PromisePatientDto::getPromisePatientId, p -> p));
                for (MedicalPromiseDTO medicalPromiseDTO : res){
                    if (ppidToPatient.containsKey(medicalPromiseDTO.getPromisePatientId())){
                        PromisePatientDto promisePatientDto = ppidToPatient.get(medicalPromiseDTO.getPromisePatientId());
                        medicalPromiseDTO.setName(Objects.nonNull(promisePatientDto.getUserName()) ? promisePatientDto.getUserName().getName() : null);
                        medicalPromiseDTO.setPhone(Objects.nonNull(promisePatientDto.getPhoneNumber()) ? promisePatientDto.getPhoneNumber().getPhone() : null);
                        medicalPromiseDTO.setMarriage(promisePatientDto.getMarriage());
                        medicalPromiseDTO.setPatientId(promisePatientDto.getPatientId());
                        medicalPromiseDTO.setAge(Objects.nonNull(promisePatientDto.getBirthday()) ? promisePatientDto.getBirthday().getAge() : null);
                        medicalPromiseDTO.setGender(promisePatientDto.getGender());
                        try {
                            PromiseDto promiseDto = promiseDtoMap.get(promisePatientDto.getPromiseId());
                            PromiseStationDto store = promiseDto.getStore();
                            medicalPromiseDTO.setAppointmentAddress(Objects.nonNull(store) ? store.getStoreAddr() : null);
                        }catch (Exception e){
                            // ignore
                        }
                    }
                }
            }
        }
    }

    /**
     *
     * @param medicalPromiseRequest
     * @param res
     */
    private  void packServiceItem(MedicalPromiseRequest medicalPromiseRequest,MedicalPromiseDTO res){
        log.info("MedicalPromiseApplicationImpl->packServiceItem,medicalPromiseRequest={}",JsonUtil.toJSONString(medicalPromiseRequest));
        if (Objects.nonNull(res) && Boolean.TRUE.equals(medicalPromiseRequest.getItemDetail())){
            if (StringUtils.isBlank(res.getServiceItemId())){
                return;
            }
            List<JdhStationServiceItemRelDto> jdhStationServiceItemRelDtos = providerStoreApplication.queryStationServiceItemRelList(JdhStationServiceItemRelRequest.builder().serviceItemId(Long.valueOf(res.getServiceItemId())).build());
            if (CollectionUtils.isEmpty(jdhStationServiceItemRelDtos)){
                return;
            }
            JdhStationServiceItemRelDto jdhStationServiceItemRelDto = jdhStationServiceItemRelDtos.stream().filter(p -> StringUtils.equals(p.getStationId(), res.getStationId())).findFirst().orElse(null);
            if (Objects.nonNull(jdhStationServiceItemRelDto)){
                DictRequest dictRequest = new DictRequest();
                dictRequest.setDictGroups(Sets.newHashSet("sampleType"));
                Map<String, List<DictInfoDto>> stringListMap = dictApplication.queryMultiDictList(dictRequest);
                if (MapUtil.isNotEmpty(stringListMap) && CollectionUtils.isNotEmpty(stringListMap.get("sampleType"))){
                    List<DictInfoDto> sampleType = stringListMap.get("sampleType");
                    DictInfoDto dictInfoDto = sampleType.stream().filter(p -> Objects.equals(p.getValue(), jdhStationServiceItemRelDto.getSampleType())).findFirst().orElse(null);
                    if (Objects.nonNull(dictInfoDto)){
                        res.setSamplingWay(dictInfoDto.getLabel());
                    }
                }

                res.setTestDuration(jdhStationServiceItemRelDto.getTestDuration());
                res.setSampleType(jdhStationServiceItemRelDto.getSampleType());
            }

        }
    }

    /**
     * 组装患者信息
     * @param medicalPromiseRequest
     * @param medicalPromise
     * @param res
     */
    private void packPatientInfo(MedicalPromiseRequest medicalPromiseRequest, MedicalPromise medicalPromise, MedicalPromiseDTO res) {
        if (Objects.nonNull(medicalPromiseRequest) && Boolean.TRUE.equals(medicalPromiseRequest.getPatientDetail())){
            PromiseDto byPromiseId = promiseApplication.findByPromiseId(PromiseIdRequest.builder().promiseId(medicalPromise.getPromiseId()).build());
            if (Objects.nonNull(byPromiseId) ){
                if (Objects.nonNull(byPromiseId.getAppointmentTime())){
                    res.setAppointmentStartTime(byPromiseId.getAppointmentTime().getAppointmentStartTime());
                    res.setAppointmentEndTime(byPromiseId.getAppointmentTime().getAppointmentEndTime());
                }
                if (CollectionUtils.isNotEmpty(byPromiseId.getPatients())){
                    List<PromisePatientDto> patients = byPromiseId.getPatients();
                    PromisePatientDto promisePatientDto = patients.stream().filter(p -> Objects.equals(p.getPromisePatientId(), medicalPromise.getPromisePatientId())).findFirst().orElse(null);
                    if (Objects.nonNull(promisePatientDto)){
                        res.setName(Objects.nonNull(promisePatientDto.getUserName()) ? promisePatientDto.getUserName().getName() : null);
                        res.setPhone(Objects.nonNull(promisePatientDto.getPhoneNumber()) ? promisePatientDto.getPhoneNumber().getPhone() : null);
                        res.setMarriage(promisePatientDto.getMarriage());
                        res.setGender(promisePatientDto.getGender());
                        res.setPatientId(promisePatientDto.getPatientId());
                        res.setAge(Objects.nonNull(promisePatientDto.getBirthday()) ? promisePatientDto.getBirthday().getAge() : null);
                        res.setAppointmentAddress(Objects.nonNull(byPromiseId.getStore()) ? byPromiseId.getStore().getStoreAddr() : null);
                    }
                }
            }

        }
    }

    private void packEtaInfo(MedicalPromiseRequest medicalPromiseRequest,MedicalPromise medicalPromise,MedicalPromiseDTO res ){
        if (!Boolean.TRUE.equals(medicalPromiseRequest.getEtaDetail()) || !verifyQuickCheck(medicalPromise)){
            return;
        }
        AngelWork angelWork = providerPromiseEventSubscriber.getAngelWork(null, medicalPromise.getPromiseId());
        for (QuickCheckStatusMappingConfig statusMappingConfig : duccConfig.getQuickCheckStatusMappingConfig()) {
            Map<String, Object> expParam = providerPromiseEventSubscriber.buildExpParam(angelWork, medicalPromise, null);
            if ((Boolean) AviatorEvaluator.compile(statusMappingConfig.getStatusExpression(), Boolean.TRUE).execute(expParam)){
                QuickCheckPushInfoBO pushInfoBO = providerPromiseEventSubscriber.buildQuickCheckPushInfoBO(medicalPromise, angelWork, statusMappingConfig,null);
                MedPromiseEtaDTO medPromiseEtaDTO = new MedPromiseEtaDTO();
                medPromiseEtaDTO.setDeliverEtaTime(pushInfoBO.getDeliverEtaTime());
                medPromiseEtaDTO.setStatus(statusMappingConfig.getStatus());
                medPromiseEtaDTO.setDeliverType(pushInfoBO.getDeliverType());
                medPromiseEtaDTO.setPickUpCode(pushInfoBO.getPickUpCode());
                medPromiseEtaDTO.setSenderName(pushInfoBO.getSenderName());
                medPromiseEtaDTO.setRequireCollectSampleTime(pushInfoBO.getRequireCollectSampleTime());
                medPromiseEtaDTO.setRequirePushReportTime(pushInfoBO.getRequirePushReportTime());
                medPromiseEtaDTO.setSenderPhone(pushInfoBO.getSenderPhone());
                res.setMedPromiseEtaDTO(medPromiseEtaDTO);
                break;
            }
        }

    }

    /**
     * 获取用户地址命中服务站对应的实验室列表
     * @param medicalPromiseDispatchCmd
     * @return
     */
    private Set<String> getExistStationSet(MedicalPromiseDispatchCmd medicalPromiseDispatchCmd,Map<String,List<SkuAngelStationDto>> skuAngelStation) {
        log.info("MedicalPromiseApplicationImpl->getExistStationSet,medicalPromiseDispatchCmd={}", JsonUtil.toJSONString(medicalPromiseDispatchCmd));
        //护士不需要判断京东的服务站
        Set<String> dispatchStationWithNoAngelStation = duccConfig.getDispatchStationWithNoAngelStation();
        if (dispatchStationWithNoAngelStation.contains(medicalPromiseDispatchCmd.getBusinessModeCode())){
            return Sets.newHashSet();
        }
        //骑手上门要校验库存、扣减库存，以父单为纬度

        AddressDetail addressDetail = new AddressDetail();
        addressDetail.setFullAddress(medicalPromiseDispatchCmd.getStartAddress());
        addressDetail.setAddressId(StringUtils.isBlank(medicalPromiseDispatchCmd.getAddressId()) ? String.valueOf(medicalPromiseDispatchCmd.getPromiseId()) : medicalPromiseDispatchCmd.getAddressId());
        QuerySkuAngelStationRequest querySkuAngelStationRequest = new QuerySkuAngelStationRequest();
        querySkuAngelStationRequest.setAngelType(AngelTypeEnum.DELIVERY.getType());
        querySkuAngelStationRequest.setModeType(AngelStationModeTypeEnum.FULL_TIME.getCode());
        querySkuAngelStationRequest.setScheduleDay(medicalPromiseDispatchCmd.getScheduleDay());
        querySkuAngelStationRequest.setBookTimeSpan(medicalPromiseDispatchCmd.getBookTimeSpan());
        querySkuAngelStationRequest.setBusinessId(String.valueOf(medicalPromiseDispatchCmd.getOrderId()));
        querySkuAngelStationRequest.setBusinessType(CommonConstant.ONE);
        querySkuAngelStationRequest.setAddressList(Lists.newArrayList(addressDetail));
        querySkuAngelStationRequest.setSkuNos(medicalPromiseDispatchCmd.getServiceIdSet());
        querySkuAngelStationRequest.setInventoryChannelNo(InventoryChannelEnum.RIDER_CHANNEL.getInventoryChannel());
        SkuAngelStationResultDto skuAngelStationResultDto = stationApplication.querySkuAngelStations(querySkuAngelStationRequest);
        if (Objects.isNull(skuAngelStationResultDto) || CollectionUtil.isEmpty(skuAngelStationResultDto.getSkuAngelStationDtos())) {
            sendDongDongMsg("分派实验室失败",medicalPromiseDispatchCmd.getMsg()+",没有可用的服务站");
            throw new BusinessException(new DynamicErrorCode("61005","没有可用的服务站"));
        }
        log.info("MedicalPromiseApplicationImpl->getExistStationSet,skuAngelStationResultDto={}", JsonUtil.toJSONString(skuAngelStationResultDto));

        boolean needKnight = StringUtils.equals(BusinessModeEnum.SELF_TEST.getCode(), medicalPromiseDispatchCmd.getBusinessModeCode());
        for (SkuAngelStationDto skuAngelStationDto : skuAngelStationResultDto.getSkuAngelStationDtos()) {
            if (needKnight && (Objects.isNull(skuAngelStationDto.getSkuInventoryDto()) || !(skuAngelStationDto.getSkuInventoryDto().getInventoryNum() > 0))) {
                if (!skuAngelStationDto.getHavePreemption()) {
                    continue;
                }
            }
            List<StationDto> stationDtoList = skuAngelStationDto.getStationDtoList();
            for (StationDto stationDto : stationDtoList) {
                String stationId = String.valueOf(stationDto.getStationId());
                skuAngelStation.computeIfAbsent(stationId, k -> new ArrayList<>()).add(skuAngelStationDto);
            }
        }
        if (MapUtils.isEmpty(skuAngelStation)) {
            sendDongDongMsg("分派实验室失败",medicalPromiseDispatchCmd.getMsg()+",没有可用的服务站");
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATION_GROUP_NULL);
        }

        Set<String> existStation = Sets.newHashSet();
        skuAngelStation.forEach((k, v) -> {
            existStation.add(k);
        });
        medicalPromiseDispatchCmd.setMsg(medicalPromiseDispatchCmd.getMsg()+"，服务站命中实验室"+StringUtil.join(existStation, ","));
        return existStation;

    }

    /**
     * 设置实验室具体信息
     * @param stationIdToObj
     * @param medicalPromise
     * @param stationId
     * @param target
     */
    private void packStationInfo(Map<String, JdhStationServiceItemRelContext> stationIdToObj, MedicalPromise medicalPromise, String stationId, String target) {
        if (StringUtils.isNotBlank(target) && !StringUtils.equals(stationId, target)){
            //根据stationId查询相关信息
            if (stationIdToObj.containsKey(target)){
                JdhStationServiceItemRelContext jdhStationServiceItemRelContext = stationIdToObj.get(stationId);
                medicalPromise.setProviderId(jdhStationServiceItemRelContext.getChannelNo());
                medicalPromise.setStationId(jdhStationServiceItemRelContext.getStationId());
                medicalPromise.setStationAddress(jdhStationServiceItemRelContext.getStationAddr());
                medicalPromise.setStationName(jdhStationServiceItemRelContext.getStationName());
                medicalPromise.setStationPhone(jdhStationServiceItemRelContext.getStationPhone());
            }else {
                StoreInfoRequest storeInfoRequest = new StoreInfoRequest();
                storeInfoRequest.setStationId(target);
                StoreInfoDto storeInfoDto = providerStoreApplication.queryStationInfo(storeInfoRequest);
                medicalPromise.setProviderId(storeInfoDto.getProviderId());
                medicalPromise.setStationId(storeInfoDto.getStationId());
                medicalPromise.setStationAddress(storeInfoDto.getStationAddress());
                medicalPromise.setStationName(storeInfoDto.getStationName());
                medicalPromise.setStationPhone(storeInfoDto.getStationPhone());
                JdhStationServiceItemRelContext itemRelContext = new JdhStationServiceItemRelContext();
                itemRelContext.setChannelNo(storeInfoDto.getProviderId());
                itemRelContext.setStationId(storeInfoDto.getStationId());
                itemRelContext.setStationAddr(storeInfoDto.getStationAddress());
                itemRelContext.setStationName(storeInfoDto.getStationName());
                itemRelContext.setStationPhone(storeInfoDto.getStationPhone());
                stationIdToObj.put(target,itemRelContext);
            }
        }else {
            JdhStationServiceItemRelContext jdhStationServiceItemRelContext = stationIdToObj.get(stationId);
            medicalPromise.setProviderId(jdhStationServiceItemRelContext.getChannelNo());
            medicalPromise.setStationId(jdhStationServiceItemRelContext.getStationId());
            medicalPromise.setStationAddress(jdhStationServiceItemRelContext.getStationAddr());
            medicalPromise.setStationName(jdhStationServiceItemRelContext.getStationName());
            medicalPromise.setStationPhone(jdhStationServiceItemRelContext.getStationPhone());
        }
    }


    /**
     * 获取最佳合管配置
     * @return
     */
    private Map<String,List<String>> getMergeMedicalPromiseBestConfig(LinkedHashMap<String, List<String>> configs,List<String> serviceItemList){
        Map<String,List<String>> res = Maps.newHashMap();
        Set<String> serviceItemSet = new HashSet<>(serviceItemList);
        for (Map.Entry<String,List<String>> entry : configs.entrySet()){
            String key = entry.getKey();
            List<String> value = entry.getValue();
            Set<String> configSet = new HashSet<>(value);
            HashSet<String> resSet = new HashSet<>(serviceItemSet);
            resSet.retainAll(configSet);
            if (resSet.size() == configSet.size()){
                res.put(key,value);
                break;
            }
        }
        return res;
    }

    /**
     * 样本编码不存在
     * @param quickStructReportVerifyResultDTO
     * @param structQuickReportResultDTO
     * @param errorMsg
     * @param errorGroup
     */
    private void codeNotExist(QuickStructReportVerifyResultDTO quickStructReportVerifyResultDTO, StructQuickReportResultDTO  structQuickReportResultDTO, List<String> errorMsg, Map<String, List<QuickStructReportVerifyResultDTO>> errorGroup) {
        quickStructReportVerifyResultDTO.setVerifyResult(false);
        quickStructReportVerifyResultDTO.setVerifyFailCanSkip(false);
        String errorMsgString = structQuickReportResultDTO.getSampleBarcode() + "样本编号不存在";
        errorMsg.add(errorMsgString);
        // 错误明细按照类型记录
        QuickStructReportVerifyResultDTO errorDetail = new QuickStructReportVerifyResultDTO();
        errorDetail.setVerifyResult(false);
        errorDetail.setVerifyFailCanSkip(false);
        errorDetail.setVerifyFailMsg(errorMsgString);
        if (errorGroup.containsKey(ReportVerifyEnum.CODE_NOT_EXIST.getCode())) {
            errorGroup.get(ReportVerifyEnum.CODE_NOT_EXIST.getCode()).add(errorDetail);
        } else {
            errorGroup.put(ReportVerifyEnum.CODE_NOT_EXIST.getCode(), Lists.newArrayList(errorDetail));
        }
    }

    /**
     * 指标数量配置
     */
    private void contrastServiceItemIndicatorNum(VerifyReportBaseRootConfig verifyReportRootConfig, MedicalPromise medicalPromise, QuickStructReportVerifyResultDTO quickStructReportVerifyResultDTO, StructQuickReportResultDTO  structQuickReportResultDTO, List<String> errorMsg, Map<String, List<QuickStructReportVerifyResultDTO>> errorGroup) {
        log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport], 校验结果与项目配置指标数量是否一致start");
        ServiceItemDto serviceItemDto = productServiceItemApplication.queryServiceItemDetail(ServiceItemQuery.builder().itemId(Long.parseLong(medicalPromise.getServiceItemId())).build());
        log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport], reportSize={} serviceItemSize={}",  structQuickReportResultDTO.getIndicators().size(), serviceItemDto.getServiceIndicatorDtoList().size());
        if (structQuickReportResultDTO.getIndicators().size() != serviceItemDto.getServiceIndicatorDtoList().size()){
            quickStructReportVerifyResultDTO.setVerifyResult(false);
            // 如果已经明确不可忽略，优先返回不可忽略
            if (!Boolean.FALSE.equals(quickStructReportVerifyResultDTO.getVerifyFailCanSkip())) {
                quickStructReportVerifyResultDTO.setVerifyFailCanSkip(Boolean.TRUE.equals(verifyReportRootConfig.getContrastServiceItemIndicatorNum().getVerifyFailCanSkip()));
            }
            String errorMsgString = structQuickReportResultDTO.getSampleBarcode() + "报告中的指标数量不对";
            errorMsg.add(errorMsgString);
            // 错误明细按照类型记录
            QuickStructReportVerifyResultDTO errorDetail = new QuickStructReportVerifyResultDTO();
            errorDetail.setVerifyResult(false);
            errorDetail.setVerifyFailCanSkip(Boolean.TRUE.equals(verifyReportRootConfig.getContrastServiceItemIndicatorNum().getVerifyFailCanSkip()));
            errorDetail.setVerifyFailMsg(errorMsgString);
            if (errorGroup.containsKey(ReportVerifyEnum.CONTRAST_SERVICE_ITEM_INDICATOR_NUM.getCode())) {
                errorGroup.get(ReportVerifyEnum.CONTRAST_SERVICE_ITEM_INDICATOR_NUM.getCode()).add(errorDetail);
            } else {
                errorGroup.put(ReportVerifyEnum.CONTRAST_SERVICE_ITEM_INDICATOR_NUM.getCode(), Lists.newArrayList(errorDetail));
            }
        }
    }

    /**
     * 指标冲突配置
     */
    private void indicatorConflict(VerifyReportBaseRootConfig verifyReportRootConfig, Map<String, String> indicatorNameTypeMap, QuickStructReportVerifyResultDTO quickStructReportVerifyResultDTO, StructQuickReportResultDTO  structQuickReportResultDTO, List<String> errorMsg, Map<String, List<QuickStructReportVerifyResultDTO>> errorGroup) {
        log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport], 阳性指标冲突校验start");
        verifyReportRootConfig.getIndicatorConflict().sort(Comparator.comparing(VerifyReportAnomalyIndicatorConflictConfig::getSort));
        for (VerifyReportAnomalyIndicatorConflictConfig verifyReportAnomalyIndicatorConflictConfig : verifyReportRootConfig.getIndicatorConflict()) {
            if (CollUtil.isEmpty(verifyReportAnomalyIndicatorConflictConfig.getAnomalyIndicatorNameConflict())) {
                continue;
            }
            for (Map<String, String>  nameTypeMap : verifyReportAnomalyIndicatorConflictConfig.getAnomalyIndicatorNameConflict()) {
                List<String> conflictName = new ArrayList<>();
                for (Map.Entry<String, String> entry : nameTypeMap.entrySet()) {
                    String indicatorName = entry.getKey();
                    if (!indicatorNameTypeMap.containsKey(indicatorName)) {
                        continue;
                    }
                    Map<String, Object> param = new HashMap<>();
                    param.put("value", indicatorNameTypeMap.get(indicatorName));
                    if ((boolean) AviatorEvaluator.compile(entry.getValue(), Boolean.TRUE).execute(param)) {
                        conflictName.add(indicatorName);
                    }
                }
                if (conflictName.size() > 1) {
                    quickStructReportVerifyResultDTO.setVerifyResult(false);
                    // 如果已经明确不可忽略，优先返回不可忽略
                    if (!Boolean.FALSE.equals(quickStructReportVerifyResultDTO.getVerifyFailCanSkip())) {
                        quickStructReportVerifyResultDTO.setVerifyFailCanSkip(Boolean.TRUE.equals(verifyReportAnomalyIndicatorConflictConfig.getVerifyFailCanSkip()));
                    }
                    String errorMsgString = structQuickReportResultDTO.getSampleBarcode() + Joiner.on("和").join(conflictName) + "检测结果冲突";
                    errorMsg.add(errorMsgString);
                    // 错误明细按照类型记录
                    QuickStructReportVerifyResultDTO errorDetail = new QuickStructReportVerifyResultDTO();
                    errorDetail.setVerifyResult(false);
                    errorDetail.setVerifyFailCanSkip(Boolean.TRUE.equals(verifyReportRootConfig.getContrastServiceItemIndicatorNum().getVerifyFailCanSkip()));
                    errorDetail.setVerifyFailMsg(errorMsgString);
                    if (errorGroup.containsKey(ReportVerifyEnum.INDICATOR_CONFLICT.getCode())) {
                        errorGroup.get(ReportVerifyEnum.INDICATOR_CONFLICT.getCode()).add(errorDetail);
                    } else {
                        errorGroup.put(ReportVerifyEnum.INDICATOR_CONFLICT.getCode(), Lists.newArrayList(errorDetail));
                    }
                }
            }
        }
    }

    /**
     * 指标阳性阈值配置
     */
    private void anomalyIndicatorNum(VerifyReportBaseRootConfig verifyReportRootConfig, Map<String, String> indicatorNameTypeMap, QuickStructReportVerifyResultDTO quickStructReportVerifyResultDTO, StructQuickReportResultDTO  structQuickReportResultDTO, List<String> errorMsg, Map<String, List<QuickStructReportVerifyResultDTO>> errorGroup) {
        log.info("[MedicalPromiseApplicationImpl -> verifyStructQuickReport],指标异常数量校验start");
        int  anomalyIndicatorNum = CollUtil.isEmpty(indicatorNameTypeMap) ? 0 : (int) indicatorNameTypeMap.values().stream().filter(s -> !CommonConstant.ZERO_STR.equalsIgnoreCase(s)).count();
        VerifyReportAnomalyIndicatorNumConfig verifyReportAnomalyIndicatorNumConfig = verifyReportRootConfig.getAnomalyIndicatorNum();
        if (anomalyIndicatorNum > verifyReportAnomalyIndicatorNumConfig.getAnomalyIndicatorWarnNum()) {
            quickStructReportVerifyResultDTO.setVerifyResult(false);
            // 如果已经明确不可忽略，优先返回不可忽略
            if (!Boolean.FALSE.equals(quickStructReportVerifyResultDTO.getVerifyFailCanSkip())) {
                quickStructReportVerifyResultDTO.setVerifyFailCanSkip(Boolean.TRUE.equals(verifyReportAnomalyIndicatorNumConfig.getVerifyFailCanSkip()));
            }
            String errorMsgString = structQuickReportResultDTO.getSampleBarcode() + "阳性指标大于" + verifyReportAnomalyIndicatorNumConfig.getAnomalyIndicatorWarnNum() + "个，请仔细复审，避免出错";
            errorMsg.add(errorMsgString);
            // 错误明细按照类型记录
            QuickStructReportVerifyResultDTO errorDetail = new QuickStructReportVerifyResultDTO();
            errorDetail.setVerifyResult(false);
            errorDetail.setVerifyFailCanSkip(Boolean.TRUE.equals(verifyReportRootConfig.getContrastServiceItemIndicatorNum().getVerifyFailCanSkip()));
            errorDetail.setVerifyFailMsg(errorMsgString);
            if (errorGroup.containsKey(ReportVerifyEnum.ANOMALY_INDICATOR_NUM.getCode())) {
                errorGroup.get(ReportVerifyEnum.ANOMALY_INDICATOR_NUM.getCode()).add(errorDetail);
            } else {
                errorGroup.put(ReportVerifyEnum.ANOMALY_INDICATOR_NUM.getCode(), Lists.newArrayList(errorDetail));
            }
        }
    }

    /**
     * 报告预警
     * @param structReportStr
     */
    private void sendReportWarnMsg(String structReportStr, MedicalPromise medicalPromise) {
        try {
            log.info("MedicalPromiseApplicationImpl sendReportWarnMsg start");
            List<MedicalPromiseReportVerifyRequest> reportResult = new ArrayList<>();
            MedicalPromiseReportVerifyRequest medicalPromiseReportVerifyRequest = new MedicalPromiseReportVerifyRequest();
            medicalPromiseReportVerifyRequest.setJdStructReportStr(structReportStr);
            reportResult.add(medicalPromiseReportVerifyRequest);
            QuickStructReportVerifyResultDTO verifyResultDTO = verifyStructQuickReport(reportResult);
            if (verifyResultDTO == null) {
                return;
            }
            if (CollUtil.isEmpty(verifyResultDTO.getErrorGroup())) {
                return;
            }
            if (!verifyResultDTO.getErrorGroup().containsKey(ReportVerifyEnum.ANOMALY_INDICATOR_NUM.getCode())) {
                return;
            }
            List<QuickStructReportVerifyResultDTO> lt = verifyResultDTO.getErrorGroup().get(ReportVerifyEnum.ANOMALY_INDICATOR_NUM.getCode());
            if (CollUtil.isEmpty(lt)) {
                return;
            }
            for (QuickStructReportVerifyResultDTO dto : lt) {
                if (dto == null || Boolean.TRUE.equals(dto.getVerifyResult())) {
                    continue;
                }
                Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
                JSONObject jsonObject = robotAlarmMap.get("阳性报告数量超阈值");
                dongDongRobotRpc.sendDongDongRobotMessage(String.format("报告阳性指标过多，检测单ID：%s，检测项目名称：%s，样本编码：%s，实验室名称：%s，请关注",
                                medicalPromise.getMedicalPromiseId(), medicalPromise.getServiceItemName(), medicalPromise.getSpecimenCode(), medicalPromise.getStationName()),
                        jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
            }
        } catch (Exception e) {
            log.error("MedicalPromiseApplicationImpl sendReportWarnMsg", e);
        }
    }

    /**
     * 检查报告的下载 URL 是否合法。
     * @param medicalPromiseReportCmd 包含报告下载 URL 的命令对象。
     * @throws BusinessException 如果 URL 不合法或无法访问，抛出业务异常。
     */
    private void checkUrl(MedicalPromiseReportCmd medicalPromiseReportCmd) {
        try {
            URL url = new URL(medicalPromiseReportCmd.getReportUrl());
            if (!duccConfig.getUrlBlankSet().contains(url.getHost())){
                throw new BusinessException(MedPromiseErrorCode.REPORT_URL_ERROR);
            }
        }catch (BusinessException b){
            throw  b;
        } catch(Exception e){
            throw new BusinessException(MedPromiseErrorCode.REPORT_DOWNLOAD_ERROR);
        }
    }

    /**
     * 计算样本浓度
     */
    private void calculateIndicatorConcentration(MedicalPromiseReportCmd medicalPromiseReportCmd, StructQuickReportResultIndicatorDTO quickReportResultIndicatorDTO) {
        try {
            if (StringUtils.isBlank(medicalPromiseReportCmd.getStationId()) || StringUtils.isBlank(quickReportResultIndicatorDTO.getIndicatorNo()) || StringUtils.isBlank(quickReportResultIndicatorDTO.getCtValue())) {
                log.info("MedicalPromiseApplicationImpl calculateIndicatorConcentration 计算样本浓度忽略 stationId={} indicatorId={} ctValue={}", medicalPromiseReportCmd.getStationId(), quickReportResultIndicatorDTO.getIndicatorNo(), quickReportResultIndicatorDTO.getCtValue());
                return;
            }
            JdhStationIndicatorRel jdhStationIndicatorRel = new JdhStationIndicatorRel();
            jdhStationIndicatorRel.setIndicatorId(Long.parseLong(quickReportResultIndicatorDTO.getIndicatorNo()));
            jdhStationIndicatorRel.setStationId(medicalPromiseReportCmd.getStationId());
            JdhStationIndicatorRel ret = jdhServiceIndicatorRepository.queryStationIndicator(jdhStationIndicatorRel);
            if (ret == null || ret.getConcentrationFormula() == null ||  StringUtils.isBlank(ret.getConcentrationFormula().getConcentrationFormula())) {
                log.info("MedicalPromiseApplicationImpl calculateIndicatorConcentration 计算样本浓度实验室无配置 ret={}", JSON.toJSONString(ret));
                return;
            }
            Expression compiledExp = AviatorEvaluator.compile(ret.getConcentrationFormula().getConcentrationFormula());
            Double result =
                    (Double)compiledExp.execute(compiledExp.newEnv("ct", Double.parseDouble(quickReportResultIndicatorDTO.getCtValue())));
            BigDecimal bigDecimal = new BigDecimal(String.valueOf(result));
            quickReportResultIndicatorDTO.setIndicatorConcentration(bigDecimal.toString());
            quickReportResultIndicatorDTO.setIndicatorConcentrationBigDecimal(bigDecimal);
            quickReportResultIndicatorDTO.setConcentrationFormula(ret.getConcentrationFormula().getConcentrationFormula());
            log.info("MedicalPromiseApplicationImpl calculateIndicatorConcentration 计算样本浓度结果 indicatorConcentration={}", bigDecimal);
        } catch (Exception e) {
             log.error("MedicalPromiseApplicationImpl calculateIndicatorConcentration", e);
        }
    }

    /**
     * 设置Ct值
     * @param indicatorDTO dto
     */
    private void setCtValue(StructQuickReportResultIndicatorDTO indicatorDTO) {
        if(indicatorDTO == null || StringUtils.isBlank(indicatorDTO.getValue())){
             return;
        }
        try {
            boolean isNumber = indicatorDTO.getValue().matches("^[0-9]+(\\.[0-9]+)?$");
            log.info("MedicalPromiseApplicationImpl setCtValue isNumber={} value={} ctValue={}", isNumber, indicatorDTO.getValue(), indicatorDTO.getCtValue());
            if (isNumber && StringUtils.isBlank(indicatorDTO.getCtValue())) {
                indicatorDTO.setCtValue(indicatorDTO.getValue());
            }
        } catch (Exception e) {
            log.error("MedicalPromiseApplicationImpl setCtValue", e);
        }
    }

    /**
     * 设置正常范围值
     * @param quickReportResultIndicatorDTO dto
     * @param medicalPromiseReportCmd cmd
     */
    private void setNormalRangeValue(MedicalPromiseReportCmd medicalPromiseReportCmd, StructQuickReportResultIndicatorDTO quickReportResultIndicatorDTO, Map<String, ReportNormalRangeStationConfig> reportNormalRangeStationConfigMaps) {
        if (quickReportResultIndicatorDTO == null || medicalPromiseReportCmd == null || StringUtils.isBlank(medicalPromiseReportCmd.getStationId())) {
            return;
        }
        if (CollUtil.isEmpty(reportNormalRangeStationConfigMaps) || !reportNormalRangeStationConfigMaps.containsKey(medicalPromiseReportCmd.getStationId())) {
            log.info("MedicalPromiseApplicationImpl setNormalRangeValue 实验室无正常范围配置 reportNormalRangeStationConfig={}", JSON.toJSONString(reportNormalRangeStationConfigMaps));
            return;
        }
        ReportNormalRangeStationConfig reportNormalRangeStationConfig = JSON.parseObject(JSON.toJSONString(reportNormalRangeStationConfigMaps.get(medicalPromiseReportCmd.getStationId())), ReportNormalRangeStationConfig.class);
        Map<String, ReportIndicatorNormalRangeConfig> reportIndicatorNormalRangeConfigMap = reportNormalRangeStationConfig.getReportIndicatorNormalRangeConfig();
        if (CollUtil.isNotEmpty(reportIndicatorNormalRangeConfigMap) && reportIndicatorNormalRangeConfigMap.containsKey(quickReportResultIndicatorDTO.getIndicatorName())) {
            ReportIndicatorNormalRangeConfig reportIndicatorNormalRangeConfig = reportIndicatorNormalRangeConfigMap.get(quickReportResultIndicatorDTO.getIndicatorName());
            if (reportIndicatorNormalRangeConfig != null && StringUtils.isNotBlank(reportIndicatorNormalRangeConfig.getDefaultValue())) {
                log.info("MedicalPromiseApplicationImpl setNormalRangeValue 命中实验室指标正常范围配置 reportIndicatorNormalRangeConfig={}", JSON.toJSONString(reportIndicatorNormalRangeConfig));
                quickReportResultIndicatorDTO.setReferenceRangeValue(reportIndicatorNormalRangeConfig.getDefaultValue());
                return;
            }
        }
        // 兜底实验室全局配置
        log.info("MedicalPromiseApplicationImpl setNormalRangeValue 命中实验室兜底正常范围配置 reportNormalRangeStationConfig={}", JSON.toJSONString(reportNormalRangeStationConfig));
        quickReportResultIndicatorDTO.setReferenceRangeValue(reportNormalRangeStationConfig.getDefaultValue());
    }

    /**
     * 下载结构化报告字符串
     *
     * @param jssUrl
     * @return
     */
    private String getSourceReportStr(String jssUrl) {
        if (StringUtils.isBlank(jssUrl)) {
            return null;
        }
        log.info("MedicalPromiseApplicationImpl.getSourceReportStr -> jssUrl={}", jssUrl);
        InputStream inputStream = fileManageService.get(jssUrl);
        String result = new BufferedReader(new InputStreamReader(inputStream))
                .lines().collect(Collectors.joining("\n"));
        log.info("MedicalPromiseApplicationImpl.getSourceReportStr -> result={}", result);
        return result;
    }

    /**
     * 将表达式转换为 RangeValueDTO 列表。
     * @param indicatorDTO 表达式字符串，支持多种格式如 "1.2~3.4"、">5.6" 等。
     * @return 转换后的 RangeValueDTO 列表，包含了每个范围的起始值、结束值和状态。
     */
    public  List<RangeValueDTO> convertExpression(StructQuickReportResultIndicatorDTO indicatorDTO,String template) {

        String expression = indicatorDTO.getReferenceRangeValue();
        if (StringUtils.isBlank(expression)) {
            return null;
        }

        List<RangeValueDTO> rangeValueDTOS = com.google.common.collect.Lists.newArrayList();

        //ct模版
        if (StringUtils.equals(IndicatorTemplateEnum.CT.getTemplateType(), template)){

            if (StringUtils.isBlank(indicatorDTO.getCtValue()) || StringUtils.isBlank(indicatorDTO.getReferenceRangeValue()) ){
                indicatorDTO.setTemplateType(IndicatorTemplateEnum.BREAK_EVEN.getTemplateType() );
                return null;
            }

        // 正则表达式匹配不同格式
            RangeValueDTO rangeValueDTO = getCtRangeValue(indicatorDTO);
            if (rangeValueDTO == null) {
                return null;
            }
            rangeValueDTOS.add(rangeValueDTO);
            return rangeValueDTOS;
        }

        // 正则表达式匹配不同格式
        packNumberRangeValue(indicatorDTO, expression, rangeValueDTOS);

        return rangeValueDTOS;
    }

    /**
     * 解析数值范围表达式并将其转换为RangeValueDTO对象列表。
     * @param indicatorDTO 一个StructQuickReportResultIndicatorDTO对象，包含指标的值。
     * @param expression 一个字符串，表示数值范围表达式。
     * @param rangeValueDTOS 一个List<RangeValueDTO>对象，用于存储解析后的数值范围。
     */
    private void packNumberRangeValue(StructQuickReportResultIndicatorDTO indicatorDTO, String expression, List<RangeValueDTO> rangeValueDTOS) {
        Pattern pattern1 = Pattern.compile("^([0-9]*\\.?[0-9]+)~([0-9]+\\.?[0-9]*)$");
        Pattern pattern2 = Pattern.compile("^([0-9]*\\.?[0-9]+)--([0-9]+\\.?[0-9]*)$");
        Pattern pattern3 = Pattern.compile("^([0-9]*\\.?[0-9]+)-([0-9]+\\.?[0-9]*)$");
        Pattern pattern4 = Pattern.compile("^>([0-9]+\\.?[0-9]*)$");
        Pattern pattern5 = Pattern.compile("^<([0-9]+\\.?[0-9]*)$");
        Pattern pattern6 = Pattern.compile("^≤([0-9]+\\.?[0-9]*)$");


        Matcher matcher1 = pattern1.matcher(expression);
        Matcher matcher2 = pattern2.matcher(expression);
        Matcher matcher3 = pattern3.matcher(expression);
        Matcher matcher4 = pattern4.matcher(expression);
        Matcher matcher5 = pattern5.matcher(expression);
        Matcher matcher6 = pattern6.matcher(expression);

        if (matcher1.matches() || matcher2.matches() || matcher3.matches()) {
            String a = matcher1.matches() ? matcher1.group(1) : (matcher2.matches() ? matcher2.group(1) : matcher3.group(1));
            String b = matcher1.matches() ? matcher1.group(2) : (matcher2.matches() ? matcher2.group(2) : matcher3.group(2));
            if (isZero(a)) {
                rangeValueDTOS.add(packRangeValueDTO("0",b,"正常", indicatorDTO.getValue()));
                rangeValueDTOS.add(packRangeValueDTO(b,"","异常", indicatorDTO.getValue()));
//                return "0~" + b + " 正常;" + b + " 异常";
            } else {
                rangeValueDTOS.add(packRangeValueDTO("0",a,"偏低", indicatorDTO.getValue()));
                rangeValueDTOS.add(packRangeValueDTO(a,b,"正常", indicatorDTO.getValue()));
                rangeValueDTOS.add(packRangeValueDTO(b,"","异常", indicatorDTO.getValue()));
//                return "0~" + a + " 偏低;" + a + "~" + b + " 正常;" + b + " 偏高";
            }
        } else if (matcher4.matches()) {
            String a = matcher4.group(1);
            rangeValueDTOS.add(packRangeValueDTO("0",a,"异常", indicatorDTO.getValue()));
            rangeValueDTOS.add(packRangeValueDTO(a,"","正常", indicatorDTO.getValue()));
//            return "0~" + a + " 异常;" + a + " 正常";
        } else if (matcher5.matches()) {
            String a = matcher5.group(1);
            rangeValueDTOS.add(packRangeValueDTO("0",a,"正常", indicatorDTO.getValue()));
            rangeValueDTOS.add(packRangeValueDTO(a,"","异常", indicatorDTO.getValue()));
//            return "0~" + a + " 正常;" + a + " 异常";
        }else if (matcher6.matches()) {
            String a = matcher6.group(1);
            rangeValueDTOS.add(packRangeValueDTO("0",a,"正常", indicatorDTO.getValue()));
            rangeValueDTOS.add(packRangeValueDTO(a,"","异常", indicatorDTO.getValue()));
//            return "0~" + a + " 正常;" + a + " 异常";
        }
    }

    /**
     * 根据指标DTO获取CT值的范围和百分比。
     * @param indicatorDTO StructQuickReportResultIndicatorDTO对象，包含指标的正常范围值、浓度公式和CT值。
     * @return RangeValueDTO对象，包含范围最小值、范围最大值、实际值和百分比。
     */
    private   RangeValueDTO getCtRangeValue(StructQuickReportResultIndicatorDTO indicatorDTO) {
        String[] split = indicatorDTO.getReferenceRangeValue().split("或");
        String spiltNormalRange = split.length>1 ? split[1] : split[0];
        Pattern pattern = Pattern.compile("(?i)\\bct\\s*>\\s*([0-9.]+)|\\b>\\s*([0-9.]+)|\\bct\\s*值\\s*>\\s*([0-9.]+)|\\b([0-9.]+)\\b");
        Matcher matcher = pattern.matcher(spiltNormalRange);
        String rangeMin = null;
        if (matcher.find()){
            for (int i = 1; i <= matcher.groupCount(); i++) {
                String numStr = matcher.group(i);
                if (numStr != null) {
                    rangeMin = numStr;
                    break;
                }
            }
        }
        if (StringUtils.isBlank(rangeMin) ){
            indicatorDTO.setTemplateType(IndicatorTemplateEnum.BREAK_EVEN.getTemplateType());
            return null;
        }

        if (StringUtils.isBlank(indicatorDTO.getIndicatorConcentration())
                || StringUtils.isBlank(indicatorDTO.getConcentrationFormula())
        ){
            return null;
        }

        BigDecimal min = new BigDecimal("0");
        BigDecimal max = new BigDecimal(rangeMin);


        RangeValueDTO rangeValueDTO = new RangeValueDTO();
        rangeValueDTO.setRangeMin(min.toPlainString());
        rangeValueDTO.setRangeMax(max.toPlainString());
        rangeValueDTO.setValue(indicatorDTO.getIndicatorConcentration());
        String plainString = max.subtract(new BigDecimal(indicatorDTO.getCtValue())).divide(max.subtract(min), 2, RoundingMode.HALF_UP).toPlainString();
        indicatorDTO.setPercentage(plainString);
        return rangeValueDTO;
    }

    /**
     * 判断字符串是否表示为0
     * @param value 要判断的字符串
     * @return 如果字符串可以被解析为0，则返回true；否则返回false
     */
    private  boolean isZero(String value) {
        try {
            return Double.parseDouble(value) == 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 将指定的最小值、最大值和标签打包成一个RangeValueDTO对象。
     * @param min 最小值
     * @param max 最大值
     * @param label 标签
     * @return 打包后的RangeValueDTO对象
     */
    private RangeValueDTO packRangeValueDTO(String min,String max,String label,String value) {
        RangeValueDTO rangeValueDTO = new RangeValueDTO();
        rangeValueDTO.setRangeMin(min);
        rangeValueDTO.setRangeMax(max);
        rangeValueDTO.setRangeLabel(label);
        if (new BigDecimal(value).compareTo(new BigDecimal(min)) >= 0) {
            if (StringUtils.isBlank(max) || new BigDecimal(value).compareTo(new BigDecimal(max)) <= 0) {
                rangeValueDTO.setValue(value);
            }
        }
        return rangeValueDTO;
    }


    /**
     * 将医疗承诺报告转换为图片并上传到OSS，更新报告保存命令的图片路径。
     * @param medicalPromiseReportCmd 医疗承诺报告命令对象，包含报告的URL地址和医疗承诺ID。
     * @param medicalReportSaveCmd 医疗报告保存命令对象，用于存储报告的相关信息。
     */
    private void packReportImg(MedicalPromiseReportCmd medicalPromiseReportCmd, MedicalReportSaveCmd medicalReportSaveCmd) {
        InputStream inputStream = null;
        InputStream imgInputStream = null;
        try {

             inputStream = fileManageService.get(medicalPromiseReportCmd.getReportUrl());

            //转jpg
            imgInputStream = transformPdfToImg(inputStream);
            if (Objects.nonNull(imgInputStream)){
                String imgName = medicalPromiseReportCmd.getMedicalPromiseId()+"_"+DateUtil.format(new Date(),CommonConstant.YMDHMS2)+".jpg";
                PutFileResult put = fileManageService.put(imgName, imgInputStream, FileManageServiceImpl.FolderPathEnum.REPORT, ContentTypeEnum.JPG.getValue(), Boolean.TRUE);
                medicalReportSaveCmd.setReportJpgOss(put.getFilePath());
            }
        }catch (Exception e){
            log.info("getMedicalReportSaveCmd->transformPdfToImg,error");
        }finally {
            try {
                if (Objects.nonNull(inputStream)){
                    inputStream.close();
                }
                if (Objects.nonNull(imgInputStream)){
                    imgInputStream.close();
                }
            }catch (Exception e){
                log.info("getMedicalReportSaveCmd->close,error");
            }

        }
    }

    /**
     * 将 PDF 转换为长图的 JPG 格式。
     * @param pdfInputStream PDF 文件的输入流。
     * @return 转换后的 JPG 图片的输入流。
     */
    private InputStream transformPdfToImg(InputStream pdfInputStream){
        InputStream inputStream = null;
        try {
            PDDocument document = PDDocument.load(pdfInputStream);
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            // 计算长图的高度
            int totalHeight = 0;
            int maxWidth = 0;
            for (int page = 0; page < document.getNumberOfPages(); ++page) {
                BufferedImage bim = pdfRenderer.renderImageWithDPI(page, 100);
                totalHeight += bim.getHeight();
                maxWidth = Math.max(maxWidth, bim.getWidth());
            }

            // 创建长图
            BufferedImage longImage = new BufferedImage(maxWidth, totalHeight, BufferedImage.TYPE_INT_RGB);

            int y = 0;
            for (int page = 0; page < document.getNumberOfPages(); ++page) {
                BufferedImage bim = pdfRenderer.renderImageWithDPI(page, 100);
                longImage.createGraphics().drawImage(bim, 0, y, null);
                y += bim.getHeight();
            }

            // 保存为JPG文件
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(longImage, "jpg", baos);

            inputStream = new ByteArrayInputStream(baos.toByteArray());

            // 关闭文档
            document.close();
        }catch (IOException e) {
            e.printStackTrace();
        }

        return inputStream;

    }

    private void sendDongDongMsg(String key ,String msg){
        Map<String, JSONObject> robotAlarmMap = duccConfig.getRobotAlarmMap();
        JSONObject jsonObject = robotAlarmMap.get(key);
        dongDongRobotRpc.sendDongDongRobotMessage(msg, jsonObject.getString("groupId"), jsonObject.getJSONArray("atUsers"));
    }

    private void packShowType(MedicalPromiseListRequest medicalPromiseListRequest, List<MedicalPromiseDTO> res) {
        if (CollectionUtils.isNotEmpty(res) && Boolean.TRUE.equals(medicalPromiseListRequest.getItemSummaryDetail())){
            Set<Long> itemIds = res.stream().map(p -> Long.valueOf(p.getServiceItemId())).collect(Collectors.toSet());
            List<ServiceItemDto> serviceItemDtos = productServiceItemApplication.queryServiceItemList(ServiceItemQuery.builder().itemIds(itemIds).build());
            if (CollectionUtils.isNotEmpty(serviceItemDtos)) {
                Map<Long, ServiceItemDto> itemIdToObj = serviceItemDtos.stream().collect(Collectors.toMap(ServiceItemDto::getItemId, p -> p));

                for (MedicalPromiseDTO medicalPromiseDTO : res) {
                    ServiceItemDto serviceItemDto = itemIdToObj.get(Long.valueOf(medicalPromiseDTO.getServiceItemId()));
                    if (Objects.nonNull(serviceItemDto)){
                        medicalPromiseDTO.setReportShowType(serviceItemDto.getReportShowType());
                    }
                }

            }
        }
    }

}
