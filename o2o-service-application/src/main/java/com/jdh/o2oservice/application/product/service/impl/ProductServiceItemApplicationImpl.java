package com.jdh.o2oservice.application.product.service.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.gms.component.labrador.api.domain.*;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.message.Message;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.product.ProductServiceItemExtApplication;
import com.jdh.o2oservice.application.product.convert.ProductServiceIndicatorConvertor;
import com.jdh.o2oservice.application.product.convert.ProductServiceItemConvert;
import com.jdh.o2oservice.application.product.listener.ImportProductServiceItemListener;
import com.jdh.o2oservice.application.product.listener.ImportProductServiceItemListenerContext;
import com.jdh.o2oservice.application.product.service.ProductProgramApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceIndicatorApplication;
import com.jdh.o2oservice.application.product.service.ProductServiceItemApplication;
import com.jdh.o2oservice.application.support.service.BizCategoryApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.*;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.ExceptionFactory;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.BeanUtil;
import com.jdh.o2oservice.common.enums.*;
import com.jdh.o2oservice.common.result.response.ImportResult;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angel.enums.ItemTypeEnum;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngelSkillDict;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelSkillDictRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.AngelSkillDictRepQuery;
import com.jdh.o2oservice.core.domain.product.bo.JdhContentBO;
import com.jdh.o2oservice.core.domain.product.context.IndicatorCategoryQueryContext;
import com.jdh.o2oservice.core.domain.product.context.ServiceIndicatorQueryContext;
import com.jdh.o2oservice.core.domain.product.context.ServiceItemConditionQueryContext;
import com.jdh.o2oservice.core.domain.product.context.ServiceItemQueryContext;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.enums.StandardItemApplySceneEnum;
import com.jdh.o2oservice.core.domain.product.model.*;
import com.jdh.o2oservice.core.domain.product.repository.db.*;
import com.jdh.o2oservice.core.domain.product.repository.query.*;
import com.jdh.o2oservice.core.domain.product.rpc.param.JdhContentContext;
import com.jdh.o2oservice.core.domain.product.service.ItemDomainService;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.core.domain.support.basic.dict.model.DictInfo;
import com.jdh.o2oservice.core.domain.support.basic.dict.repository.DictRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.category.model.JdhBizCategory;
import com.jdh.o2oservice.core.domain.support.category.repository.JdhBizCategoryRepository;
import com.jdh.o2oservice.core.domain.support.category.repository.query.BizCategoryRepQuery;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.trade.rpc.VenderShopRpc;
import com.jdh.o2oservice.core.domain.trade.vo.VenderInfoValueObject;
import com.jdh.o2oservice.export.product.cmd.*;
import com.jdh.o2oservice.export.product.dto.*;
import com.jdh.o2oservice.export.product.enums.ProductSaleChannelEnum;
import com.jdh.o2oservice.export.product.enums.ProductSaleChannelTypeEnum;
import com.jdh.o2oservice.export.product.excel.ServiceIndicatorCateExcelModel;
import com.jdh.o2oservice.export.product.excel.ServiceIndicatorExcelModel;
import com.jdh.o2oservice.export.product.excel.ServiceItemExcelModel;
import com.jdh.o2oservice.export.product.query.*;
import com.jdh.o2oservice.export.report.cmd.SyncServiceItemIndicatorCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: duanqiaona1
 * @Date: 2024/4/16 5:49 下午
 * @Description:
 */
@Slf4j
@Service
public class ProductServiceItemApplicationImpl implements ProductServiceItemApplication, ProductServiceItemExtApplication {

    /**
     * 项目域
     */
    @Autowired
    private ItemDomainService itemDomainService;


    /**
     * 词典仓库
     */
    @Resource
    DictRepository dictRepository;

    /**
     * 指标仓储
     */
    @Autowired
    private JdhServiceIndicatorRepository jdhServiceIndicatorRepository;

    /**
     * 技能仓储
     */
    @Autowired
    private AngelSkillDictRepository angelSkillDictRepository;

    /**
     * 指标分类业务身份仓储
     */
    @Autowired
    private JdhBusinessIndicatorCategoryRepository jdhBusinessIndicatorCategoryRepository;
    /**
     * 指标分类仓储
     */
    @Autowired
    private JdhIndicatorCategoryRepository jdhIndicatorCategoryRepository;

    /**
     * JdhServiceIndicatorRepository
     */
    @Resource
    private ProductServiceIndicatorApplication productServiceIndicatorApplication;

    /**
     * 耗材包仓储
     */
    @Resource
    JdhMaterialPackageRepository jdhMaterialPackageRepository;

    /**
     * 文件服务仓
     */
    @Resource
    JdhFileRepository jdhFileRepository;

    /**
     * 服务者
     */
    @Resource
    FileManageService fileManageService;

    /**
     * 线程池
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * ducc配置
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 标准项目仓储
     */
    @Resource
    private JdhStandardItemRepository jdhStandardItemRepository;

    /**
     * 业务分类仓储
     */
    @Resource
    private BizCategoryApplication bizCategoryApplication;

    /**
     * 标准指标仓储
     */
    @Resource
    private JdhStandardIndicatorRepository jdhStandardIndicatorRepository;

    /**
     * 业务项目仓储
     */
    @Resource
    private JdhBizItemRepository jdhBizItemRepository;

    /**
     * 仓储
     */
    @Resource
    private JdhBizCategoryRepository jdhBizCategoryRepository;

    /**
     * 供应商
     */
    @Resource
    private VenderShopRpc venderShopRpc;

    /**
     * 全局id
     */
    @Resource
    private GenerateIdFactory generateIdFactory;

    /**
     * sku信息
     */
    @Resource
    private SkuInfoRpc skuInfoRpc;

    /**
     * 业务项目
     */
    @Resource
    ProductProgramApplication productProgramApplication;

    /**
     * 套餐仓库
     */
    @Resource
    JdhProgramRepository jdhProgramRepository;

    /**
     * 套餐仓库
     */
    @Resource
    RedisUtil redisUtil;

    @Autowired
    private JdhProgramItemRelRepository jdhProgramItemRelRepository;

    @Autowired
    private JdhServiceItemRepository jdhServiceItemRepository;

    /**
     * sku 项目同步预发
     */
    @Value("${topics.event.jdhServiceItemToYfTopic}")
    private String jdhServiceItemToYfTopic;

    /**
     * reachStoreProducer
     */
    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Autowired
    private ProviderStoreRepository providerStoreRepository;


    /**
     * 查询内容百科
     *
     * @param query
     * @return
     */
    @Override
    public JdhContentBO queryContentById(JdhContentQuery query) {
        log.info("ProductServiceItemApplicationImpl#queryContentById start query={}", JSON.toJSONString(query));
        JdhContentContext contentContext = ProductServiceItemConvert.ins.query2JdhContentContext(query);
        return itemDomainService.queryContentById(contentContext);
    }

    /**
     * 批量保存项目
     *
     * @param serviceItemCmdList
     * @return
     */
    @Override
    public Boolean batchSaveServiceItem(List<SaveServiceItemCmd> serviceItemCmdList) {
        log.info("ProductServiceItemApplicationImpl#batchSaveServiceItem start serviceItemCmdList={}", JSON.toJSONString(serviceItemCmdList));
        List<ServiceItem> serviceItemList = ProductServiceItemConvert.ins.cmd2ServiceItems(serviceItemCmdList);
        log.info("ProductServiceItemApplicationImpl#batchSaveServiceItem start serviceItemList={}", JSON.toJSONString(serviceItemList));
        Boolean response = itemDomainService.batchSaveServiceItem(serviceItemList);
        return response;
    }

    /**
     * 查询项目分页
     *
     * @param serviceItemQuery
     * @return
     */
    @Override
    public PageDto<ServiceItemDto> queryServiceItemPage(ServiceItemQuery serviceItemQuery) {
        log.info("ProductServiceItemApplicationImpl#queryServiceItemPage start serviceItemQuery={}", JSON.toJSONString(serviceItemQuery));
        //默认查除体检的项目
        if (Objects.isNull(serviceItemQuery.getItemType())) {
            serviceItemQuery.setItemTypes(new HashSet<>(Arrays.asList(ItemTypeEnum.TESTING.getCode(), ItemTypeEnum.NURSING.getCode())));
        }
        //1、分页查询项目
        ServiceItemQueryContext serviceItemQueryContext = ProductServiceIndicatorConvertor.ins.convertToServiceItemQueryContext(serviceItemQuery);
        Page<ServiceItem> serviceItemPage = itemDomainService.queryServiceItemPage(serviceItemQueryContext);
        if (serviceItemPage == null) {
            return PageDto.getEmptyPage();
        }
        PageDto<ServiceItemDto> serviceItemDtoPageDto = ProductServiceIndicatorConvertor.ins.convert2ServiceItemDtoRes(serviceItemPage);

        return serviceItemDtoPageDto;
    }


    /**
     * 查询项目详情
     *
     * @param serviceItemQuery
     * @return
     */
    @Override
    public ServiceItemDto queryServiceItemDetail(ServiceItemQuery serviceItemQuery) {
        log.info("ProductServiceItemApplicationImpl#queryServiceItemDetail start serviceItemQuery={}", JSON.toJSONString(serviceItemQuery));
        if (Objects.isNull(serviceItemQuery) || Objects.isNull(serviceItemQuery.getItemId())) {
            return null;
        }
        ServiceItemQueryContext serviceItemQueryContext = ProductServiceIndicatorConvertor.ins.convertToServiceItemQueryContext(serviceItemQuery);
        List<ServiceItem> serviceItems = itemDomainService.queryServiceItemList(serviceItemQueryContext);
        if (CollUtil.isEmpty(serviceItems)) {
            return null;
        }
        List<ServiceItemDto> serviceItemDtos = ProductServiceIndicatorConvertor.ins.convertToServiceItemDtos(serviceItems);

        Set<String> skillCodeList = serviceItemDtos.stream().filter(e -> CollectionUtils.isNotEmpty(e.getAngelSkillCodeList())).map(ServiceItemDto::getAngelSkillCodeList).flatMap(List::stream).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(skillCodeList)) {
            AngelSkillDictRepQuery repQuery = AngelSkillDictRepQuery.builder().angelSkillCodeList(new ArrayList<>(skillCodeList)).build();
            List<JdhAngelSkillDict> skillDictList = angelSkillDictRepository.findList(repQuery);
            ProductServiceIndicatorConvertor.ins.packSkill(serviceItemDtos, skillDictList);
        }

        return serviceItemDtos.get(0);
    }


    /**
     * 查询项目列表
     *
     * @param serviceItemQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public List<ServiceItemDto> queryServiceItemList(ServiceItemQuery serviceItemQuery) {
        log.info("ProductServiceItemApplicationImpl#queryServiceItemList start serviceItemQuery={}", JSON.toJSONString(serviceItemQuery));
        if (Objects.isNull(serviceItemQuery)) {
            return null;
        }
        ServiceItemQueryContext serviceItemQueryContext = ProductServiceIndicatorConvertor.ins.convertToServiceItemQueryContext(serviceItemQuery);
        List<ServiceItem> serviceItems = itemDomainService.queryServiceItemList(serviceItemQueryContext);
        if (CollUtil.isEmpty(serviceItems)) {
            return Collections.emptyList();
        }
        List<ServiceItemDto> serviceItemDtos = ProductServiceIndicatorConvertor.ins.convertToServiceItemDtos(serviceItems);

        Set<String> skillCodeList = serviceItemDtos.stream().filter(e -> CollectionUtils.isNotEmpty(e.getAngelSkillCodeList())).map(ServiceItemDto::getAngelSkillCodeList).flatMap(List::stream).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(skillCodeList)) {
            AngelSkillDictRepQuery repQuery = AngelSkillDictRepQuery.builder().angelSkillCodeList(new ArrayList<>(skillCodeList)).build();
            List<JdhAngelSkillDict> skillDictList = angelSkillDictRepository.findList(repQuery);
            ProductServiceIndicatorConvertor.ins.packSkill(serviceItemDtos, skillDictList);
        }
        return serviceItemDtos;

    }

    /**
     * 查询指标分类
     *
     * @param categoryQuery
     * @return
     */
    @Override
    public List<IndicatorCategoryDto> queryIndicatorCategory(IndicatorCategoryQuery categoryQuery) {
        log.info("ProductServiceItemApplicationImpl#queryIndicatorCategory start categoryQuery={}", JSON.toJSONString(categoryQuery));
        IndicatorCategoryQueryContext queryContext = ProductServiceIndicatorConvertor.ins.convertToIndicatorCategoryQueryContext(categoryQuery);

        List<IndicatorCategory> categoryList = itemDomainService.queryIndicatorCategory(queryContext);
        List<IndicatorCategoryDto> res = ProductServiceIndicatorConvertor.ins.convertToIndicatorCategoryDtos(categoryList);

        return res;
    }

    /**
     * 导入
     *
     * @param inputStream
     * @param pin
     * @param imptResult
     */
    @Override
    public void importServiceItem(InputStream inputStream, String pin, ImportResult imptResult) {
        List<ServiceItemExcelModel> parseFailList = new ArrayList<>();
        List<ServiceItemExcelModel> listFromDoc = getServiceItemList(imptResult, inputStream, parseFailList);
        if (CollectionUtils.isEmpty(listFromDoc)) {
            imptResult.setSuccess(Boolean.FALSE);
            imptResult.setMsg("解析excel数据为空");
            return;
        }

        packServiceItemExcelModelList(imptResult, listFromDoc, parseFailList);
        if (!imptResult.isSuccess()) {
            return;
        }
        List<ServiceItem> serviceItemList = ProductServiceIndicatorConvertor.ins.packServiceItems(listFromDoc, pin);
        Boolean importRes = itemDomainService.batchSaveServiceItem(serviceItemList);
        if (importRes) {
            imptResult.setSuccess(Boolean.TRUE);
            imptResult.setSuccessNum(listFromDoc.size());
            imptResult.setFailNum(0);
        } else {
            imptResult.setSuccess(Boolean.FALSE);
            imptResult.setSuccessNum(0);
            imptResult.setFailNum(listFromDoc.size());
        }
    }

    @Override
    public void importIndicator(InputStream inputStream, String pin, ImportResult imptResult) {
        List<ServiceIndicatorExcelModel> parseFailList = new ArrayList<>();
        List<ServiceIndicatorExcelModel> listFromDoc = getServiceIndicatorList(imptResult, inputStream, parseFailList);
        if (CollectionUtils.isEmpty(listFromDoc)) {
            imptResult.setSuccess(Boolean.FALSE);
            imptResult.setMsg("解析excel数据为空");
            return;
        }
        Boolean importRes = Boolean.TRUE;
        List<SaveServiceIndicatorCmd> serviceIndicatorList = ProductServiceIndicatorConvertor.ins.packSaveServiceIndicatorCmds(listFromDoc, pin);
        packCateId(imptResult, serviceIndicatorList, parseFailList);
        if (!imptResult.isSuccess()) {
            return;
        }
        for (SaveServiceIndicatorCmd cmd : serviceIndicatorList) {
            try {
                importRes = productServiceIndicatorApplication.saveServiceIndicator(cmd);
                if (!importRes) {
                    log.error("importIndicator--saveServiceIndicator ，保存指标param={},res=", JSONObject.toJSONString(cmd), JSONObject.toJSONString(importRes));
                }
            } catch (Exception e) {
                log.error("importIndicator--saveServiceIndicator ，保存指标param={},e=", JSONObject.toJSONString(cmd), e);
            }
        }
        if (importRes) {
            imptResult.setSuccess(Boolean.TRUE);
            imptResult.setSuccessNum(listFromDoc.size());
            imptResult.setFailNum(0);
        } else {
            imptResult.setSuccess(Boolean.FALSE);
            imptResult.setSuccessNum(0);
            imptResult.setFailNum(listFromDoc.size());
        }
    }

    /**
     * 导入指标分类
     * @param inputStream
     * @param pin
     * @param imptResult
     */
    @Override
    public void importIndicatorCate(InputStream inputStream, String pin, ImportResult imptResult) {
        List<ServiceIndicatorCateExcelModel> parseFailList = new ArrayList<>();
        List<ServiceIndicatorCateExcelModel> listFromDoc = getServiceIndicatorCateList(imptResult, inputStream, parseFailList);
        if (CollectionUtils.isEmpty(listFromDoc)) {
            imptResult.setSuccess(Boolean.FALSE);
            imptResult.setMsg("解析excel数据为空");
            return;
        }
        if (!imptResult.isSuccess()) {
            return;
        }
        Boolean importRes = Boolean.TRUE;
        List<BusinessIndicatorCategory> businessIndicatorCategoryList = ProductServiceIndicatorConvertor.ins.packBusinessIndicatorCategorys(listFromDoc, pin);
        Integer offset = jdhBusinessIndicatorCategoryRepository.saveBusinessIndicatorCategoryList(businessIndicatorCategoryList);
        if (offset < 1) {
            imptResult.setSuccess(Boolean.FALSE);
            imptResult.setMsg("保存数据失败");
            return;
        }
        List<IndicatorCategory> indicatorCategories = ProductServiceIndicatorConvertor.ins.packIndicatorCategorys(listFromDoc, pin);
        offset = jdhIndicatorCategoryRepository.saveIndicatorCate(indicatorCategories);
        if (offset < 1) {
            imptResult.setSuccess(Boolean.FALSE);
            imptResult.setMsg("保存数据失败");
            return;
        }
        imptResult.setSuccess(Boolean.TRUE);
        imptResult.setSuccessNum(listFromDoc.size());
        imptResult.setFailNum(0);
    }

    /**
     * 异步导入指标业务分类
     *
     * @param cmd cmd
     * @return true
     */
    @Override
    public Boolean importProductItem(ServiceItemImportCmd cmd) {
        log.info("ProductServiceItemApplicationImpl#importProductItem cmd={}", JSON.toJSONString(cmd));
        AssertUtils.nonNull(cmd, ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("ServiceItemImportCmd"));
        AssertUtils.hasText(cmd.getFileId(), ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("文件id"));
        JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(Long.parseLong(cmd.getFileId())).build());
        if (jdhFile == null) {
            throw new BusinessException(SupportErrorCode.SUPPORT_FILE_UPLOAD_FILE_NOT_EXIST);
        }
        String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.IMPORT_PRODUCT_ITEM_IMPORT_LOCK_KEY, cmd.getErp());
        boolean lock = redisUtil.tryLock(lockRedisKey, RedisKeyEnum.IMPORT_PRODUCT_ITEM_IMPORT_LOCK_KEY.getExpireTime(), RedisKeyEnum.IMPORT_PRODUCT_ITEM_IMPORT_LOCK_KEY.getExpireTimeUnit());
        if (!lock) {
            throw new BusinessException(ProductErrorCode.PRODUCT_SERVICE_ITEM_IMPORT_EXIST);
        }
        try {
            String logId = Objects.toString(MDC.get("PFTID"), null);
            // 锁释放在导入逻辑,如果在这个try finally释放，异步锁会立刻释放
            CompletableFuture.runAsync(() -> importProductServiceItemExcel(logId, jdhFile.getFilePath(), cmd.getErp()), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_IMPORT_HAND_POOL));
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("ProductServiceItemApplicationImpl#importProductItem exception", e);
        }
        return Boolean.FALSE;
    }

    /**
     * 查询标准项目
     *
     * @param request
     * @return
     */
    @Override
    public List<StandardItemDTO> queryStandardItemList(StandardItemRequest request) {
        log.info("ProductServiceItemApplicationImpl->queryStandardItemList request={}", JSON.toJSONString(request));
        // 查询标准项目列表
        JdhStandardItemRepPageQuery standardItemRepQuery = new JdhStandardItemRepPageQuery();
        BeanUtil.copyProperties(request,standardItemRepQuery);
        standardItemRepQuery.setServiceType(StringUtils.isBlank(request.getServiceType()) ? ServiceTypeEnum.PHYSICAL.getServiceType() : request.getServiceType());
        Page<JdhStandardItem> itemPage = jdhStandardItemRepository.queryPage(standardItemRepQuery);
        if (Objects.isNull(itemPage) || CollectionUtils.isEmpty(itemPage.getRecords())){
            return Lists.newArrayList();
        }

        // 返回结果
        List<StandardItemDTO> result = new ArrayList<>();
        itemPage.getRecords().forEach(s->{
            StandardItemDTO itemDTO = new StandardItemDTO();
            itemDTO.setInspectionItemId(s.getItemId());
            itemDTO.setInspectionItemName(s.getItemName());
            itemDTO.setIndicatorNum(s.getIndicatorNum());
            itemDTO.setItemMean(s.getItemMean());
            itemDTO.setFirstBizCategory(s.getFirstBizCategory());
            itemDTO.setSecondBizCategory(s.getSecondBizCategory());
            itemDTO.setThirdBizCategory(s.getThirdBizCategory());
            result.add(itemDTO);
        });
        return result;
    }

    /**
     * 查询标准指标
     *
     * @param request
     * @return
     */
    @Override
    public List<StandardIndicatorDTO> queryStandardIndicatorList(StandardIndicatorRequest request) {
        log.info("ProductServiceItemApplicationImpl->queryStandardIndicatorList request={}", JSON.toJSONString(request));
        // 根据标准项目id查询标准指标
        List<JdhStandardIndicator> standardIndicatorList = jdhStandardItemRepository.queryStandardListByItemIds(Collections.singletonList(request.getInspectionItemId()));
        if (CollectionUtils.isEmpty(standardIndicatorList)){
            return Lists.newArrayList();
        }

        // 返回结果
        List<StandardIndicatorDTO> result = new ArrayList<>();
        standardIndicatorList.forEach(s->{
            StandardIndicatorDTO indicatorDTO = new StandardIndicatorDTO();
            indicatorDTO.setIndicatorId(s.getIndicatorId());
            indicatorDTO.setIndicatorName(s.getIndicatorName());
            indicatorDTO.setIndicatorMean(s.getIndicatorMean());
            result.add(indicatorDTO);
        });
        return result;
    }

    /**
     * 查询适用人群
     *
     * @return
     */
    @Override
    public List<ItemSuitableDTO> queryItemSuitableList() {
        return JSON.parseArray(duccConfig.getItemSuitableConfig(), ItemSuitableDTO.class);
    }

    /**
     * 分页查询业务项目
     *
     * @param request
     * @return
     */
    @Override
    public PageDto<BizItemDTO> pageBizItem(PageBizItemRequest request) {
        log.info("ProductServiceItemApplicationImpl->pageBizItem request={}", JSON.toJSONString(request));
        // 初始化分页数据
        PageDto<BizItemDTO> result = new PageDto<>();
        result.setTotalPage(NumConstant.NUM_0);
        result.setPageNum(request.getPageNum());
        result.setPageSize(request.getPageSize());
        result.setTotalCount(NumConstant.NUM_0);
        result.setList(Collections.emptyList());

        // 标准项目id
        List<Long> standardItemIds = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getFuzzyInspectionItemName())){
            JdhStandardItemRepQuery standardItemRepQuery = JdhStandardItemRepQuery.builder().itemName(request.getFuzzyInspectionItemName()).build();
            List<JdhStandardItem> standardItems = jdhStandardItemRepository.queryList(standardItemRepQuery);
            if (CollectionUtils.isEmpty(standardItems)){
                return result;
            }
            standardItemIds = standardItems.stream().map(JdhStandardItem::getItemId).collect(Collectors.toList());
        }
        log.info("ProductServiceItemApplicationImpl->pageBizItem standardItemIds={}", JSON.toJSONString(standardItemIds));

        // 分页查询业务项目
        JdhBizItemReqPageQuery bizItemReqPageQuery = new JdhBizItemReqPageQuery();
        BeanUtils.copyProperties(request,bizItemReqPageQuery);
        bizItemReqPageQuery.setBizItemName(request.getFuzzyItemName());
        bizItemReqPageQuery.setStandardItemIdList(standardItemIds);
        if (CollectionUtils.isNotEmpty(request.getItemSuitableList())){
            bizItemReqPageQuery.setSuitable(request.getItemSuitableList());
        }
        bizItemReqPageQuery.setBizItemName(request.getFuzzyItemName());
        bizItemReqPageQuery.setBusinessModeCode(request.getBusinessModeCode());
        Page<JdhBizItem> jdhBizItemPage = jdhBizItemRepository.queryPageList(bizItemReqPageQuery);
        log.info("ProductServiceItemApplicationImpl->pageBizItem bizItemReqPageQuery={}, jdhBizItemPage={}", JSON.toJSONString(bizItemReqPageQuery)
                , JSON.toJSONString(jdhBizItemPage));
        List<JdhBizItem> bizItemList = jdhBizItemPage.getRecords();
        if(CollectionUtils.isEmpty(bizItemList)){
            return result;
        }

        // 业务分类分组
        Map<Long, BizCategoryDTO> bizCategoryMap = getBizCategoryMapByCategoryIds(bizItemList);

        // 标准项目分组
        Map<Long, JdhStandardItem> standardItemMap = getStandardItemMapByItemIds(bizItemList);

        // 根据业务项目id进行分组
        Map<Long, List<JdhBizItemStandardIndicatorRel>> bizItemIndicatorRelMap = new HashMap<>();
        // 根据指标id进行分组
        Map<Long, JdhStandardIndicator> standardIndicatorMap = new HashMap<>();
        // 根据业务项目id查询业务项目标准指标关联关系
        List<JdhBizItemStandardIndicatorRel> relList = jdhBizItemRepository.queryItemIndicatorRelByBizItemIds(bizItemList.stream().map(JdhBizItem::getBizItemId)
                .collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(relList)){
            bizItemIndicatorRelMap = relList.stream().collect(Collectors.groupingBy(JdhBizItemStandardIndicatorRel::getBizItemId));
            // 查询指标列表
            JdhStandardIndicatorRepQuery standardIndicatorRepQuery = new JdhStandardIndicatorRepQuery();
            standardIndicatorRepQuery.setIndicatorIdList(relList.stream().map(JdhBizItemStandardIndicatorRel::getStandardIndicatorId).collect(Collectors.toList()));
            List<JdhStandardIndicator> standardIndicators = jdhStandardIndicatorRepository.queryList(standardIndicatorRepQuery);
            standardIndicatorMap = standardIndicators.stream().collect(Collectors.toMap(JdhStandardIndicator::getIndicatorId, Function.identity(), (key1, key2) -> key2));
        }

        // 参数转换
        List<BizItemDTO> dataList = transBizItemDataList(bizItemList, bizCategoryMap, standardItemMap, bizItemIndicatorRelMap, standardIndicatorMap);
        result.setList(dataList);
        result.setTotalPage(jdhBizItemPage.getPages());
        result.setTotalCount(jdhBizItemPage.getTotal());
        return result;
    }

    private Map<Long, JdhStandardItem> getStandardItemMapByItemIds(List<JdhBizItem> bizItemList) {
        // 查询标准项目列表
        JdhStandardItemRepQuery standardItemRepQuery = new JdhStandardItemRepQuery();
        standardItemRepQuery.setItemIdList(bizItemList.stream().map(JdhBizItem::getStandardItemId).collect(Collectors.toList()));
        List<JdhStandardItem> standardItemList = jdhStandardItemRepository.queryList(standardItemRepQuery);
        return standardItemList.stream().collect(Collectors.toMap(JdhStandardItem::getItemId, Function.identity(), (key1, key2) -> key2));
    }

    private Map<Long, BizCategoryDTO> getBizCategoryMapByCategoryIds(List<JdhBizItem> bizItemList) {
        List<Long> categoryIds = new ArrayList<>();
        categoryIds.addAll(bizItemList.stream().map(JdhBizItem::getFirstBizCategory).distinct().collect(Collectors.toList()));
        categoryIds.addAll(bizItemList.stream().map(JdhBizItem::getSecondBizCategory).distinct().collect(Collectors.toList()));
        categoryIds.addAll(bizItemList.stream().map(JdhBizItem::getThirdBizCategory).distinct().collect(Collectors.toList()));
        BizCategoryRequest jdhBizCategory = new BizCategoryRequest();
        jdhBizCategory.setBizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode());
        jdhBizCategory.setCategoryIdList(categoryIds);
        // 查找分类列表
        List<BizCategoryDTO> bizCategoryList = bizCategoryApplication.queryCategoryList(jdhBizCategory);
        return bizCategoryList.stream().collect(Collectors.toMap(BizCategoryDTO::getCategoryId, Function.identity(), (key1, key2) -> key2));
    }

    private List<BizItemDTO> transBizItemDataList(List<JdhBizItem> bizItemList, Map<Long, BizCategoryDTO> bizCategoryMap, Map<Long, JdhStandardItem> standardItemMap
            , Map<Long, List<JdhBizItemStandardIndicatorRel>> bizItemIndicatorRelMap, Map<Long, JdhStandardIndicator> standardIndicatorMap) {
        List<BizItemDTO> dataList = new ArrayList<>();
        bizItemList.forEach(b->{
            BizItemDTO bizItemDTO = new BizItemDTO();
            BeanUtils.copyProperties(b,bizItemDTO);
            bizItemDTO.setItemName(b.getBizItemName());
            bizItemDTO.setItemId(b.getBizItemId());
            bizItemDTO.setMean(b.getMean());
            bizItemDTO.setApplySceneDesc(ApplySceneEnum.getDesc(b.getApplyScene()));
            if (CollUtil.isNotEmpty(bizCategoryMap)) {
                bizItemDTO.setFirstBizCategoryName(bizCategoryMap.get(b.getFirstBizCategory()) == null ?  "" : bizCategoryMap.get(b.getFirstBizCategory()).getCategoryName());
                bizItemDTO.setSecondBizCategoryName(bizCategoryMap.get(b.getSecondBizCategory()) == null ?  "" : bizCategoryMap.get(b.getSecondBizCategory()).getCategoryName());
                bizItemDTO.setThirdBizCategoryName(bizCategoryMap.get(b.getThirdBizCategory()) == null ?  "" : bizCategoryMap.get(b.getThirdBizCategory()).getCategoryName());
            }
            bizItemDTO.setInspectionItemId(b.getStandardItemId());
            if (CollUtil.isNotEmpty(standardItemMap)){
                bizItemDTO.setInspectionItemName(standardItemMap.get(b.getStandardItemId()) == null ?  "" : standardItemMap.get(b.getStandardItemId()).getItemName());
            }
            bizItemDTO.setItemPrice(b.getPrice() == null ? "" : b.getPrice().toPlainString());
            List<String> suitableList = b.getSuitable();
            if(CollUtil.isNotEmpty(suitableList)){
                bizItemDTO.setItemSuitable(SuitableEnum.getSuitableList(suitableList));
                bizItemDTO.setItemSuitableList(suitableList);
            }
            if (CollUtil.isNotEmpty(bizItemIndicatorRelMap)) {
                List<JdhBizItemStandardIndicatorRel> relList = bizItemIndicatorRelMap.get(b.getBizItemId());
                if (CollectionUtils.isNotEmpty(relList)){
                    List<StandardIndicatorDTO> standardIndicatorList = new ArrayList<>();
                    relList.forEach(r->{
                        JdhStandardIndicator indicatorSource = null;
                        if (CollUtil.isNotEmpty(standardIndicatorMap)) {
                            indicatorSource = standardIndicatorMap.get(r.getStandardIndicatorId());
                        }
                        if (Objects.isNull(indicatorSource)){
                            return;
                        }
                        StandardIndicatorDTO indicatorTarget = new StandardIndicatorDTO();
                        indicatorTarget.setIndicatorId(indicatorSource.getIndicatorId());
                        indicatorTarget.setIndicatorName(indicatorSource.getIndicatorName());
                        indicatorTarget.setIndicatorMean(indicatorSource.getIndicatorMean());
                        standardIndicatorList.add(indicatorTarget);
                    });
                    bizItemDTO.setStandardIndicatorList(standardIndicatorList);
                }
            }
            if (b.getExtJson() != null) {
                JdhBizItemExtJsonDTO jsonDTO = new JdhBizItemExtJsonDTO();
                jsonDTO.setOldItemId(b.getExtJson().getOldItemId());
                bizItemDTO.setExtJson(jsonDTO);
            }
            dataList.add(bizItemDTO);
        });
        return dataList;
    }

    /**
     * 创建发品业务项目
     *
     * @param productBizItemCmd
     * @return
     */
    @Override
    public Boolean createPublishProductBizItem(SaveProductBizItemCmd productBizItemCmd) {
        log.info("ProductServiceItemApplicationImpl->createPublishProductBizItem productBizItemCmd={}", JSON.toJSONString(productBizItemCmd));
        List<SaveProgramBizItemRelCmd> saveProgramBizItemRelCmds = new ArrayList<>();
        Long productId = productBizItemCmd.getProductId();
        productBizItemCmd.getBizItemCmdList().forEach(sourceBizItemCmd->{
            // 校验服务项目去重逻辑
            JdhBizItem repeatBizItem = verifyBizItemRepeat(sourceBizItemCmd);
            log.info("ProductServiceItemApplicationImpl->createPublishProductBizItem repeatBizItem={}", JSON.toJSONString(repeatBizItem));
            SaveProgramBizItemRelCmd saveProgramBizItemRelCmd = new SaveProgramBizItemRelCmd();
            // 未有重复服务项目
            if (Objects.isNull(repeatBizItem)){
                // 新增服务项目
                JdhBizItem bizItem = saveProductBizItem(sourceBizItemCmd);
                saveProgramBizItemRelCmd.setBizItemId(bizItem.getBizItemId());
            }else{
                // 有重复服务项目
                saveProgramBizItemRelCmd.setBizItemId(repeatBizItem.getBizItemId());
            }
            if (Boolean.TRUE.equals(sourceBizItemCmd.getImportantItem())) {
                saveProgramBizItemRelCmd.setIsImportant(CommonConstant.ONE);
            } else {
                saveProgramBizItemRelCmd.setIsImportant(CommonConstant.ZERO);
            }
            saveProgramBizItemRelCmds.add(saveProgramBizItemRelCmd);
        });

        JdhProgramSkuQuery jdhProgramQuery = new JdhProgramSkuQuery();
        jdhProgramQuery.setProductId(productId.toString());
        jdhProgramQuery.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
        jdhProgramQuery.setBusinessModeCode(BusinessModeEnum.POP_LOC.getCode());
        List<JdhProgram> list = jdhProgramRepository.queryProgramListBySkuOrProductId(jdhProgramQuery);
        log.info("ProductServiceItemApplicationImpl->createPublishProductBizItem queryProgramListBySkuOrProductId param={} ret={}", JSON.toJSONString(jdhProgramQuery), JSON.toJSONString(list));
        // 如果不为空 删除旧对应关系
        if (CollectionUtils.isNotEmpty(list)) {
            // 删除旧商品套餐关联关系
            JdhProgramSku deleteJdhProgramSku = new JdhProgramSku();
            deleteJdhProgramSku.setProductId(productId.toString());
            deleteJdhProgramSku.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
            deleteJdhProgramSku.setBusinessModeCode(BusinessModeEnum.POP_LOC.getCode());
            jdhProgramRepository.deleteProgramSku(deleteJdhProgramSku);
            log.info("ProductServiceItemApplicationImpl->createPublishProductBizItem deleteProgramSku productId={}", productId);
            for (JdhProgram jdhProgram : list) {
                // 删除旧套餐信息
                JdhProgram deleteJdhProgram = new JdhProgram();
                deleteJdhProgram.setProgramId(jdhProgram.getProgramId());
                log.info("ProductServiceItemApplicationImpl->createPublishProductBizItem delete programId={}", jdhProgram.getProgramId());
                jdhProgramRepository.delete(deleteJdhProgram);
                // 删除旧套餐信息及项目关联信息
                JdhProgramBizItemRel jdhProgramBizItemRel = new JdhProgramBizItemRel();
                jdhProgramBizItemRel.setProgramId(jdhProgram.getProgramId());
                log.info("ProductServiceItemApplicationImpl->createPublishProductBizItem deleteProgramBizItemRel programId={}", jdhProgram.getProgramId());
                jdhProgramRepository.deleteProgramBizItemRel(jdhProgramBizItemRel);
            }
        }

        // 创建新套餐信息
        SaveProgramCmd createProgramCmd = new SaveProgramCmd();
        createProgramCmd.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
        createProgramCmd.setBusinessModeCode(BusinessModeEnum.POP_LOC.getCode());
        createProgramCmd.setProgramName("");
        createProgramCmd.setSuitable(productBizItemCmd.getProgramSuitableList());
        createProgramCmd.setVenderId(productBizItemCmd.getVenderId());
        createProgramCmd.setOperator(CommonConstant.SYSTEM);
        createProgramCmd.setSaveBizItemRelCmdList(saveProgramBizItemRelCmds);
        createProgramCmd.setSkipCheck(true);
        log.info("ProductServiceItemApplicationImpl->createPublishProductBizItem saveProgram={}", JSON.toJSONString(createProgramCmd));
        JdhProgram jdhProgram = productProgramApplication.saveProgram(createProgramCmd);
        // 创建新套餐商品关系
        SaveProgramSkuCmd saveProgramSkuCmd = new SaveProgramSkuCmd();
        saveProgramSkuCmd.setProductId(productId.toString());
        saveProgramSkuCmd.setVenderId(productBizItemCmd.getVenderId());
        saveProgramSkuCmd.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
        saveProgramSkuCmd.setBusinessModeCode(BusinessModeEnum.POP_LOC.getCode());
        saveProgramSkuCmd.setProgramId(jdhProgram.getProgramId());
        saveProgramSkuCmd.setChannelType(ProductSaleChannelTypeEnum.JD.getChannelType());
        saveProgramSkuCmd.setChannelId(ProductSaleChannelEnum.XFYL.getChannelId());
        saveProgramSkuCmd.setCreateUser(CommonConstant.SYSTEM);
        saveProgramSkuCmd.setUpdateUser(CommonConstant.SYSTEM);
        log.info("ProductServiceItemApplicationImpl->createPublishProductBizItem saveProgramSkuCmd={}", JSON.toJSONString(saveProgramSkuCmd));
        productProgramApplication.saveProgramSku(saveProgramSkuCmd);
        return true;
    }

    private JdhBizItem saveProductBizItem(SaveBizItemCmd bizItemCmd) {
        // 创建业务项目
        JdhBizItem createBizItem = new JdhBizItem();
        BeanUtils.copyProperties(bizItemCmd,createBizItem);
        createBizItem.setVenderId(bizItemCmd.getVenderId());
        // 查询商家
        VenderInfoValueObject venderInfo = venderShopRpc.getVenderInfoByVenderId(Long.valueOf(bizItemCmd.getVenderId()));
        if (!Objects.isNull(venderInfo)){
            createBizItem.setVenderName(venderInfo.getVenderName());
        }
        createBizItem.setBusinessModeCode(BusinessModeEnum.POP_LOC.getCode());
        createBizItem.setServiceType(ServiceTypeEnum.PHYSICAL.getServiceType());
        createBizItem.setApplyScene(Integer.valueOf(StandardItemApplySceneEnum.TO_C.getScene()));
        // 生成业务项目id
        Long bizItemId = generateIdFactory.getBatchId(NumConstant.NUM_1).poll();
        createBizItem.setBizItemId(bizItemId);
        createBizItem.setBizItemName(bizItemCmd.getItemName());
        createBizItem.setStandardItemId(bizItemCmd.getInspectionItemId());
        createBizItem.setPrice(new BigDecimal(bizItemCmd.getItemPrice()));
        // 适用人群
        List<String> itemSuitableList = bizItemCmd.getItemSuitableList();
        createBizItem.setSuitable(itemSuitableList);
        // 性别
        List<Integer> genderList = new ArrayList<>();
        if (itemSuitableList.contains(SuitableEnum.MAN.getCode())){
            genderList.add(GenderEnum.MAN.getType());
        }
        if (itemSuitableList.contains(SuitableEnum.UNMARRY_WOMEN.getCode()) || itemSuitableList.contains(SuitableEnum.MARRIED_WOMEN.getCode())){
            genderList.add(GenderEnum.WOMEN.getType());
        }
        createBizItem.setGender(genderList);
        createBizItem.setIndicatorList(bizItemCmd.getStandardIndicatorList().stream().map(StandardIndicatorCmd::getIndicatorId).distinct().collect(Collectors.toList()));
        createBizItem.setCreateUser(CommonConstant.SYSTEM);
        createBizItem.setUpdateUser(CommonConstant.SYSTEM);
        log.info("ProductServiceItemApplicationImpl->saveProductBizItem createBizItem={}", JSON.toJSONString(createBizItem));
        jdhBizItemRepository.createBizItem(createBizItem);
        return createBizItem;
    }

    public JdhBizItem verifyBizItemRepeat(SaveBizItemCmd bizItemCmd){
        // 去重逻辑：商家id、体检项目名称、项目分类、检查项目、检查指标、适用人群、价格，完全一致的，去重保存在商家下，保存时生成ID。
        JdhBizItemReqQuery bizItemReqQuery = new JdhBizItemReqQuery();
        BeanUtils.copyProperties(bizItemCmd,bizItemReqQuery);
        bizItemReqQuery.setStandardItemId(bizItemCmd.getInspectionItemId());
        bizItemReqQuery.setBizItemName(bizItemCmd.getItemName());
        bizItemReqQuery.setPrice(new BigDecimal(bizItemCmd.getItemPrice()));
        bizItemReqQuery.setSuitable(bizItemCmd.getItemSuitableList());
        // 查询业务项目列表
        List<JdhBizItem> bizItemList = jdhBizItemRepository.queryList(bizItemReqQuery);
        log.info("ProductServiceItemApplicationImpl->verifyBizItemRepeat bizItemReqQuery={},bizItemList={}", JSON.toJSONString(bizItemReqQuery)
                , JSON.toJSONString(bizItemList));
        if (CollectionUtils.isEmpty(bizItemList)){
            return null;
        }
        // 发品指标ids
        List<Long> reqIndicatorIdList = bizItemCmd.getStandardIndicatorList().stream().map(StandardIndicatorCmd::getIndicatorId).distinct().collect(Collectors.toList());
        Collections.sort(reqIndicatorIdList);

        // 根据业务项目id查询业务项目标准指标关联关系
        List<JdhBizItemStandardIndicatorRel> bizItemStandardIndicatorRelList = jdhBizItemRepository.queryItemIndicatorRelByBizItemIds(bizItemList.stream()
                .map(JdhBizItem::getBizItemId).collect(Collectors.toList()));
        // 按照业务项目id分组
        Map<Long, List<JdhBizItemStandardIndicatorRel>> bizItemStandardIndicatorRelMap = bizItemStandardIndicatorRelList.stream().collect(Collectors
                .groupingBy(JdhBizItemStandardIndicatorRel::getBizItemId));
        for (JdhBizItem bizItem : bizItemList) {
            List<JdhBizItemStandardIndicatorRel> relList = bizItemStandardIndicatorRelMap.get(bizItem.getBizItemId());
            if (CollectionUtils.isEmpty(relList)){
                continue;
            }
            // db中指标ids
            List<Long> dbIndicatorIdList = relList.stream().map(JdhBizItemStandardIndicatorRel::getStandardIndicatorId).distinct().collect(Collectors.toList());
            Collections.sort(dbIndicatorIdList);
            if (reqIndicatorIdList.equals(dbIndicatorIdList)){
                return bizItem;
            }
        }
        return null;
    }

    /**
     * 查询业务项目对应的指标
     *
     * @param bizItemId
     * @return
     */
    @Override
    public List<StandardIndicatorDTO> queryIndicatorListById(Long bizItemId) {
        JdhBizItemStandardIndicatorRel jdhBizItemStandardIndicatorRel = new JdhBizItemStandardIndicatorRel();
        jdhBizItemStandardIndicatorRel.setBizItemId(bizItemId);
        List<JdhBizItemStandardIndicatorRel> list = jdhBizItemRepository.queryBizItemStandardIndicatorRelList(jdhBizItemStandardIndicatorRel);
        if(CollUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        List<Long> standardIndicatorIdList = list.stream().map(JdhBizItemStandardIndicatorRel::getStandardIndicatorId).collect(Collectors.toList());
        JdhStandardIndicatorRepQuery query = JdhStandardIndicatorRepQuery.builder().indicatorIdList(standardIndicatorIdList).build();
        List<JdhStandardIndicator> jdhStandardIndicatorList = jdhStandardIndicatorRepository.queryList(query);

        return ProductServiceIndicatorConvertor.ins.convert2StandardIndicatorDTO(jdhStandardIndicatorList);
    }

    /**
     * 保存业务项目
     *
     * @param productBizItemAddCmd
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveProductBizItem(ProductBizItemAddCmd productBizItemAddCmd) {
         if (StringUtils.isBlank(productBizItemAddCmd.getItemPrice())){
            productBizItemAddCmd.setItemPrice(null);
        }
        //1.校验
        checkParam(productBizItemAddCmd);
        //2.保存业务项目
        JdhBizItem jdhBizItem = ProductServiceItemConvert.ins.convert(productBizItemAddCmd);
        int save = jdhBizItemRepository.save(jdhBizItem);
        return save>0;
    }

    /**
     * 发布商家商品服务项目
     *
     * @param productBizItemCmd
     * @return
     */
    @Override
    public Boolean publishMerchantProductItem(SaveProductBizItemCmd productBizItemCmd) {
        log.info("ProductServiceItemApplicationImpl->publishMerchantProductItem productBizItemCmd={}", JSON.toJSONString(productBizItemCmd));
        // 先查商品现有类目属性数据 https://joyspace.jd.com/page/4FTGMPrW7KEHqCKzYWOe
        Product product = skuInfoRpc.getProductById(productBizItemCmd.getVenderId(), productBizItemCmd.getProductId(), Sets.newHashSet("customProps", "multiCategoryId", "categoryId")
                , Sets.newHashSet(CommonConstant.SAME_CITY_ATTRIBUTE));
        if (Objects.isNull(product)) {
            throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("product"));
        }
        // 获取商品自定义属性
        Set<CustomProp> productCustomProps = Optional.ofNullable(product.getCustomProps()).orElse(new HashSet<>());
        // 按照定制属性id分组
        Map<String, CustomProp> customPropMap = productCustomProps.stream().collect(Collectors.toMap(CustomProp::getAttrId, customProp -> customProp, (t, t2) -> t2));

        // 组装要更新的类目属性数据
        CustomProp serviceItemProp = new CustomProp();
        serviceItemProp.setCustomPropsMode(CommonConstant.SAME_CITY_ATTRIBUTE);
        Map<String, String> skuCategoryAttrIdMap = duccConfig.getSkuCategoryAttrIdMap();
        String attrId = null;
        if (Objects.nonNull(product.getCategoryId())) {
            attrId = skuCategoryAttrIdMap.get(product.getCategoryId().toString());
        }
        if (Objects.nonNull(product.getMultiCategoryId())) {
            attrId = skuCategoryAttrIdMap.get(product.getMultiCategoryId().toString());
        }
        if (StringUtil.isBlank(attrId)) {
            throw new BusinessException(ProductErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("attrId"));
        }
        serviceItemProp.setAttrId(attrId);
        Set<CustomAttrValue> customAttrValues = Sets.newHashSetWithExpectedSize(productBizItemCmd.getBizItemCmdList().size());
        int index = 1;
        for (SaveBizItemCmd examinationItem : productBizItemCmd.getBizItemCmdList()) {
            // 转换商品自定义类目属性数据
            CustomAttrValue customAttrValue = transCustomAttrValue(productBizItemCmd, index, examinationItem);
            customAttrValues.add(customAttrValue);
            index++;
        }
        log.info("ProductServiceItemApplicationImpl->publishMerchantProductItem customAttrValues={}", JsonUtil.toJSONString(customAttrValues));
        serviceItemProp.setCustomAttrValues(customAttrValues);

        List<CustomProp> customProps = Collections.singletonList(serviceItemProp);
        // 将要更新的类目属性数据更新到现有数据集中
        for (CustomProp customProp : customProps) {
            // 查询现有类目数据中是否有要修改的属性
            CustomProp prop = customPropMap.get(customProp.getAttrId());
            // 如果有，删除现有属性数据
            if (Objects.nonNull(prop)) {
                productCustomProps.remove(prop);
            }
            // 将要更新的属性数据放入商品类目属性集合
            productCustomProps.add(customProp);
        }
        log.info("ProductServiceItemApplicationImpl->publishMerchantProductItem productCustomProps={}", JsonUtil.toJSONString(productCustomProps));

        // 商品类目属性更新（全量覆盖）
        skuInfoRpc.updateProduct(productBizItemCmd.getVenderId(), productBizItemCmd.getProductId(), Sets.newHashSet(productCustomProps));
        return true;
    }

    private CustomAttrValue transCustomAttrValue(SaveProductBizItemCmd productBizItemCmd, int index, SaveBizItemCmd examinationItem) {
        CustomAttrValue customAttrValue = new CustomAttrValue();
        customAttrValue.setIndex(index);
        Set<ComplexExpand> complexExpands = Sets.newHashSet();
        int complexExpandIndex = 1;

        // 设置项目名称
        ComplexExpand complexExpandItemName = createComplexExpand(examinationItem.getItemName(), "enName", "itemName", complexExpandIndex++, "体检项目名称", "");
        complexExpands.add(complexExpandItemName);

        // 设置项目分类
        List<Long> categoryLevelIds = Arrays.asList(examinationItem.getFirstBizCategory(), examinationItem.getSecondBizCategory(), examinationItem.getThirdBizCategory());
        List<String> categoryLevelIdList = categoryLevelIds.stream().map(String::valueOf).collect(Collectors.toList());
        ComplexExpand complexExpandItemCategoryLevelName = createComplexExpand(JSON.toJSONString(categoryLevelIdList), "enName", "itemCategoryLevelIdList", complexExpandIndex++, "项目分类", "");
        complexExpands.add(complexExpandItemCategoryLevelName);

        // 设置检查项目
        ComplexExpand complexExpandInspectionItemName = createComplexExpand(String.valueOf(examinationItem.getInspectionItemId()), "enName", "inspectionItemId", complexExpandIndex++, "检查项目", "");
        complexExpands.add(complexExpandInspectionItemName);

        // 设置检查指标
        List<JSONObject> indicatorList = examinationItem.getStandardIndicatorList().stream().map(standardIndicatorCmd -> {
            JSONObject object = new JSONObject();
            object.put("indicatorId", String.valueOf(standardIndicatorCmd.getIndicatorId()));
            object.put("indicatorName", standardIndicatorCmd.getIndicatorName());
            object.put("indicatorMean", standardIndicatorCmd.getIndicatorMean());
            object.put("healthIndicatorCode", standardIndicatorCmd.getHealthIndicatorCode());
            object.put("firstBizCategory", String.valueOf(standardIndicatorCmd.getFirstBizCategory()));
            object.put("secondBizCategory", String.valueOf(standardIndicatorCmd.getSecondBizCategory()));
            object.put("thirdBizCategory", String.valueOf(standardIndicatorCmd.getThirdBizCategory()));
            object.put("serviceType", standardIndicatorCmd.getServiceType());
            return object;
        }).collect(Collectors.toList());
        ComplexExpand complexExpandStandardIndicatorList = createComplexExpand(JSON.toJSONString(indicatorList), "enName", "standardIndicatorList", complexExpandIndex++, "检查指标", "");
        complexExpands.add(complexExpandStandardIndicatorList);

        // 设置适用人群
        ComplexExpand complexExpandItemItemSuitableList = createComplexExpand(JSON.toJSONString(examinationItem.getItemSuitableList()), "enName", "itemSuitableList", complexExpandIndex++, "适用人群", "");
        complexExpands.add(complexExpandItemItemSuitableList);

        // 设置价格
        ComplexExpand complexExpandItemPrice = createComplexExpand(examinationItem.getItemPrice(), "enName", "itemPrice", complexExpandIndex++, "价格（元）", "");
        complexExpands.add(complexExpandItemPrice);

        // 设置是否重点项目
        Boolean importantItem = Objects.equals(CommonConstant.ONE, examinationItem.getImportantItem()) ? Boolean.TRUE : Boolean.FALSE;
        ComplexExpand complexExpandImportantItem = createComplexExpand(importantItem.toString(), "enName", "importantItem", complexExpandIndex++, "是否重点项目", "");
        complexExpands.add(complexExpandImportantItem);

        // 设置扩展字段
        JSONObject itemExtraObj = new JSONObject();
        itemExtraObj.put("itemName",examinationItem.getItemName());
        itemExtraObj.put("itemCategoryLevelIdList",categoryLevelIdList);
        itemExtraObj.put("inspectionItemId",String.valueOf(examinationItem.getInspectionItemId()));
        itemExtraObj.put("standardIndicatorList",indicatorList);
        itemExtraObj.put("itemSuitableList",examinationItem.getItemSuitableList());
        itemExtraObj.put("itemPrice",examinationItem.getItemPrice());
        itemExtraObj.put("importantItem",examinationItem.getImportantItem());
        itemExtraObj.put("skuSecondaryCategoryId", CategoryId2Enum.EXAMINATION.getCategoryId());
        itemExtraObj.put("venderId",productBizItemCmd.getVenderId());

        // 查询业务分类
        BizCategoryRepQuery bizCategoryRepQuery = new BizCategoryRepQuery();
        bizCategoryRepQuery.setBizScene(BizCategorySceneEnum.STANDARD_ITEM_CATEGORY.getCode());
        bizCategoryRepQuery.setCategoryIdList(categoryLevelIds);
        List<JdhBizCategory> bizCategoryList = jdhBizCategoryRepository.queryJdhBizCategoryList(bizCategoryRepQuery);
        Map<Long, JdhBizCategory> bizCategoryMap = bizCategoryList.stream().collect(Collectors.toMap(JdhBizCategory::getCategoryId, Function.identity(), (key1, key2) -> key2));

        Map<String,Object> categoryMap = new HashMap<>();
        JdhBizCategory firstBizCategoryDTO = bizCategoryMap.get(examinationItem.getFirstBizCategory());
        if (firstBizCategoryDTO != null){
            categoryMap.put("firstBizCategory",String.valueOf(firstBizCategoryDTO.getCategoryId()));
            categoryMap.put("firstBizCategoryName",firstBizCategoryDTO.getCategoryName());
        }
        JdhBizCategory secondBizCategoryDTO = bizCategoryMap.get(examinationItem.getSecondBizCategory());
        if (secondBizCategoryDTO != null){
            categoryMap.put("secondBizCategory",String.valueOf(secondBizCategoryDTO.getCategoryId()));
            categoryMap.put("secondBizCategoryName",secondBizCategoryDTO.getCategoryName());
        }
        JdhBizCategory thirdBizCategoryDTO = bizCategoryMap.get(examinationItem.getThirdBizCategory());
        if (thirdBizCategoryDTO != null){
            categoryMap.put("thirdBizCategory",String.valueOf(thirdBizCategoryDTO.getCategoryId()));
            categoryMap.put("thirdBizCategoryName",thirdBizCategoryDTO.getCategoryName());
        }
        itemExtraObj.put("itemCategoryLevelInfo",categoryMap);

        // 查询标准项目
        JdhStandardItemRepQuery jdhStandardItemRepQuery = new JdhStandardItemRepQuery();
        jdhStandardItemRepQuery.setItemIdList(Collections.singletonList(examinationItem.getInspectionItemId()));
        List<JdhStandardItem> standardItems = jdhStandardItemRepository.queryList(jdhStandardItemRepQuery);
        JdhStandardItem jdhStandardItem = standardItems.get(0);

        JSONObject inspectionItemInfo = new JSONObject();
        inspectionItemInfo.put("itemMean", jdhStandardItem.getItemMean());
        inspectionItemInfo.put("inspectionItemName", jdhStandardItem.getItemName());
        inspectionItemInfo.put("inspectionItemId", String.valueOf(jdhStandardItem.getItemId()));
        inspectionItemInfo.put("indicatorNum", jdhStandardItem.getIndicatorNum());
        itemExtraObj.put("inspectionItemInfo",inspectionItemInfo);

        ComplexExpand complexExpandItemExtra = createComplexExpand(JsonUtil.toJSONString(itemExtraObj), "enName", "itemExtra", complexExpandIndex++, "扩展字段", "");
        complexExpands.add(complexExpandItemExtra);

        customAttrValue.setComplexExpands(complexExpands);
        return customAttrValue;
    }

    /**
     * createComplexExpand
     *
     * @param expand
     * @param featureKey
     * @param featureValue
     * @param index
     * @param prefix
     * @param suffix
     * @return
     */
    private ComplexExpand createComplexExpand(String expand, String featureKey, String featureValue, int index, String prefix, String suffix) {
        ComplexExpand complexExpand = new ComplexExpand();
        complexExpand.setExpand(expand);
        complexExpand.setFeatures(Sets.newHashSet(new Feature(featureKey, featureValue)));
        complexExpand.setIndex(index);
        complexExpand.setPrefix(prefix);
        complexExpand.setSuffix(suffix);
        return complexExpand;
    }

    /**
     * 删除业务项目
     *
     * @param productBizItemDeleteCmd
     * @return
     */
    @Override
    public Boolean deleteBizItem(ProductBizItemDeleteCmd productBizItemDeleteCmd) {
        JdhProgramItemRelQuery jdhProgramItemRelQuery = new JdhProgramItemRelQuery();
        jdhProgramItemRelQuery.setBizItemId(productBizItemDeleteCmd.getItemId());
        List<JdhProgramBizItemRel> list = jdhProgramItemRelRepository.findList(jdhProgramItemRelQuery);
        if (CollectionUtils.isNotEmpty(list)){
            throw new BusinessException(ProductErrorCode.PRODUCT_SELF_BIZ_ITEM_BIND_PROGRAM);
        }
        return jdhBizItemRepository.deleteBizItemById(productBizItemDeleteCmd.getItemId(),productBizItemDeleteCmd.getUpdateUser()) > 0;
    }

    /**
     * 分页查询业务项目
     *
     * @param request
     * @return
     */
    @Override
    public List<BizItemDTO> queryBizItemList(PageBizItemRequest request) {
        JdhBizItemReqQuery build = ProductServiceIndicatorConvertor.ins.reqToQuery(request);
        List<JdhBizItem> bizItemList = jdhBizItemRepository.queryList(build);
        return transBizItemDataList(bizItemList, null, null, null, null);
    }

    /**
     * 查询业务项目对应的指标
     *
     * @param bizItemId
     * @return
     */
    @Override
    public List<StandardIndicatorDTO> queryIndicatorListByBizItemList(List<Long> bizItemId) {
        JdhBizItemStandardIndicatorRel jdhBizItemStandardIndicatorRel = new JdhBizItemStandardIndicatorRel();
        jdhBizItemStandardIndicatorRel.setBizItemIdList(bizItemId);
        List<JdhBizItemStandardIndicatorRel> list = jdhBizItemRepository.queryBizItemStandardIndicatorRelList(jdhBizItemStandardIndicatorRel);
        if(CollUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        List<Long> standardIndicatorIdList = list.stream().map(JdhBizItemStandardIndicatorRel::getStandardIndicatorId).collect(Collectors.toList());
        JdhStandardIndicatorRepQuery query = JdhStandardIndicatorRepQuery.builder().indicatorIdList(standardIndicatorIdList).build();
        List<JdhStandardIndicator> jdhStandardIndicatorList = jdhStandardIndicatorRepository.queryList(query);

        return ProductServiceIndicatorConvertor.ins.convert2StandardIndicatorDTO(jdhStandardIndicatorList);
    }

    /**
     * 根据条件查询项目列表
     *
     * @param serviceItemConditionQuery
     * @return
     */
    @Override
    public List<ServiceItemDto> queryServiceItemListCondition(ServiceItemConditionQuery serviceItemConditionQuery) {
        if (Objects.isNull(serviceItemConditionQuery)) {
            return null;
        }
        ServiceItemConditionQueryContext serviceItemQueryContext = ProductServiceIndicatorConvertor.ins.convert(serviceItemConditionQuery);
        List<ServiceItem> serviceItems = itemDomainService.queryServiceItemListCondition(serviceItemQueryContext);
        if (CollUtil.isEmpty(serviceItems)) {
            return Collections.emptyList();
        }
        return ProductServiceIndicatorConvertor.ins.convertToServiceItemDtos(serviceItems);
    }

    /**
     * @param syncServiceItemIndicatorCmd
     * @return
     */
    @Override
    public Boolean syncServiceItemIndicator(SyncServiceItemIndicatorCmd syncServiceItemIndicatorCmd) {
        //1.查项目
        ServiceItem serviceItem = jdhServiceItemRepository.find(ServiceItemIdentifier.builder().itemId(Long.valueOf(syncServiceItemIndicatorCmd.getServiceItemId())).build());
        if (Objects.isNull(serviceItem)) {
            return false;
        }

        Map<String,String> mqBody = Maps.newHashMap();
        mqBody.put("serviceItem",JsonUtil.toJSONString(serviceItem));

        //2.查项目指标
        if (CollectionUtils.isNotEmpty(serviceItem.getIndicatorRelList())){
            Set<Long> indicatorIdSet = serviceItem.getIndicatorRelList().stream().map(ServiceItemIndicatorRel::getIndicatorId).collect(Collectors.toSet());
            List<Indicator> indicators = jdhServiceIndicatorRepository.queryIndicatorList(ServiceIndicatorQueryContext.builder().indicatorIds(indicatorIdSet).build());
            if (CollectionUtils.isNotEmpty(indicators)) {
                mqBody.put("indicators",JsonUtil.toJSONString(indicators));
            }

            if (StringUtil.isNotBlank(syncServiceItemIndicatorCmd.getStationId())){
                JdhStationServiceItemRel jdhStationServiceItemRel = providerStoreRepository.queryStationServiceItem(JdhStationServiceItemRel.builder().serviceItemId(Long.valueOf(syncServiceItemIndicatorCmd.getServiceItemId())).stationId(syncServiceItemIndicatorCmd.getStationId()).build());
                mqBody.put("stationServiceItemRel",JsonUtil.toJSONString(jdhStationServiceItemRel));
            }
        }

        try {
            //3.发mq
            log.info("syncServiceItemIndicator->mqBody={}",JsonUtil.toJSONString(mqBody));
            Message message = new Message(jdhServiceItemToYfTopic, JSON.toJSONString(mqBody), String.valueOf(serviceItem.getItemId()));
            reachStoreProducer.send(message);
        }catch (Exception e){
            log.info("同步项目指标失败,serviceItemId={}",syncServiceItemIndicatorCmd.getServiceItemId(),e);
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 解析文件
     *
     * @param imptResult
     * @param inputStream
     * @param parseFailList
     * @return
     */
    private List<ServiceItemExcelModel> getServiceItemList(ImportResult imptResult, InputStream inputStream, List<ServiceItemExcelModel> parseFailList) {
        ImportParams params = new ImportParams();
        params.setHeadRows(1);
        params.setNeedVerify(true);
        params.getLastOfInvalidRow();
        try {
            imptResult.setSuccess(Boolean.TRUE);
            ExcelImportResult<ServiceItemExcelModel> importResult = ExcelImportUtil.importExcelMore(inputStream, ServiceItemExcelModel.class, params);
            List<ServiceItemExcelModel> parseExcelFailList = importResult.getFailList();
            List<ServiceItemExcelModel> parseSuccessList = importResult.getList();
            //设置总条数
            imptResult.setTotalNum(importResult.isVerfiyFail() ? parseSuccessList.size() + parseExcelFailList.size() : parseSuccessList.size());
            //获取导入解析失败数据
            if (importResult.isVerfiyFail() && CollectionUtils.isNotEmpty(parseExcelFailList)) {
                for (ServiceItemExcelModel billDeadlineExcelModel : parseExcelFailList) {
                    parseFailList.add(billDeadlineExcelModel);
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("第" + billDeadlineExcelModel.getRowNum() + "行数据有字段为空，请确认" + billDeadlineExcelModel.getErrorMsg());
                    break;
                }
            }
            if (log.isDebugEnabled()) {
                log.error("解析excel集合：parseSuccessList={}", JSON.toJSONString(parseSuccessList));
                log.error("解析失败集合：parseFailList={}", JSON.toJSONString(parseFailList));
            }
            return parseSuccessList;
        } catch (Exception e) {
            log.error("项目信息导入excel信息异常", e);
            return new ArrayList<>();
        }
    }


    /**
     * 组装项目对象
     *
     * @param imptResult
     * @param listFromDoc
     * @param parseFailList
     */
    void packServiceItemExcelModelList(ImportResult imptResult, List<ServiceItemExcelModel> listFromDoc, List<ServiceItemExcelModel> parseFailList) {
        imptResult.setSuccess(Boolean.TRUE);

        Set<String> groups = new HashSet<>();
        groups.addAll(Arrays.stream(DictKeyEnum.values()).map(DictKeyEnum::getKey).collect(Collectors.toSet()));
        Map<String, List<DictInfo>> dic = dictRepository.queryMultiDictList(groups);
        if (MapUtils.isEmpty(dic)) {
            imptResult.setSuccess(Boolean.FALSE);
            imptResult.setMsg("查询词典为空");
            return;
        }

        List<Indicator> indicators = new ArrayList<>();
        List<JdhMaterialPackage> materials = new ArrayList<>();
        List<JdhAngelSkillDict> skillDictList = new ArrayList<>();

        Set<String> indicatorNameList = listFromDoc.stream().map(e -> {
            return Arrays.stream(e.getIndicatorIdListStr().split(",")).collect(Collectors.toList());
        }).flatMap(List::stream).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(indicatorNameList)) {
            ServiceIndicatorQueryContext indicatorQueryContext = ServiceIndicatorQueryContext.builder().indicatorNameList(indicatorNameList).build();
            indicators = jdhServiceIndicatorRepository.queryIndicatorList(indicatorQueryContext);
            if (CollectionUtils.isEmpty(indicators)) {
                imptResult.setSuccess(Boolean.FALSE);
                imptResult.setMsg("查询指标为空");
                return;
            }
        }
        Set<String> skillNameList = listFromDoc.stream().map(e -> {
            return Arrays.stream(e.getAngelSkillNameListStr().split(",")).collect(Collectors.toList());
        }).flatMap(List::stream).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(skillNameList)) {
            AngelSkillDictRepQuery repQuery = AngelSkillDictRepQuery.builder().angelSkillNameList(new ArrayList<>(skillNameList)).build();
            skillDictList = angelSkillDictRepository.findList(repQuery);
            if (CollectionUtils.isEmpty(skillDictList)) {
                imptResult.setSuccess(Boolean.FALSE);
                imptResult.setMsg("查询技能为空");
                return;
            }
        }
        Set<String> materialNameList = listFromDoc.stream().map(e -> {
            return Arrays.stream(e.getMaterialIdNeedListStr().split(",")).collect(Collectors.toList());
        }).flatMap(List::stream).collect(Collectors.toSet());
        materialNameList.addAll(listFromDoc.stream().filter(e -> StringUtils.isNotBlank(e.getMaterialIdListStr())).map(e -> {
            return Arrays.stream(e.getMaterialIdListStr().split(",")).collect(Collectors.toList());
        }).flatMap(List::stream).collect(Collectors.toSet()));
        if (CollectionUtils.isNotEmpty(materialNameList)) {
            List<JdhMaterialPackage> packageList = ProductServiceIndicatorConvertor.ins.packPackList(materialNameList);
            materials = jdhMaterialPackageRepository.queryList(packageList);
            if (CollectionUtils.isEmpty(materials)) {
                imptResult.setSuccess(Boolean.FALSE);
                imptResult.setMsg("查询耗材为空");
                return;
            }
        }
        for (ServiceItemExcelModel excelModel : listFromDoc) {
            //项目类型
            if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.ITEM_TYPE.getKey())) ||
                    !dic.get(DictKeyEnum.ITEM_TYPE.getKey()).stream().map(DictInfo::getLabel).collect(Collectors.toList()).contains(excelModel.getItemTypeStr())) {
                imptResult.setSuccess(Boolean.FALSE);
                imptResult.setMsg("不支持的项目类型:" + excelModel.getItemTypeStr());
                break;
            } else {
                excelModel.setItemType((Integer) dic.get(DictKeyEnum.ITEM_TYPE.getKey()).stream().filter(e -> Objects.equals(e.getLabel(), excelModel.getItemTypeStr())).findFirst().get().getValue());
            }
            //项目来源
            if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.ITEM_SOURCE.getKey())) ||
                    !dic.get(DictKeyEnum.ITEM_SOURCE.getKey()).stream().map(DictInfo::getLabel).collect(Collectors.toList()).contains(excelModel.getItemSourceStr())) {
                imptResult.setSuccess(Boolean.FALSE);
                imptResult.setMsg("不支持的项目来源:" + excelModel.getItemSourceStr());
                break;
            } else {
                excelModel.setItemSource((Integer) dic.get(DictKeyEnum.ITEM_SOURCE.getKey()).stream().filter(e -> Objects.equals(e.getLabel(), excelModel.getItemSourceStr())).findFirst().get().getValue());
            }
            //服务资源类型
            if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.SERVICE_RESOURCE_TYPE.getKey())) ||
                    !dic.get(DictKeyEnum.SERVICE_RESOURCE_TYPE.getKey()).stream().map(DictInfo::getLabel).collect(Collectors.toList()).contains(excelModel.getServiceResourceType())) {
                imptResult.setSuccess(Boolean.FALSE);
                imptResult.setMsg("不支持的服务资源类型:" + excelModel.getItemSourceStr());
                break;
            } else {
                excelModel.setServiceResourceType((Integer) dic.get(DictKeyEnum.SERVICE_RESOURCE_TYPE.getKey()).stream().filter(e -> Objects.equals(e.getLabel(), excelModel.getServiceResourceType())).findFirst().get().getValue());
            }
            //检测类
            //指标
            if (Objects.equals(excelModel.getItemType(), 2)) {
                if (StringUtils.isBlank(excelModel.getIndicatorIdListStr())) {
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("检测类项目指标必填:" + excelModel.getIndicatorIdListStr());
                    break;
                }
                //样本类型
                if (StringUtils.isBlank(excelModel.getSampleTypeStr())) {
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("检测类项目样本类型必填:" + excelModel.getSampleTypeStr());
                    break;
                }
                //样本类型
                if (StringUtils.isBlank(excelModel.getTestWayStr())) {
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("检测类检测方法学必填:" + excelModel.getTestWayStr());
                    break;
                }
            }
            if (StringUtils.isNotBlank(excelModel.getIndicatorIdListStr())) {
                List<String> indicatorNames = Arrays.stream(excelModel.getIndicatorIdListStr().split(",")).collect(Collectors.toList());
                excelModel.setIndicatorIdList(indicators.stream().filter(e -> indicatorNames.contains(e.getIndicatorName())).map(Indicator::getIndicatorId).collect(Collectors.toSet()));

            }
            if (StringUtils.isNotBlank(excelModel.getSampleTypeStr())) {
                if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.SAMPLE_TYPE.getKey())) ||
                        !dic.get(DictKeyEnum.SAMPLE_TYPE.getKey()).stream().map(DictInfo::getLabel).collect(Collectors.toList()).contains(excelModel.getSampleTypeStr())) {
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("不支持的样本类型:" + excelModel.getSampleTypeStr());
                    break;
                } else {
                    excelModel.setSampleType((Integer) dic.get(DictKeyEnum.SAMPLE_TYPE.getKey()).stream().filter(e -> Objects.equals(e.getLabel(), excelModel.getSampleTypeStr())).findFirst().get().getValue());
                }
            }
            if (StringUtils.isNotBlank(excelModel.getTestWayStr())) {
                if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.TEST_WAY.getKey())) ||
                        !dic.get(DictKeyEnum.TEST_WAY.getKey()).stream().map(DictInfo::getLabel).collect(Collectors.toList()).contains(excelModel.getTestWayStr())) {
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("不支持的检测方法学:" + excelModel.getTestWayStr());
                    break;
                } else {
                    excelModel.setTestWay((Integer) dic.get(DictKeyEnum.TEST_WAY.getKey()).stream().filter(e -> Objects.equals(e.getLabel(), excelModel.getTestWayStr())).findFirst().get().getValue());
                }
            }
            if (StringUtils.isNotBlank(excelModel.getSexStr())) {
                if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.SEX.getKey())) ||
                        !dic.get(DictKeyEnum.SEX.getKey()).stream().map(DictInfo::getLabel).collect(Collectors.toList()).contains(excelModel.getSexStr())) {
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("不支持的检测方法学:" + excelModel.getTestWayStr());
                    break;
                } else {
                    excelModel.setSex(dic.get(DictKeyEnum.SEX.getKey()).stream().filter(e -> Objects.equals(e.getLabel(), excelModel.getSexStr())).findFirst().get().getValue().toString());
                }
            }

            if (StringUtils.isNotBlank(excelModel.getCollectNotesTypeStr())) {
                if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.COLLECT_NOTES_TYPE.getKey())) ||
                        !dic.get(DictKeyEnum.COLLECT_NOTES_TYPE.getKey()).stream().map(DictInfo::getLabel).collect(Collectors.toList()).contains(excelModel.getCollectNotesTypeStr())) {
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("不支持的收费票据分类:" + excelModel.getTestWayStr());
                    break;
                } else {
                    excelModel.setCollectNotesType((Integer) dic.get(DictKeyEnum.COLLECT_NOTES_TYPE.getKey()).stream().filter(e -> Objects.equals(e.getLabel(), excelModel.getCollectNotesTypeStr())).findFirst().get().getValue());
                }
            }

            if (StringUtils.isNotBlank(excelModel.getAccountingSubjectTypeStr())) {
                if (CollectionUtils.isEmpty(dic.get(DictKeyEnum.ACCOUNTING_SUBJECT_TYPE.getKey())) ||
                        !dic.get(DictKeyEnum.ACCOUNTING_SUBJECT_TYPE.getKey()).stream().map(DictInfo::getLabel).collect(Collectors.toList()).contains(excelModel.getAccountingSubjectTypeStr())) {
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("不支持的收费票据分类:" + excelModel.getTestWayStr());
                    break;
                } else {
                    excelModel.setAccountingSubjectType((Integer) dic.get(DictKeyEnum.ACCOUNTING_SUBJECT_TYPE.getKey()).stream().filter(e -> Objects.equals(e.getLabel(), excelModel.getAccountingSubjectTypeStr())).findFirst().get().getValue());
                }
            }
            // 技能id
            if (StringUtils.isNotBlank(excelModel.getAngelSkillNameListStr())) {
                List<String> skillNames = Arrays.stream(excelModel.getAngelSkillNameListStr().split(",")).collect(Collectors.toList());
                excelModel.setAngelSkillCodeList(skillDictList.stream().filter(e -> skillNames.contains(e.getAngelSkillName())).map(JdhAngelSkillDict::getAngelSkillCode).collect(Collectors.toSet()));

            }
        }
    }

    /**
     * 组装项目对象
     *
     * @param imptResult
     * @param listFromDoc
     * @param parseFailList
     */
    void packCateId(ImportResult imptResult, List<SaveServiceIndicatorCmd> listFromDoc, List<ServiceIndicatorExcelModel> parseFailList) {
        imptResult.setSuccess(Boolean.TRUE);
        IndicatorCategoryQueryContext queryContext = IndicatorCategoryQueryContext.builder().indicatorCategoryType(2).build();
        List<IndicatorCategory> categoryList = itemDomainService.queryIndicatorCategory(queryContext);
        if (CollectionUtils.isEmpty(categoryList)) {
            imptResult.setSuccess(Boolean.FALSE);
            imptResult.setMsg("指标分类为空");
            return;
        }
        Long cateId1 = categoryList.stream().filter(e -> Objects.equals(e.getCategoryLevel(), 1)).collect(Collectors.toList()).get(0).getCategoryId();
        for (SaveServiceIndicatorCmd indicatorCmd : listFromDoc) {
            if (CollectionUtils.isEmpty(categoryList.stream().filter(e -> Objects.equals(e.getCategoryName(), indicatorCmd.getThirdIndicatorCategoryName())).collect(Collectors.toList()))) {
                imptResult.setSuccess(Boolean.FALSE);
                imptResult.setMsg("三级分类不存在,cateName=" + indicatorCmd.getThirdIndicatorCategoryName());
                break;
            }
            indicatorCmd.setFirstIndicatorCategory(cateId1);
            indicatorCmd.setThirdIndicatorCategory(categoryList.stream().filter(e -> Objects.equals(e.getCategoryName(), indicatorCmd.getThirdIndicatorCategoryName())).collect(Collectors.toList()).get(0).getCategoryId());
            indicatorCmd.setSecondIndicatorCategory(categoryList.stream().filter(e -> Objects.equals(e.getCategoryName(), indicatorCmd.getThirdIndicatorCategoryName())).collect(Collectors.toList()).get(0).getParentCategoryId());
        }
    }


    /**
     * 解析文件
     *
     * @param imptResult
     * @param inputStream
     * @param parseFailList
     * @return
     */
    private List<ServiceIndicatorExcelModel> getServiceIndicatorList(ImportResult imptResult, InputStream inputStream, List<ServiceIndicatorExcelModel> parseFailList) {
        ImportParams params = new ImportParams();
        params.setHeadRows(1);
        params.setNeedVerify(true);
        params.getLastOfInvalidRow();
        try {
            imptResult.setSuccess(Boolean.TRUE);
            ExcelImportResult<ServiceIndicatorExcelModel> importResult = ExcelImportUtil.importExcelMore(inputStream, ServiceIndicatorExcelModel.class, params);
            List<ServiceIndicatorExcelModel> parseExcelFailList = importResult.getFailList();
            List<ServiceIndicatorExcelModel> parseSuccessList = importResult.getList();
            //设置总条数
            imptResult.setTotalNum(importResult.isVerfiyFail() ? parseSuccessList.size() + parseExcelFailList.size() : parseSuccessList.size());
            //获取导入解析失败数据
            if (importResult.isVerfiyFail() && CollectionUtils.isNotEmpty(parseExcelFailList)) {
                for (ServiceIndicatorExcelModel billDeadlineExcelModel : parseExcelFailList) {
                    parseFailList.add(billDeadlineExcelModel);
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("第" + billDeadlineExcelModel.getRowNum() + "行数据有字段为空，请确认" + billDeadlineExcelModel.getErrorMsg());
                    break;
                }
            }
            if (log.isDebugEnabled()) {
                log.error("解析excel集合：parseSuccessList={}", JSON.toJSONString(parseSuccessList));
                log.error("解析失败集合：parseFailList={}", JSON.toJSONString(parseFailList));
            }
            return parseSuccessList;
        } catch (Exception e) {
            log.error("指标信息导入excel信息异常", e);
            return new ArrayList<>();
        }
    }


    /**
     * 解析文件
     *
     * @param imptResult
     * @param inputStream
     * @param parseFailList
     * @return
     */
    private List<ServiceIndicatorCateExcelModel> getServiceIndicatorCateList(ImportResult imptResult, InputStream inputStream, List<ServiceIndicatorCateExcelModel> parseFailList) {
        ImportParams params = new ImportParams();
        params.setHeadRows(1);
        params.setNeedVerify(true);
        params.getLastOfInvalidRow();
        try {
            imptResult.setSuccess(Boolean.TRUE);
            ExcelImportResult<ServiceIndicatorCateExcelModel> importResult = ExcelImportUtil.importExcelMore(inputStream, ServiceIndicatorCateExcelModel.class, params);
            List<ServiceIndicatorCateExcelModel> parseExcelFailList = importResult.getFailList();
            List<ServiceIndicatorCateExcelModel> parseSuccessList = importResult.getList();
            //设置总条数
            imptResult.setTotalNum(importResult.isVerfiyFail() ? parseSuccessList.size() + parseExcelFailList.size() : parseSuccessList.size());
            //获取导入解析失败数据
            if (importResult.isVerfiyFail() && CollectionUtils.isNotEmpty(parseExcelFailList)) {
                for (ServiceIndicatorCateExcelModel billDeadlineExcelModel : parseExcelFailList) {
                    parseFailList.add(billDeadlineExcelModel);
                    imptResult.setSuccess(Boolean.FALSE);
                    imptResult.setMsg("第" + billDeadlineExcelModel.getRowNum() + "行数据有字段为空，请确认" + billDeadlineExcelModel.getErrorMsg());
                    break;
                }
            }
            if (log.isDebugEnabled()) {
                log.error("解析excel集合：parseSuccessList={}", JSON.toJSONString(parseSuccessList));
                log.error("解析失败集合：parseFailList={}", JSON.toJSONString(parseFailList));
            }
            return parseSuccessList;
        } catch (Exception e) {
            log.error("指标信息导入excel信息异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 读取导入文件
     *
     */
    private void importProductServiceItemExcel(String logId, String filePath, String erp){
        try {
            MDC.put("PFTID", logId);
            InputStream inputStream = fileManageService.get(filePath);
            ImportProductServiceItemListenerContext context = new ImportProductServiceItemListenerContext();
            context.setErp(erp);
            ImportProductServiceItemListener readListener = new ImportProductServiceItemListener(context);
            EasyExcelFactory.read(inputStream, ImportServiceItem.class, readListener).sheet().doRead();
        } catch (Exception e) {
            log.error("ProductServiceItemApplicationImpl#importProductServiceItemExcel exception", e);
            throw new BusinessException(SystemErrorCode.SYSTEM_ERROR);
        } finally {
            String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.IMPORT_PRODUCT_ITEM_IMPORT_LOCK_KEY, erp);
            redisUtil.unLock(lockRedisKey);
        }
    }


    /**
     * 查询项目列表
     *
     * @param serviceItemIdSet
     * @return
     */
    @Override
    public List<ServiceItemDto> queryServiceItemList(Set<Long> serviceItemIdSet) {
        ServiceItemQuery itemQuery = ServiceItemQuery.builder().itemIds(serviceItemIdSet).build();
        List<ServiceItemDto> response = queryServiceItemList(itemQuery);
        return response;
    }

    @Override
    public List<ServiceItemDto> queryServiceItemList(ServiceItemExtQuery serviceItemExtQuery) {
        return queryServiceItemList(serviceItemExtQuery.getItemIds());
    }

    private void checkParam(ProductBizItemAddCmd productBizItemAddCmd) {
        //1.校验名称
        JdhBizItemReqQuery jdhBizItemReqQuery = new JdhBizItemReqQuery();
        jdhBizItemReqQuery.setBizItemName(productBizItemAddCmd.getItemName());
        jdhBizItemReqQuery.setBizItemQueryAccurate(Boolean.TRUE);
        jdhBizItemReqQuery.setBusinessModeCode(productBizItemAddCmd.getBusinessModeCode());
        jdhBizItemReqQuery.setServiceType(productBizItemAddCmd.getServiceType());
        List<JdhBizItem> jdhBizItems = jdhBizItemRepository.queryList(jdhBizItemReqQuery);
        if (CollectionUtils.isNotEmpty(jdhBizItems)) {
            //新增
            if (Objects.isNull(productBizItemAddCmd.getItemId())) {
                //名称重复
                throw new BusinessException(ProductErrorCode.PRODUCT_SELF_BIZ_ITEM_NAME_REPEAT);
            } else {
                //更新
                JdhBizItem jdhBizItem = jdhBizItems.stream().filter(p -> Objects.equals(productBizItemAddCmd.getItemId(), p.getBizItemId())).findFirst().orElse(null);
                if (Objects.isNull(jdhBizItem)) {
                    //名称重复
                    throw new BusinessException(ProductErrorCode.PRODUCT_SELF_BIZ_ITEM_NAME_REPEAT);
                }
            }
        }

        //2.校验指标数量
        List<Long> indicatorList = productBizItemAddCmd.getIndicatorList();
        JdhStandardItem jdhStandardItem = jdhStandardItemRepository.find(JdhStandardItemIdentifier.builder().itemId(productBizItemAddCmd.getInspectionItemId()).build());
        if (Objects.isNull(jdhStandardItem)) {
            throw new BusinessException(ProductErrorCode.PRODUCT_STANDARD_ITEM_NULL);
        }
        //TOC 项目 需要校验指标
        if (Objects.equals(ApplySceneEnum.TO_C.getScene(), productBizItemAddCmd.getApplyScene())) {
            if (Objects.nonNull(jdhStandardItem.getIndicatorNum())) {
                if (!Objects.equals(jdhStandardItem.getIndicatorNum(), indicatorList.size())) {
                    throw ExceptionFactory.getArgumentsException(ProductErrorCode.PRODUCT_INDICATOR_NUM_ERROR, String.valueOf(jdhStandardItem.getIndicatorNum()));
                }
            }
        }
    }
}
