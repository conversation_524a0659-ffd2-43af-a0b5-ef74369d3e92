package com.jdh.o2oservice.application.angelpromise.service;

import com.jdh.o2oservice.core.domain.angelpromise.context.AngelShipCallBackContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipCancelCodeStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelTask;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.export.angelpromise.cmd.*;
import com.jdh.o2oservice.export.angelpromise.dto.*;
import com.jdh.o2oservice.export.angelpromise.query.*;

import java.util.List;

/**
 * @InterfaceName:AngelWorkApplication
 * @Description: 服务者工单application
 * @Author: yaoqinghai
 * @Date: 2024/4/18 16:54
 * @Vserion: 1.0
 **/
public interface AngelWorkApplication {

    /**
     * 创建工单
     *
     * @param jdhAngelWorkSaveCmd
     * @return
     */
    AngelWorkCreateResultDto createAngelWork(JdhAngelWorkSaveCmd jdhAngelWorkSaveCmd);

    /**
     * 检查工单是否绑定了条码
     *
     * @param angelCheckBarCodeCmd
     * @return
     */
    Boolean submitBindBarCode(AngelCheckBarCodeCmd angelCheckBarCodeCmd);


    /**
     * 护士呼叫选择配送方式
     *
     * @param angelWorkCreateShipCmd
     * @return
     */
    Boolean createDeliveryShip(AngelWorkCreateShipCmd angelWorkCreateShipCmd);

    /**
     * 接收达达状态回调
     *
     * @param callBackRequest
     */
    Boolean shipStatusCallback(ShipInfoForCallBackRequest callBackRequest);

    /**
     * 接收运力供应商状态回调
     *
     * @param callBackRequest
     */
    Boolean shipStatusCallback(AngelShipCallBackContext callBackRequest);

    /**
     * 取消运单
     * @param enumQuery
     * @return
     */
    Boolean cancelShip(AngelWorkCancelShipCmd enumQuery);

    /**
     * 取消工单下的运单，并更新工单状态
     * @param angelWorkCancelShipCmd angelWorkCancelShipCmd
     * @return
     */
    Boolean cancelShipFormWork(AngelWorkCancelShipCmd angelWorkCancelShipCmd, AngelShipCancelCodeStatusEnum angelShipCancelCodeStatusEnum);

    /**
     * 重新呼叫工单下的运单，并更新工单状态
     * @param angelWorkCancelShipCmd angelWorkCancelShipCmd
     * @return
     */
    Boolean reCallShipFormWork(AngelWorkReCallShipCmd angelWorkCancelShipCmd, AngelShipCancelCodeStatusEnum angelShipCancelCodeStatusEnum);

    /**
     * 统计工单数量
     *
     * @param angelWorkCountQuery
     * @return
     */
    AngelWorkCountDto countAngelWorkCount(AngelWorkCountQuery angelWorkCountQuery);

    /**
     * 查询护士实时位置
     *
     * @param angelWork
     * @return
     */
    AngelRealLocationDto queryAngelRealLocation(AngelWork angelWork);

    /**
     * 查询护士轨迹
     *
     * @param angelRealLocationQuery
     * @return
     */
    AngelRealTrackDto queryAngelRealTrack(AngelRealLocationQuery angelRealLocationQuery);

    /**
     * 运营端查询工单明细
     *
     * @param detailForManQuery
     * @return
     */
    AngelWorkDetailForManDto queryAngelWorkForMan(AngelWorkDetailForManRequest detailForManQuery);

    /**
     * 服务完成
     *
     * @param angelCheckBarCodeCmd
     * @return
     */
    Boolean finishService(AngelCheckBarCodeCmd angelCheckBarCodeCmd);

    /**
     * 确认全部运单已配送
     *
     * @param angelCheckBarCodeCmd
     * @return
     */
    Boolean confirmTransferOrderDeliver(AngelCheckBarCodeCmd angelCheckBarCodeCmd);

    /**
     * 查询工单分配的实验室信息
     *
     * @param angelWorkQuery
     * @return
     */
    List<AngelWorkCourierFloorDto> queryWorkStationList(AngelWorkQuery angelWorkQuery);

    /**
     * 查询轨迹
     *
     * @param angelTrackQuery 骑手轨迹查询条件
     */
    AngelTrackDto getTransferTrack(AngelTrackQuery angelTrackQuery);

    /**
     * 作废冻结服务者工单
     *
     * @param angelTaskList
     * @return
     */
    Boolean angelWorkFreeze(List<AngelTask> angelTaskList);

    /**
     * 作废服务者工单
     *
     * @param angelTaskList
     * @return
     */
    Boolean angelWorkInvalid(List<AngelTask> angelTaskList);

    Boolean delayTest(Long id);

    /**
     *
     * @param promiseId
     * @return
     */
    AngelWorkDto getAngelWorkByPromiseId(Long promiseId);

    /**
     * 修改工单时间
     *
     * @param jdhAngelWorkModifyDateCmd
     * @return
     */
    Boolean modifyAngelWorkStartEndDate(JdhAngelWorkModifyDateCmd jdhAngelWorkModifyDateCmd);

    /**
     * 查询工单对应的运单列表
     * @param angelShipDBQuery
     * @return
     */
    List<AngelShipDto> queryAngelShipList(AngelShipDBQuery angelShipDBQuery);

    /**
     *
     * @param angelShipDBQuery
     * @return
     */
    AngelShipDto getAngelShipByShipInfo(AngelShipDBQuery angelShipDBQuery);

    /**
     *
     * @param angelShipQuery
     * @return
     */
    AngelShipDto getShipOrderDetail(AngelShipQuery angelShipQuery);

    /**
     * 根据运单状态给护士结算
     *
     * @param angelWork
     */
    Boolean angelSettleByShip(AngelWork angelWork);
    /**
     * 根据promiseId查询运单详情
     * @param getDetailByPromiseIdQuery 入参
     * @return AngelShipDto
     */
    AngelShipDto getDetailByPromiseId(GetDetailByPromiseIdQuery getDetailByPromiseIdQuery);

    /**
     * 查询工单列表
     * @param angelWorkQuery
     * @return
     */
    List<AngelWorkDto> getAngelWorkList(AngelWorkQuery angelWorkQuery);

}
