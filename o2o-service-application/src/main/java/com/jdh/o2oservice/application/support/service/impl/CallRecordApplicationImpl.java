package com.jdh.o2oservice.application.support.service.impl;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.client.producer.MessageProducer;
import com.jd.jmq.client.springboot.annotation.JmqProducer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.jdh.o2oservice.application.support.service.CallRecordApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.ContentTypeEnum;
import com.jdh.o2oservice.base.enums.VoiceTypeEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.TdeClientUtil;
import com.jdh.o2oservice.core.domain.angel.model.JdhAngel;
import com.jdh.o2oservice.core.domain.angel.repository.db.AngelRepository;
import com.jdh.o2oservice.core.domain.angel.repository.query.JdhAngelRepQuery;
import com.jdh.o2oservice.core.domain.angel.rpc.NethpAngelRpc;
import com.jdh.o2oservice.core.domain.angel.rpc.bo.NethpBaseDoctorInfoClientBo;
import com.jdh.o2oservice.core.domain.angel.rpc.param.NethpBaseDoctorInfoClientParam;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseFileBizTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkAggregateEnum;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.product.bo.UserAddressDetailBO;
import com.jdh.o2oservice.core.domain.product.service.ProductDomainService;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromisePatient;
import com.jdh.o2oservice.core.domain.promise.model.PromiseStation;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.query.PromiseRepQuery;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.core.domain.support.securitynumber.enums.SecurityNumberBizCallTypeEnum;
import com.jdh.o2oservice.core.domain.support.securitynumber.model.CallRecord;
import com.jdh.o2oservice.core.domain.support.securitynumber.repository.CallRecordRepository;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.ISecurityNumberServiceRpc;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo.CallRecordingBO;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo.SecurityNumberBindAxbResultBO;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.bo.SecurityNumberCallRecordBO;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.param.SecurityNumberBindAxbParam;
import com.jdh.o2oservice.core.domain.support.securitynumber.rpc.param.SecurityNumberReleaseRpcParam;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.export.support.command.ParseVoiceCmd;
import com.jdh.o2oservice.export.support.command.PutHttpFileCommand;
import com.jdh.o2oservice.export.support.command.SecurityNumberBindAxbCmd;
import com.jdh.o2oservice.export.support.dto.*;
import com.jdh.o2oservice.export.support.query.QueryCallRecordRequest;
import com.jdh.o2oservice.export.support.query.SyncAngelPhoneRequest;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhAngelPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhAngelPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description 外呼服务
 * @Date 2024/12/19 下午5:32
 * <AUTHOR>
 **/
@Component
@Slf4j
public class CallRecordApplicationImpl implements CallRecordApplication {

    @Resource
    private CallRecordRepository callRecordRepository;

    @Resource
    private AngelRepository angelRepository;

    @Resource
    private AngelWorkRepository angelWorkRepository;

    @Resource
    private PromiseRepository promiseRepository;

    @Resource
    private ISecurityNumberServiceRpc iSecurityNumberServiceRpc;

    @Resource
    private FileManageService fileManageService;

    @Resource
    private JdhAddressRpc jdhAddressRpc;

    @Resource
    private TdeClientUtil tdeClientUtil;

    @Resource
    private JdOrderRepository orderRepository;

    @JmqProducer(name = "reachStoreProducer")
    private MessageProducer reachStoreProducer;

    @Value("${topics.jdhReachStoreConsumer.callRecordingRetrievalTopic}")
    private String securityNumberTopic;

    @Value("${topics.jdhReachStoreConsumer.callBindReleaseTopic}")
    private String callBindReleaseTopic;

    @Resource
    private DuccConfig duccConfig;

    @Resource
    private Cluster jimClient;
    @Resource
    private FileManageApplication fileManageApplication;
    @Value("${topics.reach.file.submit.topic}")
    private String voiceTopic;

    private static String CALL_BIND_CACHE_KEY = "O2O:CALL:BIND:REDIS:KEY:{0}:{1}:{2}";

    private static String CALL_BIND_SECRET_NO_CACHE_KEY = "O2O:CALL:BIND:SECRET:NO:REDIS:KEY:{0}:{1}";

    @Resource
    private JdhAngelPoMapper jdhAngelPoMapper;

    @Resource
    private NethpAngelRpc nethpAngelRpc;

    @Resource
    private ProductDomainService productDomainService;

    @Resource
    private SkuInfoRpc skuInfoRpc;

    /**
     * 虚拟号绑定
     * @param cmd
     * @return
     */
    @Override
    public SecurityNumberBindAxbResultDto bindAxb(SecurityNumberBindAxbCmd cmd) {
        log.info("CallRecordApplicationImpl bindAxb cmd={}", JSON.toJSONString(cmd));
        // 查询履约单
        PromiseRepQuery promiseRepQuery = new PromiseRepQuery();
        promiseRepQuery.setPromiseId(cmd.getPromiseId());
        List<JdhPromise> promiseList = promiseRepository.findList(promiseRepQuery);
        log.info("CallRecordApplicationImpl bindAxb promiseList={}", JSON.toJSONString(promiseList));
        if (CollectionUtils.isEmpty(promiseList)){
            throw new BusinessException(BusinessErrorCode.JDH_PROMISE_NOT_EXIST);
        }

        // 查询服务者工单
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(cmd.getPromiseId());
        List<Integer> angelWorkStatusList = Arrays.asList(AngelWorkStatusEnum.RECEIVED.getType(), AngelWorkStatusEnum.WAIT_SERVICE.getType()
                , AngelWorkStatusEnum.SERVICING.getType(), AngelWorkStatusEnum.SERVICED.getType(), AngelWorkStatusEnum.DELIVERING.getType());
        angelWorkDBQuery.setStatusList(angelWorkStatusList);
        List<AngelWork> angelWorks = angelWorkRepository.findList(angelWorkDBQuery);
        log.info("CallRecordApplicationImpl bindAxb angelWorks={}", JSON.toJSONString(angelWorks));
        if (CollectionUtils.isEmpty(angelWorks)){
            throw new BusinessException(BusinessErrorCode.CALL_ANGEL_ERROR);
        }
        JdhPromise promise = promiseList.get(0);
        AngelWork angelWork = angelWorks.get(0);

        if (duccConfig.getBindAxbCallAuthSwitch()){
            if (!cmd.getUserPin().equals(promise.getUserPin()) && !cmd.getUserPin().equals(angelWork.getAngelPin())){
                log.error("CallRecordApplicationImpl bindAxb ultra vires");
                throw new BusinessException(BusinessErrorCode.VIRTUAL_NUMBER_FULL_ERROR);
            }
        }

        // 获取商家店铺id
        String venderId = getVenderId(promise);
        // 查询护士
        JdhAngelRepQuery angelRepQuery = new JdhAngelRepQuery();
        angelRepQuery.setAngelId(Long.valueOf(angelWork.getAngelId()));
        List<JdhAngel> angelList = angelRepository.findList(angelRepQuery);
        log.info("CallRecordApplicationImpl bindAxb angelList={}", JSON.toJSONString(angelList));
        if (CollectionUtils.isEmpty(angelWorks)){
            throw new BusinessException(BusinessErrorCode.ANGEL_NOT_EXIST);
        }
        JdhAngel angel = angelList.get(0);

        // 构建虚拟号绑定参数
        SecurityNumberBindAxbParam bindAxbParam = buildBindAxbParam(cmd, promise, angelWork, venderId, angel);
        if (bindAxbParam.getPhoneNoA().equals(bindAxbParam.getPhoneNoB())){
            throw new BusinessException(BusinessErrorCode.VIRTUAL_NUMBER_A_B_EQUALS_ERROR);
        }
        String cacheKey = MessageFormat.format(CALL_BIND_CACHE_KEY, bindAxbParam.getOrderId(), bindAxbParam.getPhoneNoA(), bindAxbParam.getPhoneNoB());
        String cacheValue = jimClient.get(cacheKey);
        log.info("CallRecordApplicationImpl bindAxb cacheValue={}", cacheValue);
        if (StringUtils.isNotBlank(cacheValue)){
            log.info("CallRecordApplicationImpl bindAxb cacheValue exist");
            return JSON.parseObject(cacheValue, SecurityNumberBindAxbResultDto.class);
        }

        String cacheKey2 = MessageFormat.format(CALL_BIND_CACHE_KEY, bindAxbParam.getOrderId(), bindAxbParam.getPhoneNoB(), bindAxbParam.getPhoneNoA());
        String cacheValue2 = jimClient.get(cacheKey2);
        log.info("CallRecordApplicationImpl bindAxb cacheValue2={}", cacheValue2);
        if (StringUtils.isNotBlank(cacheValue2)){
            log.info("CallRecordApplicationImpl bindAxb cacheValue2 exist");
            return JSON.parseObject(cacheValue2, SecurityNumberBindAxbResultDto.class);
        }

        // 虚拟号绑定
        SecurityNumberBindAxbResultBO bindAxbResult = iSecurityNumberServiceRpc.bindAxb(bindAxbParam);
        SecurityNumberBindAxbResultDto result = new SecurityNumberBindAxbResultDto();
        BeanUtils.copyProperties(bindAxbResult, result);

        JSONObject securityNumberConfig = JSON.parseObject(duccConfig.getSecurityNumberConfig());
        jimClient.setEx(cacheKey, JSON.toJSONString(result), securityNumberConfig.getLong("expirationMinute"), TimeUnit.MINUTES);
        jimClient.setEx(cacheKey2, JSON.toJSONString(result), securityNumberConfig.getLong("expirationMinute"), TimeUnit.MINUTES);

        String secretNoCacheKey = MessageFormat.format(CALL_BIND_SECRET_NO_CACHE_KEY, bindAxbParam.getOrderId(), result.getSecretNo());
        jimClient.setEx(secretNoCacheKey, JSON.toJSONString(bindAxbParam), 24, TimeUnit.HOURS);

        try {
            // mq消息发送-延迟队列-虚拟号资源释放
            SecurityNumberReleaseDto sendMqDto = new SecurityNumberReleaseDto();
            sendMqDto.setOrderId(cmd.getPromiseId());
            sendMqDto.setBindId(result.getBindId());
            Message message = new Message(callBindReleaseTopic, JSON.toJSONString(sendMqDto), UUID.randomUUID().toString());
            reachStoreProducer.send(message);
            log.info("CallRecordApplicationImpl bindAxb send mq sendMqDto={}", JSON.toJSONString(sendMqDto));
        } catch (JMQException e) {
            log.error("CallRecordApplicationImpl bindAxb send error e", e);
        }
        return result;
    }

    /**
     * 商家店铺id
     * @param promise
     * @return
     */
    private String getVenderId(JdhPromise promise) {
        String venderId = "1000477238";
        try {
            JdOrder order = orderRepository.find(new JdOrderIdentifier(Long.valueOf(promise.getSourceVoucherId())));
            log.info("CallRecordApplicationImpl getVenderId order={}", JSON.toJSONString(order));
            if (order != null && StringUtils.isNotBlank(order.getVenderId())){
                venderId = order.getVenderId();
                log.info("CallRecordApplicationImpl order venderId={}", venderId);
            }else {
                if (CollectionUtils.isEmpty(promise.getServices())){
                    return venderId;
                }
                Long serviceId = promise.getServices().get(0).getServiceId();
                RpcSkuBO crsSku = skuInfoRpc.getCrsSkuBoBySkuId(String.valueOf(serviceId));
                if (crsSku != null && crsSku.getVenderId() != null){
                    venderId = String.valueOf(crsSku.getVenderId());
                    log.info("CallRecordApplicationImpl sku venderId={}", venderId);
                }
            }
        } catch (Exception e) {
            log.error("CallRecordApplicationImpl getVenderId error e", e);
        }
        return venderId;
    }

    /**
     * 构建虚拟号绑定参数
     * @param cmd
     * @param promise
     * @param angelWork
     * @param venderId
     * @param angel
     * @return
     */
    private SecurityNumberBindAxbParam buildBindAxbParam(SecurityNumberBindAxbCmd cmd, JdhPromise promise, AngelWork angelWork, String venderId, JdhAngel angel) {
        SecurityNumberBindAxbParam bindAxbParam = new SecurityNumberBindAxbParam();
        if (SecurityNumberBizCallTypeEnum.APPOINTMENT_TO_ANGEL.getCode().equals(cmd.getBizCallType())){
            // 预约人打给服务者
            if (StringUtils.isNotBlank(cmd.getEncryptMobile())){
                bindAxbParam.setPhoneNoA(decryptPhone(cmd.getEncryptMobile()));
            }else {
                bindAxbParam.setPhoneNoA(getAppointmentPhone(promise));
            }
            bindAxbParam.setPhoneNoB(angel.getPhone());
        }else if (SecurityNumberBizCallTypeEnum.SERVICED_TO_ANGEL.getCode().equals(cmd.getBizCallType())) {
            // 被服务人打给服务者
            if (StringUtils.isNotBlank(cmd.getEncryptMobile())){
                bindAxbParam.setPhoneNoA(decryptPhone(cmd.getEncryptMobile()));
            }else {
                List<JdhPromisePatient> promisePatientList = promise.getPatients().stream().filter(p -> cmd.getPromisePatientId()
                        .equals(p.getPromisePatientId())).collect(Collectors.toList());
                bindAxbParam.setPhoneNoA(promisePatientList.get(0).getPhoneNumber().getPhone());
            }
            bindAxbParam.setPhoneNoB(angel.getPhone());
        }else if (SecurityNumberBizCallTypeEnum.ANGEL_TO_APPOINTMENT.getCode().equals(cmd.getBizCallType())){
            // 服务者打给预约人
            bindAxbParam.setPhoneNoA(angel.getPhone());
            bindAxbParam.setPhoneNoB(getAppointmentPhone(promise));
        }else if (SecurityNumberBizCallTypeEnum.ANGEL_TO_SERVICED.getCode().equals(cmd.getBizCallType())){
            // 服务者打给被服务人
            bindAxbParam.setPhoneNoA(angel.getPhone());
            List<JdhPromisePatient> promisePatientList = promise.getPatients().stream().filter(p -> cmd.getPromisePatientId()
                    .equals(p.getPromisePatientId())).collect(Collectors.toList());
            bindAxbParam.setPhoneNoB(promisePatientList.get(0).getPhoneNumber().getPhone());
        }

        // 虚拟号配置
        JSONObject obj = JSON.parseObject(duccConfig.getSecurityNumberConfig());
        DateTime offset = DateUtil.offset(DateUtil.date(), DateField.MINUTE, obj.getInteger("expirationMinute"));
        String expiration = DateUtil.format(new Date(offset.getTime()), "yyyy-MM-dd HH:mm:ss");
        bindAxbParam.setExpiration(expiration);
        bindAxbParam.setBuId(venderId);
        bindAxbParam.setChargingId(promise.getSourceVoucherId());
        bindAxbParam.setOrderId(cmd.getPromiseId());

        // 虚拟号业务数据
        CallBillingUserDataDto userData = new CallBillingUserDataDto();
        userData.setUserPin(promise.getUserPin());
        userData.setAngelPin(angelWork.getAngelPin());
        userData.setAngelId(angel.getAngelId());
        userData.setPromiseId(promise.getPromiseId());
        userData.setSourceVoucherId(promise.getSourceVoucherId());
        userData.setBizCallType(cmd.getBizCallType());
        userData.setBuId(venderId);
        userData.setPhoneNoA(bindAxbParam.getPhoneNoA());
        userData.setPhoneNoB(bindAxbParam.getPhoneNoB());
        userData.setBindDate(new Date());
        bindAxbParam.setUserData(JSON.toJSONString(userData));
        log.info("CallRecordApplicationImpl buildBindAxbParam bindAxbParam={}", JSON.toJSONString(bindAxbParam));
        return bindAxbParam;
    }

    private String decryptPhone(String encryptMobile) {
        try {
            PhoneNumber phoneNumber = new PhoneNumber(encryptMobile);
            phoneNumber.decrypt();
            return phoneNumber.getPhone();
        } catch (Exception e) {
            log.error("CallRecordApplicationImpl decryptPhone decrypt error e", e);
            try {
                String decodedMobile = URLDecoder.decode(encryptMobile, "UTF-8");
                log.info("CallRecordApplicationImpl decryptPhone decodedMobile={}",decodedMobile);
                PhoneNumber phoneNumber = new PhoneNumber(decodedMobile);
                phoneNumber.decrypt();
                return phoneNumber.getPhone();
            } catch (Exception ex) {
                log.error("CallRecordApplicationImpl decryptPhone decodedMobile error e", ex);
                return "";
            }
        }
    }

    /**
     * 获取预约人电话
     * @param promise
     * @return
     */
    public String getAppointmentPhone(JdhPromise promise){
        PromiseStation store = promise.getStore();
        String userPin = promise.getUserPin();
        String phone = promise.getAppointmentPhone();
        try {
            if (StringUtils.isNotBlank(phone)){
                return phone;
            }
            List<AddressDetailBO> addressList = jdhAddressRpc.queryAddressList(userPin);
            log.info("CallRecordApplicationImpl getAppointmentPhone userPin={},addressList={}", userPin, JSON.toJSONString(addressList));
            if (StringUtils.isNotBlank(store.getStoreId())){
                addressList = addressList.stream().filter(a -> Long.valueOf(store.getStoreId()).equals(a.getAddressId())).collect(Collectors.toList());
            }else {
                addressList = addressList.stream().filter(a -> store.getStoreAddr().equals(a.getFullAddress())).collect(Collectors.toList());
            }
            log.info("CallRecordApplicationImpl getAppointmentPhone filter addressList={}", JSON.toJSONString(addressList));
            String appointmentPhone = tdeClientUtil.decrypt(addressList.get(0).getEncryptMobile());
            log.info("CallRecordApplicationImpl getAppointmentPhone appointmentPhone={}", appointmentPhone);
            return appointmentPhone;
        } catch (Exception e) {
            try {
                UserAddressDetailBO userAddressDetail = productDomainService.getUserLastAddress(userPin, null);
                log.info("CallRecordApplicationImpl getAppointmentPhone userAddressDetail={}", JSON.toJSONString(userAddressDetail));
                return userAddressDetail.getMobile();
            } catch (Exception exception) {
                log.error("CallRecordApplicationImpl getAppointmentPhone error e", e);
                throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
            }
        }
    }

    /**
     * 接收话单
     * @param request
     */
    @Override
    public Boolean receiveCallBilling(CallBillingNotificationDto request) {
        log.info("CallRecordApplicationImpl receiveCallBilling request={}", JSON.toJSONString(request));
        CallRecord existCallRecord = callRecordRepository.queryCallRecordByCallId(request.getCallId());
        if (Objects.nonNull(existCallRecord)){
            log.info("CallRecordApplicationImpl receiveCallBilling existCallRecord exist");
            return false;
        }

        // 新增外呼记录
        CallRecord callRecord = new CallRecord();
        BeanUtils.copyProperties(request, callRecord);
        callRecord.setConnectedStatus(0);
        if (request.getCallDuration() != null && new BigDecimal(request.getCallDuration()).compareTo(BigDecimal.ZERO)  > 0){
            callRecord.setConnectedStatus(1);
            callRecord.setCallTime(new Date(request.getCallTime()));
            callRecord.setStartTime(new Date(request.getStartTime()));
            callRecord.setFinishTime(new Date(request.getFinishTime()));
            callRecord.setCallDuration(Long.valueOf(request.getCallDuration()));
        }
        CallBillingUserDataDto userData = request.getUserDataDto();
        callRecord.setUserPin(userData.getUserPin());
        callRecord.setAngelPin(userData.getAngelPin());
        callRecord.setPromiseId(userData.getPromiseId());
        callRecord.setSourceVoucherId(userData.getSourceVoucherId());
        callRecord.setBizCallType(userData.getBizCallType());
        log.info("CallRecordApplicationImpl buildCreateCallRecord callRecord={}", JSON.toJSONString(callRecord));
        callRecordRepository.save(callRecord);

        // mq消息发送-延迟队列-录音文件拉取
        try {
            if (callRecord.getConnectedStatus() == 1){
                CallRecordingRetrievalDto sendMqDto = new CallRecordingRetrievalDto();
                sendMqDto.setPromiseId(callRecord.getPromiseId());
                sendMqDto.setBindId(request.getBindId());
                sendMqDto.setCallId(request.getCallId());
                Message message = new Message(securityNumberTopic, JSON.toJSONString(sendMqDto), UUID.randomUUID().toString());
                reachStoreProducer.send(message);
                log.info("CallRecordApplicationImpl receiveCallBilling send mq sendMqDto={}", JSON.toJSONString(sendMqDto));
            }
        } catch (JMQException e) {
            log.error("CallRecordApplicationImpl receiveCallBilling send error e", e);
        }
        return true;
    }

    /**
     * 录音调取
     * @param request
     */
    @Override
    public Boolean callRecordingRetrieval(CallRecordingRetrievalDto request) {
        log.info("CallRecordApplicationImpl callRecordingRetrieval request={}", JSON.toJSONString(request));
        // 查询履约单
        PromiseRepQuery promiseRepQuery = new PromiseRepQuery();
        promiseRepQuery.setPromiseId(request.getPromiseId());
        List<JdhPromise> promiseList = promiseRepository.findList(promiseRepQuery);
        log.info("CallRecordApplicationImpl callRecordingRetrieval promiseList={}", JSON.toJSONString(promiseList));
        if (CollectionUtils.isEmpty(promiseList)){
            return false;
        }
        // 获取商家店铺id
        String venderId = getVenderId(promiseList.get(0));
        // 录音调取
        SecurityNumberCallRecordBO callRecordBO = iSecurityNumberServiceRpc.queryCallRecord(venderId, request.getPromiseId());
        if (Objects.isNull(callRecordBO) || CollectionUtils.isEmpty(callRecordBO.getCallRecordings())){
            return false;
        }
        for (CallRecordingBO callRecording : callRecordBO.getCallRecordings()) {
            try {
                if (StringUtils.isBlank(callRecording.getRecordingFileDownloadUrl())){
                    log.info("CallRecordApplicationImpl callRecordingRetrieval recordingFileDownloadUrl empty callRecording={}",JSON.toJSONString(callRecording));
                    continue;
                }
                CallRecord callRecord = callRecordRepository.queryCallRecordByCallId(callRecording.getCallId());
                if (StringUtils.isNotBlank(callRecord.getAudioUrl())){
                    continue;
                }
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();

                // 上传文件
                AngelWork angelWork = angelWorkRepository.findLastAngelWork(request.getPromiseId());
                PutHttpFileCommand command = new PutHttpFileCommand();
                command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
                command.setFileBizType(AngelPromiseFileBizTypeEnum.CALL_SOUND.getBizType());
                command.setSuffix(ContentTypeEnum.MP3.getSuffix());
                command.setContentType(ContentTypeEnum.MP3.getValue());
                command.setFolder(FileManageServiceImpl.FolderPathEnum.CALL_RECORD.getPath());
                command.setHttpFileUrl(callRecording.getRecordingFileDownloadUrl());
                command.setIsPublic(Boolean.TRUE);
                command.setUserPin(angelWork.getAngelPin());
                JdhFile jdhFile = fileManageApplication.putHttpFile( command );
                stopWatch.stop();
                log.info("CallRecordApplicationImpl callRecordingRetrieval callId={}, jdhFile={}, 总运行时间（毫秒）={}", callRecording.getCallId()
                        , JSON.toJSONString(jdhFile), stopWatch.getTotalTimeMillis());
                CallBillingUserDataDto userData = JSON.parseObject(callRecord.getUserData(), CallBillingUserDataDto.class);
                userData.setRecordingFileDownloadUrl(callRecording.getRecordingFileDownloadUrl());
                callRecord.setUserData(JSON.toJSONString(userData));
                callRecord.setAudioUrl(jdhFile.getFilePath());
                callRecord.setUpdateTime(new Date());
                callRecordRepository.updateById(callRecord);


                CompletableFuture.runAsync(()->{
                    try {
                        List<Long> fileIds = Lists.newArrayList(jdhFile.getFileId());
                                ParseVoiceCmd voiceCmd = ParseVoiceCmd.builder()
                                .domianCode(DomainEnum.ANGEL_PROMISE.getCode())
                                .aggregateCode(AngelWorkAggregateEnum.WORK.getCode())
                                .aggregateId(String.valueOf(angelWork.getWorkId()))
                                .fileIds(fileIds)
                                .voiceType(VoiceTypeEnum.ANGEL_CALL_SOUND.getType())
                                .build();
                        log.info("CallRecordApplicationImpl -> sendMessage cmd:{}", JSON.toJSONString(voiceCmd));
                        Message message = new Message(voiceTopic, JSON.toJSONString(voiceCmd), String.valueOf(angelWork.getWorkId()));
                        log.info("CallRecordApplicationImpl-> sendMessage message={}", JSON.toJSONString(message));
                        reachStoreProducer.send(message);
                    }catch (Exception e){
                        log.error("CallRecordApplicationImpl-> sendMessage error:{}", e.getMessage());
                    }
                });


                String cacheKey = MessageFormat.format(CALL_BIND_CACHE_KEY, callRecord.getPromiseId(), userData.getPhoneNoA(), userData.getPhoneNoB());
                jimClient.del(cacheKey);

                String cacheKe2 = MessageFormat.format(CALL_BIND_CACHE_KEY, callRecord.getPromiseId(), userData.getPhoneNoB(), userData.getPhoneNoA());
                jimClient.del(cacheKe2);
            } catch (Exception e) {
                log.error("CallRecordApplicationImpl callRecordingRetrieval error e", e);
            }
        }

        // 虚拟号解绑
        SecurityNumberReleaseRpcParam releaseParam = new SecurityNumberReleaseRpcParam();
        releaseParam.setOrderId(request.getPromiseId());
        releaseParam.setBindId(request.getBindId());
        iSecurityNumberServiceRpc.release(releaseParam);
        return true;
    }

    /**
     * 外呼记录列表
     * @param request
     * @return
     */
    @Override
    public List<CallRecordDto> queryCallRecordList(QueryCallRecordRequest request) {
        log.info("CallRecordApplicationImpl queryCallRecordList request={}", JSON.toJSONString(request));
        CallRecord callRecordQuery = new CallRecord();
        callRecordQuery.setPromiseId(request.getPromiseId());
        callRecordQuery.setConnectedStatus(1);
        List<CallRecord> callRecordList = callRecordRepository.queryList(callRecordQuery);
        log.info("CallRecordApplicationImpl queryCallRecordList callRecordList={}", JSON.toJSONString(callRecordList));
        if (CollectionUtils.isEmpty(callRecordList)){
            return Lists.newArrayList();
        }
        callRecordList = callRecordList.stream().filter(c->StringUtils.isNotBlank(c.getAudioUrl())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(callRecordList)){
            return Lists.newArrayList();
        }
        List<CallRecordDto> dataList = JSON.parseArray(JSON.toJSONString(callRecordList), CallRecordDto.class);
        dataList.sort(Comparator.comparing(CallRecordDto::getStartTime));
        return dataList;
    }

    /**
     * 查询外呼url
     * @param request
     * @return
     */
    @Override
    public String queryCallRecordUrl(QueryCallRecordRequest request) {
        log.info("CallRecordApplicationImpl queryCallRecordUrl request={}", JSON.toJSONString(request));
        CallRecord callRecord = callRecordRepository.queryCallRecordByCallId(request.getCallId());
        if (Objects.isNull(callRecord)){
            throw new BusinessException(BusinessErrorCode.UNKNOWN_ERROR);
        }
        JSONObject obj = JSON.parseObject(duccConfig.getSecurityNumberConfig());
        Date expire = DateUtil.offsetMinute(new Date(), obj.getInteger("callRecordUrlExpirationMinute"));
        return fileManageService.getPublicUrl(callRecord.getAudioUrl(), Boolean.TRUE, expire);
    }

    /**
     * 虚拟号解绑
     * @param request
     * @return
     */
    @Override
    public Boolean callBindRelease(SecurityNumberReleaseDto request) {
        log.info("CallRecordApplicationImpl callBindRelease request={}", JSON.toJSONString(request));
        // 虚拟号解绑
        SecurityNumberReleaseRpcParam releaseParam = new SecurityNumberReleaseRpcParam();
        releaseParam.setOrderId(request.getOrderId());
        releaseParam.setBindId(request.getBindId());
        iSecurityNumberServiceRpc.release(releaseParam);
        return true;
    }

    /**
     * 同步护士手机号
     * @param request
     * @return
     */
    @Override
    public Boolean syncAngelPhone(SyncAngelPhoneRequest request) {
        log.info("CallRecordApplicationImpl syncAngelPhone request={}", JSON.toJSONString(request));
        List<Map<String, Object>> diffPhoneList = new ArrayList<>();
        while (true){
            IPage<JdhAngelPo> pageResult = queryAngelPhonePageList(request);
            if (Objects.isNull(pageResult) || CollectionUtils.isEmpty(pageResult.getRecords())) {
                log.info("CallRecordApplicationImpl syncAngelPhone pageResult empty diffPhoneCount={},diffPhoneList={}",diffPhoneList.size(), JSON.toJSONString(diffPhoneList));
                return true;
            }

            try {
                if (!duccConfig.getSyncAngelPhoneSwitch()) {
                    log.info("CallRecordApplicationImpl syncAngelPhone stop");
                    return true;
                }
                Thread.sleep(request.getSleep());
            } catch (Exception e) {
                log.error("CallRecordApplicationImpl syncAngelPhone sleep error e", e);
            }

            for (JdhAngelPo angel : pageResult.getRecords()) {
                NethpBaseDoctorInfoClientBo baseDoctorInfo = null;
                LambdaUpdateWrapper<JdhAngelPo> updateWrapper = new LambdaUpdateWrapper<>();
                try {
                    NethpBaseDoctorInfoClientParam docParam = new NethpBaseDoctorInfoClientParam();
                    docParam.setPlatformId(angel.getNethpDocId());
                    baseDoctorInfo = nethpAngelRpc.getBaseDoctorInfo(docParam);
                    log.info("CallRecordApplicationImpl syncAngelPhone angel={}, docParam={}, baseDoctorInfo={} ", JSON.toJSONString(angel), JSON.toJSONString(docParam), JSON.toJSONString(baseDoctorInfo));
                    if (baseDoctorInfo.getPhone().equals(angel.getPhone())){
                        log.info("CallRecordApplicationImpl syncAngelPhone equals nethpDocId={}", angel.getNethpDocId());
                        updateWrapper.eq(JdhAngelPo::getNethpDocId, angel.getNethpDocId())
                                .eq(JdhAngelPo::getAngelId, angel.getAngelId())
                                .eq(JdhAngelPo::getId, angel.getId())
                                .set(JdhAngelPo::getUpdateTime, addMinutes(new Date(),request.getMinutes()));
                        jdhAngelPoMapper.update(null, updateWrapper);
                        continue;

                    } else {
                        Map<String, Object> diffMap = new HashMap<>();
                        diffMap.put("nethpDocId", angel.getNethpDocId());
                        diffMap.put("nethpPhone", baseDoctorInfo.getPhone());
                        diffMap.put("angelId", angel.getAngelId());
                        diffMap.put("angelPin", angel.getAngelPin());
                        diffMap.put("angelName", angel.getAngelName());
                        diffMap.put("angelPhone", angel.getPhone());
                        log.info("CallRecordApplicationImpl syncAngelPhone diffMap={}", JSON.toJSONString(diffMap));
                        diffPhoneList.add(diffMap);

                        if (request.getRefreshPhone()){
                            log.info("CallRecordApplicationImpl syncAngelPhone refreshPhone true");
                            updateWrapper.eq(JdhAngelPo::getNethpDocId, angel.getNethpDocId())
                                    .eq(JdhAngelPo::getAngelId, angel.getAngelId())
                                    .eq(JdhAngelPo::getId, angel.getId())
                                    .set(JdhAngelPo::getPhone, tdeClientUtil.encrypt(baseDoctorInfo.getPhone()))
                                    .set(JdhAngelPo::getPhoneIndex, tdeClientUtil.obtainKeyWordIndex(baseDoctorInfo.getPhone()))
                                    .set(JdhAngelPo::getUpdateTime, addMinutes(new Date(),request.getMinutes()));
                        }else {
                            log.info("CallRecordApplicationImpl syncAngelPhone refreshPhone false");
                            updateWrapper.eq(JdhAngelPo::getNethpDocId, angel.getNethpDocId())
                                    .eq(JdhAngelPo::getAngelId, angel.getAngelId())
                                    .eq(JdhAngelPo::getId, angel.getId())
                                    .set(JdhAngelPo::getUpdateTime, addMinutes(new Date(),request.getMinutes()));
                        }
                        jdhAngelPoMapper.update(null, updateWrapper);
                    }
                } catch (Exception e) {
                    log.error("CallRecordApplicationImpl syncAngelPhone error angel={}, e", JSONObject.toJSONString(angel), e);
                    updateWrapper.eq(JdhAngelPo::getNethpDocId, angel.getNethpDocId())
                            .eq(JdhAngelPo::getAngelId, angel.getAngelId())
                            .eq(JdhAngelPo::getId, angel.getId())
                            .set(JdhAngelPo::getUpdateTime, addMinutes(new Date(),request.getMinutes()));
                    jdhAngelPoMapper.update(null, updateWrapper);
                }
            }
        }
    }


    private IPage<JdhAngelPo> queryAngelPhonePageList(SyncAngelPhoneRequest req){
        Page<JdhAngelPo> queryPage = new Page<>(req.getPageNo(), req.getPageSize());
        QueryWrapper<JdhAngelPo> queryWrapper = new QueryWrapper<>();
        if (req.getNethpDocId() != null){
            queryWrapper.eq("nethp_doc_id", req.getNethpDocId());
        }
        queryWrapper.isNotNull("nethp_doc_id");
        queryWrapper.eq("yn", 1)
                .and(wap->{
                    wap.le("update_time", new Date())
                            .or().isNull("update_time");
                });
        Page<JdhAngelPo> pageResult = jdhAngelPoMapper.selectPage(queryPage, queryWrapper);
        log.info("queryAngelPhonePageList req={},pageResult={}",JSON.toJSONString(req), JSON.toJSONString(pageResult));
        return pageResult;
    }

    /**
     * 时间计算
     * @param in
     * @param minutes
     * @return
     */
    public Date addMinutes(Date in, int minutes) {
        Date date = in;
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.add(Calendar.MINUTE, minutes);
        return instance.getTime();
    }


}
