package com.jdh.o2oservice.application.trade.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.jd.jim.cli.Cluster;
import com.jd.jmq.common.message.Message;
import com.jd.ofc.domain.ChildMessage;
import com.jd.purchase.domain.old.bean.Cart;
import com.jd.purchase.domain.old.bean.Order;
import com.jd.purchase.domain.old.bean.SKU;
import com.jd.purchase.order.msg.domain.PurchaseOrderMsg;
import com.jd.purchase.utils.serializer.dict.SerializeType;
import com.jd.purchase.utils.serializer.helper.SerializersHelper;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.promise.VoucherApplication;
import com.jdh.o2oservice.application.trade.convert.TradeOrderConverter;
import com.jdh.o2oservice.application.trade.service.JdOrderApplication;
import com.jdh.o2oservice.application.trade.service.JdOrderMsgApplication;
import com.jdh.o2oservice.application.trade.util.OrderEntityBoUtil;
import com.jdh.o2oservice.base.annotation.MapAutowired;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.statemachine.StateMachine;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.common.enums.HasAddedEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.JdhVoucherSourceTypeEnum;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderAppointRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.RpcSkuBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.ServiceDetailBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.ServiceDetailRpcParam;
import com.jdh.o2oservice.core.domain.support.vertical.repository.JdhServiceTypeCategoryRelationRepository;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.trade.bo.OrderEntityBO;
import com.jdh.o2oservice.core.domain.trade.context.JdOrderContext;
import com.jdh.o2oservice.core.domain.trade.context.OrderCompleteVoucherContext;
import com.jdh.o2oservice.core.domain.trade.context.OrderVoucherExtendContext;
import com.jdh.o2oservice.core.domain.trade.enums.*;
import com.jdh.o2oservice.core.domain.trade.event.OrderSplitEventBody;
import com.jdh.o2oservice.core.domain.trade.factory.JdOrderFactory;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderExtRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderItemRepository;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.core.domain.trade.repository.query.JdOrderItemQuery;
import com.jdh.o2oservice.core.domain.trade.service.JdhOrderDomainService;
import com.jdh.o2oservice.core.domain.trade.service.VerticalCodeAbstractHandler;
import com.jdh.o2oservice.core.domain.trade.vo.SkuFeaturesVo;
import com.jdh.o2oservice.export.promise.cmd.CreateVoucherCmd;
import com.jdh.o2oservice.export.promise.cmd.VoucherExtend;
import com.jdh.o2oservice.export.promise.cmd.VoucherItem;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jdh.o2oservice.base.enums.OrderMsgTypeEnum;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.SendpayValueEnum;
import com.jdh.o2oservice.base.enums.OrderStatusEnum;
import com.jdh.o2oservice.base.enums.PartnerSourceEnum;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName:PopOrderNoSplitMsgApplicationImpl
 * @Description: 订单消息应用服务
 * @Author: yaoqinghai
 * @Date: 2023/12/27 10:19
 * @Vserion: 1.0
 **/
@Slf4j
@Service
public class JdOrderMsgApplicationImpl implements JdOrderMsgApplication {

    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * jimClient
     */
    @Resource
    private Cluster jimClient;

    /**
     * serviceMap
     */
    @MapAutowired
    private Map<String, VerticalCodeAbstractHandler> verticalCodeAbstractHandlerMap;

    /**
     * jdOrderRepository
     */
    @Resource
    private JdOrderRepository jdOrderRepository;
    @Resource
    private JdOrderApplication jdOrderApplication;
    @Resource
    private JdhServiceTypeCategoryRelationRepository jdhServiceTypeCategoryRelationRepository;
    @Resource
    private JdhOrderDomainService jdhOrderDomainService;
    /**
     * jdOrderExtRepository
     */
    @Resource
    private JdOrderExtRepository jdOrderExtRepository;
    /**
     * jdOrderItemRepository
     */
    @Resource
    private JdOrderItemRepository jdOrderItemRepository;

    /**
     * eventCoordinator
     */
    @Resource
    private EventCoordinator eventCoordinator;

    /**
     * orderStatemachine
     */
    @Resource
    private StateMachine<TradeStatus, TradeEventTypeEnum, StateContext> orderStatemachine;

    /**
     * voucherApplication
     */
    @Resource
    private VoucherApplication voucherApplication;
    /**
     * 商品
     */
    @Autowired
    private ProductApplication productApplication;

    @Resource
    private SkuInfoRpc skuInfoRpc;

    @Resource
    ProviderAppointRpc providerAppointRpc;

    @Resource
    private RedisLockUtil redisLockUtil;
    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    /**
     * 订单不拆单数量限流
     *
     * @return
     */
    @Override
    public Boolean orderCountLimit() {
        //限流开关  平时是关闭状态 false
        if (duccConfig.getOrderNoSplitOnOff()) {
            log.info("JdOrderApplicationImpl -> orderCountLimit 限流开关开启了！");
            //1秒
            String cacheKey = StringUtils.join(RedisKeyEnum.BEAUTY_ORDER_NO_SPLIT_MSG_KEY.getRedisKeyPrefix(), CommonConstant.ORDER_NO_SPLIT_SUFFIX);
            long count = jimClient.incr(cacheKey);
            jimClient.expire(cacheKey, RedisKeyEnum.BEAUTY_ORDER_NO_SPLIT_MSG_KEY.getExpireTime(), RedisKeyEnum.BEAUTY_ORDER_NO_SPLIT_MSG_KEY.getExpireTimeUnit());
            if (count > Long.valueOf(duccConfig.getOrderLimitNum())) {
                throw new RuntimeException("JdOrderApplicationImpl 不拆单MQ触发限流，重试！");
            }
        }
        return false;
    }

    /**
     * 反序列化创建订单对象
     *
     * @param message
     * @return
     */
    @Override
    public Order fireSerializersOrderMsg(Message message, OrderMsgTypeEnum orderMsgTypeEnum) {
        log.info("[JdOrderApplicationImpl->fireSerializersOrderMsg],orderMsgTypeEnum={}, msgText={}", JSON.toJSONString(orderMsgTypeEnum), message.getText());
        Order order;
        if (OrderMsgTypeEnum.NO_SPLIT_ORDER.equals(orderMsgTypeEnum)) {
            ChildMessage childMessage = SerializersHelper.ofString(message.getText(), ChildMessage.class, SerializeType.PROTOSTUFF.getCode());
            order = childMessage.getOrder();
        } else if (OrderMsgTypeEnum.ORDER_COMPLETE.equals(orderMsgTypeEnum)) {
            PurchaseOrderMsg purchaseMsg = SerializersHelper.ofString(message.getText(), PurchaseOrderMsg.class, SerializeType.PROTOSTUFF.getCode());
            //订单完成mq单独解析订单车信息
            Cart cart = SerializersHelper.ofString(purchaseMsg.getCartBody(), Cart.class, purchaseMsg.getSerializationType());
            order = SerializersHelper.ofString(purchaseMsg.getOrderBody(), Order.class, purchaseMsg.getSerializationType());
            order.setCart(cart);
        } else {
            throw new ArgumentsException(new DynamicErrorCode("D0001", "订单消息类型不正确"));
        }
        log.info("[JdOrderApplicationImpl->fireSerializersOrderMsg],orderId={}", order.getOrderId());
        return order;
    }

    /**
     * 反序列化创建订单对象
     *
     * @param message
     * @return
     */
    @Override
    public List<com.jd.ofc.domain.Order> fireSerializersChildOrderMsg(Message message) {
        log.info("[JdOrderApplicationImpl->fireSerializersOrderMsg], msgText={}", message.getText());
        ChildMessage childMessage = SerializersHelper.ofString(message.getText(), ChildMessage.class, SerializeType.PROTOSTUFF.getCode());
        List<com.jd.ofc.domain.Order> childOrders = childMessage.getChildOrders();
        return childOrders;
    }

    /**
     * 业务处理消息
     *
     * @param order
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealMessage(Order order) {
        log.info("[JdOrderApplicationImpl->dealMessage], orderId={}", order.getOrderId());
        OrderEntityBO orderEntityBO = OrderEntityBoUtil.getOrderEntity(order);
        //create订单
        JdOrder jdOrder = JdOrderFactory.createOrder(orderEntityBO);
        log.info("[JdOrderApplicationImpl->dealMessage], jdOrder={}", JSON.toJSONString(jdOrder));
        // 补充订单标记
        fillSkuTag(jdOrder);
        log.info("[JdOrderApplicationImpl->dealMessage], skuTag jdOrder={}", JSON.toJSONString(jdOrder));
        //save
        jdOrderRepository.save(jdOrder);
        //推送事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdOrder, TradeEventTypeEnum.POP_ORDER_SPLIT, new OrderSplitEventBody(jdOrder)));
    }

    private void fillSkuTag(JdOrder jdOrder) {
        if (Objects.isNull(jdOrder) || Collections.isEmpty(jdOrder.getJdOrderItemList())) {
            return;
        }
        Set<String> skuIds = jdOrder.getJdOrderItemList().stream().map(e -> String.valueOf(e.getSkuId())).collect(Collectors.toSet());
        Map<String, RpcSkuBO> skuInfos = skuInfoRpc.getSkuInfo(skuIds);

        jdOrder.getJdOrderItemList().forEach(e -> {
            Map<String, String> skuTagMap = new HashMap<>();
            // appontType
            try {
                ServiceDetailBo serviceDetailBo = providerAppointRpc.queryServiceDetail(ServiceDetailRpcParam.builder().skuNo(String.valueOf(e.getSkuId())).build());
                if (Objects.nonNull(serviceDetailBo) && Objects.nonNull(serviceDetailBo.getAppointmentType())) {
                    skuTagMap.put("appointType", String.valueOf(serviceDetailBo.getAppointmentType()));
                }
            } catch (Exception exception) {
                log.error("[JdOrderApplicationImpl->fillSkuTag], orderId={} appontType异常", jdOrder.getOrderId(), e);
            }

            // 商品的yymb标记不为空，而且yymb的值以200开头
            if (skuInfos.containsKey(String.valueOf(e.getSkuId()))) {
                RpcSkuBO skuBo = skuInfos.get(String.valueOf(e.getSkuId()));
                if (Objects.nonNull(skuBo) && StringUtils.isNotBlank(skuBo.getYymb())) {
                    skuTagMap.put("yymb", skuBo.getYymb());
                }
            }

            if (skuTagMap.size() > 0) {
                String skuFeaturesStr = e.getSkuFeatures();
                SkuFeaturesVo skuFeaturesVo = SkuFeaturesVo.builder().build();
                if (StringUtils.isNotBlank(skuFeaturesStr)) {
                    skuFeaturesVo = JSON.parseObject(skuFeaturesStr, SkuFeaturesVo.class);
                }
                skuFeaturesVo.setSkuTag(skuTagMap);
                e.setSkuFeatures(JSON.toJSONString(skuFeaturesVo));
                log.info("[JdOrderApplicationImpl->fillSkuTag], orderId={} skuTagMap={}", jdOrder.getOrderId(), JSON.toJSONString(skuTagMap));
            }
        });
    }



    /**
     * 自营不拆单信息
     *
     * @param order
     */
    @Override
    public void dealSelfHomeNoSplitOrderMessage(Order order) {
        Long orderId = order.getOrderId();
        String sendPayMap = order.getSendPayMap();
        if (StringUtils.isBlank(sendPayMap)){
            log.warn("[JdOrderApplicationImpl->dealSelfHomeNoSplitOrderMessage],sendPayMap is blank");

        }
        log.info("[JdOrderApplicationImpl->dealSelfHomeNoSplitOrderMessage], sendPayMap={}", sendPayMap);

        Map<String, String> sendPayDict = JSON.parseObject(sendPayMap, new TypeReference< Map<String, String> >(){});

        JdOrder jdOrder;
        // 处理sendPay 294的订单;sendPayDict的标记位是从0开始计数的，所以需要减去1
        if (sendPayDict.containsKey(SendpayValueEnum.SEND_PAY_294_6.getSendPayStr())){
            log.info("[JdOrderApplicationImpl->dealSelfHomeNoSplitOrderMessage], orderId={}", orderId);
            JdOrder dbOrder = jdOrderRepository.find(new JdOrderIdentifier(orderId));
            if (Objects.isNull(dbOrder)) {
                log.error("[JdOrderApplicationImpl->dealSelfHomeNoSplitOrderMessage],未查询到订单信息!order={}", orderId);
                return;
            }
            if (!Objects.equals(dbOrder.getOrderStatus(), OrderStatusEnum.ORDER_WAIT_PAY.getStatus())) {
                // 订单存在且状态非待付款，记录日志并返回，不处理订单
                log.info("JdOrderApplicationImpl -> dealSelfHomeNoSplitOrderMessage, 此订单已在数据库中存在并且非待付款状态，默认过滤不再处理, orderId={}", orderId);
                return;
            }

            String value = sendPayDict.get(SendpayValueEnum.SEND_PAY_294_6.getSendPayStr());
            jdOrder = bulidJdOrder(order, dbOrder, value);
            // create订单
            log.info("[JdOrderApplicationImpl->dealSelfHomeNoSplitOrderMessage], jdOrder={}", JSON.toJSONString(jdOrder));

            jdOrderRepository.updateNoSplitOrder(jdOrder);
            createVoucherAndPublish(jdOrder, TradeEventTypeEnum.SELF_ORDER_NO_SPLIT, new OrderSplitEventBody(jdOrder));

        }else if(sendPayDict.containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr())){
            JdOrder dbOrder = jdOrderRepository.find(new JdOrderIdentifier(orderId));
            if (Objects.nonNull(dbOrder)){
                log.info("[JdOrderApplicationImpl->dealSelfHomeNoSplitOrderMessage], dbOrder exist orderId={}", orderId);
            }else {
                createSelfTestTransportOrder(order);
            }
        }else{
            log.info("[JdOrderApplicationImpl->dealSelfHomeNoSplitOrderMessage], sendPayDict not match");
            return;
        }
    }




    /**
     * 自营拆单信息
     *
     * @param order
     * @param sendPayFlag
     */
    @Override
    public void dealSelfHomeOrderSplitMessage(Order order, String sendPayFlag) {
        Long orderId = order.getOrderId();
        String sendPayMap = order.getSendPayMap();
        if (StringUtils.isBlank(sendPayMap)){
            log.warn("[JdOrderApplicationImpl->dealSelfHomeOrderSplitMessage],sendPayMap is blank");

        }
        log.info("[JdOrderApplicationImpl->dealSelfHomeOrderSplitMessage], sendPayMap={}", sendPayMap);

        Map<String, String> sendPayDict = JSON.parseObject(sendPayMap, new TypeReference< Map<String, String> >(){});
        JdOrder childOrder;
        // 处理sendPay 294的订单;sendPayDict的标记位是从0开始计数的，所以需要减去1
        if (sendPayDict.containsKey(SendpayValueEnum.SEND_PAY_294_6.getSendPayStr())){
            Long parentId = order.getParentId();
            log.info("[JdOrderApplicationImpl->dealSelfHomeOrderSplitMessage], orderId={},parentId={}", orderId, parentId);
            JdOrder parentIdOrder = jdOrderRepository.find(new JdOrderIdentifier(parentId));
            if (Objects.isNull(parentIdOrder)) {
                log.error("[JdOrderApplicationImpl->dealSelfHomeOrderSplitMessage],未查询到订单信息!parentId={}", parentId);
                return;
            }
            SKU sku = this.getOrderSku(order);
            JdOrderItem jdOrderItem = jdOrderItemRepository.findJdOrderItemDetail(parentId, sku.getId());
            if (Objects.isNull(jdOrderItem)) {
                log.error("[JdOrderApplicationImpl->dealSelfHomeOrderSplitMessage],未查询到订单明细信息!order={}", order.getOrderId());
                return;
            }
            //create订单
            childOrder = bulidJdOrder(order, parentIdOrder, sendPayFlag);
            log.info("[JdOrderApplicationImpl->dealSelfHomeOrderSplitMessage], childOrder={}", JSON.toJSONString(childOrder));
            jdOrderRepository.saveSplitOrder(childOrder, parentIdOrder, jdOrderItem);
        }else if(sendPayDict.containsKey(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr())){
            String value = sendPayDict.get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr());
            if (StringUtils.equals(value, SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())){
                JdOrder dbOrder = jdOrderRepository.find(new JdOrderIdentifier(orderId));
                if (Objects.nonNull(dbOrder)){
                    log.info("[JdOrderApplicationImpl->dealSelfHomeOrderSplitMessage], dbOrder exist orderId={}", orderId);
                }else {
                    createSelfTestTransportOrder(order);
                }
            }else{
                log.info("[JdOrderApplicationImpl->dealSelfHomeOrderSplitMessage], sendPay 1254 类型的值不支持");
            }
        }else{
            log.info("[JdOrderApplicationImpl->dealSelfHomeOrderSplitMessage], sendPayDict not match");
        }
    }


    /**
     * 业务处理订单完成消息
     *
     * @param order
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealOrderCompleteMessage(Order order) {
        int orderType = order.getOrderType();
        // LOC逻辑兼容
        if (orderType == OrderTypeEnum.COMMON_LOC_TYPE.getType()
                || orderType == OrderTypeEnum.XFYL_POP_TYPE.getType()) {
            locPopOrder(order);

            // 0号单订单完成过滤
        } else if (order.getOrderType() == OrderTypeEnum.DEFAULT_ORDER_TYPE.getType()) {
            String sendPayMap = order.getSendPayMap();
            if (StringUtils.isBlank(sendPayMap)){
                log.warn("[JdOrderApplicationImpl->dealOrderCompleteMessage],sendPayMap is blank");

            }
            log.info("[JdOrderApplicationImpl->dealOrderCompleteMessage], sendPayMap={}", sendPayMap);
            Map<String, String> sendPayDict = JSON.parseObject(sendPayMap, new TypeReference< Map<String, String> >(){});
            String value = sendPayDict.get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr());
            if (StringUtils.equals(value, SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())){
                selfTransportCompleteOrder(order);
            }

        }

    }

    /**
     * 到家检测物流订单
     *
     * @param order
     */
    private void selfTransportCompleteOrder(Order order) {
        log.info("[JdOrderMsgApplicationImpl->dealMessage], orderId={}", order.getOrderId());
        JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(order.getOrderId()));
        // 订单不存在则创建订单
        if (Objects.isNull(jdOrder)){
            jdOrder = createSelfTestTransportOrder(order);
        }
        jdOrder.setOrderStatus(OrderStatusEnum.ORDER_SIGNED.getStatus());
        log.info("[JdOrderMsgApplicationImpl->dealMessage], jdOrder={}", JSON.toJSONString(jdOrder));
        //save
        jdOrderRepository.updateOrderStatusByOrderId(jdOrder);
        //推送事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdOrder, TradeEventTypeEnum.ORDER_DELIVERY_COMPLETED, new OrderSplitEventBody(jdOrder)));
    }


    private void locPopOrder(Order order) {
        Cart cart = order.getCart();
        if (Objects.isNull(cart)) {
            throw new BusinessException(BusinessErrorCode.ORDER_CART_IS_EMPTY);
        }
        JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(order.getOrderId()));
        log.info("[JdOrderApplicationImpl->dealOrderCompleteMessage],jdOrder={}", JSON.toJSONString(jdOrder));
        if (Objects.isNull(jdOrder)) {
            log.error("[JdOrderApplicationImpl->dealOrderCompleteMessage],未查询到订单信息!order={}", order.getOrderId());
            throw new BusinessException(BusinessErrorCode.ORDER_NOT_EXIST);
        }
        JdOrderItemQuery jdOrderItemQuery = new JdOrderItemQuery();
        jdOrderItemQuery.setOrderId(jdOrder.getOrderId());
        jdOrderItemQuery.setUserPin(jdOrder.getUserPin());
        List<JdOrderItem> jdOrderItemList = jdOrderItemRepository.findJdOrderItemList(jdOrderItemQuery);
        if (CollectionUtils.isEmpty(jdOrderItemList)) {
            log.error("[JdOrderApplicationImpl->dealOrderCompleteMessage],未查询到订单明细信息!order={}", order.getOrderId());
            throw new BusinessException(BusinessErrorCode.ORDER_ITEM_NOT_EXIST);
        }
        jdOrder.setJdOrderItemList(jdOrderItemList);

        List<SKU> allSkusAndNum = cart.findAllSkusAndNum();
        if (CollectionUtils.isEmpty(allSkusAndNum)) {
            throw new BusinessException(BusinessErrorCode.ORDER_CART_SKU_IS_EMPTY);
        }
        // 75号订单需过滤yymb为200开头数字才创建voucher
        boolean matchYymb = jdOrderItemList.stream().allMatch(e -> StringUtils.isNotBlank(e.getSkuFeatures()) && JSON.parseObject(e.getSkuFeatures(), SkuFeaturesVo.class).getSkuTag().getOrDefault("yymb", "").startsWith("200"));
        if (matchYymb) {
            createVoucherAndPublish(jdOrder, TradeEventTypeEnum.POP_ORDER_COMPLETE, null);
        }
        //更新订单状态
        jdOrder.setOrderStatus(OrderStatusEnum.ORDER_COMPLETE.getStatus());
        jdOrder.setJdOrderItemList(null);
        jdOrderRepository.updateOrderByOrderId(jdOrder);
    }

    /**
     *
     * （1）200号到家订单，买了加项的订单才会拆单，对于加项订单是以父订单去创建voucher的。
     * （2）200号订单，互医场景会走拆单逻辑，以parentOrderId创建voucher
     * @param parentId
     */
    @Override
    public void dealSplitParentOrder(Long parentId) {
        log.info("[JdOrderApplicationImpl->dealSelfHomeOrderSplitExt], parentId={}", parentId);
        JdOrder jdOrder = jdOrderRepository.find(new JdOrderIdentifier(parentId));
        if (Objects.isNull(jdOrder)) {
            log.error("[JdOrderApplicationImpl->dealSelfHomeOrderSplitExt],未查询到订单信息!order={}", parentId);
            return;
        }

        if (!Objects.equals(jdOrder.getOrderType(), OrderTypeEnum.XFYL_ORDER_TYPE.getType())) {
            log.error("[JdOrderApplicationImpl->dealSelfHomeOrderSplitExt] 非200号订单不处理");
            return;
        }
        List<JdOrderItem> jdOrderItemList = jdOrderItemRepository.listByOrderId(parentId);
        if (CollectionUtils.isEmpty(jdOrderItemList)) {
            return;
        }
        jdOrder.setJdOrderItemList(jdOrderItemList);

        createVoucherAndPublish(jdOrder, TradeEventTypeEnum.SELF_ORDER_SPLIT, new OrderSplitEventBody(jdOrder));
    }


    /**
     * 解析xml生成订单信息,create订单
     *
     * @param order
     * @param dbOrder
     * @param sendPayFlag
     * @return
     */
    private JdOrder bulidJdOrder(Order order, JdOrder dbOrder, String sendPayFlag) {
        OrderEntityBO orderEntityBO = OrderEntityBoUtil.getOrderEntity(order);
        log.info("[JdOrderApplicationImpl->bulidJdOrder], orderEntityBO={}", JSON.toJSONString(orderEntityBO));
        //create订单
        JdOrder jdOrder = JdOrderFactory.createOrder(orderEntityBO);
        VerticalCodeAbstractHandler service = verticalCodeAbstractHandlerMap.get(sendPayFlag);
        if (Objects.nonNull(service)) {
            JdOrderContext jdOrderContext = TradeOrderConverter.INSTANCE.convertToJdOrderContext(jdOrder);
            jdOrderContext.setJdOrder(jdOrder);
            jdOrderContext.setDbOrder(dbOrder);
            service.bulidVerticalCode(jdOrderContext);
        }
        log.info("[JdOrderApplicationImpl->bulidJdOrder], jdOrder={}", JSON.toJSONString(jdOrder));
        return jdOrder;
    }

    /**
     * @param order
     * @return
     */
    private SKU getOrderSku(Order order) {
        Cart cart = order.getCart();
        List<SKU> skus = cart.findAllSkusAndNum();
        if (CollectionUtils.isNotEmpty(skus)) {
            return skus.get(0);
        }
        return null;
    }

    /**
     * 创建服务单并推送事件
     *
     * @param jdOrder
     */
    @Override
    public void createVoucherAndPublish(JdOrder jdOrder, TradeEventTypeEnum tradeEventTypeEnum, OrderSplitEventBody orderSplitEventBody) {
        JdOrderContext jdOrderContext = TradeOrderConverter.INSTANCE.convertToJdOrderContext(jdOrder);
        jdOrderContext.setJdOrder(jdOrder);
        jdOrderContext.init(tradeEventTypeEnum);
        log.info("[JdOrderApplicationImpl->createVoucherAndPublish],before.jdOrderContext={}", JSON.toJSONString(jdOrderContext));
        orderStatemachine.fireEvent(ResolveOrderConditionEnum.ORDER_PAID, tradeEventTypeEnum, jdOrderContext);
        log.info("[JdOrderApplicationImpl->createVoucherAndPublish],after.jdOrderContext={}", JSON.toJSONString(jdOrderContext));
        //维护是否含有加项品
        if (HasAddedEnum.HAS_ADDED.getValue().equals(jdOrder.getHasAdded()) && CollectionUtils.isNotEmpty(jdOrderContext.getOrderCompleteVoucherContextList())) {
            log.info("[JdOrderApplicationImpl->createVoucherAndPublish],进入for循环");
            for (OrderCompleteVoucherContext orderCompleteVoucherContext : jdOrderContext.getOrderCompleteVoucherContextList()) {
                OrderVoucherExtendContext extend = orderCompleteVoucherContext.getExtend();
                extend.setHasAdded(jdOrder.getHasAdded());
                orderCompleteVoucherContext.setExtend(JSON.parseObject(JSON.toJSONString(extend), OrderVoucherExtendContext.class));
                log.info("[JdOrderApplicationImpl->createVoucherAndPublish], orderCompleteVoucherContext={}", JSON.toJSONString(orderCompleteVoucherContext));
            }
        }
        //创建服务单
        List<CreateVoucherCmd> createVoucherCmds =
                TradeOrderConverter.INSTANCE.convertToCreateVoucherCmdList(jdOrderContext.getOrderCompleteVoucherContextList());
        voucherApplication.batchCreateVoucher(createVoucherCmds);
        //推送事件
        eventCoordinator.publish(EventFactory.newDefaultEvent(jdOrder, tradeEventTypeEnum, orderSplitEventBody));
    }

    /**
     * 创建快递模式的上门检查单
     * @param order
     */
    private JdOrder createSelfTestTransportOrder(Order order){

        JdOrder exist = jdOrderRepository.find(new JdOrderIdentifier(order.getOrderId()));
        if (Objects.nonNull(exist)){
            log.info("JdOrderApplicationImpl->createSelfTestTransportOrder order exist orderId={}", order.getOrderId());
            return exist;
        }

        String sendPayMap = order.getSendPayMap();
        Map<String, String> sendPayDict = JSON.parseObject(sendPayMap, new TypeReference< Map<String, String> >(){});
        String value = sendPayDict.get(SendpayValueEnum.SEND_PAY_1254_1.getSendPayStr());
        if (Objects.equals(value, SendpayValueEnum.SEND_PAY_1254_1.getSendPayValue())){
            OrderEntityBO orderEntityBO = OrderEntityBoUtil.getOrderEntityByCartSku(order);
            JdOrder jdOrder = JdOrderFactory.createOrder(orderEntityBO);


            jdOrder.setServiceType(ServiceTypeEnum.TEST.getServiceType());
            jdOrder.setVerticalCode(ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode());
            jdOrder.setPartnerSource(PartnerSourceEnum.JDH_XFYL.getCode());
            for (JdOrderItem orderItem : jdOrder.getJdOrderItemList()) {
                orderItem.setVerticalCode(ServiceHomeTypeEnum.XFYL_HOME_SELF_TEST_TRANSPORT.getVerticalCode());
            }
            jdOrderRepository.save(jdOrder);

            CreateVoucherCmd createVoucherCmd = new CreateVoucherCmd();
            createVoucherCmd.setServiceType(jdOrder.getServiceType());
            createVoucherCmd.setVerticalCode(jdOrder.getVerticalCode());
            createVoucherCmd.setUserPin(jdOrder.getUserPin());
            createVoucherCmd.setSourceVoucherId(String.valueOf(jdOrder.getOrderId()));
            createVoucherCmd.setSourceType(JdhVoucherSourceTypeEnum.JD_ORDER.getType());
            createVoucherCmd.setExpireDate(CommonConstant.EXPIRE_DATE);

            VoucherExtend extend = new VoucherExtend();
            extend.setOrderPhone(orderEntityBO.getUserPhone());
            extend.setOrderId(String.valueOf(jdOrder.getOrderId()));
            jdOrder.getJdOrderItemList().stream().findFirst().ifPresent(e->{
                createVoucherCmd.setPromiseNum(e.getSkuNum());
                List<VoucherItem> voucherItemList = Lists.newArrayList();
                VoucherItem voucherItem = new VoucherItem();
                voucherItem.setVerticalCode(jdOrder.getVerticalCode());
                voucherItem.setServiceType(jdOrder.getServiceType());
                voucherItem.setServiceId(e.getSkuId());
                voucherItemList.add(voucherItem);
                createVoucherCmd.setVoucherItemList(voucherItemList);
                extend.setPromisePatientNum(e.getSkuNum());
            });


            createVoucherCmd.setExtend(extend);
            voucherApplication.createVoucher(createVoucherCmd);
            return jdOrder;
        }else{
            log.info("[JdOrderApplicationImpl->dealSelfHomeNoSplitOrderMessage], sendPay 1254 类型的值不支持");
            return null;
        }
    }
}
