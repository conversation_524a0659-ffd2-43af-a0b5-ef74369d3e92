package com.jdh.o2oservice.application.provider.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.jd.health.xfyl.merchant.export.param.supplier.store.StoreInfoQueryParam;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.provider.convert.ProviderApplicationConverter;
import com.jdh.o2oservice.application.provider.convert.ProviderStoreApplicationConverter;
import com.jdh.o2oservice.application.provider.listener.ImportStationServiceItemListener;
import com.jdh.o2oservice.application.provider.listener.ImportStationServiceItemListenerContext;
import com.jdh.o2oservice.application.provider.service.ProviderStoreApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.cache.RedisUtil;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.SystemErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.BeanUtil;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipDBQuery;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.product.context.ServiceItemQueryContext;
import com.jdh.o2oservice.core.domain.product.enums.ProductErrorCode;
import com.jdh.o2oservice.core.domain.product.model.JdhMaterialPackage;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuItemRel;
import com.jdh.o2oservice.core.domain.product.model.ServiceItem;
import com.jdh.o2oservice.core.domain.product.model.ServiceItemIdentifier;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhMaterialPackageRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhServiceItemRepository;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.provider.bo.StationAddressBo;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.context.AppointmentMigrationContext;
import com.jdh.o2oservice.core.domain.provider.context.ProviderContext;
import com.jdh.o2oservice.core.domain.provider.context.ProviderStoreContext;
import com.jdh.o2oservice.core.domain.provider.enums.ProviderErrorCode;
import com.jdh.o2oservice.core.domain.provider.model.ImportStationServiceItem;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.model.Provider;
import com.jdh.o2oservice.core.domain.provider.query.ProviderStoreDetailQuery;
import com.jdh.o2oservice.core.domain.provider.query.QueryMerchantStoreListByParamQuery;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderMedicalPromiseRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.AppointmentApiMerchantParam;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.MedicalPromiseFromEsBo;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.MedicalPromiseListFromEsParam;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.UpdateMedicalPromiseStationParam;
import com.jdh.o2oservice.core.domain.provider.service.ProviderDomainService;
import com.jdh.o2oservice.core.domain.provider.service.ProviderStoreDomainService;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.export.laboratory.cmd.AddQuickMerchantStoreRequest;
import com.jdh.o2oservice.export.laboratory.cmd.AppointmentMigrationRequest;
import com.jdh.o2oservice.export.laboratory.cmd.UpdateQuickMerchantStoreRequest;
import com.jdh.o2oservice.export.laboratory.dto.QueryMedicalPromisePageResponse;
import com.jdh.o2oservice.export.laboratory.dto.QueryMerchantStoreDetailResponse;
import com.jdh.o2oservice.export.laboratory.enums.DockingTypeEnum;
import com.jdh.o2oservice.export.laboratory.query.QueryMedicalPromisePageRequest;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreDetailByParamRequest;
import com.jdh.o2oservice.export.laboratory.query.QueryMerchantStoreListByParamRequest;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.provider.cmd.*;
import com.jdh.o2oservice.export.provider.dto.JdhStationServiceItemRelDto;
import com.jdh.o2oservice.export.provider.dto.StationAddressDto;
import com.jdh.o2oservice.export.provider.dto.StoreInfoDto;
import com.jdh.o2oservice.export.provider.query.JdhStationServiceItemRelRequest;
import com.jdh.o2oservice.export.provider.query.StationAddressRequest;
import com.jdh.o2oservice.export.provider.query.StoreInfoRequest;
import com.jdh.o2oservice.ext.ship.enums.StanderAngelShipStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 供应商门店集合类
 *
 * <AUTHOR>
 * @date 2024/04/28
 */
@Component
@Slf4j
public class ProviderStoreApplicationImpl implements ProviderStoreApplication {

    /**
     * 门店仓储
     */
    @Resource
    ProviderStoreRepository providerStoreRepository;

    /**
     * 门店查询rpc
     */
    @Resource
    ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    /**
     * 项目查询
     */
    @Resource
    JdhServiceItemRepository jdhServiceItemRepository;

    /**
     * 商品仓储
     */
    @Resource
    JdhSkuRepository jdhSkuRepository;

    /**
     * 商品应用
     */
    @Resource
    ProductApplication productApplication;

    /**
     * 耗材包
     */
    @Resource
    JdhMaterialPackageRepository jdhMaterialPackageRepository;
    /**
     * 文件服务仓
     */
    @Resource
    JdhFileRepository jdhFileRepository;

    /**
     * 服务者
     */
    @Resource
    FileManageService fileManageService;

    /**
     * redisUtil
     */
    @Resource
    private RedisUtil redisUtil;

    /**
     * 线程池
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    @Resource
    private ProviderDomainService providerDomainService;

    @Resource
    private ProviderStoreDomainService providerStoreDomainService;

    @Resource
    private GenerateIdFactory generateIdFactory;

    @Resource
    private Cluster jimClient;

    private String APPOINTMENTMIGRATION = "appointmentMigration_lock";

    @Resource
    private ProviderMedicalPromiseRpc providerMedicalPromiseRpc;

    @Resource
    private AngelShipRepository angelShipRepository;

    @Resource
    private ProviderRepository providerRepository;

    @Resource
    private EventCoordinator eventCoordinator;

    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;


    /**
     * 查询门店信息
     *
     * @param request req
     * @return dto
     */
    @Override
    public StoreInfoDto queryStationInfo(StoreInfoRequest request) {
        log.info("ProviderStoreApplicationImpl#queryStoreInfo req={}", JSON.toJSONString(request));
        StoreInfoBo storeInfoBo = providerStoreExportServiceRpc.queryByStoreId(request.getStationId());
        if (storeInfoBo != null) {
            StoreInfoDto storeInfoDto = new StoreInfoDto();
            storeInfoDto.setStationId(storeInfoBo.getJdStoreId());
            storeInfoDto.setStoreName(storeInfoBo.getStoreName());
            storeInfoDto.setLat(storeInfoBo.getLat());
            storeInfoDto.setLng(storeInfoBo.getLng());
            storeInfoDto.setProviderId(storeInfoBo.getChannelNo());
            storeInfoDto.setStationAddress(storeInfoBo.getStoreAddr());
            storeInfoDto.setStationPhone(storeInfoBo.getStorePhone());

            log.info("ProviderStoreApplicationImpl#queryStoreInfo storeInfoDto={}", JSON.toJSONString(storeInfoDto));
            return storeInfoDto;
        }
        log.info("ProviderStoreApplicationImpl#queryStoreInfo result null");
        return null;
    }

    /**
     * 创建门店项目信息
     *
     * @param cmd cmd
     * @return dto
     */
    @Override
    public Boolean addStationServiceItemRel(JdhStationServiceItemRelCreateCmd cmd) {
        log.info("ProviderStoreApplicationImpl#addStationServiceItemRel, cmd={}", JSON.toJSONString(cmd));
        AssertUtils.nonNull(cmd, ProviderErrorCode.PARAM_NULL_ERROR);
        JdhStationServiceItemRel jdhStationServiceItemRel = ProviderStoreApplicationConverter.INSTANCE.createCmdToModel(cmd);
        jdhStationServiceItemRel.setQueryIgnoreOnOffShelf(true);
        JdhStationServiceItemRel queryResult = providerStoreRepository.queryStationServiceItem(jdhStationServiceItemRel);
        if (queryResult != null) {
            throw new BusinessException(ProviderErrorCode.PROVIDER_STATION_SERVICE_ITEM_EXIST);
        }
        buildItemInfo(jdhStationServiceItemRel);
        if (jdhStationServiceItemRel.getOnOffShelf() == null) {
            jdhStationServiceItemRel.setOnOffShelf(1);
        }
        int count = providerStoreRepository.saveStationServiceItem(jdhStationServiceItemRel);
        return count > 0;
    }

    /**
     * 更新门店项目信息
     *
     * @param cmd cmd
     * @return dto
     */
    @Override
    public Boolean updateStationServiceItemRel(JdhStationServiceItemRelUpdateCmd cmd) {
        log.info("ProviderStoreApplicationImpl#updateStationServiceItemRel, cmd={}", JSON.toJSONString(cmd));
        AssertUtils.nonNull(cmd, ProviderErrorCode.PARAM_NULL_ERROR);
        AssertUtils.nonNull(cmd.getServiceItemId() , ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("serviceItemId"));
        AssertUtils.hasText(cmd.getStationId(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("stationId"));
        JdhStationServiceItemRel jdhStationServiceItemRel = ProviderStoreApplicationConverter.INSTANCE.updateCmdToModel(cmd);
        jdhStationServiceItemRel.setQueryIgnoreOnOffShelf(true);
        jdhStationServiceItemRel.setOnOffShelf(null);
        JdhStationServiceItemRel snapshot = providerStoreRepository.queryStationServiceItem(jdhStationServiceItemRel);
        BeanUtil.copyProperties(jdhStationServiceItemRel, snapshot, true);
        buildItemInfo(snapshot);
        if (cmd.getOnOffShelf() != null) {
            snapshot.setOnOffShelf(cmd.getOnOffShelf());
        }
        int count = providerStoreRepository.updateStationServiceItem(snapshot);
        return count > 0;
    }

    /**
     * 分页查询门店项目信息
     *
     * @param request req
     * @return dto
     */
    @Override
    public PageDto<JdhStationServiceItemRelDto> queryStationServiceItemRelPage(JdhStationServiceItemRelRequest request) {
        log.info("ProviderStoreApplicationImpl#queryStoreInfoPage, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProviderErrorCode.PARAM_NULL_ERROR);
        AssertUtils.hasText(request.getStationId(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("stationId"));
        PageDto<JdhStationServiceItemRelDto> pageDto = new PageDto<>();
        pageDto.setTotalPage(0);
        pageDto.setPageNum(request.getPageNum());
        pageDto.setPageSize(request.getPageSize());
        pageDto.setTotalCount(0);
        pageDto.setList(Collections.emptyList());
        List<Long> itemIds = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getServiceItemName())) {
            ServiceItemQueryContext serviceItemQueryContext = ServiceItemQueryContext.builder().itemName(request.getServiceItemName().trim()).build();
            List<ServiceItem> serviceItems = jdhServiceItemRepository.queryJdhItemList(serviceItemQueryContext);
            if (CollUtil.isNotEmpty(serviceItems)) {
                itemIds = serviceItems.stream().map(ServiceItem::getItemId).collect(Collectors.toList());
            } else {
                return pageDto;
            }
        }
        if (request.getServiceItemId() != null && CollUtil.isNotEmpty(itemIds) && !itemIds.contains(request.getServiceItemId())) {
            return pageDto;
        }
        request.setServiceItemIds(itemIds);
        if (request.getServiceItemId() != null) {
            request.setServiceItemIds(null);
        }
        Page<JdhStationServiceItemRel> page = providerStoreRepository.queryStationServiceItemListPage(ProviderStoreApplicationConverter.INSTANCE.requestToModel(request));
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        if (CollUtil.isEmpty(page.getRecords())) {
            pageDto.setList(Collections.emptyList());
            log.info("ProviderStoreApplicationImpl#queryStoreInfoPage, pageDto={}", JSON.toJSONString(pageDto));
            return pageDto;
        }
        pageDto.setList(page.getRecords().stream().map(ProviderStoreApplicationConverter.INSTANCE::modelToDto).filter(Objects::nonNull).collect(Collectors.toList()));
        ServiceItemQueryContext serviceItemQueryContext = ServiceItemQueryContext.builder().itemIds(pageDto.getList().stream().map(JdhStationServiceItemRelDto::getServiceItemId).collect(Collectors.toSet())).build();
        List<ServiceItem> serviceItems = jdhServiceItemRepository.queryJdhItemList(serviceItemQueryContext);
        if (CollUtil.isNotEmpty(serviceItems)) {
            Map<Long, ServiceItem> itemMap = serviceItems.stream().collect(Collectors.toMap(ServiceItem::getItemId, a -> a, (k1, k2) -> k1));
            for (JdhStationServiceItemRelDto jdhStationServiceItemRelDto : pageDto.getList()) {
                if (CollUtil.isNotEmpty(itemMap) && itemMap.containsKey(jdhStationServiceItemRelDto.getServiceItemId())) {
                    ServiceItem serviceItem = itemMap.get(jdhStationServiceItemRelDto.getServiceItemId());
                    jdhStationServiceItemRelDto.setServiceItemName(serviceItem.getItemName());
                    jdhStationServiceItemRelDto.setServiceItemNameEn(serviceItem.getItemNameEn());
                }
                if (jdhStationServiceItemRelDto.getOnOffShelf() != null && jdhStationServiceItemRelDto.getOnOffShelf() == 1) {
                    jdhStationServiceItemRelDto.setOnOffShelfName("上架");
                } else {
                    jdhStationServiceItemRelDto.setOnOffShelfName("下架");
                }
            }
        }
        log.info("ProviderStoreApplicationImpl#queryStoreInfoPage, pageDto={}", JSON.toJSONString(pageDto));
        return pageDto;
    }

    /**
     * 查询门店项目信息列表
     *
     * @param request req
     * @return dto
     */
    @Override
    public List<JdhStationServiceItemRelDto> queryStationServiceItemRelList(JdhStationServiceItemRelRequest request) {
        log.info("ProviderStoreApplicationImpl#queryStoreInfoList, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProviderErrorCode.PARAM_NULL_ERROR);
        JdhStationServiceItemRel jdhStationServiceItemRel = ProviderStoreApplicationConverter.INSTANCE.requestToModel(request);
        List<JdhStationServiceItemRel> list = providerStoreRepository.queryStationServiceItemList(jdhStationServiceItemRel);
        if (Boolean.TRUE.equals(request.getQueryStationDetail()) && CollUtil.isNotEmpty(list)) {
            List<StoreInfoBo> storeInfoBoList = providerStoreExportServiceRpc.listByStoreIds(list.stream().map(JdhStationServiceItemRel::getStationId).collect(Collectors.toSet()));
            Map<String, StoreInfoBo> storeInfoBoMap = storeInfoBoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(StoreInfoBo::getJdStoreId, item -> item, (v1, v2) -> v2));
            for (JdhStationServiceItemRel rel : list) {
                if (storeInfoBoMap.containsKey(rel.getStationId())) {
                    StoreInfoBo storeInfoBo = storeInfoBoMap.get(rel.getStationId());
                    rel.setChannelNo(storeInfoBo.getChannelNo());
                    rel.setStationName(storeInfoBo.getStoreName());
                    rel.setStationStatus(storeInfoBo.getStatus());
                    rel.setStationHours(storeInfoBo.getStoreHours());
                    rel.setStationAddr(storeInfoBo.getStoreAddr());
                    rel.setStationLat(storeInfoBo.getLat());
                    rel.setStationLng(storeInfoBo.getLng());
                    rel.setStorePhone(storeInfoBo.getStorePhone());
                    rel.setLimitBuyStatus(storeInfoBo.getLimitBuyStatus());
                    rel.setChannelRuleCode(storeInfoBo.getChannelRuleCode());
                    rel.setStationStatus(storeInfoBo.getStatus());
                }
            }
        }
        return ProviderStoreApplicationConverter.INSTANCE.modelToDtoList(list);
    }

    /**
     * 查询门店项目信息
     *
     * @param request req
     * @return dto
     */
    @Override
    public JdhStationServiceItemRelDto queryStationServiceItemRel(JdhStationServiceItemRelRequest request) {
        log.info("ProviderStoreApplicationImpl#queryStationServiceItem, request={}", JSON.toJSONString(request));
        AssertUtils.nonNull(request, ProviderErrorCode.PARAM_NULL_ERROR);
        AssertUtils.nonNull(request.getServiceItemId() , ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("serviceItemId"));
        AssertUtils.hasText(request.getStationId(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("stationId"));
        JdhStationServiceItemRel jdhStationServiceItemRel = providerStoreRepository.queryStationServiceItem(ProviderStoreApplicationConverter.INSTANCE.requestToModel(request));
        ServiceItem serviceItem = jdhServiceItemRepository.find(ServiceItemIdentifier.builder().itemId(jdhStationServiceItemRel.getServiceItemId()).build());
        if (serviceItem != null) {
            jdhStationServiceItemRel.setServiceItemName(serviceItem.getItemName());
            jdhStationServiceItemRel.setServiceItemNameEn(serviceItem.getItemNameEn());
            jdhStationServiceItemRel.setSpecimenType(serviceItem.getSampleType());
            jdhStationServiceItemRel.setTestWay(serviceItem.getTestWay());
        }
        JdhMaterialPackage jdhMaterialPackage = jdhMaterialPackageRepository.query(JdhMaterialPackage.builder().materialPackageId(jdhStationServiceItemRel.getMaterialPackageId()).build());
        if (jdhMaterialPackage != null) {
            jdhStationServiceItemRel.setMaterialPackageName(jdhMaterialPackage.getMaterialPackageName());
        }
        JdhStationServiceItemRelDto dto = ProviderStoreApplicationConverter.INSTANCE.modelToDto(jdhStationServiceItemRel);
        log.info("ProviderStoreApplicationImpl#queryStationServiceItem, dto={}", JSON.toJSONString(dto));
        return dto;
    }

    /**
     * 更新门店项目信息
     *
     * @param cmd cmd
     * @return dto
     */
    @Override
    public Boolean deleteStationServiceItemRel(JdhStationServiceItemRelDeleteCmd cmd) {
        log.info("ProviderStoreApplicationImpl#deleteStationServiceItemRel, cmd={}", JSON.toJSONString(cmd));
        if (cmd == null || StringUtils.isBlank(cmd.getStationId()) || cmd.getServiceItemId() == null) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        List<JdhSkuItemRel> jdhSkuItemRels = jdhSkuRepository.queryJdhSkuItemRelList(JdhSkuItemRel.builder().skuItemId(cmd.getServiceItemId().toString()).build());
        Set<Long> skuIds = jdhSkuItemRels.stream().map(JdhSkuItemRel::getSkuId).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(skuIds)) {
            JdhSkuListRequest request = new JdhSkuListRequest();
            request.setSkuIdList(skuIds);
            request.setQueryServiceItem(true);
            Map<Long, JdhSkuDto> maps = productApplication.queryJdhSkuInfoList(request);
            for (Long skuId : maps.keySet()) {
                JdhSkuDto jdhSkuDto = maps.get(skuId);
                Boolean ret = productApplication.checkSkuServiceItemAllInOneStore(jdhSkuDto, cmd.getStationId());
                if (!Boolean.TRUE.equals(ret)) {
                    throw new BusinessException(ProductErrorCode.PRODUCT_SERVICE_ITEM_ALL_IN_STATION);
                }
            }
        }
        return providerStoreRepository.deleteStationServiceItem(ProviderStoreApplicationConverter.INSTANCE.deleteCmdToModel(cmd)) > 0;
    }

    /**
     * 构建门店服务项信息
     *
     * @param jdhStationServiceItemRel rel
     */
    private void buildItemInfo(JdhStationServiceItemRel jdhStationServiceItemRel) {
        log.info("ProviderStoreApplicationImpl#buildItemInfo,start jdhStationServiceItemRel={}", JSON.toJSONString(jdhStationServiceItemRel));
        StoreInfoBo storeInfoBo = providerStoreExportServiceRpc.queryByStoreId(jdhStationServiceItemRel.getStationId());
        jdhStationServiceItemRel.setProvinceId(storeInfoBo.getProvinceId());
        jdhStationServiceItemRel.setCityId(storeInfoBo.getCityId());
        log.info("ProviderStoreApplicationImpl#buildItemInfo,end jdhStationServiceItemRel={}", JSON.toJSONString(jdhStationServiceItemRel));
    }

    /**
     * 根据实验室名称查询实验室列表
     * @param listLaboratoryByStoreNameCmd
     * @return
     */
    @Override
    public List<StoreInfoDto> listLaboratoryByStoreName(ListLaboratoryByStoreNameCmd listLaboratoryByStoreNameCmd) {
        log.info("ProviderStoreApplicationImpl#listLaboratoryByStoreName, request={}", JSON.toJSONString(listLaboratoryByStoreNameCmd));
        StoreInfoQueryParam storeInfoQueryParam = new StoreInfoQueryParam();
        storeInfoQueryParam.setStoreName(StringUtils.defaultIfBlank(listLaboratoryByStoreNameCmd.getStoreName(),"").trim());
        //查询实验室
        storeInfoQueryParam.setBusinessType(listLaboratoryByStoreNameCmd.getBusinessType());
        storeInfoQueryParam.setPageSize(listLaboratoryByStoreNameCmd.getPageSize());
        storeInfoQueryParam.setPageNum(listLaboratoryByStoreNameCmd.getPageNum());
        storeInfoQueryParam.setStoreStatus(listLaboratoryByStoreNameCmd.getStoreStatus());
        PageInfo<StoreInfoBo> page = providerStoreExportServiceRpc.pageStoreByParams(storeInfoQueryParam);
        return ProviderStoreApplicationConverter.INSTANCE.boToDtoList(page.getList());
    }

    /**
     * 异步导入项目列表
     *
     * @param cmd cmd
     * @return true
     */
    @Override
    public Boolean importServiceItem(StationServiceItemImportCmd cmd) {
        log.info("ProviderStoreApplicationImpl#importServiceItem cmd={}", JSON.toJSONString(cmd));
        AssertUtils.nonNull(cmd, ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("StationServiceItemImportCmd"));
        AssertUtils.hasText(cmd.getFileId(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("文件id"));
        JdhFile jdhFile = jdhFileRepository.find(JdhFileIdentifier.builder().fileId(Long.parseLong(cmd.getFileId())).build());
        if (jdhFile == null) {
            throw new BusinessException(SupportErrorCode.SUPPORT_FILE_UPLOAD_FILE_NOT_EXIST);
        }
        String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.IMPORT_STATION_SERVICE_ITEM_IMPORT_LOCK_KEY, cmd.getErp());
        boolean lock = redisUtil.tryLock(lockRedisKey, RedisKeyEnum.IMPORT_STATION_SERVICE_ITEM_IMPORT_LOCK_KEY.getExpireTime(), RedisKeyEnum.IMPORT_STATION_SERVICE_ITEM_IMPORT_LOCK_KEY.getExpireTimeUnit());
        if (!lock) {
            throw new BusinessException(ProviderErrorCode.PROVIDER_STATION_SERVICE_ITEM_IMPORT_FAIL);
        }
        try {
            String logId = Objects.toString(MDC.get("PFTID"), null);
            // 锁释放在导入逻辑,如果在这个try finally释放，异步锁会立刻释放
            CompletableFuture.runAsync(() -> importServiceItemExcel(logId, jdhFile.getFilePath(), cmd.getErp()), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_IMPORT_HAND_POOL));
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("ProviderStoreApplicationImpl#importServiceItem exception", e);
        }
        return Boolean.FALSE;
    }

    /**
     * 查询已开通省市
     *
     * @param param
     * @return
     */
    @Override
    public List<StationAddressDto> queryStationAddress(StationAddressRequest param) {
        StoreInfoQueryParam storeInfoQueryParam = ProviderStoreApplicationConverter.INSTANCE.storeAddressReq(param);
        storeInfoQueryParam.setStoreStatus(1);
        List<StationAddressBo> stationAddressBoList = providerStoreExportServiceRpc.queryMerchantStoreAddress(storeInfoQueryParam);
        if (CollUtil.isEmpty(stationAddressBoList)) {
            return Collections.emptyList();
        }
        return ProviderStoreApplicationConverter.INSTANCE.storeAddressBoToDtoList(stationAddressBoList);
    }

    /**
     * 根据实验室名称查询实验室列表
     *
     * @param param
     * @return
     */
    @Override
    public List<StoreInfoDto> listStationByAddress(StationAddressRequest param) {
        if (param == null || (param.getProvinceId() == null && param.getCityId() == null && param.getCountyId() == null)) {
            throw new BusinessException(SystemErrorCode.PARAM_NULL_ERROR);
        }
        StoreInfoQueryParam storeInfoQueryParam = new StoreInfoQueryParam();
        //查询实验室
        storeInfoQueryParam.setPageNum(1);
        storeInfoQueryParam.setPageSize(1);
        storeInfoQueryParam.setProvinceId(param.getProvinceId());
        storeInfoQueryParam.setCityId(param.getCityId());
        storeInfoQueryParam.setCountyId(param.getCountyId());
        storeInfoQueryParam.setStoreStatus(1);
        PageInfo<StoreInfoBo> countPage = providerStoreExportServiceRpc.pageStoreByParams(storeInfoQueryParam);
        if (countPage == null || countPage.getTotal() == 0) {
            return Collections.emptyList();
        }
        storeInfoQueryParam.setPageSize((int) countPage.getTotal());
        PageInfo<StoreInfoBo> page = providerStoreExportServiceRpc.pageStoreByParams(storeInfoQueryParam);
        return ProviderStoreApplicationConverter.INSTANCE.boToDtoList(page.getList());
    }

    /**
     * 读取导入文件
     *
     */
    private void importServiceItemExcel(String logId, String filePath, String erp){
        try {
            MDC.put("PFTID", logId);
            InputStream inputStream = fileManageService.get(filePath);
            ImportStationServiceItemListenerContext context = new ImportStationServiceItemListenerContext();
            context.setErp(erp);
            ImportStationServiceItemListener readListener = new ImportStationServiceItemListener(context);
            EasyExcelFactory.read(inputStream, ImportStationServiceItem.class, readListener).sheet().doRead();
        } catch (Exception e) {
            log.error("ProviderStoreApplicationImpl#importServiceItemExcel exception", e);
            throw new BusinessException(SystemErrorCode.SYSTEM_ERROR);
        } finally {
            String lockRedisKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.IMPORT_STATION_SERVICE_ITEM_IMPORT_LOCK_KEY, erp);
            redisUtil.unLock(lockRedisKey);
        }
    }

    /**
     * 新建实验室
     * @param addQuickMerchantStoreRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean addQuickMerchantStore(AddQuickMerchantStoreRequest addQuickMerchantStoreRequest) {
        this.checkAddQuickMerchantStoreParam(addQuickMerchantStoreRequest);
        //记录虚拟商家编号
        String firstChannelNo=null;
        if(DockingTypeEnum.MERCHANT.getType().equals(addQuickMerchantStoreRequest.getDockingType())){
            //创建商家
            ProviderContext providerContext = ProviderApplicationConverter.INS.toProviderContext(addQuickMerchantStoreRequest);
            Long channelNo = providerDomainService.createProvider(providerContext);
            addQuickMerchantStoreRequest.setChannelNo(channelNo+"");
            firstChannelNo = channelNo+"";
        }
        //创建实验室
        ProviderStoreContext providerStoreContext = ProviderStoreApplicationConverter.INSTANCE.toProviderStoreContext(addQuickMerchantStoreRequest);
        providerStoreContext.setStoreId(generateIdFactory.getIdStr());
        providerStoreContext.setFirstChannelNo(firstChannelNo);
        return providerStoreDomainService.addQuickMerchantStore(providerStoreContext);
    }

    /**
     * 编辑实验室
     * @param updateQuickMerchantStoreRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean updateQuickMerchantStore(UpdateQuickMerchantStoreRequest updateQuickMerchantStoreRequest) {
        this.checkUpdateQuickMerchantStoreParam(updateQuickMerchantStoreRequest);
        //记录虚拟商家编号
        String firstChannelNo=null;
        if(DockingTypeEnum.MERCHANT.getType().equals(updateQuickMerchantStoreRequest.getDockingType())){
            //端接入
            ProviderStoreDetailQuery providerStoreDetailQuery = ProviderStoreApplicationConverter.INSTANCE.toProviderStoreDetailQuery(updateQuickMerchantStoreRequest);
            Long channelNo;
            //查询实验室详情,判断是否已绑定虚拟商家
            StoreInfoBo storeInfoBo = providerStoreDomainService.queryMerchantStoreDetailByParam(providerStoreDetailQuery);
            if(StringUtils.isEmpty(storeInfoBo.getFirstChannelNo())){
                //创建虚拟商家
                ProviderContext providerContext = ProviderApplicationConverter.INS.toProviderContext(updateQuickMerchantStoreRequest);
                channelNo = providerDomainService.createProvider(providerContext);
            }else{
                //使用之前已创建好的虚拟商家编号
                channelNo = Long.parseLong(storeInfoBo.getFirstChannelNo());
            }
            firstChannelNo = channelNo+"";
        }
        //编辑实验室
        ProviderStoreContext providerStoreContext = ProviderStoreApplicationConverter.INSTANCE.toProviderStoreContext(updateQuickMerchantStoreRequest);
        providerStoreContext.setFirstChannelNo(firstChannelNo);
        StoreInfoBo fromStoreInfoBo = providerStoreDomainService.queryMerchantStoreDetailByParam(ProviderStoreDetailQuery.builder().jdStoreId(providerStoreContext.getJdStoreId()).build());

        providerStoreContext.setStoreId(fromStoreInfoBo.getStoreId());
        providerStoreContext.setChannelNo(fromStoreInfoBo.getChannelNo()+"");

        if(DockingTypeEnum.MERCHANT.getType().equals(updateQuickMerchantStoreRequest.getDockingType())){
            //端接入方式,ChangeChannelNo为空,渠道号使用firstChannelNo
            providerStoreContext.setChangeChannelNo(firstChannelNo);
        }else{
            //api接入方式,使用前端传过来的新渠道号
            providerStoreContext.setChangeChannelNo(updateQuickMerchantStoreRequest.getChannelNo());
        }
        return providerStoreDomainService.updateQuickMerchantStore(providerStoreContext);
    }

    /**
     * 查询实验室详情
     * @param queryMerchantStoreDetailByParamRequest
     * @return
     */
    @LogAndAlarm
    @Override
    public QueryMerchantStoreDetailResponse queryMerchantStoreDetailByParam(QueryMerchantStoreDetailByParamRequest queryMerchantStoreDetailByParamRequest) {
        ProviderStoreDetailQuery providerStoreDetailQuery = ProviderStoreApplicationConverter.INSTANCE.toProviderStoreDetailQuery(queryMerchantStoreDetailByParamRequest);
        StoreInfoBo storeInfoBo = providerStoreDomainService.queryMerchantStoreDetailByParam(providerStoreDetailQuery);
        if(storeInfoBo.getDockingType()==null){
            //兼容旧数据
            Provider provider = providerRepository.findByProviderId(storeInfoBo.getChannelNo());
            storeInfoBo.setDockingType(provider.getDockingType());
        }
        return ProviderStoreApplicationConverter.INSTANCE.toQueryMerchantStoreDetailResponse(storeInfoBo);
    }

    /**
     * 查询实验室列表分页
     * @param queryMerchantStoreListByParamRequest
     * @return
     */
    @LogAndAlarm
    @Override
    public PageDto<QueryMerchantStoreDetailResponse> queryMerchantStoreListByParam(QueryMerchantStoreListByParamRequest queryMerchantStoreListByParamRequest) {
        QueryMerchantStoreListByParamQuery queryMerchantStoreListByParamQuery = ProviderStoreApplicationConverter.INSTANCE.toQueryMerchantStoreListByParamQuery(queryMerchantStoreListByParamRequest);
        PageDto<StoreInfoBo> pageDto = providerStoreDomainService.queryMerchantStoreListByParam(queryMerchantStoreListByParamQuery);
        return JSON.parseObject(JSON.toJSONString(pageDto),new TypeReference<PageDto<QueryMerchantStoreDetailResponse>>(){});
    }

    /**
     * 实验室迁移配置
     * @param appointmentMigrationRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean appointmentMigration(AppointmentMigrationRequest appointmentMigrationRequest) {
        Boolean lock = jimClient.set(APPOINTMENTMIGRATION,"lock",60, TimeUnit.SECONDS,false);
        try{
            if(!lock){
                throw new BusinessException(new DynamicErrorCode("-1","实验室迁移中,请稍后在尝试!!"));
            }
            this.checkAppointmentMigrationParam(appointmentMigrationRequest);
            StoreInfoBo fromStoreInfoBo = providerStoreDomainService.queryMerchantStoreDetailByParam(ProviderStoreDetailQuery.builder().jdStoreId(appointmentMigrationRequest.getFromJdStoreId()).build());
            StoreInfoBo targetStoreInfoBo = providerStoreDomainService.queryMerchantStoreDetailByParam(ProviderStoreDetailQuery.builder().jdStoreId(appointmentMigrationRequest.getTargetJdStoreId()).build());
            appointmentMigrationRequest.setFromStoreId(fromStoreInfoBo.getStoreId());
            appointmentMigrationRequest.setFromChannelNo(fromStoreInfoBo.getChannelNo()+"");

            appointmentMigrationRequest.setTargetStoreId(targetStoreInfoBo.getStoreId());
            appointmentMigrationRequest.setTargetChannelNo(targetStoreInfoBo.getChannelNo()+"");

            AppointmentMigrationContext appointmentMigrationContext = ProviderStoreApplicationConverter.INSTANCE.toAppointmentMigrationContext(appointmentMigrationRequest);
            Boolean result = providerStoreDomainService.appointmentMigration(appointmentMigrationContext);
            if(result){
                //处理检测单数据
                executorPoolFactory.get(ThreadPoolConfigEnum.DEFAULT).execute(()->{
                    this.processHistoryMedPromise(appointmentMigrationContext,APPOINTMENTMIGRATION);
                });
            }
            return result;
        }catch (Exception ex){
            jimClient.del(APPOINTMENTMIGRATION);
            throw new BusinessException(new DynamicErrorCode("-1","系统异常,请稍后处理"));
        }
    }

    /**
     * 处理检测单数据
     * @param appointmentMigrationContext
     */
    public void processHistoryMedPromise(AppointmentMigrationContext appointmentMigrationContext,String lockKey){
        log.info("processHistoryMedPromise appointmentMigrationContext={}",JSON.toJSONString(appointmentMigrationContext));
        try{
            //查询商家信息
            Provider provider = providerRepository.findByProviderId(Long.parseLong(appointmentMigrationContext.getTargetChannelNo()));
            if(provider==null){
                log.error("商家信息为空,逻辑终止");
                throw new BusinessException(new DynamicErrorCode("-1","商家信息为空"));
            }
            //查询实验室数据
            StoreInfoBo storeInfoBo = providerStoreExportServiceRpc.queryByStoreId(appointmentMigrationContext.getTargetJdStoreId());

            if(!appointmentMigrationContext.getMigrationSwitch()){
                log.info("实验室迁移开关关闭,逻辑终止");
                return ;
            }

            //收样前,迁移检测单处理
            if(appointmentMigrationContext.getCreateMigration()){
                this.createMigration(appointmentMigrationContext,storeInfoBo,provider);
            }
            //收样后,迁移检测单处理
            if(appointmentMigrationContext.getReceiveMigration()){
                this.receiveMigration(appointmentMigrationContext,storeInfoBo,provider);
            }
        }finally {
            jimClient.del(lockKey);
        }
    }

    /**
     * 迁移收样之前的检测单数据
     * @param appointmentMigrationContext
     * @param storeInfoBo
     * @param provider
     */
    private void createMigration(AppointmentMigrationContext appointmentMigrationContext,StoreInfoBo storeInfoBo,Provider provider){
        log.info("已生成订单开始处理 appointmentMigrationContext={},storeInfoBo={}, provider={}",JSON.toJSONString(appointmentMigrationContext),JSON.toJSONString(storeInfoBo),JSON.toJSONString(provider));
        MedicalPromiseListFromEsParam medicalPromiseListFromEsParam =  new MedicalPromiseListFromEsParam();
        medicalPromiseListFromEsParam.setMedicalPromiseStatus(Arrays.asList(MedicalPromiseStatusEnum.MEDICAL_CHECK_WAIT.getStatus(),
                MedicalPromiseStatusEnum.WAIT_COLLECTED.getStatus(),
                MedicalPromiseStatusEnum.COLLECTED.getStatus()));
        medicalPromiseListFromEsParam.setStationId(appointmentMigrationContext.getFromJdStoreId());
        medicalPromiseListFromEsParam.setAppointmentStartTime(appointmentMigrationContext.getCreateMigrationAppointTimeBegin().getTime());
        medicalPromiseListFromEsParam.setAppointmentEndTime(appointmentMigrationContext.getCreateMigrationAppointTimeEnd().getTime());
        medicalPromiseListFromEsParam.setPageSize(100);
        PageDto<MedicalPromiseFromEsBo> pageDto = providerMedicalPromiseRpc.queryMedicalPromiseListFromEs(medicalPromiseListFromEsParam);
        if(CollectionUtils.isEmpty(pageDto.getList())) {
            log.info("pageDto 数据为空");
            return;
        }
        //批量更新检测数据
        UpdateMedicalPromiseStationParam updateMedicalPromiseStationParam = new UpdateMedicalPromiseStationParam();
        updateMedicalPromiseStationParam.setErp(appointmentMigrationContext.getErp());
        updateMedicalPromiseStationParam.setMedPromiseIds(pageDto.getList().stream().map(t->Long.parseLong(t.getMedicalPromiseId())).collect(Collectors.toList()));
        updateMedicalPromiseStationParam.setStationAddress(storeInfoBo.getStoreAddr());
        updateMedicalPromiseStationParam.setStationName(storeInfoBo.getStoreName());
        updateMedicalPromiseStationParam.setStationId(storeInfoBo.getJdStoreId());
        updateMedicalPromiseStationParam.setStationPhone(storeInfoBo.getContactPhone());
        providerMedicalPromiseRpc.updateMedPromiseStation(updateMedicalPromiseStationParam);
        if(com.jdh.o2oservice.base.enums.DockingTypeEnum.INTERFACE.getType().equals(provider.getDockingType())){
            //API接入,三方实验室预约单
            this.appointmentApiMerchant(pageDto.getList(),appointmentMigrationContext);
        }
        for (int i = 2; i <= pageDto.getTotalPage(); i++) {
            medicalPromiseListFromEsParam.setPageNum(i);
            pageDto = providerMedicalPromiseRpc.queryMedicalPromiseListFromEs(medicalPromiseListFromEsParam);
            if(CollectionUtils.isEmpty(pageDto.getList())) {
                log.info("pageDto 数据为空");
                return;
            }
            //批量更新检测数据
            updateMedicalPromiseStationParam.setErp(appointmentMigrationContext.getErp());
            updateMedicalPromiseStationParam.setMedPromiseIds(pageDto.getList().stream().map(t->Long.parseLong(t.getMedicalPromiseId())).collect(Collectors.toList()));
            updateMedicalPromiseStationParam.setStationAddress(storeInfoBo.getStoreAddr());
            updateMedicalPromiseStationParam.setStationName(storeInfoBo.getStoreName());
            updateMedicalPromiseStationParam.setStationId(storeInfoBo.getJdStoreId());
            updateMedicalPromiseStationParam.setStationPhone(storeInfoBo.getContactPhone());
            providerMedicalPromiseRpc.updateMedPromiseStation(updateMedicalPromiseStationParam);
            if(com.jdh.o2oservice.base.enums.DockingTypeEnum.INTERFACE.getType().equals(provider.getDockingType())){
                //API接入,三方实验室预约单
                this.appointmentApiMerchant(pageDto.getList(),appointmentMigrationContext);
            }
        }
    }

    /**
     * 迁移收样之后的检测单数据
     */
    private void receiveMigration(AppointmentMigrationContext appointmentMigrationContext,StoreInfoBo storeInfoBo,Provider provider){
        log.info("实验室已收样订单开始处理 appointmentMigrationContext={},storeInfoBo={}, provider={}",JSON.toJSONString(appointmentMigrationContext),JSON.toJSONString(storeInfoBo),JSON.toJSONString(provider));
        MedicalPromiseListFromEsParam medicalPromiseListFromEsParam =  new MedicalPromiseListFromEsParam();
        medicalPromiseListFromEsParam.setMedicalPromiseStatus(Arrays.asList(MedicalPromiseStatusEnum.MEDICAL_CHECK_ING.getStatus()));
        medicalPromiseListFromEsParam.setStationId(appointmentMigrationContext.getFromJdStoreId());
        medicalPromiseListFromEsParam.setCheckStartTime(appointmentMigrationContext.getReceiveMigrationCheckTimeBegin().getTime());
        medicalPromiseListFromEsParam.setCheckEndTime(appointmentMigrationContext.getReceiveMigrationCheckTimeEnd().getTime());
        medicalPromiseListFromEsParam.setPageSize(100);
        PageDto<MedicalPromiseFromEsBo> pageDto = providerMedicalPromiseRpc.queryMedicalPromiseListFromEs(medicalPromiseListFromEsParam);
        if(CollectionUtils.isEmpty(pageDto.getList())) {
            return;
        }
        //批量更新检测数据
        UpdateMedicalPromiseStationParam updateMedicalPromiseStationParam = new UpdateMedicalPromiseStationParam();
        updateMedicalPromiseStationParam.setErp(appointmentMigrationContext.getErp());
        updateMedicalPromiseStationParam.setMedPromiseIds(pageDto.getList().stream().map(t->Long.parseLong(t.getMedicalPromiseId())).collect(Collectors.toList()));
        updateMedicalPromiseStationParam.setStationAddress(storeInfoBo.getStoreAddr());
        updateMedicalPromiseStationParam.setStationName(storeInfoBo.getStoreName());
        updateMedicalPromiseStationParam.setStationId(storeInfoBo.getJdStoreId());
        updateMedicalPromiseStationParam.setStationPhone(storeInfoBo.getContactPhone());
        providerMedicalPromiseRpc.updateMedPromiseStation(updateMedicalPromiseStationParam);
        if(com.jdh.o2oservice.base.enums.DockingTypeEnum.INTERFACE.getType().equals(provider.getDockingType())){
            //API接入,三方实验室预约单
            this.appointmentApiMerchant(pageDto.getList(),appointmentMigrationContext);
        }
        for (int i = 2; i <= pageDto.getTotalPage(); i++) {
            medicalPromiseListFromEsParam.setPageNum(i);
            pageDto = providerMedicalPromiseRpc.queryMedicalPromiseListFromEs(medicalPromiseListFromEsParam);
            if(CollectionUtils.isEmpty(pageDto.getList())) {
                return;
            }
            //批量更新检测数据
            updateMedicalPromiseStationParam.setErp(appointmentMigrationContext.getErp());
            updateMedicalPromiseStationParam.setMedPromiseIds(pageDto.getList().stream().map(t->Long.parseLong(t.getMedicalPromiseId())).collect(Collectors.toList()));
            updateMedicalPromiseStationParam.setStationAddress(storeInfoBo.getStoreAddr());
            updateMedicalPromiseStationParam.setStationName(storeInfoBo.getStoreName());
            updateMedicalPromiseStationParam.setStationId(storeInfoBo.getJdStoreId());
            updateMedicalPromiseStationParam.setStationPhone(storeInfoBo.getContactPhone());
            providerMedicalPromiseRpc.updateMedPromiseStation(updateMedicalPromiseStationParam);
            if(com.jdh.o2oservice.base.enums.DockingTypeEnum.INTERFACE.getType().equals(provider.getDockingType())){
                //API接入,三方实验室预约单
                this.appointmentApiMerchant(pageDto.getList(),appointmentMigrationContext);
            }
        }
    }

    /**
     * 预约API商家检测
     * @return
     */
    private void appointmentApiMerchant(List<MedicalPromiseFromEsBo> medicalPromiseFromEsBoList,AppointmentMigrationContext appointmentMigrationContext){
        log.info("预约API商家检测 appointmentApiMerchant medicalPromiseFromEsBoList={}, provider={}",JSON.toJSONString(medicalPromiseFromEsBoList),JSON.toJSONString(appointmentMigrationContext));

        if(CollectionUtils.isEmpty(medicalPromiseFromEsBoList)){
            log.info("appointmentApiMerchant medicalPromiseFromEsBoList为空");
            return;
        }
        for (MedicalPromiseFromEsBo medicalPromiseFromEsBo:medicalPromiseFromEsBoList) {
            try{
                AppointmentApiMerchantParam appointmentApiMerchantParam = new AppointmentApiMerchantParam();
                appointmentApiMerchantParam.setMedicalPromiseId(Long.parseLong(medicalPromiseFromEsBo.getMedicalPromiseId()));
                appointmentApiMerchantParam.setExtJson(JSON.toJSONString(appointmentMigrationContext));
                Boolean result = providerMedicalPromiseRpc.appointmentApiMerchant(appointmentApiMerchantParam);

                MedicalPromise snapshot = medicalPromiseRepository.find(MedicalPromiseIdentifier.builder().medicalPromiseId(appointmentApiMerchantParam.getMedicalPromiseId()).build());
                //发送事件
                eventCoordinator.publish(EventFactory.newDefaultEvent(MedicalPromise.builder().medicalPromiseId(appointmentApiMerchantParam.getMedicalPromiseId()).build(), MedPromiseEventTypeEnum.MED_PROMISE_STATION_MIGRATION,
                        MedicalPromiseEventBody.builder()
                                .medicalPromiseId(appointmentApiMerchantParam.getMedicalPromiseId())
                                .beforeStatus(Integer.parseInt(medicalPromiseFromEsBo.getMedicalPromiseStatus()))
                                .status(snapshot.getStatus())
                                .stationId(appointmentMigrationContext.getTargetJdStoreId())
                                .beforeStationId(appointmentMigrationContext.getFromJdStoreId())
                                .specimenCode(medicalPromiseFromEsBo.getSpecimenCode())
                                .verticalCode(medicalPromiseFromEsBo.getVerticalCode())
                                .serviceType(medicalPromiseFromEsBo.getServiceType())
                                .version(snapshot.getVersion())
                                .build()));
                log.info("appointmentApiMerchant medicalPromiseId={} result={}",medicalPromiseFromEsBo.getMedicalPromiseId(),result);
            }catch (Exception ex){
                log.info("appointmentApiMerchant 异常",ex);
            }
        }
    }

    /**
     * 查询检测单数据
     * @param queryMedicalPromisePageRequest
     * @return
     */
    @Override
    @LogAndAlarm
    public PageDto<QueryMedicalPromisePageResponse> queryMedicalPromisePage(QueryMedicalPromisePageRequest queryMedicalPromisePageRequest) {
        MedicalPromiseListFromEsParam medicalPromiseListFromEsParam = new MedicalPromiseListFromEsParam();
        medicalPromiseListFromEsParam.setMedicalPromiseStatus(queryMedicalPromisePageRequest.getMedicalPromiseStatus());
        medicalPromiseListFromEsParam.setStationId(queryMedicalPromisePageRequest.getJdStoreId());
        medicalPromiseListFromEsParam.setPageNum(queryMedicalPromisePageRequest.getPageNum());
        medicalPromiseListFromEsParam.setPageSize(queryMedicalPromisePageRequest.getPageSize());
        if(queryMedicalPromisePageRequest.getSearchType()==1){
            medicalPromiseListFromEsParam.setAppointmentStartTime(queryMedicalPromisePageRequest.getQueryTimeBegin().getTime());
            medicalPromiseListFromEsParam.setAppointmentEndTime(queryMedicalPromisePageRequest.getQueryTimeEnd().getTime());
        }else{
            medicalPromiseListFromEsParam.setCheckStartTime(queryMedicalPromisePageRequest.getQueryTimeBegin().getTime());
            medicalPromiseListFromEsParam.setCheckEndTime(queryMedicalPromisePageRequest.getQueryTimeEnd().getTime());
        }

        PageDto<MedicalPromiseFromEsBo> pageDto = providerMedicalPromiseRpc.queryMedicalPromiseListFromEs(medicalPromiseListFromEsParam);
        //同步分页数据
        PageDto<QueryMedicalPromisePageResponse> result = JSON.parseObject(JSON.toJSONString(pageDto),new TypeReference<PageDto<QueryMedicalPromisePageResponse>>(){});

        if(CollectionUtils.isEmpty(result.getList())){
            log.info("result 为空,直接返回结果");
            return result;
        }

        AngelShipDBQuery angelShipDBQuery = new AngelShipDBQuery();
        angelShipDBQuery.setShipIds(pageDto.getList().stream().map(MedicalPromiseFromEsBo::getShipId).filter(Objects::nonNull).collect(Collectors.toList()));
        List<AngelShip> angelShips = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(angelShipDBQuery.getShipIds())){
            angelShips = angelShipRepository.findList(angelShipDBQuery);
        }
        Map<Long,List<AngelShip>> angelShipMap = angelShips.stream().collect(Collectors.groupingBy(AngelShip::getShipId));

        //同步业务数据
        List<QueryMedicalPromisePageResponse> queryMedicalPromisePageResponses = new ArrayList<>();
        for (MedicalPromiseFromEsBo medicalPromiseFromEsBo:pageDto.getList()) {
            QueryMedicalPromisePageResponse queryMedicalPromisePageResponse = JSON.parseObject(JSON.toJSONString(medicalPromiseFromEsBo),QueryMedicalPromisePageResponse.class);
            queryMedicalPromisePageResponse.setMedicalPromiseStatusDesc(MedicalPromiseStatusEnum.getDescByStatus(queryMedicalPromisePageResponse.getMedicalPromiseStatus()));
            queryMedicalPromisePageResponse.setAppointmentDate(medicalPromiseFromEsBo.getAppointmentStartTime());
            if(CollectionUtils.isNotEmpty(angelShipMap.get(medicalPromiseFromEsBo.getShipId()))){
                queryMedicalPromisePageResponse.setShipStatus(angelShipMap.get(medicalPromiseFromEsBo.getShipId()).get(0).getShipStatus());
            }
            queryMedicalPromisePageResponse.setShipStatusDesc(StanderAngelShipStatusEnum.getStatusDesc(queryMedicalPromisePageResponse.getShipStatus()));
            queryMedicalPromisePageResponse.setStoreAddr(medicalPromiseFromEsBo.getLaboratoryStationDetail());
            queryMedicalPromisePageResponses.add(queryMedicalPromisePageResponse);
        }
        result.setList(queryMedicalPromisePageResponses);
        return result;
    }

    /**
     * 参数校验
     */
    @LogAndAlarm
    private void checkAppointmentMigrationParam(AppointmentMigrationRequest appointmentMigrationRequest){
        AssertUtils.nonNull(appointmentMigrationRequest, ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("appointmentMigrationRequest"));

        AssertUtils.hasText(appointmentMigrationRequest.getFromJdStoreId(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("原实验室id"));

        AssertUtils.hasText(appointmentMigrationRequest.getTargetJdStoreId(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("目标实验室id"));

        AssertUtils.nonNull(appointmentMigrationRequest.getCreateMigration(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("已生成订单是否迁移"));
        if (Boolean.TRUE.equals(appointmentMigrationRequest.getCreateMigration())) {
            AssertUtils.nonNull(appointmentMigrationRequest.getCreateMigrationAppointTimeEnd(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("按用户预约时间选择迁移的订单时间范围"));
            AssertUtils.nonNull(appointmentMigrationRequest.getCreateMigrationAppointTimeBegin(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("按用户预约时间选择迁移的订单时间范围"));
        }

        AssertUtils.nonNull(appointmentMigrationRequest.getReceiveMigration(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("实验室已收样订单是否迁移"));
        if (Boolean.TRUE.equals(appointmentMigrationRequest.getReceiveMigration())) {
            AssertUtils.nonNull(appointmentMigrationRequest.getReceiveMigrationCheckTimeBegin(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("按样本接收时间选择迁移的订单时间范围"));
            AssertUtils.nonNull(appointmentMigrationRequest.getReceiveMigrationCheckTimeEnd(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("按样本接收时间选择迁移的订单时间范围"));
        }

        AssertUtils.nonNull(appointmentMigrationRequest.getMigrationSwitch(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("迁移配置总开关"));
        if(appointmentMigrationRequest.getFromJdStoreId().equals(appointmentMigrationRequest.getTargetJdStoreId())){
            throw new BusinessException(ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("迁移实验室和目标实验室不能是同一个"));
        }
    }

    /**
     * 参数校验
     * @param addQuickMerchantStoreRequest
     */
    private void checkAddQuickMerchantStoreParam(AddQuickMerchantStoreRequest addQuickMerchantStoreRequest) {
        AssertUtils.nonNull(addQuickMerchantStoreRequest, ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("addQuickMerchantStoreRequest"));
        AssertUtils.nonNull(addQuickMerchantStoreRequest.getBusinessType(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("业务类型"));
        AssertUtils.hasText(addQuickMerchantStoreRequest.getStoreAddr(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("实验室地址"));
        AssertUtils.hasText(addQuickMerchantStoreRequest.getStorePhone(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("门店电话"));
        AssertUtils.hasText(addQuickMerchantStoreRequest.getStoreName(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("实验室名称"));
        AssertUtils.hasText(addQuickMerchantStoreRequest.getContactName(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("联系人名称"));
        AssertUtils.hasText(addQuickMerchantStoreRequest.getErp(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("操作人名称"));
        AssertUtils.nonNull(addQuickMerchantStoreRequest.getStatus(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("操作人名称"));
        AssertUtils.hasText(addQuickMerchantStoreRequest.getContactPhone(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("联系人电话"));
        AssertUtils.hasText(addQuickMerchantStoreRequest.getLng(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("经度"));
        AssertUtils.hasText(addQuickMerchantStoreRequest.getLat(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("纬度"));
        AssertUtils.nonNull(addQuickMerchantStoreRequest.getDockingType(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("接入方式"));
        AssertUtils.hasText(addQuickMerchantStoreRequest.getChannelRuleCode(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("天算渠道"));
        if(DockingTypeEnum.INTERFACE.getType().equals(addQuickMerchantStoreRequest.getDockingType())){
            AssertUtils.hasText(addQuickMerchantStoreRequest.getChannelNo(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("渠道编号"));
        }
    }

    /**
     * 参数校验
     * @param updateQuickMerchantStoreRequest
     */
    private void checkUpdateQuickMerchantStoreParam(UpdateQuickMerchantStoreRequest updateQuickMerchantStoreRequest) {
        AssertUtils.nonNull(updateQuickMerchantStoreRequest, ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("addQuickMerchantStoreRequest"));
        AssertUtils.nonNull(updateQuickMerchantStoreRequest.getBusinessType(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("业务类型"));
        AssertUtils.hasText(updateQuickMerchantStoreRequest.getStoreAddr(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("实验室地址"));
        AssertUtils.hasText(updateQuickMerchantStoreRequest.getStorePhone(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("门店电话"));
        AssertUtils.hasText(updateQuickMerchantStoreRequest.getStoreName(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("实验室名称"));
        AssertUtils.hasText(updateQuickMerchantStoreRequest.getContactName(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("联系人名称"));
        AssertUtils.hasText(updateQuickMerchantStoreRequest.getErp(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("操作人名称"));
        AssertUtils.nonNull(updateQuickMerchantStoreRequest.getStatus(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("操作人名称"));
        AssertUtils.hasText(updateQuickMerchantStoreRequest.getContactPhone(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("联系人电话"));
        AssertUtils.hasText(updateQuickMerchantStoreRequest.getLng(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("经度"));
        AssertUtils.hasText(updateQuickMerchantStoreRequest.getLat(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("纬度"));
        AssertUtils.hasText(updateQuickMerchantStoreRequest.getJdStoreId(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("京东实验室id"));
        AssertUtils.nonNull(updateQuickMerchantStoreRequest.getDockingType(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("接入方式"));
        AssertUtils.hasText(updateQuickMerchantStoreRequest.getChannelRuleCode(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("天算渠道"));
        if(DockingTypeEnum.INTERFACE.getType().equals(updateQuickMerchantStoreRequest.getDockingType())){
            AssertUtils.hasText(updateQuickMerchantStoreRequest.getChannelNo(), ProviderErrorCode.PARAM_NULL_ERROR_FORMAT.formatDescription("渠道编号"));
        }
    }
}
