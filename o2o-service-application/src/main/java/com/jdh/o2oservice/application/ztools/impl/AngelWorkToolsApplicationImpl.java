package com.jdh.o2oservice.application.ztools.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.fastjson.JSON;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.angelpromise.convert.AngelPromiseApplicationConverter;
import com.jdh.o2oservice.application.medicalpromise.MedicalPromiseApplication;
import com.jdh.o2oservice.application.support.service.FileManageApplication;
import com.jdh.o2oservice.application.ztools.AngelWorkToolsApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.event.factory.EventFactory;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.groovy.core.GroovyScript;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.RedisLockUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelShipOrderDetailContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelPromiseBizErrorCode;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelShipRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelShipPageDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseErrorCode;
import com.jdh.o2oservice.core.domain.medpromise.enums.MedPromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.medpromise.event.MedicalPromiseEventBody;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.db.MedicalPromiseRepository;
import com.jdh.o2oservice.core.domain.product.bo.ServiceImagesExportBO;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseIdentifier;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DirectionServiceRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DongDongRobotRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.DuccRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.DirectionResultBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.DirectionRequestParam;
import com.jdh.o2oservice.core.domain.support.file.context.PutFileResult;
import com.jdh.o2oservice.core.domain.support.file.enums.FileOperationStatusEnum;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFileIdentifier;
import com.jdh.o2oservice.core.domain.support.file.repository.db.JdhFileRepository;
import com.jdh.o2oservice.core.domain.support.file.service.FileManageService;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailForManDto;
import com.jdh.o2oservice.export.medicalpromise.cmd.*;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseListRequest;
import com.jdh.o2oservice.export.medicalpromise.query.MedicalPromiseRequest;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.ztools.cmd.AngelWorkExportFileCmd;
import com.jdh.o2oservice.export.ztools.cmd.FlushShipCmd;
import com.jdh.o2oservice.export.ztools.cmd.ManBindSpecimenCodeCmd;
import com.jdh.o2oservice.export.ztools.cmd.ManSyncStationCmd;
import com.jdh.o2oservice.export.ztools.query.AngelWorkPageRequest;
import com.jdh.o2oservice.export.ztools.query.ManMedicalPromiseRequest;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryOrderDetailResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @ClassName AngelWorkToolsApplicationImpl
 * @Description 工单工具类应用服务
 * <AUTHOR>
 * @Date 2024/9/3 23:14
 */
@Service("angelWorkToolsApplication")
@Slf4j
public class AngelWorkToolsApplicationImpl implements AngelWorkToolsApplication {

    /** */
    @Resource
    private AngelWorkRepository angelWorkRepository;
    /** */
    @Resource
    private JdhFileRepository jdhFileRepository;
    /** */
    @Resource
    private FileManageService fileManageService;

    @Resource
    private GenerateIdFactory generateIdFactory;

    @Resource
    private MedicalPromiseApplication medicalPromiseApplication;

    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;

    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    private MedicalPromiseRepository medicalPromiseRepository;

    @Resource
    private PromiseRepository promiseRepository;

    /**
     * angelShipRepository
     */
    @Resource
    private AngelShipRepository angelShipRepository;

    @Resource
    private DirectionServiceRpc directionServiceRpc;

    @Resource
    private AddressRpc addressRpc;

    @Resource
    private AngelShipDomainService angelShipDomainService;

    @Autowired
    private DuccConfig duccConfig;

    @Autowired
    private EventCoordinator eventCoordinator;

    /**
     * 京me消息
     */
    @Resource
    DongDongRobotRpc dongDongRobotRpc;

    /**
     * fileManageApplication
     */
    @Resource
    private FileManageApplication fileManageApplication;

    /**
     * 线程池
     */
    @Resource
    private ExecutorPoolFactory executorPoolFactory;

    /**
     * 查询工单分页信息
     *
     * @param angelWorkPageRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkToolsApplicationImpl.queryAngelWorkPage")
    public PageDto<AngelWorkDetailForManDto> queryAngelWorkPage(AngelWorkPageRequest angelWorkPageRequest) {
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setWorkIds(Objects.nonNull(angelWorkPageRequest.getAngelWorkId()) ? Lists.newArrayList(angelWorkPageRequest.getAngelWorkId()) : null);
        angelWorkDBQuery.setAngelIds(Objects.nonNull(angelWorkPageRequest.getAngelId()) ? Lists.newArrayList(String.valueOf(angelWorkPageRequest.getAngelId())) : null);
        angelWorkDBQuery.setPromiseId(angelWorkPageRequest.getPromiseId());
        angelWorkDBQuery.setSourceId(angelWorkPageRequest.getSourceId());
        angelWorkDBQuery.setStatusList(Objects.nonNull(angelWorkPageRequest.getWorkStatus()) ? Lists.newArrayList(angelWorkPageRequest.getWorkStatus()) : null);
        angelWorkDBQuery.setJdOrderId(angelWorkPageRequest.getJdOrderId());
        angelWorkDBQuery.setWorkType(angelWorkPageRequest.getWorkType());
        angelWorkDBQuery.setVerticalCode(angelWorkPageRequest.getVerticalCode());
        angelWorkDBQuery.setServiceType(angelWorkPageRequest.getServiceType());
        angelWorkDBQuery.setPageNum(angelWorkPageRequest.getPageNum());
        angelWorkDBQuery.setPageSize(angelWorkPageRequest.getPageSize());

        Page<AngelWork> page = angelWorkRepository.findPage(angelWorkDBQuery);
        PageDto pageDto = new PageDto();
        if (Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())){
            return pageDto;
        }

        PageDto<AngelWorkDetailForManDto> angelWorkDtoPageDto = new PageDto<>();
        List<AngelWorkDetailForManDto> angelWorkDetailForManDtoList = Lists.newArrayList();
        page.getRecords().forEach(work -> {
            AngelWorkDetailForManDto angelWorkDetailForManDto = AngelPromiseApplicationConverter.instance.convertToAngelWorkDetailForManDto(work);
            angelWorkDetailForManDtoList.add(angelWorkDetailForManDto);
        });
        angelWorkDtoPageDto.setTotalPage(page.getPages());
        angelWorkDtoPageDto.setTotalCount(page.getTotal());
        angelWorkDtoPageDto.setPageSize(angelWorkPageRequest.getPageSize());
        angelWorkDtoPageDto.setPageNum(angelWorkPageRequest.getPageNum());
        angelWorkDtoPageDto.setList(angelWorkDetailForManDtoList);
        return angelWorkDtoPageDto;
    }

    /**
     * 导出工单文件
     *
     * @param angelWorkExportFileCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkToolsApplicationImpl.exportFile")
    public Boolean exportFile(AngelWorkExportFileCmd angelWorkExportFileCmd) {
        //参数检查
        AssertUtils.nonNull(angelWorkExportFileCmd, "参数异常");
        AssertUtils.isNotEmpty(angelWorkExportFileCmd.getFileTypeCode(), "文件类型编码不能为空");
        checkExportParam(angelWorkExportFileCmd);

        if(Objects.isNull(angelWorkExportFileCmd.getStartTime())
                && Objects.isNull(angelWorkExportFileCmd.getEndTime())
                && CollectionUtils.isEmpty(angelWorkExportFileCmd.getAngelWorkIdList())) {
            log.error("[AngelWorkToolsApplicationImpl -> exportFile],导出文件参数缺失!angelWorkExportFileCmd={}", JSON.toJSONString(angelWorkExportFileCmd));
            throw new BusinessException(BusinessErrorCode.ILLEGAL_ARG_ERROR);
        }

        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setCreateStartTime(angelWorkExportFileCmd.getStartTime());
        angelWorkDBQuery.setCreateEndTime(angelWorkExportFileCmd.getEndTime());
        angelWorkDBQuery.setWorkIds(angelWorkExportFileCmd.getAngelWorkIdList());
        angelWorkDBQuery.setStatusList(angelWorkExportFileCmd.getWorkStatusList());
        angelWorkDBQuery.setVerticalCode(angelWorkExportFileCmd.getVerticalCode());
        angelWorkDBQuery.setServiceType(angelWorkExportFileCmd.getServiceType());
        List<AngelWork> angelWorkList = angelWorkRepository.findList(angelWorkDBQuery);
        if(CollectionUtils.isEmpty(angelWorkList)) {
            log.error("[AngelWorkToolsApplicationImpl -> exportFile],没有工单信息!angelWorkExportFileCmd={}", JSON.toJSONString(angelWorkExportFileCmd));
            throw new BusinessException(AngelPromiseBizErrorCode.ANGEL_WORK_NOT_EXIST);
        }
        if(angelWorkList.size() > CommonConstant.THREE_HUNDRED) {
            log.error("[AngelWorkToolsApplicationImpl -> exportFile],工单数量太多了，磁盘和内存装不下!angelWorkExportFileCmd={}", JSON.toJSONString(angelWorkExportFileCmd));
            throw new BusinessException(AngelPromiseBizErrorCode.WORK_EXPORT_NUM_ERROR);
        }

        //查询文件id
        List<JdhFileIdentifier> downLoadFiles = Lists.newArrayList();
        String dirPath = "/home/<USER>/Data/business/file/" + TimeUtils.getCurrentDateTime() + File.separator;
        File filePath = new File(dirPath);
        if (!filePath.exists()) {
            filePath.mkdirs();
        }

        AtomicInteger fileCnt = new AtomicInteger();

        angelWorkList.forEach(work -> {
            List<Long> fileIds = work.getFileId(angelWorkExportFileCmd.getFileTypeCode());
            List<JdhFileIdentifier> fileIdentifierList = fileIds.stream().map(fileId -> JdhFileIdentifier.builder().fileId(fileId).build()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(fileIdentifierList)) {
                log.error("[AngelWorkToolsApplicationImpl -> exportFile],服务记录文件id为空!");
                return;
            }

            //获取文件链接
            List<JdhFile> jdhFileList = jdhFileRepository.findList(fileIdentifierList, null);
            jdhFileList.forEach(jdhFile -> {
                log.info("[AngelWorkToolsApplicationImpl -> exportFile],jdhFile={}", JSON.toJSONString(jdhFile));
                String filePath1 = jdhFile.getFilePath();
                if(StringUtils.isBlank(filePath1)) {
                    log.info("[AngelWorkToolsApplicationImpl -> exportFile]文件目录不存在,jdhFile={}", filePath1);
                    return;
                }
                String fileSuffix = filePath1.contains(".") ? filePath1.split("\\.")[1] : "jpg";
                InputStream inputStream = fileManageService.get(jdhFile.getFilePath());
                //文件名
                String fileName = work.getAngelId().concat("_")
                        .concat(work.getAngelName()).concat("_")
                        .concat(String.valueOf(work.getWorkId())).concat("_")
                        .concat(generateIdFactory.getIdStr()).concat("." + fileSuffix);
                File file = new File(filePath + File.separator + fileName);
                log.info("[AngelWorkToolsApplicationImpl -> exportFile],文件全路径。filePath={}", file.getAbsolutePath());
                try{
                    FileOutputStream fileOutputStream = new FileOutputStream(file);
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        fileOutputStream.write(buffer, 0, bytesRead);
                    }
                    fileOutputStream.close();
                    inputStream.close();
                    log.info("[AngelWorkToolsApplicationImpl -> exportFile],文件写入成功!writeFIle={}", file.getAbsolutePath());
                    //统计导出文件的个数
                    fileCnt.addAndGet(1);
                } catch (Exception ex) {
                    log.error("[AngelWorkToolsApplicationImpl -> exportFile],写文件失败!", ex);
                }
            });
        });

        log.info("[AngelWorkToolsApplicationImpl -> exportFile],共计导出文件{}份!", fileCnt.get());
        int cnt = fileCnt.get();
        if(cnt <= 0) {
            log.info("[AngelWorkToolsApplicationImpl -> exportFile],没有文件导出，直接退出!");
        }

        // 压缩文件
        String outputZipFile = "/home/<USER>/Data/business/"+ TimeUtils.getCurrentDateTime() +".zip";
        try {
            ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(outputZipFile));
            // 递归遍历源目录中的所有文件和子目录
            File sourceFile = new File(dirPath);
            addFileToZip(sourceFile, zipOut, "");
            // 关闭ZipOutputStream对象
            zipOut.close();
            log.info("[AngelWorkToolsApplicationImpl -> exportFile],文件以压缩!outputZipFile={}", outputZipFile);

            //上传压缩包
            File zipFile = new File(outputZipFile);
            FileInputStream fis = new FileInputStream(zipFile);

            String fileName = System.currentTimeMillis()+".zip";
            fileManageService.put(fileName, fis, FileManageServiceImpl.FolderPathEnum.BUSINESS_DOWNLOAD, null, Boolean.FALSE);
            log.info("[AngelWorkToolsApplicationImpl -> exportFile],文件以上传!fileName={}", fileName);
        } catch (Exception e) {
            log.error("[AngelWorkToolsApplicationImpl -> exportFile],!", e);
        }
        return Boolean.TRUE;
    }

    /**
     * 运营端绑码工具
     *
     * @param manBindSpecimenCodeCmd
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkToolsApplicationImpl.manBindSpecimenCode")
    public Boolean manBindSpecimenCode(ManBindSpecimenCodeCmd manBindSpecimenCodeCmd) {
        AssertUtils.nonNull(manBindSpecimenCodeCmd, "请求参数不能为空");
        AssertUtils.hasText(manBindSpecimenCodeCmd.getNewSpecimenCode(), "新条码不能为空");

        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.JD_BIND_SPECIMEN_CODE_LOCK_KEY, manBindSpecimenCodeCmd.getNewSpecimenCode());
        boolean lock = redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.JD_BIND_SPECIMEN_CODE_LOCK_KEY.getExpireTime(), RedisKeyEnum.JD_BIND_SPECIMEN_CODE_LOCK_KEY.getExpireTimeUnit());
        if(!lock) {
            log.error("[AngelWorkToolsApplicationImpl -> manBindSpecimenCode],任务正在执行!manBindSpecimenCodeCmd={}", JSON.toJSONString(manBindSpecimenCodeCmd));
            return Boolean.FALSE;
        }
        try{
            if (Objects.isNull(manBindSpecimenCodeCmd.getMedicalPromiseId()) && StringUtil.isBlank(manBindSpecimenCodeCmd.getSpecimenCode())){
                throw new BusinessException(MedPromiseErrorCode.PARAM_NULL);
            }
            MedicalPromiseDTO medicalPromiseDTO = medicalPromiseApplication.queryMedicalPromise(
                    MedicalPromiseRequest.builder().medicalPromiseId(manBindSpecimenCodeCmd.getMedicalPromiseId()).specimenCode(manBindSpecimenCodeCmd.getSpecimenCode()).build()
            );
            if (Objects.isNull(medicalPromiseDTO)){
                throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_NULL);
            }
            MedicalPromiseBindSpecimenCodeCmd medicalPromiseBindSpecimenCodeCmd = new MedicalPromiseBindSpecimenCodeCmd();
            medicalPromiseBindSpecimenCodeCmd.setVerticalCode(medicalPromiseDTO.getVerticalCode());
            medicalPromiseBindSpecimenCodeCmd.setServiceType(medicalPromiseDTO.getServiceType());
            MedicalPromiseCmdSpecimenCode medicalPromiseCmdSpecimenCode = new MedicalPromiseCmdSpecimenCode();
            medicalPromiseCmdSpecimenCode.setMedicalPromiseId(medicalPromiseDTO.getMedicalPromiseId());
            medicalPromiseCmdSpecimenCode.setPromiseId(medicalPromiseDTO.getPromiseId());
            medicalPromiseCmdSpecimenCode.setSpecimenCode(manBindSpecimenCodeCmd.getNewSpecimenCode());
            medicalPromiseBindSpecimenCodeCmd.setSpecimenCodeList(Lists.newArrayList(medicalPromiseCmdSpecimenCode));
            medicalPromiseBindSpecimenCodeCmd.setCheckCodeRules(false);
            medicalPromiseApplication.batchBindSpecimenCode(medicalPromiseBindSpecimenCodeCmd);

            if(Objects.nonNull(manBindSpecimenCodeCmd.getManSource())
                    && CommonConstant.ONE == manBindSpecimenCodeCmd.getManSource()
                    && MedicalPromiseStatusEnum.COMPLETED.getStatus().equals(medicalPromiseDTO.getStatus())) {
                manBindSpecimenCodeCmd.setSubmitToStation(Boolean.FALSE);
            }

            //如果需要提交给实验室
            if (Objects.equals(Boolean.TRUE,manBindSpecimenCodeCmd.getSubmitToStation())){
                //如果是非自检测的单子，提交给实验室（因为自检测绑完码后会主动提交）
                JdhVerticalBusiness jdhVerticalBusiness = verticalBusinessRepository.find(medicalPromiseDTO.getVerticalCode());
                if (!StringUtil.equals(BusinessModeEnum.SELF_TEST.getCode(),jdhVerticalBusiness.getBusinessModeCode())){
                    medicalPromiseApplication.submitMedicalPromiseToStation(MedicalPromiseSubmitCmd.builder().medicalPromiseId(medicalPromiseDTO.getMedicalPromiseId()).build());
                }
            }
            return Boolean.TRUE;
        }finally {
            if(lock) {
                redisLockUtil.unLock(lockKey);
            }
        }
    }

    /**
     * 查询检测单列表
     *
     * @param manMedicalPromiseRequest
     * @return
     */
    @Override
    @LogAndAlarm(jKey = "AngelWorkToolsApplicationImpl.queryMedicalPromise")
    public List<MedicalPromiseDTO> queryMedicalPromise(ManMedicalPromiseRequest manMedicalPromiseRequest) {
        MedicalPromiseListRequest medicalPromiseListRequest = new MedicalPromiseListRequest();
        medicalPromiseListRequest.setPromiseId(manMedicalPromiseRequest.getPromiseId());
        return medicalPromiseApplication.queryMedicalPromiseList(medicalPromiseListRequest);
    }

    /**
     * 同步采样码到实验室
     *
     * @param manSyncStationCmd
     * @return
     */
    @Override
    public Boolean syncMedicalStation(ManSyncStationCmd manSyncStationCmd) {
        AssertUtils.nonNull(manSyncStationCmd, "参数异常");
        AssertUtils.nonNull(manSyncStationCmd.getMedicalPromiseId(), "检测单id不能为空");

        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.JD_SYNC_SPECIMEN_STATION_LOCK_KEY, manSyncStationCmd.getMedicalPromiseId());
        boolean lock = redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.JD_SYNC_SPECIMEN_STATION_LOCK_KEY.getExpireTime(), RedisKeyEnum.JD_SYNC_SPECIMEN_STATION_LOCK_KEY.getExpireTimeUnit());
        if(!lock) {
            log.error("[AngelWorkToolsApplicationImpl -> syncMedicalStation],任务正在执行!manSyncStationCmd={}", JSON.toJSONString(manSyncStationCmd));
            return Boolean.FALSE;
        }

        try {
            MedicalPromise medicalPromise = medicalPromiseRepository.findMedicalPromise(MedicalPromiseRepQuery.builder().medicalPromiseId(manSyncStationCmd.getMedicalPromiseId()).build());

            if (duccConfig.getQuickCheckStationIdWhiteList().contains(medicalPromise.getStationId())){
                eventCoordinator.publish(EventFactory.newDefaultEvent(medicalPromise, MedPromiseEventTypeEnum.MED_PROMISE_TOOL_SUBMIT,
                        MedicalPromiseEventBody.builder()
                                .medicalPromiseId(medicalPromise.getMedicalPromiseId())
                                .status(medicalPromise.getStatus())
                                .specimenCode(medicalPromise.getSpecimenCode())
                                .verticalCode(medicalPromise.getVerticalCode())
                                .serviceType(medicalPromise.getServiceType())
                                .promiseId(medicalPromise.getPromiseId())
                                .build()));
                return Boolean.TRUE;
            }else {
                return medicalPromiseApplication.submitMedicalPromiseToStation(MedicalPromiseSubmitCmd.builder().medicalPromiseId(manSyncStationCmd.getMedicalPromiseId()).build());
            }
        }finally {
            if(lock){
                redisLockUtil.unLock(lockKey);
            }
        }
    }

    /**
     * 批量推送实验室
     *
     * @param manSyncStationCmd
     * @return
     */
    @Override
    public Boolean syncPromiseStation(ManSyncStationCmd manSyncStationCmd) {
        AssertUtils.nonNull(manSyncStationCmd, "参数异常");
        AssertUtils.nonNull(manSyncStationCmd.getPromiseId(), "检测单id不能为空");

        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.JD_BATCH_SYNC_SPECIMEN_STATION_LOCK_KEY, manSyncStationCmd.getPromiseId());
        boolean lock = redisLockUtil.tryLock(lockKey, "1", RedisKeyEnum.JD_BATCH_SYNC_SPECIMEN_STATION_LOCK_KEY.getExpireTime(), RedisKeyEnum.JD_BATCH_SYNC_SPECIMEN_STATION_LOCK_KEY.getExpireTimeUnit());
        if(!lock) {
            log.error("[AngelWorkToolsApplicationImpl -> manBindSpecimenCode],任务正在执行!manSyncStationCmd={}", JSON.toJSONString(manSyncStationCmd));
            return Boolean.FALSE;
        }
        try{
            BatchMedicalPromiseSubmitCmd batchMedicalPromiseSubmitCmd = new BatchMedicalPromiseSubmitCmd();
            batchMedicalPromiseSubmitCmd.setPromiseId(manSyncStationCmd.getPromiseId());
            return medicalPromiseApplication.batchSubmitMedicalPromiseToStation(batchMedicalPromiseSubmitCmd);
        }finally {
            if(lock) {
                redisLockUtil.unLock(lockKey);
            }
        }
    }

    /**
     * 检查检测单状态
     *
     * @param optionCode
     * @param manBindSpecimenCodeCmd
     * @return
     */
    @Override
    public Boolean checkMedicalPromiseStatus(String optionCode, ManBindSpecimenCodeCmd manBindSpecimenCodeCmd) {
        MedicalPromiseListQuery medicalPromiseListQuery = new MedicalPromiseListQuery();
        medicalPromiseListQuery.setMedicalPromiseId(manBindSpecimenCodeCmd.getMedicalPromiseId());
        List<MedicalPromise> medicalPromises = medicalPromiseRepository.queryMedicalPromiseList(medicalPromiseListQuery);
        if(CollectionUtils.isEmpty(medicalPromises)) {
            log.error("[AngelWorkToolsApplicationImpl.checkMedicalPromsiseStatus],检查");
            throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_NULL);
        }
        MedicalPromise medicalPromise = medicalPromises.get(0);
        JdhPromise jdhPromise = promiseRepository.find(new JdhPromiseIdentifier(medicalPromise.getPromiseId()));
        if("bindSpecimenCode".equalsIgnoreCase(optionCode)) {
            boolean serviceTypeFlag = ServiceTypeEnum.TEST.getServiceType().equals(jdhPromise.getServiceType());
            boolean promiseUnSupport = JdhPromiseStatusEnum.UN_BIND_STATUS.contains(jdhPromise.getPromiseStatus());
            boolean medPromiseUnSupport = MedicalPromiseStatusEnum.UN_BIND_STATUS.contains(medicalPromise.getStatus());
            log.info("[AngelWorkToolsApplicationImpl.checkMedicalPromsiseStatus],serviceTypeFlag={}, promiseUnSupport={}, medPromiseUnSupport={}", serviceTypeFlag, promiseUnSupport, medPromiseUnSupport);
            if(!serviceTypeFlag || promiseUnSupport || medPromiseUnSupport) {
                throw new BusinessException(MedPromiseErrorCode.MEDICAL_PROMISE_STATUS_NOT_ALLOW_OPERATION);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 执行groovy脚本
     *
     * @param scriptName
     * @return
     */
    @Override
    public Boolean executeGroovyScript(String scriptName) {
        log.info("[AngelWorkToolsApplicationImpl -> executeGroovyScript], 执行脚本的结果为, scriptName={}", JSON.toJSONString(scriptName));
        return Boolean.TRUE;
    }

    /**
     * @param flushShipCmd
     * @return
     */
    @Override
    public Boolean flushShipAddress(FlushShipCmd flushShipCmd) {
        String lockKey = RedisKeyEnum.getRedisKey(RedisKeyEnum.FLUSH_PROMISE_ADDRESS_PREFIX, "123", "123");
        if(!redisLockUtil.tryLockWithThrow(lockKey, "1",RedisKeyEnum.FLUSH_PROMISE_ADDRESS_PREFIX.getExpireTime(), RedisKeyEnum.FLUSH_PROMISE_ADDRESS_PREFIX.getExpireTimeUnit())) {
            log.error("[JdhPromiseToolsApplicationServiceImpl->flushPromiseAddress],分布式所失败!");
            return Boolean.FALSE;
        }
        try{
            AngelShipPageDBQuery dbQuery = new AngelShipPageDBQuery();
            dbQuery.setStartTime(flushShipCmd.getStartTime());
            dbQuery.setEndTime(flushShipCmd.getEndTime());

            int pageNum = dbQuery.getPageNum();

            dbQuery.setPageNum(pageNum > 0 ? pageNum : 1);
            dbQuery.setPageSize(flushShipCmd.getPageSize() > 10 ? flushShipCmd.getPageSize() : 100);

            Page<AngelShip> angelShipPage = angelShipRepository.finShipPage(dbQuery);

            do{
                log.info("[AngelWorkToolsApplicationImpl -> flushShipAddress],开始处理第{}页!", pageNum);

                if(Objects.isNull(angelShipPage) || CollectionUtils.isEmpty(angelShipPage.getRecords())) {
                    log.error("[AngelWorkToolsApplicationImpl -> flushShipAddress], 没有查询到运单={}", JSON.toJSONString(angelShipPage));
                    return Boolean.TRUE;
                }
                log.info("[JdhPromiseToolServiceImpl -> flushPromiseAddress],本页数据条数{}条!", angelShipPage.getRecords().size());

                List<AngelShip> records = angelShipPage.getRecords();
                DirectionRequestParam directionRequestParam = new DirectionRequestParam();
                directionRequestParam.setTravelMode(DirectionServiceRpc.TravelMode.BICYCLING);

                for (AngelShip record : records) {
                    if(Objects.nonNull(record.getTotalDistance())) {
                        log.info("[AngelWorkToolsApplicationImpl -> flushShipAddress],已经有距离了={},无需处理!", record.getShipId());
                        continue;
                    }
                    try{
                        if(DeliveryTypeEnum.RIDER_DELIVERY.getType().equals(record.getType())) {
                            //查询订单详情
                            AngelShipOrderDetailContext detailContext = new AngelShipOrderDetailContext();
                            detailContext.setShipId(record.getShipId());
                            detailContext.setOutShipId(record.getOutShipId());
                            detailContext.setDeliveryType(record.getType());
                            try{
                                DeliveryOrderDetailResponse detailResponse = angelShipDomainService.getShipOrderDetail(detailContext);
                                if(Objects.isNull(detailResponse)) {
                                    log.error("[AngelWorkToolsApplicationImpl -> flushShipAddress],运单详情信息是空的!");
                                    throw new BusinessException(BusinessErrorCode.RPC_JSF_ERROR);
                                }
                                record.setTotalDistance(BigDecimal.valueOf(detailResponse.getDistance()));
                            }catch (Exception ex) {
                                log.error("[AngelWorkToolsApplicationImpl -> flushShipAddress],运单详情信息是空,查询规划路径!");
                                if(StringUtils.isBlank(record.getSenderFullAddress()) || StringUtils.isBlank(record.getReceiverFullAddress())) {
                                    continue;
                                }
                                GisPointBo senderPos = addressRpc.getLngLatByAddress(record.getSenderFullAddress());
                                GisPointBo receivePos = addressRpc.getLngLatByAddress(record.getReceiverFullAddress());
                                directionRequestParam.setFromLocation(String.format("%s,%s", senderPos.getLatitude(), senderPos.getLongitude()));
                                directionRequestParam.setToLocation(String.format("%s,%s", receivePos.getLatitude(), receivePos.getLongitude()));
                                DirectionResultBO directionResult = directionServiceRpc.getDirectionResult(directionRequestParam);
                                record.setTotalDistance(BigDecimal.valueOf(directionResult.getDistance()));
                            }
                        }else if(DeliveryTypeEnum.SELF_DELIVERY.getType().equals(record.getType())) {
                            if(StringUtils.isBlank(record.getSenderFullAddress()) || StringUtils.isBlank(record.getReceiverFullAddress())) {
                                continue;
                            }
                            GisPointBo senderPos = addressRpc.getLngLatByAddress(record.getSenderFullAddress());
                            GisPointBo receivePos = addressRpc.getLngLatByAddress(record.getReceiverFullAddress());
                            directionRequestParam.setFromLocation(String.format("%s,%s", senderPos.getLatitude(), senderPos.getLongitude()));
                            directionRequestParam.setToLocation(String.format("%s,%s", receivePos.getLatitude(), receivePos.getLongitude()));
                            DirectionResultBO directionResult = directionServiceRpc.getDirectionResult(directionRequestParam);
                            record.setTotalDistance(BigDecimal.valueOf(directionResult.getDistance()));
                        }else {
                            log.info("[AngelWorkToolsApplicationImpl -> flushShipAddress],运单类型不需要处理.recordId={}", record.getShipId());
                        }
                        angelShipRepository.saveDistance(record);
                    }catch (Exception ex) {
                        log.error("[AngelWorkToolsApplicationImpl -> flushShipAddress], 处理运单信息异常={}", JSON.toJSONString(record), ex);
                    }
                }

                pageNum += 1;
                dbQuery.setPageNum(pageNum);
                angelShipPage = angelShipRepository.finShipPage(dbQuery);
            }while (pageNum < CommonConstant.NUMBER_FIVE_THOUSAND);
        }finally {
            redisLockUtil.unLock(lockKey);
        }

        return Boolean.TRUE;
    }

    /**
     * 工单导出文件
     *
     * @param request
     * @return
     */
    @Override
    public Boolean exportServiceImg(AngelWorkPageRequest request) {
        if (request == null || StringUtils.isBlank(request.getErp())) {
            throw new BusinessException(AngelPromiseBizErrorCode.RECEIVE_MSG_USER_ERP_NOT_NULL);
        }
        CompletableFuture.runAsync(() -> export(request), executorPoolFactory.get(ThreadPoolConfigEnum.MAN_IMPORT_HAND_POOL));
        return true;
    }

    /**
     * 导出数据的参数检查
     *
     * @param angelWorkExportFileCmd
     */
    private void checkExportParam(AngelWorkExportFileCmd angelWorkExportFileCmd) {
        Date now = new Date();
        if(CollectionUtils.isNotEmpty(angelWorkExportFileCmd.getAngelWorkIdList())
                && angelWorkExportFileCmd.getAngelWorkIdList().size() > CommonConstant.THREE_HUNDRED) {
            log.error("[AngelWorkToolsApplicationImpl -> checkExportParam],导出的工单数量不能大约300条!");
            throw new BusinessException(AngelPromiseBizErrorCode.WORK_EXPORT_NUM_ERROR);
        }
        if(CollectionUtils.isEmpty(angelWorkExportFileCmd.getAngelWorkIdList())
                && Objects.isNull(angelWorkExportFileCmd.getStartTime())) {
            log.error("[AngelWorkToolsApplicationImpl -> checkExportParam],开始");
            throw new BusinessException(AngelPromiseBizErrorCode.WORK_EXPORT_START_DATE_EMPTY_ERROR);
        }
        if(Objects.nonNull(angelWorkExportFileCmd.getStartTime()) && Objects.isNull(angelWorkExportFileCmd.getEndTime())) {
            angelWorkExportFileCmd.setEndTime(now);
        }

        if(Objects.nonNull(angelWorkExportFileCmd.getStartTime()) && Objects.nonNull(angelWorkExportFileCmd.getEndTime())) {
            long l = DateUtil.betweenDay(angelWorkExportFileCmd.getStartTime(), angelWorkExportFileCmd.getEndTime(), Boolean.FALSE);
            if(l > 3) {
               throw new BusinessException(AngelPromiseBizErrorCode.WORK_EXPORT_START_DATE_OVER_ERROR);
            }
        }
    }

    private static void addFileToZip(File file, ZipOutputStream zipOut, String parentDir) throws IOException {
        if (file.isDirectory()) {
            // 如果是目录，递归调用addFileToZip方法
            String dirPath = parentDir + file.getName() + "/";
            zipOut.putNextEntry(new ZipEntry(dirPath));
            File[] files = file.listFiles();
            for (File f : files) {
                addFileToZip(f, zipOut, dirPath);
            }
        } else {
            // 如果是文件，读取文件内容并写入到压缩流中
            byte[] data = new byte[1024];
            FileInputStream fis = new FileInputStream(file);
            ZipEntry entry = new ZipEntry(parentDir + file.getName());
            zipOut.putNextEntry(entry);
            int count;
            while ((count = fis.read(data))!= -1) {
                zipOut.write(data, 0, count);
            }
            fis.close();
        }
    }

    /**
     * 导出
     * @param angelWorkPageRequest
     */
    private void export(AngelWorkPageRequest angelWorkPageRequest) {
        log.info("AngelWorkToolsApplicationImpl->getData,pageRequest={}", com.alibaba.fastjson.JSON.toJSONString(angelWorkPageRequest));
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setWorkIds(Objects.nonNull(angelWorkPageRequest.getAngelWorkId()) ? Lists.newArrayList(angelWorkPageRequest.getAngelWorkId()) : null);
        angelWorkDBQuery.setAngelIds(Objects.nonNull(angelWorkPageRequest.getAngelId()) ? Lists.newArrayList(String.valueOf(angelWorkPageRequest.getAngelId())) : null);
        angelWorkDBQuery.setPromiseId(angelWorkPageRequest.getPromiseId());
        angelWorkDBQuery.setSourceId(angelWorkPageRequest.getSourceId());
        angelWorkDBQuery.setStatusList(Objects.nonNull(angelWorkPageRequest.getWorkStatus()) ? Lists.newArrayList(angelWorkPageRequest.getWorkStatus()) : null);
        angelWorkDBQuery.setJdOrderId(angelWorkPageRequest.getJdOrderId());
        angelWorkDBQuery.setWorkType(angelWorkPageRequest.getWorkType());
        angelWorkDBQuery.setVerticalCode(angelWorkPageRequest.getVerticalCode());
        String erp = angelWorkPageRequest.getErp();
        if (CollUtil.isNotEmpty(angelWorkPageRequest.getWorkStatusList())) {
            angelWorkDBQuery.setStatusList(angelWorkPageRequest.getWorkStatusList());
        }
        if (angelWorkPageRequest.getWorkStatus() != null) {
            angelWorkDBQuery.setStatusList(Lists.newArrayList(angelWorkPageRequest.getWorkStatus()));
        }
        if (StringUtils.isNotBlank(angelWorkPageRequest.getServiceStartTimeBegin())) {
            angelWorkDBQuery.setServiceStartTimeBegin(TimeUtils.timeStrToDate(angelWorkPageRequest.getServiceStartTimeBegin(), TimeFormat.LONG_PATTERN_LINE));
        }
        if (StringUtils.isNotBlank(angelWorkPageRequest.getServiceStartTimeEnd())) {
            angelWorkDBQuery.setServiceStartTimeEnd(TimeUtils.timeStrToDate(angelWorkPageRequest.getServiceStartTimeEnd(), TimeFormat.LONG_PATTERN_LINE));
        }
        int pageSize = 500;
        angelWorkDBQuery.setServiceType(angelWorkPageRequest.getServiceType());
        angelWorkDBQuery.setPageSize(pageSize);
        if (!Boolean.FALSE.equals(angelWorkPageRequest.getQueryExtServiceRecordFileIdsNotEmpty())) {
            angelWorkDBQuery.setQueryExtServiceRecordFileIdsNotEmpty(true);
        }
        Date endTime =TimeUtils.localDateTimeToDate(LocalDateTime.now().plusDays(CommonConstant.SEVEN));
        Boolean isPub = false;

        int currentSheet = 1;   // 当前处于第几个sheet
        int totalLine = 0;      // 总共写入的条数
        int currentBatch = 1;   // 当前写入excel的批次(第几页)
        int lineNum = 1;        // 行号,当前写入的是第几条数据
        int sheetSize = 50000;
        ByteArrayInputStream inputStream = null;
        try {
            SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
            if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
                Field field;
                try {
                    // SpreadsheetVersion.EXCEL2007的_maxTextLength变量
                    field = excel2007.getClass().getDeclaredField("_maxTextLength");
                    // 关闭反射机制的安全检查，可以提高性能
                    field.setAccessible(true);
                    // 重新设置这个变量属性值
                    field.set(excel2007,Integer.MAX_VALUE);
                } catch (Exception e) {
                    log.error("resetCellMaxTextLength", e);
                }
            }
            //存储输出流
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(byteArrayOutputStream, ServiceImagesExportBO.class).build();
            WriteSheet sheet = EasyExcel.writerSheet("sheet").build();

            while (true) {
                // 获取数据，然后currentBatch+1,下次调用就会获取新的数据
                angelWorkDBQuery.setPageNum(currentBatch);
                angelWorkDBQuery.setPageSize(pageSize);
                List<AngelWork> sourceDataList = new ArrayList<>();
                Page<AngelWork> nextPage = angelWorkRepository.findPage(angelWorkDBQuery);
                if (nextPage != null && CollUtil.isNotEmpty(nextPage.getRecords())) {
                    sourceDataList.addAll(nextPage.getRecords());
                    if (currentBatch == 1) {
                        if (StringUtils.isNotBlank(erp)) {
                            dongDongRobotRpc.sendDongDongRobotMessage("符合导出条件记录" + nextPage.getTotal() + "条,导出结果将通过京ME通知", erp);
                        }
                    }
                } else {
                    if (currentBatch == 1) {
                        if (StringUtils.isNotBlank(erp)) {
                            dongDongRobotRpc.sendDongDongRobotMessage("没有符合导出条件记录", erp);
                        }
                    }
                }
                currentBatch++;

                List<ServiceImagesExportBO> exportEntityList = new ArrayList<>();
                if (CollUtil.isNotEmpty(sourceDataList)) {
                    totalLine += sourceDataList.size();
                    log.info("EasyExcel开始写入第{}批数据,当前批次数据大小为{}", currentBatch - 1, sourceDataList.size());
                    for (AngelWork sourceData : sourceDataList) {
                        exportEntityList.add(convertSourceData2ExportEntity(sourceData, isPub, endTime));
                        lineNum++;

                        // 当前sheet数据已经到达最大值，将当前数据全写入当前sheet，下一条数据就会写入新sheet
                        if (lineNum > sheetSize) {
                            excelWriter.write(exportEntityList, sheet);
                            exportEntityList.clear();
                            lineNum = 1;
                            currentSheet++;
                            sheet = EasyExcel.writerSheet("sheet" + currentSheet).build();
                        }
                    }

                    // 写入excel
                    excelWriter.write(exportEntityList, sheet);
                } else {
                    // 未获取到数据,结束
                    break;
                }
            }
            excelWriter.finish();
            inputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            // 上传文件名称
            String uploadFileName = DomainEnum.ANGEL.getCode() + "_exportServiceImages_" + System.currentTimeMillis() + ".xlsx";
            PutFileResult putFileResult = fileManageService.put(uploadFileName, inputStream, FileManageServiceImpl.FolderPathEnum.FILE_OUT_PUT, null,Boolean.FALSE);
            String downloadUrl = fileManageService.getPublicUrl(putFileResult.getFilePath(), false, DateUtil.offsetDay(new Date(), 1));
            if (StringUtils.isNotBlank(erp)) {
                dongDongRobotRpc.sendDongDongRobotMessage("您执行的导出任务已完成,链接有效期1天,链接地址:" + downloadUrl, erp);
            }
            log.info("共导出{}条", totalLine);
        } catch (Exception e) {
            if (StringUtils.isNotBlank(erp)) {
                dongDongRobotRpc.sendDongDongRobotMessage("您执行的导出任务失败,失败原因:" + e.getMessage(), erp);
            }
            log.error("EasyExcel导出异常", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                    log.info("导出服务记录关闭流");
                } catch (Exception e) {
                    log.error("关闭流异常", e);
                }
            }
        }
    }

    /**
     * 转换成EXCEL对象
     * @param angelWork
     * @param isPub
     * @param endTime
     */
    private ServiceImagesExportBO convertSourceData2ExportEntity(AngelWork angelWork, Boolean isPub, Date endTime) {
        ServiceImagesExportBO imagesExportBO = new ServiceImagesExportBO();
        imagesExportBO.setOrderId(String.valueOf(angelWork.getJdOrderId()));
        imagesExportBO.setAngelStationId(angelWork.getAngelStationId());
        imagesExportBO.setAngelId(angelWork.getAngelId());
        imagesExportBO.setServiceStartTime(TimeUtils.dateTimeToStr(angelWork.getWorkStartTime()));
        imagesExportBO.setServiceEndTime(TimeUtils.dateTimeToStr(angelWork.getWorkEndTime()));
        AngelWorkStatusEnum angelWorkStatusEnum = AngelWorkStatusEnum.getEnumByCode(angelWork.getWorkType());
        imagesExportBO.setWorkStatus(angelWorkStatusEnum == null ? "无" : angelWorkStatusEnum.getShowDesc());
        if (CollectionUtils.isNotEmpty(angelWork.getJdhAngelWorkExtVo().getServiceRecordFileIds())) {
            GenerateGetUrlCommand command = new GenerateGetUrlCommand();
            command.setFileIds(Sets.newHashSet(angelWork.getJdhAngelWorkExtVo().getServiceRecordFileIds()));
            command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
            command.setIsPublic(isPub);
            command.setExpireTime(endTime);
            List<FilePreSignedUrlDto> urlDtos = fileManageApplication.generateGetUrl(command);
            List<String> medicalCertificateUrls = urlDtos.stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
            imagesExportBO.setServiceRecordPicUrls(JSON.toJSONString(medicalCertificateUrls));
        }
        if (CollectionUtils.isNotEmpty(angelWork.getJdhAngelWorkExtVo().getClothingFileIds())) {
            GenerateGetUrlCommand command = new GenerateGetUrlCommand();
            command.setFileIds(Sets.newHashSet(angelWork.getJdhAngelWorkExtVo().getClothingFileIds()));
            command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
            command.setIsPublic(isPub);
            command.setExpireTime(endTime);
            List<String> clothingPic = fileManageApplication.generateGetUrl(command).stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
            imagesExportBO.setClothingPicUrls(JSON.toJSONString(clothingPic));
        }
        if (CollectionUtils.isNotEmpty(angelWork.getJdhAngelWorkExtVo().getWasteDestroyFileIds())) {
            GenerateGetUrlCommand command = new GenerateGetUrlCommand();
            command.setFileIds(Sets.newHashSet(angelWork.getJdhAngelWorkExtVo().getWasteDestroyFileIds()));
            command.setDomainCode(DomainEnum.ANGEL_PROMISE.getCode());
            command.setIsPublic(isPub);
            command.setExpireTime(endTime);
            List<String> wasteUrls = fileManageApplication.generateGetUrl(command).stream().map(FilePreSignedUrlDto::getUrl).collect(Collectors.toList());
            imagesExportBO.setWastePicUrls(JSON.toJSONString(wasteUrls));
        }
        return imagesExportBO;
    }
}
