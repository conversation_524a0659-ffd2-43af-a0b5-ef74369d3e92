package com.jdh.o2oservice.application.product.service.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.jd.jim.cli.Cluster;
import com.jd.jim.cli.exception.ScriptNotFoundException;
import com.jdh.o2oservice.application.product.service.ProductLabelServiceApplication;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.RedisKeyEnum;
import com.jdh.o2oservice.export.product.dto.JdhLabelDto;
import com.jdh.o2oservice.export.product.enums.ProductLabelKeylEnum;
import com.jdh.o2oservice.export.product.query.JdhLabelAddressQuery;
import com.jdh.o2oservice.export.product.query.JdhLabelQuery;
import com.jdh.o2oservice.export.product.query.JdhLabelSkuQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.jd.jim.cli.protocol.ScriptOutputType.MULTI;


/**
 * @ClassName ProductLabelServiceApplicationImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 11:40
 **/
@Service
@Slf4j
public class ProductLabelServiceApplicationImpl implements ProductLabelServiceApplication, InitializingBean {


    @Resource
    private Cluster jimClient;

    @Value("${product.label.lua.sha}")
    private String sha;

    /**
     * 商品缓存
     */
    private LoadingCache<String, Set<String>> productLabelSkuTypeCache;
    /**
     * 省份缓存
     */
    private LoadingCache<String, Set<String>> productLabelProvinceCache;
    /**
     * 城市缓存
     */
    private LoadingCache<String, Set<String>> productLabelCityCache;
    /**
     * 县缓存
     */
    private LoadingCache<String, Set<String>> productLabelCountyCache;
    /**
     * 镇缓存
     */
    private LoadingCache<String, Set<String>> productLabelTownCache;

    @Override
    @LogAndAlarm(logSwitch = false)
    public List<JdhLabelDto> queryProductLabel(JdhLabelQuery query) {
        try {
            return memoryCache(query);
        } catch (Exception e) {
            log.error("ProductLabelServiceApplicationImpl -> queryProductLabel 内存缓存处理逻辑异常", e);
        }
        return jimScript(query);
    }

    private List<JdhLabelDto> memoryCache(JdhLabelQuery query) throws Exception {
        JdhLabelAddressQuery addressQuery = query.getAddressQuery();
        Long provinceId = addressQuery.getProvinceId();
        Long cityId = addressQuery.getCityId();
        Long countyId = addressQuery.getCountyId();
        Long townId = addressQuery.getTownId();

        List<JdhLabelSkuQuery> skuQueryList = query.getSkuLabelQueryList();
        if (Objects.isNull(skuQueryList)) {
            skuQueryList = new ArrayList<>();
        }
        List<JdhLabelDto> jdhLabelDtos = new ArrayList<>();

        Set<String> skuTypeList = productLabelSkuTypeCache.get(RedisKeyEnum.PRODUCT_LABEL_SKU_TYPE2.getRedisKeyPrefix());
        if (Objects.isNull(skuTypeList)) {
            skuTypeList = new HashSet<>();
        }

        Set<String> provinceList = productLabelProvinceCache.get(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_PROVINCE.getRedisKeyPrefix());
        if (Objects.isNull(provinceList)) {
            provinceList = new HashSet<>();
        }
        Set<String> cityList = productLabelCityCache.get(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_CITY.getRedisKeyPrefix());
        if (Objects.isNull(cityList)) {
            cityList = new HashSet<>();
        }
        Set<String> countyList = productLabelCountyCache.get(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_COUNTY.getRedisKeyPrefix());
        if (Objects.isNull(countyList)) {
            countyList = new HashSet<>();
        }
        Set<String> townList = productLabelTownCache.get(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_TOWN.getRedisKeyPrefix());
        if (Objects.isNull(townList)) {
            townList = new HashSet<>();
        }

        boolean checkAddress = checkAddress(provinceList, cityList, countyList, townList, provinceId, cityId, countyId, townId);

        for (JdhLabelSkuQuery skuQuery : skuQueryList) {
            List<Long> giftSkuIds = skuQuery.getGiftSkuIds();
            if (Objects.isNull(giftSkuIds)) {
                giftSkuIds = new ArrayList<>();
            }

            JdhLabelDto jdhLabelDto = new JdhLabelDto();
            jdhLabelDto.setMainSkuId(skuQuery.getMainSkuId());
            jdhLabelDto.setGiftSkuIds(giftSkuIds);
            jdhLabelDto.setKey(ProductLabelKeylEnum.NURSE_HOME_LABEL);
            jdhLabelDtos.add(jdhLabelDto);

            for (Long giftSku : giftSkuIds) {
                // 赠品不在缓存中
                if (!skuTypeList.contains(String.valueOf(giftSku))) {
                    continue;
                }
                if(checkAddress){
                    jdhLabelDto.setShow(true);
                    break;
                }
            }
        }
        return jdhLabelDtos;
    }

    private boolean checkAddress(Set<String> provinceList, Set<String> cityList, Set<String> countyList, Set<String> townList, Long provinceId, Long cityId, Long countyId, Long townId){
        // 优先处理镇是否存在
        if (Objects.nonNull(townId)) {
            if (townList.contains(String.valueOf(townId))) {
                return true;
            }
        }
        if (Objects.nonNull(countyId)) {
            if (countyList.contains(String.valueOf(countyId))) {
                return true;
            }
        }
        if (Objects.nonNull(cityId)) {
            if (cityList.contains(String.valueOf(cityId))) {
                return true;
            }
        }
        if (Objects.nonNull(provinceId)) {
            if (provinceList.contains(String.valueOf(provinceId))) {
                return true;
            }
        }
        return false;
    }

    private List<JdhLabelDto> jimScript(JdhLabelQuery query) {
        List<String> keys = new ArrayList<String>();
        keys.add(RedisKeyEnum.PRODUCT_LABEL_SKU_TYPE2.getRedisKeyPrefix());
        keys.add(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_PROVINCE.getRedisKeyPrefix());
        keys.add(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_CITY.getRedisKeyPrefix());
        keys.add(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_COUNTY.getRedisKeyPrefix());
        keys.add(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_TOWN.getRedisKeyPrefix());

        JdhLabelAddressQuery addressQuery = query.getAddressQuery();
        Long provinceId = addressQuery.getProvinceId();
        Long cityId = addressQuery.getCityId();
        Long countyId = addressQuery.getCountyId();
        Long townId = addressQuery.getTownId();

        List<JdhLabelSkuQuery> skuQueryList = query.getSkuLabelQueryList();
        List<JdhLabelDto> jdhLabelDtos = new ArrayList<>();

        for (JdhLabelSkuQuery skuQuery : skuQueryList) {
            List<Long> giftSkuIds = skuQuery.getGiftSkuIds();

            JdhLabelDto jdhLabelDto = new JdhLabelDto();
            jdhLabelDto.setMainSkuId(skuQuery.getMainSkuId());
            jdhLabelDto.setGiftSkuIds(giftSkuIds);
            jdhLabelDto.setKey(ProductLabelKeylEnum.NURSE_HOME_LABEL);
            jdhLabelDtos.add(jdhLabelDto);

            for (Long giftSku : giftSkuIds) {
                List<String> args = new ArrayList<String>();
                args.add(String.valueOf(giftSku));
                args.add(String.valueOf(provinceId));
                args.add(String.valueOf(cityId));
                args.add(String.valueOf(countyId));
                if (Objects.nonNull(townId)) {
                    args.add(String.valueOf(townId));
                }
                try {
                    List<Long> responses = (List<Long>) jimClient.evalsha(sha, keys, args, true, MULTI);
                    if (CollectionUtils.isNotEmpty(responses) && Objects.nonNull(responses.get(0)) && responses.get(0).longValue() > 0) {
                        jdhLabelDto.setShow(true);
                        break;
                    }
                } catch (ScriptNotFoundException ex) {
                    continue;
                }
            }
        }
        return jdhLabelDtos;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        productLabelSkuTypeCache = loadCache(RedisKeyEnum.PRODUCT_LABEL_SKU_TYPE2.getRedisKeyPrefix());
        productLabelProvinceCache = loadCache(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_PROVINCE.getRedisKeyPrefix());
        productLabelCityCache = loadCache(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_CITY.getRedisKeyPrefix());
        productLabelCountyCache = loadCache(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_COUNTY.getRedisKeyPrefix());
        productLabelTownCache = loadCache(RedisKeyEnum.PRODUCT_LABEL_AVAIABLE_TOWN.getRedisKeyPrefix());
    }

    private LoadingCache<String, Set<String>> loadCache(String jimKey) {
        return CacheBuilder.newBuilder()
                //缓存池大小，在缓存项接近该大小时， Guava开始回收旧的缓存项
                .maximumSize(5000)
                //设置时间对象没有被读/写访问则对象从内存中删除(在另外的线程里面不定期维护)
                .expireAfterAccess(1, TimeUnit.HOURS)
                // 设置缓存在写入之后 设定时间 后失效
                .expireAfterWrite(1, TimeUnit.HOURS)
                //开启Guava Cache的统计功能
                .recordStats().build(new CacheLoader<String, Set<String>>() {
                    // 处理缓存键不存在缓存值时的处理逻辑
                    @Override
                    public Set<String> load(String key) throws Exception {
                        Set<String> redisValues = jimClient.sMembers(jimKey);
                        if (Objects.isNull(redisValues)) {
                            return new HashSet<>();
                        }
                        return redisValues;
                    }
                });
    }
}