package com.jdh.o2oservice.application.angel.event;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.application.angel.service.ActivityApplication;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.event.EventConsumerRegister;
import com.jdh.o2oservice.base.event.EventConsumerRetryTemplate;
import com.jdh.o2oservice.base.event.WrapperEventConsumer;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.angel.enums.ActivityConfigTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelEventTypeEnum;
import com.jdh.o2oservice.core.domain.angel.enums.AngelProfessionCodeEnum;
import com.jdh.o2oservice.export.angel.cmd.AngelActivityRecruitmentCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AngelActivityEventConsumer
 * @Description
 * <AUTHOR>
 * @Date 2024/11/21 16:16
 **/
@Component
@Slf4j
public class AngelActivityEventConsumer {

    /**
     * 事件消费注册
     */
    @Resource
    private EventConsumerRegister eventConsumerRegister;

    /**
     *
     */
    @Resource
    private ActivityApplication activityApplication;

    @Resource
    private DuccConfig duccConfig;

    @PostConstruct
    public void registerEventConsumer() {
        eventConsumerRegister.register(AngelEventTypeEnum.ANGEL_REGISTER_AUDIT_WAIT,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL, "angelAuditRecruitmentActivityConsumer", this::angelAuditRecruitmentActivityConsumer, Boolean.TRUE, Boolean.FALSE, EventConsumerRetryTemplate.exponentialRetryInstance(3, 60000, 5.0, 14400000)));

        eventConsumerRegister.register(AngelEventTypeEnum.ANGEL_SETTLE_AUDIT_PASS,
                WrapperEventConsumer.newInstance(DomainEnum.ANGEL, "angelAuditRecruitmentActivityConsumer", this::angelAuditRecruitmentActivityConsumer, Boolean.TRUE, Boolean.FALSE, EventConsumerRetryTemplate.exponentialRetryInstance(3, 60000, 2.0, 14400000)));

    }

    /**
     * 护拉护活动监听护士入驻待审核事件
     * @param event
     */
    private void angelAuditRecruitmentActivityConsumer(Event event) {
        log.info("AngelActivityEventConsumer -> angelAuditRecruitmentActivityConsumer event:{}", JSON.toJSONString(event));
        AngelActivityRecruitmentCmd cmd = new AngelActivityRecruitmentCmd();
        cmd.setAcceptAngelId(Long.valueOf(event.getAggregateId()));
        cmd.setEventCode(event.getEventCode());
        cmd.setActivityConfigType(ActivityConfigTypeEnum.ANGEL_RECRUITMENT.getType());

        Map<String, List<String>> angelActivityProfession = duccConfig.getAngelActivityProfession();
        if(MapUtils.isEmpty(angelActivityProfession) || CollectionUtils.isEmpty(angelActivityProfession.get(String.valueOf(ActivityConfigTypeEnum.ANGEL_RECRUITMENT.getType())))) {
            cmd.setProfessionActivity(Lists.newArrayList(String.valueOf(AngelProfessionCodeEnum.SMHS.getCode())));
        }else {
            cmd.setProfessionActivity(angelActivityProfession.get(String.valueOf(ActivityConfigTypeEnum.ANGEL_RECRUITMENT.getType())));
        }
        activityApplication.syncAngelActivityProgress(cmd);
    }
}