package com.jdh.o2oservice.application.ztools;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkDetailForManDto;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.ztools.cmd.AngelWorkExportFileCmd;
import com.jdh.o2oservice.export.ztools.cmd.FlushShipCmd;
import com.jdh.o2oservice.export.ztools.cmd.ManBindSpecimenCodeCmd;
import com.jdh.o2oservice.export.ztools.cmd.ManSyncStationCmd;
import com.jdh.o2oservice.export.ztools.query.AngelWorkPageRequest;
import com.jdh.o2oservice.export.ztools.query.ManMedicalPromiseRequest;

import java.util.List;

/**
 * @ClassName AngelWorkToolsApplication
 * @Description 运营单工具
 * <AUTHOR>
 * @Date 2024/9/3 23:13
 */
public interface AngelWorkToolsApplication {

    /**
     * 查询工单分页信息
     *
     * @param angelWorkPageRequest
     * @return
     */
    PageDto<AngelWorkDetailForManDto> queryAngelWorkPage(AngelWorkPageRequest angelWorkPageRequest);

    /**
     * 导出工单文件
     *
     * @param angelWorkExportFileCmd
     * @return
     */
    Boolean exportFile(AngelWorkExportFileCmd angelWorkExportFileCmd);

    /**
     * 运营端绑码工具
     *
     * @param manBindSpecimenCodeCmd
     * @return
     */
    Boolean manBindSpecimenCode(ManBindSpecimenCodeCmd manBindSpecimenCodeCmd);

    /**
     * 查询检测单列表
     *
     * @param manMedicalPromiseRequest
     * @return
     */
    List<MedicalPromiseDTO> queryMedicalPromise(ManMedicalPromiseRequest manMedicalPromiseRequest);

    /**
     * 同步采样码到实验室
     *
     * @param manSyncStationCmd
     * @return
     */
    Boolean syncMedicalStation(ManSyncStationCmd manSyncStationCmd);

    /**
     * 批量推送实验室
     *
     * @param manSyncStationCmd
     * @return
     */
    Boolean syncPromiseStation(ManSyncStationCmd manSyncStationCmd);

    /**
     * 检查检测单状态
     *
     * @param optionCode
     * @param manBindSpecimenCodeCmd
     * @return
     */
    Boolean checkMedicalPromiseStatus(String optionCode, ManBindSpecimenCodeCmd manBindSpecimenCodeCmd);

    /**
     * 执行groovy脚本
     *
     * @param scriptName
     * @return
     */
    Boolean executeGroovyScript(String scriptName);

    /**
     *
     * @param flushShipCmd
     * @return
     */
    Boolean flushShipAddress(FlushShipCmd flushShipCmd);

    /**
     * 工单导出文件
     *
     * @param request
     * @return
     */
    Boolean exportServiceImg(AngelWorkPageRequest request);
}
