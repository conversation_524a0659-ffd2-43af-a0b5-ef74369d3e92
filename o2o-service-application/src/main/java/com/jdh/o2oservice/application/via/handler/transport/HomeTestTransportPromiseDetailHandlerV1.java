package com.jdh.o2oservice.application.via.handler.transport;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.jdh.o2oservice.application.angelpromise.service.AngelWorkApplication;
import com.jdh.o2oservice.application.product.service.ProductApplication;
import com.jdh.o2oservice.application.trade.service.TradeApplication;
import com.jdh.o2oservice.application.via.common.ViaComponentDomainService;
import com.jdh.o2oservice.application.via.handler.AbstractViaDataFillHandler;
import com.jdh.o2oservice.application.via.handler.promise.PromiseCommentHandler;
import com.jdh.o2oservice.base.enums.ThreadPoolConfigEnum;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.factory.ExecutorPoolFactory;
import com.jdh.o2oservice.base.processor.MapAutowiredKey;
import com.jdh.o2oservice.base.util.AssertUtils;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.ServiceTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.*;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.promise.repository.db.VoucherRepository;
import com.jdh.o2oservice.core.domain.promise.service.JdhVoucherDomainService;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.basic.model.UserName;
import com.jdh.o2oservice.core.domain.support.via.context.FillViaConfigDataContext;
import com.jdh.o2oservice.core.domain.support.via.enums.*;
import com.jdh.o2oservice.core.domain.support.via.model.*;
import com.jdh.o2oservice.core.domain.trade.bo.OrderAddressBO;
import com.jdh.o2oservice.core.domain.trade.enums.TradeAggregateEnum;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;
import com.jdh.o2oservice.core.domain.trade.repository.db.JdOrderRepository;
import com.jdh.o2oservice.export.angelpromise.dto.AngelTrackDto;
import com.jdh.o2oservice.export.angelpromise.query.AngelTrackQuery;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.product.dto.JdhSkuDto;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;
import com.jdh.o2oservice.export.via.enums.EventTracingTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 快递检测模式 V1版本
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/17 19:51
 */
@Component
@Slf4j
public class HomeTestTransportPromiseDetailHandlerV1 extends AbstractViaDataFillHandler implements MapAutowiredKey {

    @Resource
    private PromiseRepository promiseRepository;
    @Resource
    private TradeApplication tradeApplication;
    @Resource
    private JdOrderRepository jdOrderRepository;
    @Resource
    private ProductApplication productApplication;
    @Resource
    private ExecutorPoolFactory executorPoolFactory;
    @Resource
    private ViaComponentDomainService viaComponentDomainService;
    @Resource
    private PromiseCommentHandler promiseCommentHandler;
    @Resource
    private AngelWorkApplication angelWorkApplication;
    @Resource
    private JdhVoucherDomainService jdhVoucherDomainService;
    @Resource
    private VoucherRepository voucherRepository;

    @Override
    public void handle(FillViaConfigDataContext ctx) {
        log.info("HomeTestTransportPromiseDetailHandlerV1 handle ctx:{}", JSON.toJSONString(ctx));
        // ==>>>> 入参校验
        checkParam(ctx);
        ViaConfig viaConfig = ctx.getViaConfig();


        // ==>>>> 数据获取
        //查订单
        JdOrder jdOrder = jdOrderRepository.findFull(Long.valueOf(ctx.getOrderId()), ctx.getUserPin());
        log.info("HomeTestTransportPromiseDetailHandlerV1 handle jdOrder:{}", JSON.toJSONString(jdOrder));
        if (Objects.isNull(jdOrder)) {
            throw new SystemException(SupportErrorCode.VIA_ORDER_INFO_NOT_EXIT);
        }

        JdhPromise jdhPromise;
        if (Objects.nonNull(ctx.getPromiseId()) && !StringUtils.equals(ctx.getPromiseId(), "null")) {
            jdhPromise = promiseRepository.find(new JdhPromiseIdentifier(Long.valueOf(ctx.getPromiseId())));
        } else {
            jdhPromise = null;
        }

        List<CompletableFuture> futures = new ArrayList<>();
        //查商品配置
        CompletableFuture<JdhSkuDto> skuDtoCf = null;
        if (CollUtil.isNotEmpty(jdOrder.getJdOrderItemList())) {
            JdOrderItem jdOrderItemDTO = jdOrder.getJdOrderItemList().get(0);
            Long skuId = jdOrderItemDTO.getSkuId();
            if (Objects.nonNull(skuId)) {
                skuDtoCf = CompletableFuture.supplyAsync(() -> querySkuInfo(skuId), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
                futures.add(skuDtoCf);
            }
        }

        //查检测单
        CompletableFuture<List<MedicalPromiseDTO>> medPromiseListCf = null;
        if (Objects.nonNull(jdhPromise)) {
            medPromiseListCf = CompletableFuture.supplyAsync(() -> promiseCommentHandler.queryMedicalPromiseList(jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(medPromiseListCf);
        }

        // 运单信息
        CompletableFuture<AngelShip> shipCf = null;
        if (Objects.nonNull(jdhPromise)) {
            shipCf = CompletableFuture.supplyAsync(() -> promiseCommentHandler.getShip(jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL));
            futures.add(shipCf);
        }

        //异步编译 Aviator
        futures.add(CompletableFuture.runAsync(() -> compileAviator(viaConfig), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)));

        if (CollUtil.isNotEmpty(futures)) {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }


        try {

            AngelShip angelShip = Objects.nonNull(shipCf) ? shipCf.get() : null;

            // ==>>>> 过滤statusMapping
            ViaStatusMapping statusMapping = viaComponentDomainService.parseHomeTransportTestMapping(
                    EntityUtil.getFiledDefaultNull(jdhPromise, JdhPromise::getPromiseStatus),
                    EntityUtil.getFiledDefaultNull(Objects.isNull(medPromiseListCf) ? null : medPromiseListCf.get(), MedicalPromiseDTO::getStatus),
                    // promise不为空，且appointmentTime不为空，再取isImmediately
                    Objects.nonNull(angelShip) ? angelShip.getShipStatus() : null,
                    ctx.getStepCode(),
                    viaConfig
            );
            log.info("HomeTestTransportPromiseDetailHandlerV1 handle statusMapping:{}", JSON.toJSONString(statusMapping));

            // ==>>>> 移除当前状态下隐藏的楼层
            List<ViaFloorInfo> floorList = viaConfig.getFloorList();
            clearHiddenFloor(statusMapping, floorList, Objects.isNull(skuDtoCf) ? null : skuDtoCf.get());
            log.info("HomeTestTransportPromiseDetailHandlerV1 handle floorList:{}", JSON.toJSONString(floorList));

            // ==>>>> 楼层处理
            dealFloorList(ctx,
                    statusMapping,
                    jdOrder,
                    jdhPromise,
                    Objects.isNull(medPromiseListCf) ? null : medPromiseListCf.get(),
                    Objects.isNull(skuDtoCf) ? null : skuDtoCf.get(),
                    Objects.isNull(shipCf) ? null : shipCf.get());
            log.info("HomeTestTransportPromiseDetailHandlerV1 handle viaConfig:{}", JSON.toJSONString(viaConfig));

            // 埋点数据
            Map<String, Object> tracingData = Maps.newHashMap();
            tracingData.put(EventTracingTypeEnum.PROMISE_STATUS.getType(), EntityUtil.getFiledDefaultNull(jdhPromise, JdhPromise::getPromiseStatus));
            tracingData.put(EventTracingTypeEnum.PROMISE_ID.getType(), EntityUtil.getFiledDefaultNull(jdhPromise, JdhPromise::getPromiseId));
            viaConfig.setEventTracing(tracingData);
        } catch (Exception e) {
            log.error("HomeTestTransportPromiseDetailHandlerV1 handle error", e);
            throw new BusinessException(SupportErrorCode.VIA_FLOOR_HAND_ERROR);
        }
    }

    @Override
    public String getMapKey() {
        return ViaPageEnum.HOME_PROMISE_DETAIL.getScene() + "_" + BusinessModeEnum.SELF_TEST_TRANSPORT.getCode()
                + "_" + ServiceTypeEnum.TEST.getServiceType();
    }

    /**
     * checkParam
     *
     * @param ctx ctx
     */
    private void checkParam(FillViaConfigDataContext ctx) {
        //场景
        AssertUtils.hasText(ctx.getScene(), SupportErrorCode.VIA_CONFIG_NOT_EXIT);
        AssertUtils.hasText(ctx.getOrderId(), SupportErrorCode.VIA_ORDER_ID_NOT_EXIT);
    }

    /**
     * 查询SKU信息
     *
     * @param skuId SKU ID
     * @return {@link JdhSkuDto}
     */
    private JdhSkuDto querySkuInfo(Long skuId) {
        try {
            Map<Long, JdhSkuDto> jdhSkuDtoMap = productApplication.queryJdhSkuInfoList(JdhSkuListRequest.builder()
                    .querySkuCoreData(Boolean.TRUE)
                    .queryServiceItem(Boolean.TRUE)
                    .skuIdList(Sets.newHashSet(skuId))
                    .build());
            return jdhSkuDtoMap.get(skuId);
        } catch (Exception e) {
            log.info("HomeTestTransportPromiseDetailHandlerV1 handle querySkuInfo exception", e);
            return null;
        }
    }

    /**
     * compileAviator
     *
     * @param viaConfig VIA配置
     */
    private void compileAviator(ViaConfig viaConfig) {
        try {
            for (ViaStatusMapping viaStatusMapping : viaConfig.getStatusMapping()) {
                if (StrUtil.isNotBlank(viaStatusMapping.getStatusExpression())) {
                    AviatorEvaluator.compile(viaStatusMapping.getStatusExpression(), Boolean.TRUE);
                }
            }
        } catch (Exception e) {
            log.info("HomeTestTransportPromiseDetailHandlerV1 handle compileAviator exception", e);
        }
    }

    /**
     * 隐藏楼层
     *
     * @param statusMapping 状态映射
     * @param floorList     楼层列表
     */
    private void clearHiddenFloor(ViaStatusMapping statusMapping, List<ViaFloorInfo> floorList, JdhSkuDto jdhSkuDto) {
        Iterator<ViaFloorInfo> iterator = floorList.iterator();
        while (iterator.hasNext()) {
            ViaFloorInfo viaFloorInfo = iterator.next();
            if (!statusMapping.getShowFloorCode().contains(viaFloorInfo.getFloorCode())) {
                iterator.remove();
            }
        }
    }

    /**
     * 处理楼层列表
     *
     * @param ctx                上下文
     * @param statusMapping      状态映射
     * @param jdOrder            JD订单
     * @param jdhPromise         jdhPromise
     * @param medicalPromiseList medicalPromiseList
     */
    private void dealFloorList(FillViaConfigDataContext ctx,
                               ViaStatusMapping statusMapping,
                               JdOrder jdOrder,
                               JdhPromise jdhPromise,
                               List<MedicalPromiseDTO> medicalPromiseList,
                               JdhSkuDto jdhSkuDto,
                               AngelShip angelShip) {
        ViaConfig viaConfig = ctx.getViaConfig();
        // 填充模版数据
        Map<String, Object> sourceData = Maps.newHashMap();
        sourceData.put(PromiseAggregateEnum.PROMISE.getCode(), jdhPromise);
        sourceData.put(TradeAggregateEnum.ORDER.getCode(), jdOrder);
        sourceData.put("statusMapping", statusMapping);
        sourceData.put("ctx", ctx);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (ViaFloorInfo viaFloorInfo : viaConfig.getFloorList()) {
            //概要 summaryInfo
            if (ViaFloorEnum.PROMISE_SUMMARY_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleSummaryInfo(viaFloorInfo, statusMapping, jdhPromise, sourceData), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestTransportPromiseDetailHandlerV1 dealFloorList handleSummaryInfo exception", exception);
                    return null;
                }));
            }

            //步骤条 stepGuideInfo
            if (ViaFloorEnum.PROMISE_STEP_GUIDE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleStepGuideInfo(viaFloorInfo, statusMapping), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestTransportPromiseDetailHandlerV1 dealFloorList handleStepGuideInfo exception", exception);
                    return null;
                }));
            }


            // 预约信息
            if (ViaFloorEnum.GETHER_APPOINT_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleAppointmentInfo(viaFloorInfo, statusMapping, jdhPromise, angelShip, medicalPromiseList, jdOrder), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestTransportPromiseDetailHandlerV1 handleShipInfo handlePromiseAngelInfo exception", exception);
                    return null;
                }));
            }
            // 物流信息
            if (ViaFloorEnum.PROMISE_SHIP_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleShipInfo(viaFloorInfo, statusMapping, jdhPromise, angelShip), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestTransportPromiseDetailHandlerV1 handleShipInfo handlePromiseAngelInfo exception", exception);
                    return null;
                }));
            }
            // 注意事项
            if (ViaFloorEnum.PROMISE_NOTES.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handlePromiseNotes(viaFloorInfo, statusMapping, jdhPromise), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestTransportPromiseDetailHandlerV1 handleShipInfo handlePromiseAngelInfo exception", exception);
                    return null;
                }));
            }


            //样本信息楼层 materialInfo
            if (ViaFloorEnum.PROMISE_MATERIAL_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> promiseCommentHandler.handleFooterMedicalPromise(viaFloorInfo, sourceData, medicalPromiseList, jdhPromise, angelShip), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestTransportPromiseDetailHandlerV1 dealFloorList handleFooterMedicalPromise exception", exception);
                    return null;
                }));
            }

            // 录入样本和检测人
            if (ViaFloorEnum.ENTER_CODE_PATIENT.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleEnterCodePatient(viaFloorInfo, jdhPromise, ctx), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestTransportPromiseDetailHandlerV1 dealFloorList handleEnterCodePatient exception", exception);
                    return null;
                }));
            }


            /**
             * 采样教程楼层（自采样需要）
             */
            if (ViaFloorEnum.PROMISE_SAMPLE_COURSE_INFO.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> promiseCommentHandler.handleFooterSampleCourse(viaFloorInfo, jdhSkuDto), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestTransportPromiseDetailHandlerV1 dealFloorList sampleCourseInfo exception", exception);
                    return null;
                }));
            }

            //底部按钮 footerButtons
            if (ViaFloorEnum.PROMISE_FOOTER_BUTTONS.getFloorCode().equals(viaFloorInfo.getFloorCode())) {
                futures.add(CompletableFuture.runAsync(() -> handleFooterButtons(ctx, viaFloorInfo, statusMapping, jdOrder, jdhPromise, medicalPromiseList), executorPoolFactory.get(ThreadPoolConfigEnum.VIA_FLOOR_HAND_POOL)).exceptionally(exception -> {
                    log.info("HomeTestTransportPromiseDetailHandlerV1 dealFloorList handleFooterButtons exception", exception);
                    return null;
                }));
            }

        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 步骤条
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     */
    private void handleStepGuideInfo(ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping) {
        for (ViaFloorConfig viaFloorConfig : viaFloorInfo.getFloorConfigList()) {
            String stepCode = viaFloorConfig.getStepCode();
            //完成
            if (statusMapping.getStepGuideFinishCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.FINISH.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideFinishIcon());
            }
            //进行中
            if (statusMapping.getStepGuideProcessCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.PROCESS.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideProcessIcon());
            }
            //等待
            if (statusMapping.getStepGuideWaitCodeList().contains(stepCode)) {
                viaFloorConfig.setStepStatus(ViaStepStatusEnum.WAIT.getStatus());
                viaFloorConfig.setStepIcon(statusMapping.getStepGuideWaitIcon());
            }
        }
        log.info("HomeTestTransportPromiseDetailHandlerV1 handleStepGuideInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 录入样本和检测人信息页面
     *
     * @param viaFloorInfo 通用楼层信息
     */
    private void handleEnterCodePatient(ViaFloorInfo viaFloorInfo, JdhPromise jdhPromise, FillViaConfigDataContext ctx) {
        List<ViaFloorConfig> floorConfigList = viaFloorInfo.getFloorConfigList();
        Integer maxCount = 0;
        try {
            JdhVoucher voucher = null;
            if (StringUtils.isNotBlank(ctx.getVoucherId())) {
                voucher = voucherRepository.find(new JdhVoucherIdentifier(Long.valueOf(ctx.getVoucherId())));
            } else {
                voucher = voucherRepository.find(new JdhVoucherIdentifier(jdhPromise.getVoucherId()));
            }
            maxCount = jdhVoucherDomainService.availableNum(voucher);
        }catch (Exception e) {
            log.error("HomeTestTransportPromiseDetailHandlerV1 handleEnterCodePatient error", e);
        }

        // 计算当前添加的最大检测人数
        for (ViaFloorConfig floorConfig : floorConfigList) {
            if (StringUtils.equals(floorConfig.getFieldKey(), "maxCount")) {
                floorConfig.setFieldValue(String.valueOf(maxCount));
            }
        }
        log.info("HomeTestTransportPromiseDetailHandlerV1 handleEnterCodePatient viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 处理预约信息
     *
     * @param viaFloorInfo
     * @param statusMapping
     * @param jdhPromise
     * @param ship
     */
    private void handleAppointmentInfo(ViaFloorInfo viaFloorInfo,
                                       ViaStatusMapping statusMapping,
                                       JdhPromise jdhPromise,
                                       AngelShip ship, List<MedicalPromiseDTO> medicalPromiseList, JdOrder jdOrder) {

        List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();

        Map<String, Object> container = statusMapping.getContainer();
        // 寄件地址信息
        ViaFloorConfig sendInfo = new ViaFloorConfig();
        String locationIcon = Objects.toString(container.get("locationIcon"), "");
        sendInfo.setMainIcon(locationIcon);
        sendInfo.setFieldKey(ViaAppointmentFieldEnum.SEND_INFO.getField());
        sendInfo.setTitle("选择寄件地址");

        // 待预约时地址回显订单地址
        OrderAddressBO orderAddressBO = jdOrder.getOrderAddress();
        String storeAddrValue;
        String addressIdValue;
        String senderNameValue;
        String senderPhoneValue;
        if (Objects.equals(jdhPromise.getPromiseStatus(), JdhPromiseStatusEnum.WAIT_PROMISE.getStatus())
                && Objects.nonNull(orderAddressBO)) {
            storeAddrValue = orderAddressBO.getFullAddress();
            addressIdValue = String.valueOf(orderAddressBO.getId());
            senderNameValue = UserName.mask(orderAddressBO.getName());
            senderPhoneValue = PhoneNumber.mask(orderAddressBO.getMobile());
        } else {
            storeAddrValue = EntityUtil.getFiledDefaultNull(jdhPromise.getStore(), PromiseStation::getStoreAddr);
            addressIdValue = jdhPromise.getStore().getStoreId();

            JdhPromiseExtend appointmentUserName = jdhPromise.findExtend(PromiseExtendKeyEnum.APPOINTMENT_USER_NAME.getFiledKey());
            senderNameValue = UserName.mask(appointmentUserName.getValue());
            senderPhoneValue = PhoneNumber.mask(jdhPromise.getAppointmentPhone());

        }

        List<ViaFormItem> formItemList = Lists.newArrayList();
        ViaFormItem storeAddr = new ViaFormItem();
        storeAddr.setFormName("storeAddr");
        storeAddr.setValue(storeAddrValue);
        formItemList.add(storeAddr);

        ViaFormItem senderName = new ViaFormItem();
        senderName.setFormName("senderName");
        senderName.setValue(senderNameValue);
        formItemList.add(senderName);

        ViaFormItem senderPhone = new ViaFormItem();
        senderPhone.setFormName("senderPhone");
        senderPhone.setValue(senderPhoneValue);
        formItemList.add(senderPhone);

        ViaFormItem skuIdItem = new ViaFormItem();
        List<Long> skuIds = jdhPromise.getServices().stream().map(PromiseService::getServiceId).collect(Collectors.toList());
        skuIdItem.setFormName("skuIds");
        skuIdItem.setValue(JSON.toJSONString(skuIds));
        formItemList.add(skuIdItem);

        ViaFormItem addressId = new ViaFormItem();
        addressId.setFormName("addressId");
        addressId.setValue(addressIdValue);
        formItemList.add(addressId);
        sendInfo.setFormItemList(formItemList);


        // 跳转常用地址列表
        sendInfo.setDisabled(Boolean.TRUE);
        ViaActionInfo action = new ViaActionInfo();
        action.setType(ActionType.JUMP.getCode());
        action.setUrl("");
        sendInfo.setAction(action);
        viaFloorConfigList.add(sendInfo);


        // 上门时间
        PromiseAppointmentTime appointmentTime = jdhPromise.getAppointmentTime();
        ViaFloorConfig appointmentTimeConfig = new ViaFloorConfig();
        String appointmentTimeIcon = Objects.toString(container.get("appointmentTimeIcon"), "");
        appointmentTimeConfig.setFieldKey("appointmentTime");
        appointmentTimeConfig.setMainIcon(appointmentTimeIcon);
        appointmentTimeConfig.setTitle("请选择期望上门时间");
        appointmentTimeConfig.setDisabled(Boolean.TRUE);
        appointmentTimeConfig.setFieldValue(appointmentTime.formatDescDetail());
        // 查询时间列表的接口
        ViaActionInfo timeAction = new ViaActionInfo();
        timeAction.setType(ActionType.JUMP.getCode());
        timeAction.setUrl("");
        appointmentTimeConfig.setAction(timeAction);

        List<ViaFormItem> timeFormItemList = Lists.newArrayList();
        ViaFormItem dateType = new ViaFormItem();
        dateType.setFormName("dateType");
        dateType.setValue(Objects.toString(appointmentTime.getDateType()));
        timeFormItemList.add(dateType);
        // 开始时间
        ViaFormItem appointmentStartTime = new ViaFormItem();
        appointmentStartTime.setFormName("appointmentStartTime");
        appointmentStartTime.setValue(appointmentTime.formatAppointmentStartTime());
        timeFormItemList.add(appointmentStartTime);

        // 结束时间
        ViaFormItem appointmentEndTime = new ViaFormItem();
        appointmentEndTime.setFormName("appointmentEndTime");
        appointmentEndTime.setValue(appointmentTime.formatAppointmentEndTime());
        timeFormItemList.add(appointmentEndTime);
        appointmentTimeConfig.setFormItemList(timeFormItemList);

        // 是否立即预约，修改预约时候默认回显false
        ViaFormItem isImmediately = new ViaFormItem();
        isImmediately.setFormName("isImmediately");
        isImmediately.setValue("false");
        timeFormItemList.add(isImmediately);
        appointmentTimeConfig.setFormItemList(timeFormItemList);
        viaFloorConfigList.add(appointmentTimeConfig);


        // 收件地址信息
        String stationName = Objects.toString(container.get("receiverName"), "");
        String receiverFullAddress = Objects.toString(container.get("receiverFullAddress"), "");
        if (CollectionUtils.isNotEmpty(medicalPromiseList)) {
            MedicalPromiseDTO medicalPromiseDTO = medicalPromiseList.get(0);
            if (StringUtils.isNotBlank(medicalPromiseDTO.getStationName())) {
                stationName = medicalPromiseDTO.getStationName();
            }
            if (StringUtils.isNotBlank(medicalPromiseDTO.getStationAddress())) {
                receiverFullAddress = medicalPromiseDTO.getStationAddress();
            }
        }

        ViaFloorConfig floor = new ViaFloorConfig();
        floor.setTitle("收件信息");
        floor.setFieldKey(ViaShipInfoFiledEnum.ACCEPT_INFO.getField());
        String acceptInfoIcon = Objects.toString(container.get("acceptInfoIcon"), "");
        floor.setMainIcon(acceptInfoIcon);

        List<ViaFormItem> acceptInfoItems = Lists.newArrayList();

        ViaFormItem receiverName = new ViaFormItem();
        receiverName.setFormName("receiverName");
        receiverName.setValue("收货实验室：" + stationName);
        acceptInfoItems.add(receiverName);

        ViaFormItem receiverFullAddressItem = new ViaFormItem();
        receiverFullAddressItem.setFormName("receiverFullAddress");
        receiverFullAddressItem.setValue(receiverFullAddress);

        acceptInfoItems.add(receiverFullAddressItem);
        floor.setFormItemList(acceptInfoItems);
        viaFloorConfigList.add(floor);
        viaFloorInfo.setFloorConfigList(viaFloorConfigList);
    }

    /**
     * 处理物流信息
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdhPromise    promise
     */
    private void handleShipInfo(ViaFloorInfo viaFloorInfo,
                                ViaStatusMapping statusMapping,
                                JdhPromise jdhPromise,
                                AngelShip ship) {

        List<ViaFloorConfig> viaFloorConfigList = new ArrayList<>();

        Map<String, List<String>> showFiledGroup = statusMapping.getShowFiledGroup();
        if (MapUtils.isEmpty(showFiledGroup)) {
            return;
        }
        List<String> fieldList = showFiledGroup.get(ViaFloorEnum.PROMISE_SHIP_INFO.getFloorCode());
        if (CollectionUtils.isEmpty(fieldList)) {
            return;
        }
        Iterator<ViaFloorConfig> iterator = viaFloorInfo.getFloorConfigList().iterator();
        Map<String, Object> container = statusMapping.getContainer();

        // 遍历配置
        while (iterator.hasNext()) {
            ViaFloorConfig config = iterator.next();
            if (!fieldList.contains(config.getFieldKey())) {
                iterator.remove();
            } else {
                // 物流单状态信息
                if (StringUtils.equals(config.getFieldKey(), ViaShipInfoFiledEnum.SHIP_INFO.getField())) {
                    ViaFloorConfig floor = new ViaFloorConfig();

                    String orderPhone = jdhPromise.findExtend(PromiseExtendKeyEnum.ORDER_PHONE.getFiledKey()).getValue();
                    String appointmentPhone = jdhPromise.getAppointmentPhone();
                    // 判断预约人手机号和下单人手机号是否一致，不一致则无法查看物流详情信息
                    boolean jumpShipUrl = StringUtils.equals(orderPhone, appointmentPhone);


                    String shipStatusIcon = Objects.toString(container.get("shipStatusIcon"), "");
                    floor.setMainIcon(shipStatusIcon);
                    if (jumpShipUrl) {
                        floor.setTitle("查看物流详情");
                    } else {
                        floor.setTitle("物流信息");
                    }
                    floor.setFieldKey(ViaShipInfoFiledEnum.SHIP_INFO.getField());
                    floor.setFieldValue(EntityUtil.getFiledDefaultNull(ship, AngelShip::getLogisticsMessage));

                    try {
                        if (jumpShipUrl) {
                            AngelTrackQuery angelTrackQuery = new AngelTrackQuery();
                            angelTrackQuery.setPromiseId(jdhPromise.getPromiseId());
                            AngelTrackDto track = angelWorkApplication.getTransferTrack(angelTrackQuery);
                            ViaActionInfo action = new ViaActionInfo();
                            action.setType(ActionType.JUMP.getCode());
                            action.setUrl(track.getTrackUrl());
                            floor.setAction(action);
                        }
                    } catch (Exception e) {
                        log.error("HomeTestTransportPromiseDetailHandlerV1 handleShipInfo angelTrackQuery error", e);
                    }
                    viaFloorConfigList.add(floor);
                } else if (StringUtils.equals(config.getFieldKey(), ViaShipInfoFiledEnum.SEND_INFO.getField())) {
                    ViaFloorConfig floor = new ViaFloorConfig();
                    floor.setTitle("寄件信息");
                    floor.setFieldKey(ViaShipInfoFiledEnum.SEND_INFO.getField());
                    String sendInfoIcon = Objects.toString(container.get("sendInfoIcon"), "");
                    floor.setMainIcon(sendInfoIcon);

                    PromiseStation store = jdhPromise.getStore();
                    List<ViaFormItem> formItemList = Lists.newArrayList();
                    ViaFormItem storeAddr = new ViaFormItem();
                    storeAddr.setFormName("storeAddr");
                    storeAddr.setValue(store.getStoreAddr());
                    formItemList.add(storeAddr);

                    ViaFormItem senderName = new ViaFormItem();
                    senderName.setFormName("senderName");
                    JdhPromiseExtend appointmentUserName = jdhPromise.findExtend(PromiseExtendKeyEnum.APPOINTMENT_USER_NAME.getFiledKey());
                    senderName.setValue(UserName.mask(appointmentUserName.getValue()));
                    formItemList.add(senderName);

                    ViaFormItem senderPhone = new ViaFormItem();
                    String appointmentPhone = jdhPromise.getAppointmentPhone();
                    senderPhone.setFormName("senderPhone");
                    senderPhone.setValue(PhoneNumber.mask(appointmentPhone));
                    formItemList.add(senderPhone);

                    floor.setFormItemList(formItemList);


                    Map<String, Object> actionCommonParams = new HashMap<>();
                    actionCommonParams.put(PromiseAggregateEnum.PROMISE.getCode(), jdhPromise);
                    // 初始化按钮
                    List<String> buttonCodeList = statusMapping.getFooterButtonCodeList();
                    if (CollectionUtils.isNotEmpty(buttonCodeList)) {
                        List<ViaBtnInfo> btnList = Lists.newArrayList();
                        Map<String, ViaBtnInfo> btnMap = config.getBtnList().stream().collect(Collectors.toMap(ViaBtnInfo::getCode, Function.identity(), (a, b) -> b));
                        for (String code : buttonCodeList) {
                            ViaBtnInfo btn = btnMap.get(code);
                            btn.init(actionCommonParams);
                            btnList.add(btn);
                        }
                        floor.setBtnList(btnList);
                    }
                    viaFloorConfigList.add(floor);
                    // 收件地址
                } else if (StringUtils.equals(config.getFieldKey(), ViaShipInfoFiledEnum.ACCEPT_INFO.getField())) {
                    ViaFloorConfig floor = new ViaFloorConfig();
                    floor.setTitle("收件信息");
                    floor.setFieldKey(ViaShipInfoFiledEnum.ACCEPT_INFO.getField());
                    String acceptInfoIcon = Objects.toString(container.get("acceptInfoIcon"), "");
                    floor.setMainIcon(acceptInfoIcon);

                    List<ViaFormItem> formItemList = Lists.newArrayList();

                    ViaFormItem receiverName = new ViaFormItem();
                    receiverName.setFormName("receiverName");
                    receiverName.setValue("收货实验室：" + EntityUtil.getFiledDefaultNull(ship, AngelShip::getReceiverName));
                    formItemList.add(receiverName);

                    ViaFormItem receiverFullAddress = new ViaFormItem();
                    receiverFullAddress.setFormName("receiverFullAddress");
                    String address = EntityUtil.getFiledDefaultNull(ship, AngelShip::getReceiverFullAddress);
                    if (StringUtils.isBlank(address)) {
                        receiverFullAddress.setValue(Objects.toString(container.get("receiverFullAddress"), ""));
                    } else {
                        receiverFullAddress.setValue(address);
                    }
                    formItemList.add(receiverFullAddress);
                    floor.setFormItemList(formItemList);

                    viaFloorConfigList.add(floor);
                }
            }
        }


        viaFloorInfo.setFloorConfigList(viaFloorConfigList);
        log.info("HomeTestTransportPromiseDetailHandlerV1 handleFooterSampleCourse viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 注意事项
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdhPromise    promise
     */
    private void handlePromiseNotes(ViaFloorInfo viaFloorInfo,
                                    ViaStatusMapping statusMapping,
                                    JdhPromise jdhPromise) {
        log.info("HomeTestTransportPromiseDetailHandlerV1 handlePromiseNotes viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }

    /**
     * 处理摘要信息
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdhPromise    promise
     */
    private void handleSummaryInfo(ViaFloorInfo viaFloorInfo,
                                   ViaStatusMapping statusMapping,
                                   JdhPromise jdhPromise,
                                   Map<String, Object> sourceData) {
        List<ViaFloorConfig> floorConfig = new ArrayList<>();
        ViaFloorConfig viaFloorConfig = new ViaFloorConfig();

        viaFloorConfig.setMainIcon(statusMapping.getMainIcon());
        viaFloorConfig.setMainTitle(statusMapping.getMainTitle());
        floorConfig.add(viaFloorConfig);
        viaFloorInfo.setFloorConfigList(floorConfig);
        log.info("HomeTestTransportPromiseDetailHandlerV1 handleSummaryInfo viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }


    /**
     * 处理底部按钮
     *
     * @param viaFloorInfo  通用楼层信息
     * @param statusMapping 状态映射
     * @param jdOrder       JD订单
     */
    private void handleFooterButtons(FillViaConfigDataContext ctx, ViaFloorInfo viaFloorInfo, ViaStatusMapping statusMapping,
                                     JdOrder jdOrder, JdhPromise jdhPromise, List<MedicalPromiseDTO> medicalPromiseList) {

        ViaConfig viaConfig = ctx.getViaConfig();
        ViaFloorConfig viaFloorConfig = viaFloorInfo.getFloorConfigList().get(0);
        List<ViaBtnInfo> btnList = viaFloorConfig.getBtnList();
        Iterator<ViaBtnInfo> btnInfoIterator = btnList.iterator();
        Map<String, Object> container = statusMapping.getContainer();

        // 按钮楼层是否展示title由配置决定
        if (statusMapping.supportFiled(ViaFloorEnum.PROMISE_FOOTER_BUTTONS.getFloorCode(), "btnFloorTitle")) {
            String btnTitle = Objects.toString(container.get("btnFloorTitle"));
            String btnIcon = Objects.toString(container.get("btnFloorTitleIcon"));
            // 根据样本数量展示不同的title
            viaFloorConfig.setTitle(btnTitle);
            viaFloorConfig.setIcon(btnIcon);
        }

        // 按钮层用户协议
        if (statusMapping.supportFiled(ViaFloorEnum.PROMISE_FOOTER_BUTTONS.getFloorCode(), "btnAgreeInfo")) {
            String name = Objects.toString(container.get("btnAgreeInfoName"));
            String content = Objects.toString(container.get("btnAgreeInfoContent"));
            String title = Objects.toString(container.get("btnAgreeInfoTitle"));

            ViaAgreementInfo agreeInfo = new ViaAgreementInfo();
            List<ViaAgreementContentInfo> agreeList = Lists.newArrayList();
            ViaAgreementContentInfo contentInfo = new ViaAgreementContentInfo();
            contentInfo.setName(name);
            contentInfo.setContent(content);
            contentInfo.setTitle(title);
            agreeList.add(contentInfo);
            agreeInfo.setAgreeList(agreeList);
            viaFloorConfig.setAgreeInfo(agreeInfo);
        }
        Map<String, Object> actionCommonParams = new HashMap<>();
        actionCommonParams.put("verticalCode", EntityUtil.getFiledDefaultNull(jdOrder, JdOrder::getVerticalCode));
        actionCommonParams.put("serviceType", viaConfig.getServiceType());
        actionCommonParams.put("envType", ctx.getEnvType());
        actionCommonParams.put("promiseId", EntityUtil.getFiledDefaultNull(jdhPromise, JdhPromise::getPromiseId));
        actionCommonParams.put(TradeAggregateEnum.ORDER.getCode(), jdOrder);
        actionCommonParams.put(PromiseAggregateEnum.PROMISE.getCode(), jdhPromise);
        actionCommonParams.put("ctx", ctx);
        log.info("HomeTestTransportPromiseDetailHandlerV1->handleFooterButtons actionCommonParams={}", JSON.toJSONString(actionCommonParams));
        while (btnInfoIterator.hasNext()) {
            ViaBtnInfo btnInfo = btnInfoIterator.next();
            if (!statusMapping.getFooterButtonCodeList().contains(btnInfo.getCode())) {
                btnInfoIterator.remove();
                continue;
            }

            //申请退款
            ViaActionInfo action = btnInfo.getAction();
            if (ViaBtnCodeEnum.SCANNING_SPECIMEN_CODE.getCode().equals(btnInfo.getCode())
                    || ViaBtnCodeEnum.ENTER_SPECIMEN_CODE.getCode().equals(btnInfo.getCode())
                    || ViaBtnCodeEnum.MULTI_ENTER_SPECIMEN_CODE.getCode().equals(btnInfo.getCode())
                    || ViaBtnCodeEnum.CREATE_PROMISE_BTN.getCode().equals(btnInfo.getCode())
                    || ViaBtnCodeEnum.BUY_FIRST_SUBMIT_APPOINT_BTN.getCode().equals(btnInfo.getCode())
                    || ViaBtnCodeEnum.MODIFY_APPOINT_BTN.getCode().equals(btnInfo.getCode())) {
                action.init(actionCommonParams);
            }
        }
        log.info("HomeTestTransportPromiseDetailHandlerV1 handleFooterButtons viaFloorInfo:{}", JSON.toJSONString(viaFloorInfo));
    }
}
