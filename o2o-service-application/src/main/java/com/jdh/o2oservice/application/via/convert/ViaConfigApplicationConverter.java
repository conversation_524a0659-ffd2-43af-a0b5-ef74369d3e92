package com.jdh.o2oservice.application.via.convert;

import com.jdh.o2oservice.core.domain.support.via.model.ViaActionInfo;
import com.jdh.o2oservice.core.domain.support.via.model.ViaBtnInfo;
import com.jdh.o2oservice.core.domain.support.via.model.ViaConfig;
import com.jdh.o2oservice.export.via.dto.ViaActionInfoDto;
import com.jdh.o2oservice.export.via.dto.ViaBtnInfoDto;
import com.jdh.o2oservice.export.via.dto.ViaConfigDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.Objects;

import java.util.Map;

@Mapper
public interface ViaConfigApplicationConverter {



    /**
     * instance
     */
    ViaConfigApplicationConverter INSTANCE =  Mappers.getMapper(ViaConfigApplicationConverter.class);

    /**
     * model转 dto
     * @param viaConfig
     * @return
     */
    ViaConfigDto convert2Dto(ViaConfig viaConfig);

    ViaActionInfoDto convert2ActionDto(ViaActionInfo actionInfo);

    default ViaBtnInfoDto convert2ActionDto(ViaBtnInfo btnInfo){
        ViaBtnInfoDto btnInfoDto = new ViaBtnInfoDto();

        ViaActionInfo action = btnInfo.getAction();
        if (Objects.nonNull(action)){
            ViaActionInfoDto actionDto = new ViaActionInfoDto();
            actionDto.setType(action.getType());
            actionDto.setFunctionId(action.getFunctionId());
            actionDto.setUrl(action.getUrl());
            actionDto.setParams(action.getParams());
            actionDto.setExtendParams(action.getExtendParams());

            ViaActionInfo nextAction = action.getNextAction();
            actionDto.setNextAction(convert2ActionDto(nextAction));
            btnInfoDto.setAction(actionDto);
        }

        btnInfoDto.setBtnTip(btnInfo.getBtnTip());
        btnInfoDto.setCode(btnInfo.getCode());
        btnInfoDto.setName(btnInfo.getName());
        btnInfoDto.setStyle(btnInfo.getStyle());
        return btnInfoDto;
    }

    default ViaActionInfoDto convert2ActionDto(ViaActionInfo actionInfo, Map<String, Object> data){

        actionInfo.init(data);
        return convert2ActionDto(actionInfo);
    }

}
