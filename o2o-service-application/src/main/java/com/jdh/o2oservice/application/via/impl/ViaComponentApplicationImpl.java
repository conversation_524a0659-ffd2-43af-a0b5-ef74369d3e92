package com.jdh.o2oservice.application.via.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.jdh.o2oservice.application.via.ViaComponentApplication;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.util.EntityUtil;
import com.jdh.o2oservice.base.util.GeoDistanceUtil;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.angel.repository.db.JdhStationRepository;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelWorkTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelWork;
import com.jdh.o2oservice.core.domain.angelpromise.repository.db.AngelWorkRepository;
import com.jdh.o2oservice.core.domain.angelpromise.repository.query.AngelWorkDBQuery;
import com.jdh.o2oservice.core.domain.angelpromise.service.AngelShipDomainService;
import com.jdh.o2oservice.core.domain.promise.repository.db.PromiseRepository;
import com.jdh.o2oservice.core.domain.support.basic.model.Birthday;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.GisPointBo;
import com.jdh.o2oservice.core.domain.support.vertical.model.JdhVerticalBusiness;
import com.jdh.o2oservice.core.domain.support.vertical.repository.VerticalBusinessRepository;
import com.jdh.o2oservice.core.domain.support.via.configcenter.ViaConfigRepository;
import com.jdh.o2oservice.core.domain.support.via.enums.PromiseAggregateStatusEnum;
import com.jdh.o2oservice.core.domain.support.via.model.ViaConfig;
import com.jdh.o2oservice.core.domain.support.via.model.ViaConfigIdentifier;
import com.jdh.o2oservice.core.domain.support.via.model.ViaStatusMapping;
import com.jdh.o2oservice.application.via.common.ViaComponentDomainService;
import com.jdh.o2oservice.export.medicalpromise.dto.MedicalPromiseDTO;
import com.jdh.o2oservice.export.promise.dto.BirthdayDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDistanceDto;
import com.jdh.o2oservice.export.promise.dto.PromisePatientDto;
import com.jdh.o2oservice.export.promise.dto.UserNameDto;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.via.query.ViaPromiseDistanceRequest;
import com.jdh.o2oservice.ext.ship.reponse.DeliveryOrderDetailResponse;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdOrderPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhMedicalPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePatientPoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.dao.JdhPromisePoMapper;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdOrderPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhMedicalPromisePo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePatientPo;
import com.jdh.o2oservice.infrastructure.repository.db.po.JdhPromisePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/4 15:50
 */
@Component
@Slf4j
public class ViaComponentApplicationImpl implements ViaComponentApplication {
    /**
     * viaConfigRepository
     */
    @Resource
    private ViaConfigRepository viaConfigRepository;
    /**
     * jdhPromiseRepository
     */
    @Resource
    private PromiseRepository promiseRepository;
    @Resource
    private JdhPromisePoMapper jdhPromisePoMapper;
    @Resource
    private JdOrderPoMapper jdOrderPoMapper;
    @Resource
    private ViaComponentDomainService viaComponentDomainService;
    @Resource
    private AngelWorkRepository angelWorkRepository;
    @Resource
    private VerticalBusinessRepository verticalBusinessRepository;
    @Resource
    private JdhStationRepository jdhStationRepository;
    @Resource
    private AngelShipDomainService angelShipDomainService;
    @Resource
    private AddressRpc addressRpc;
    @Resource
    private JdhMedicalPromisePoMapper jdhMedicalPromisePoMapper;
    @Resource
    private JdhPromisePatientPoMapper jdhPromisePatientPoMapper;

    /**
     * 获取服务者距离信息，三个环节会展示地图，派单中、上门阶段、送检阶段；需要判断阶段是否发生了变化，返回前端refresh字段，前端根据refresh判断
     * 是否刷新页面。
     *
     *
     * @param request
     * @return
     */
    @Override
    public PromiseDistanceDto queryPromiseDistance(ViaPromiseDistanceRequest request) {
        // 查询配置
        ViaConfig viaConfig = viaConfigRepository.find(ViaConfigIdentifier.builder().scene(request.getScene()).build());
        // 查询订单
        LambdaQueryWrapper<JdOrderPo> orderWrapper = Wrappers.lambdaQuery();
        orderWrapper.eq(JdOrderPo::getOrderId, Long.valueOf(request.getOrderId()));
        orderWrapper.eq(JdOrderPo::getYn, YnStatusEnum.YES.getCode());
        JdOrderPo orderPo = jdOrderPoMapper.selectOne(orderWrapper);
        // 查询promise
        LambdaQueryWrapper<JdhPromisePo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(JdhPromisePo::getPromiseId, Long.valueOf(request.getPromiseId()));
        queryWrapper.eq(JdhPromisePo::getYn, YnStatusEnum.YES.getCode());
        JdhPromisePo promisePo = jdhPromisePoMapper.selectOne(queryWrapper);
        // 查询检测单
        LambdaQueryWrapper<JdhMedicalPromisePo> medicalPromisePoLambdaQueryWrapper = Wrappers.lambdaQuery();
        medicalPromisePoLambdaQueryWrapper
                .eq(JdhMedicalPromisePo::getPromiseId, Long.valueOf(request.getPromiseId()))
                .eq(JdhMedicalPromisePo::getYn, YnStatusEnum.YES.getCode());
        List<JdhMedicalPromisePo> medicalPromisePos = jdhMedicalPromisePoMapper.selectList(medicalPromisePoLambdaQueryWrapper);
        // ==>>>> 过滤statusMapping，获取当前状态对应的mapping和最新阶段
        ViaStatusMapping statusMapping = viaComponentDomainService.parseHomeTestMapping(
                EntityUtil.getFiledDefaultNull(orderPo, JdOrderPo::getOrderStatus),
                EntityUtil.getFiledDefaultNull(promisePo, JdhPromisePo::getPromiseStatus),
                EntityUtil.getFiledDefaultNull(medicalPromisePos , JdhMedicalPromisePo::getStatus),
                EntityUtil.getFiledDefaultNull(promisePo, e -> YnStatusEnum.convert(e.getImmediately())),
                null,
                viaConfig
        );
        // 获取业务身份
        JdhVerticalBusiness business = verticalBusinessRepository.find(promisePo.getVerticalCode());
        PromiseDistanceDto dto = new PromiseDistanceDto();
        AngelWorkDBQuery angelWorkDBQuery = new AngelWorkDBQuery();
        angelWorkDBQuery.setPromiseId(Long.valueOf(request.getPromiseId()));


        // 获取服务者信息，如果服务者存在，需要获取服务者经纬度。
        AngelWork angelWork = angelWorkRepository.findAngelWork(angelWorkDBQuery);
        if (Objects.nonNull(angelWork)) {
            log.info("ViaComponentApplicationImpl->queryPromiseDistance angelWork={}", JSON.toJSONString(angelWork));
            DeliveryOrderDetailResponse shipDetail = angelShipDomainService.getShipOrderDetailByWork(angelWork, business);
            log.info("ViaComponentApplicationImpl->queryPromiseDistance shipDetail={}", JSON.toJSONString(shipDetail));
            if (Objects.nonNull(shipDetail)) {
                dto.setAngelLat(shipDetail.getTransporterLat());
                dto.setAngelLng(shipDetail.getTransporterLng());
            }
        }

        // 默认刷新页面，如果阶段一样则不刷新。
        if (StringUtils.equals(statusMapping.getAggregateStatus(), request.getAggregateStatus())){
            dto.setRefresh(Boolean.FALSE);
        }

        String angelType = AngelWorkTypeEnum.matchType(business.getBusinessModeCode()).getAngelType();
        String freshStatus = statusMapping.getAggregateStatus();
        if (Objects.equals(freshStatus, PromiseAggregateStatusEnum.TO_HOME.getCode())){
            // 根据当前状态判断骑手是赶往用户家，还是赶往实验室
            // 解析服务者地址经纬度
            dto.setAngelDesc(angelType + "正在赶来");
            try {
                log.info("ViaComponentApplicationImpl->queryPromiseDistance 开始解析用户地址经纬度");
                GisPointBo gisPointBo = addressRpc.getLngLatByAddress(promisePo.getStoreAddr());
                log.info("ViaComponentApplicationImpl->queryPromiseDistance gisPointBo={}", JSON.toJSONString(gisPointBo));
                dto.setPromiseLat(gisPointBo.getLatitude().toString());
                dto.setPromiseLng(gisPointBo.getLongitude().toString());

                if (StringUtils.isNotBlank(dto.getAngelLat()) && StringUtils.isNotBlank(dto.getAngelLng())) {
                    double angelLat = Double.parseDouble(dto.getAngelLat());
                    double angelLng = Double.parseDouble(dto.getAngelLng());
                    dto.setDistance(GeoDistanceUtil.calculateDistanceDynamicUnit(angelLat, angelLng, gisPointBo.getLatitude().doubleValue(), gisPointBo.getLongitude().doubleValue()));
                }
            }catch (Exception e){
                log.error("ViaComponentApplicationImpl->findPromiseDistance parse gisPointBo error", e);
            }
        }else if(Objects.equals(freshStatus, PromiseAggregateStatusEnum.TO_LAB.getCode())){
            // 当前阶段为送检阶段，获取实验室经纬度
            dto.setAngelDesc(angelType + "正在赶往实验室");
            try {
                medicalPromisePos.stream().filter(e -> StringUtils.isNotBlank(e.getStationAddress())).findFirst()
                        .ifPresent((e) -> {
                            log.info("ViaComponentApplicationImpl->queryPromiseDistance 开始解析实验室经纬度");
                            GisPointBo stationGis = addressRpc.getLngLatByAddress(e.getStationAddress());
                            dto.setStationLng(stationGis.getLongitude().toString());
                            dto.setStationLat(stationGis.getLatitude().toString());
                        });
                if (StringUtils.isNotBlank(dto.getAngelLat()) && StringUtils.isNotBlank(dto.getAngelLng())) {
                    double angelLat = Double.parseDouble(dto.getAngelLat());
                    double angelLng = Double.parseDouble(dto.getAngelLng());
                    double stationLat = Double.parseDouble(dto.getStationLat());
                    double stationLng = Double.parseDouble(dto.getStationLng());
                    dto.setDistance(GeoDistanceUtil.calculateDistanceDynamicUnit(angelLat, angelLng, stationLat, stationLng));
                }
            }catch (Exception e){
                log.error("ViaComponentApplicationImpl->findPromiseDistance parse stationGis error", e);
            }
        }else if(Objects.equals(freshStatus, PromiseAggregateStatusEnum.DISPATCH.getCode())){
            log.info("ViaComponentApplicationImpl->queryPromiseDistance 开始解析用户地址经纬度");
            GisPointBo gisPointBo = addressRpc.getLngLatByAddress(promisePo.getStoreAddr());
            log.info("ViaComponentApplicationImpl->queryPromiseDistance gisPointBo={}", JSON.toJSONString(gisPointBo));
            // 前端依赖骑手经纬度展示地图点位信息，临时先把服务地址经纬度返回。后续文案展示在哪个点位由服务端返回
            dto.setPromiseLat(gisPointBo.getLatitude().toString());
            dto.setPromiseLng(gisPointBo.getLongitude().toString());
            dto.setPromiseDesc("正在匹配" + angelType);
        }
        return dto;
    }

    /**
     * 获取履约服务人信息
     * @param idRequest
     * @return
     */
    @Override
    public List<PromisePatientDto> queryPromisePatient(PromiseIdRequest idRequest) {

        // 查询检测单
        LambdaQueryWrapper<JdhMedicalPromisePo> medicalPromisePoLambdaQueryWrapper = Wrappers.lambdaQuery();
        medicalPromisePoLambdaQueryWrapper
                .eq(JdhMedicalPromisePo::getPromiseId, idRequest.getPromiseId())
                .notIn(JdhMedicalPromisePo::getStatus, MedicalPromiseStatusEnum.UN_BIND_STATUS)
                .eq(JdhMedicalPromisePo::getYn, YnStatusEnum.YES.getCode());
        List<JdhMedicalPromisePo> medicalPromisePos = jdhMedicalPromisePoMapper.selectList(medicalPromisePoLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(medicalPromisePos)){
            return Collections.emptyList();
        }
        Map<Long, List<JdhMedicalPromisePo>> map = medicalPromisePos.stream().collect(Collectors.groupingBy(JdhMedicalPromisePo::getPromisePatientId));

        LambdaQueryWrapper<JdhPromisePatientPo> patientPoLambdaQueryWrapper = Wrappers.lambdaQuery();
        patientPoLambdaQueryWrapper
                .in(JdhPromisePatientPo::getPromisePatientId, map.keySet())
                .eq(JdhPromisePatientPo::getYn, YnStatusEnum.YES.getCode());
        List<JdhPromisePatientPo> promisePatientPos = jdhPromisePatientPoMapper.selectList(patientPoLambdaQueryWrapper);

        List<PromisePatientDto> patientDtos = Lists.newArrayList();
        for (JdhPromisePatientPo promisePatientPo : promisePatientPos) {
            PromisePatientDto dto = new PromisePatientDto();
            dto.setUserName(UserNameDto.builder().name(promisePatientPo.getUserName()).build());
            dto.setGender(promisePatientPo.getUserGender());
            Birthday birthday = new Birthday(promisePatientPo.getBirthday());
            dto.setBirthday(BirthdayDto.builder().birth(birthday.getBirth()).age(birthday.getAge()).build());

            List<MedicalPromiseDTO> medicalPromiseDetails = Lists.newArrayList();
            List<JdhMedicalPromisePo> specimens = map.get(promisePatientPo.getPromisePatientId());
            for (JdhMedicalPromisePo specimen : specimens) {
                MedicalPromiseDTO medicalPromiseDTO = MedicalPromiseDTO.builder()
                        .medicalPromiseId(specimen.getMedicalPromiseId())
                        .serviceItemName(specimen.getServiceItemName())
                        .build();
                medicalPromiseDetails.add(medicalPromiseDTO);
            }
            dto.setMedicalPromiseDetails(medicalPromiseDetails);
            String headerImage = viaComponentDomainService.queryPatientHeadImage(promisePatientPo.getUserGender(), birthday.getAge());
            dto.setPatientHeaderImage(headerImage);
            patientDtos.add(dto);
        }
        return patientDtos;
    }

}
