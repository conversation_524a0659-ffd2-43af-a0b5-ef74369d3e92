package com.jdh.o2oservice.application.support.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.application.support.convert.OperateLogApplicationConverter;
import com.jdh.o2oservice.application.support.service.OperateLogApplication;
import com.jdh.o2oservice.application.support.service.impl.param.OperateLogCheckParam;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.support.operateLog.model.OperateLog;
import com.jdh.o2oservice.core.domain.support.operateLog.repository.OperateLogRepository;
import com.jdh.o2oservice.export.support.FileManageJsfExport;
import com.jdh.o2oservice.export.support.dto.OperateLogPageDto;
import com.jdh.o2oservice.export.support.query.OperateLogPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 操作日志实现类
 */
@Component
@Slf4j
public class OperateLogApplicationImpl implements OperateLogApplication {
    /**
     *
     */
    @Autowired
    private OperateLogCheckParam operateLogCheckParam;
    /**
     *
     */
    @Autowired
    private FileManageJsfExport fileManageJsfExport;
    /**
     *
     */
    @Autowired
    private OperateLogRepository operateLogRepository;



    @Override
    @LogAndAlarm(jKey = "com.jdh.o2oservice.application.support.service.impl.OperateLogApplicationImpl.queryOperateLogByPage")
    public PageDto<OperateLogPageDto> queryOperateLogByPage(OperateLogPageRequest operateLogPageRequest) {
        //参数校验
        operateLogCheckParam.queryOperateLogByPage(operateLogPageRequest);
        //入参转换
        OperateLog operateLog = OperateLogApplicationConverter.instance.queryPage2OperateLog(operateLogPageRequest);
        //分页查询
        Page<OperateLog> page = operateLogRepository.queryPage(operateLog);
        List<OperateLogPageDto> operateLogPageDtoList = new ArrayList<>();
        //出参转换
        PageDto<OperateLogPageDto> pageDto = new PageDto<>();
        pageDto.setTotalPage(page.getPages());
        pageDto.setPageNum(page.getCurrent());
        pageDto.setPageSize(page.getSize());
        pageDto.setTotalCount(page.getTotal());
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<OperateLog> operateLogList = page.getRecords();
            for (OperateLog operateLogTemp : operateLogList) {
                OperateLogPageDto operateLogPageDto = OperateLogApplicationConverter.instance.convertOperateLog2Dto(operateLogTemp);
                if(StringUtil.isNotBlank(operateLogTemp.getOperateContent())){
                    String content = operateLogTemp.getOperateContent().replaceAll("\"","");
                    String feeConfigUrl = fileManageJsfExport.getDownLoadUrlByFilePath(content).getData();
                    operateLogPageDto.setOperateContent(feeConfigUrl);
                }
                operateLogPageDtoList.add(operateLogPageDto);
            }
            pageDto.setList(operateLogPageDtoList);
        }
        return pageDto;
    }

}
