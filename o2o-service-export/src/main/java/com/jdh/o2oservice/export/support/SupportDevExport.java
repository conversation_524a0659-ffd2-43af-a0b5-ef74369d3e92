package com.jdh.o2oservice.export.support;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.angel.cmd.AngelActivityRecruitmentCmd;
import com.jdh.o2oservice.export.angel.cmd.AngelSyncCmd;
import com.jdh.o2oservice.export.angel.cmd.CreateAngelSkillDictCmd;
import com.jdh.o2oservice.export.angel.dto.GetUserAccountDto;
import com.jdh.o2oservice.export.angel.dto.JdhAngelDto;
import com.jdh.o2oservice.export.angel.query.AngelPageRequest;
import com.jdh.o2oservice.export.dispatch.cmd.*;
import com.jdh.o2oservice.export.promise.cmd.CreateVoucherCmd;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.report.cmd.MedicalReportSaveCmd;
import com.jdh.o2oservice.export.support.command.ImitatePublishEventCmd;
import com.jdh.o2oservice.export.support.command.SubmitAppointmentStartCmd;
import com.jdh.o2oservice.export.trade.dto.JdOrderFullDTO;
import com.jdh.o2oservice.export.trade.query.JdOrderFullPageParam;
import com.jdh.o2oservice.export.via.dto.ViaCompletePromiseDto;
import com.jdh.o2oservice.export.via.query.ViaCompletePromiseRequest;

import java.util.List;
import java.util.Set;

/**
 * 支持开发接口
 *
 * <AUTHOR>
 * @date 2024/01/18
 */
public interface SupportDevExport {

    /**
     * 创建voucher单
     *
     * @param cmd CMD
     * @return {@link Response}<{@link Boolean}>
     */
    Response<List<PromiseDto>> createVoucher(CreateVoucherCmd cmd);

    /**
     * 创建voucher单
     *
     * @param cmdList CMD
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> batchCreateVoucher(List<CreateVoucherCmd> cmdList);

    /**
     * 手动处置事件
     *
     * @param eventId      事件ID
     * @param consumerCode 消费者代码
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> disposeAsyncEvent(Long eventId, String consumerCode);

    /**
     *
     * @param eventIds
     * @param consumerCode
     * @return
     */
    Response<Boolean> batchDisposeAsyncEvent(List<Long> eventIds, String consumerCode);

    /**
     * 模仿发布事件
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> imitatePublishEvent(ImitatePublishEventCmd cmd);

    /**
     * 获取ID
     *
     * @return {@link Response}<{@link String}>
     */
    Response<String> getId();

    /**
     * 批次获取ID
     *
     * @param num num
     * @return {@link Response}<{@link List}<{@link String}>>
     */
    Response<List<String>> batchGetId(Integer num);

    /**
     * 同步订单
     *
     * @param orderId    订单ID
     * @param createTime 创建时间
     * @return {@link Response}<{@link String}>
     */
    Response<String> syncOrder(String orderId, String createTime);

    /**
     * 同步履约单
     *
     * @param orderId    订单ID
     * @param createTime 创建时间
     * @return {@link Response}<{@link String}>
     */
    Response<String> syncPromise(String orderId, String createTime);

    /**
     * 缓存批处理删除
     *
     * @param keyList keyList
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> batchDelCacheByKeyList(List<String> keyList);

    /**
     * 缓存批处理删除
     *
     * @param keyPrefix keyPrefix
     * @return {@link Response}<{@link Boolean}>
     */
    Response<List<String>> batchGetCacheKeyByPrefix(String keyPrefix);


    /**
     * 缓存批处理删除
     *
     * @param keyPrefix keyPrefix
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> batchDelCacheByPrefix(String keyPrefix);


    /**
     * 获取字符串缓存值
     *
     * @param key 关键
     * @return {@link Response}<{@link String}>
     */
    Response<String> getStringCacheByKey(String key);

    /**
     *
     * @param fromLocation
     * @param toLocation
     * @return
     */
    Response<String> getDirectionResult(String fromLocation, String toLocation);

    /**
     * 是可解密
     *
     * @param str str
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> isDecryptable(String str);

    /**
     * TDE解密
     *
     * @param cipherText 密文
     * @return {@link Response}<{@link String}>
     */
    Response<String> tdeDecrypt(String cipherText);

    /**
     * TDE加密
     *
     * @param writePlaintext 明文
     * @return {@link Response}<{@link String}>
     */
    Response<String> tdeEncrypt(String writePlaintext);

    /**
     * 计算通配符关键字
     *
     * @param writePlaintext 明文
     * @return {@link Response}<{@link String}>
     */
    Response<String> calculateWildCardKeyWord(String writePlaintext);

    /**
     * obtainWildCardKeyWordIndex
     *
     * @param writePlaintext 写明文
     * @return {@link Response}<{@link String}>
     */
    Response<String> obtainWildCardKeyWordIndex(String writePlaintext);


    /**
     * 计算通配符关键字
     *
     * @param writePlaintext 明文
     * @return {@link Response}<{@link String}>
     */
    Response<String> calculateKeyWord(String writePlaintext);

    /**
     * obtainWildCardKeyWordIndex
     *
     * @param writePlaintext 写明文
     * @return {@link Response}<{@link String}>
     */
    Response<String> obtainKeyWordIndex(String writePlaintext);

    /**
     * 手动处理派单结果
     * @param cmd
     * @return
     */
    Response<Boolean> createDispatchDetail(String cmd);

    /**
     * 创建派单任务
     * @param cmd
     * @return
     */
    Response<Boolean> submitDispatch(SubmitDispatchCmd cmd);

    /**
     * 处理互医侧派单
     * @param cmd
     * @return
     */
    Response<Boolean> handleNewNethpDispatch(DispatchNewNethpHandleCmd cmd);

    /**
     * 刷新履约人信息
     *
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> refreshPromisePatient(String verticalCode,String createTime);

    /**
     * 刷新履约人信息
     *
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> refreshPromisePatientByPromiseId(List<Long> promiseIdList);

    /**
     *
     * @param contentType
     * @param filePath
     * @return
     */
    Response<String> generatePutUrl(String contentType, String filePath );

    /**
     * 导入标准指标
     * @param ossKey
     * @return
     */
    Response<Boolean> importStandardIndicator(String ossKey);

    /**
     * 导出新老指标映射
     * @param mappingOssKey
     * @return
     */
    Response<Boolean> exportMappingIndicator(String mappingOssKey);

    /**
     * 导出新老数据清洗结果
     * @param mappingOssKey
     * @param type
     * @return
     */
    Response<Boolean> exportProgramItemData(String mappingOssKey, Integer type, Integer partNum);

    /**
     * 清洗业务项目数据（老 -> 新）
     * @param mappingOssKey
     * @return
     */
    Response<Boolean> cleanBizServiceItem(String mappingOssKey);

    /**
     * 清洗自营商品项目
     * @param ossKey
     * @param mappingOssKey
     * @return
     */
    Response<Boolean> cleanSelfServiceItem(String ossKey, String mappingOssKey);

    /**
     * 新增自营商品项目
     * @param ossKey
     * @return
     */
    Response<Boolean> addSelfServiceItem(String ossKey);

    /**
     * 清洗京东服务
     * @param mappingOssKey
     * @return
     */
    Response<Boolean> cleanService(String mappingOssKey);

    /**
     * 清洗自营商品套餐和项目关系
     * @param mappingOssKey
     * @return
     */
    Response<Boolean> cleanSelfServiceRel(String mappingOssKey);

    /**
     * 清洗pop商品数据
     * @param ossKey
     * @return
     */
    Response<Boolean> cleanPopProductSku(String ossKey);

    /**
     * 修正pop sku信息
     * @param ossKey
     * @return
     */
    Response<Boolean> revisePopProductSku(String ossKey, String mappingOssKey);

    /**
     * 导出老项目创建新业务项目
     * @param ossKey
     * @return
     */
    Response<Boolean> importOldItemList(String ossKey, Long standardItemId, Set<Long> serviceIdList);

    /**
     *
     * @param ossKey1
     * @param ossKey2
     * @return
     */
    Response<Boolean> comparePopProductSku(String ossKey1, String ossKey2);

    /**
     * 刷新适用人群
     * @param ossKey
     * @return
     */
    Response<Boolean> refreshProductSkuSuitable(String ossKey);

    /**
     * 清洗pop商品项目
     * @param ossKey
     * @return
     */
    Response<Boolean> cleanPopProductItem(String ossKey);

    /**
     * 清洗自营SKU套餐项目名称
     * @param ossKey
     * @return
     */
    Response<Boolean> cleanSelfProgramItemName(String ossKey);

    /**
     * 修正自营 项目数据
     * @param ossKey
     * @param mappingOssKey
     * @return
     */
    Response<Boolean> reviseSelfProductSku(String ossKey, String mappingOssKey);

    /**
     *
     * @param json
     * @return
     */
    Response<Boolean> supplementProductSku(String json);

    /**
     *
     * @param json
     * @return
     */
    Response<Boolean> refreshProductSku(String json);

    /**
     * 执行触达任务
     * @param taskId
     * @return
     */
    Response<Boolean> executeTask(String taskId);

    /**
     * 测试用例-补全订单宽表数据
     * @param promiseIds
     * @return
     */
    Response<Boolean> reloadFullOrderInfo(String promiseIds);

    /**
     * 同步护士数据
     * @param angelSyncCmd
     * @return
     */
    Response<Boolean> syncAngelAuditInfoFromNethp(AngelSyncCmd angelSyncCmd);

    /**
     * reloadFullOrderInfoBatch
     *
     * @param verticalCode 业务身份
     * @param createTime   创建时间
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> reloadFullOrderInfoBatch(String verticalCode,String createTime);

    /**
     * 触发定时任务
     * @param jobName
     * @return
     */
    Response<Boolean> triggerJob(String jobName, String method);

    /**
     * 重新派单
     * @return
     */
    Response<Boolean> reDispatch(ReDispatchCmd cmd);

    /**
     * 指定派单
     * @param cmd
     * @return
     */
    Response<Boolean> targetDispatch(TargetDispatchCmd cmd);

    /**
     * 接单护士转单
     * @param cmd
     * @return
     */
    Response<Boolean> receiveTransferDispatch(TargetDispatchCmd cmd);

    /**
     * @Description: 分页查询服务者信息
     * @param request
     **/
    Response<PageDto<JdhAngelDto>> queryAngelByPage(AngelPageRequest request);

    /**
     * expireVoucher
     *
     * @param itemIds itemIds
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> queryStationListForServiceItemAllInOneStore(List<Long> itemIds);
    /**
     * 查询es的服务单列表
     *
     * @param param param
     * @return {@link Response}<{@link PageDto}<{@link JdOrderFullDTO}>>
     */
    Response<PageDto<JdOrderFullDTO>> queryPage(JdOrderFullPageParam param);

    /**
     * queryCompletePromise
     *
     * @param request 请求
     * @return {@link Response}<{@link ViaCompletePromiseDto}>
     */
    Response<ViaCompletePromiseDto> queryCompletePromise(ViaCompletePromiseRequest request);


    /**
     * 刷新es数据
     *
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> refreshFullOrderIndex(String serviceType,String createTime,String endTime);

    /**
     * expireVoucher
     *
     * @param voucherId 凭证id
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> expireVoucher(Long voucherId);

    /**
     * 发起派单
     * @param cmd
     * @return
     */
    Response<Boolean> executeDispatch(AngelDispatchCmd cmd);

    /**
     * 派单拉完成
     * @param promiseIds
     * @return
     */
    Response<Boolean> dispatchComplete(List<String> promiseIds);

    /**
     * 护士活动同步状态
     * @param cmd
     * @return
     */
    Response<Boolean> syncAngelActivityProgress(AngelActivityRecruitmentCmd cmd);

    /**
     * getExcludeAlarmCode
     *
     * @return {@link Response}<{@link Object}>
     */
    Response<Object> getExcludeAlarmCode();


    /**
     * 订单大数据
     *
     * @return {@link Response}<{@link Object}>
     */
    Response<Object> getOrderDataInfo(Long orderId);

    /**
     * 同步报告到档案中心
     * @param medicalReportSaveCmd
     * @return
     */
    Response<Boolean> syncMedicalReportToReportCenter(MedicalReportSaveCmd medicalReportSaveCmd);
    String invokeMethod(String className, String methodName, List<String> paramJsons, List<String> paramTypes, String rType);

    Response<String> testLimitMax();

    Response<String> testPageLimit();

    Response<String> testPageLimitSkip();

    Response<Boolean> distributeFeedback(String feedbackScene, Long promiseId);

    /**
     * 刷新自动化测试白名单数据
     * @return
     */
    Response<Boolean> refreshAutomatedTestAggregate();

    /**
     * 重置订单数据
     * @param orderId
     * @return
     */
    Response<Boolean> resetAutomatedTestAggregateData(String orderId);

    /**
     * 提交预约信息执行自动化测试流程
     * @param cmd
     * @return
     */
    Response<Boolean> submitAppointmentAndAutomatedTestStart(SubmitAppointmentStartCmd cmd);

    /**
     * 新增护士技能
     * @param createAngelSkillDictCmd
     * @return
     */
    Response<Boolean> insertAngelSkillDict(CreateAngelSkillDictCmd createAngelSkillDictCmd);

    /**
     * 查询闪送余额
     * @return
     */
    Response<GetUserAccountDto> getUserAccount();

    /**
     * 在指定日期范围内删除消息
     * @param startDate 开始日期，格式为yyyy-MM-dd
     * @param endDate 结束日期，格式为yyyy-MM-dd
     * @return 删除操作是否成功
     */
    Boolean removeMessage(String startDate, String endDate, List<Integer> statusList);

    Boolean expireVoucherByDate(String startDate);

    /**
     * 验证事件的发布
     * @return
     */
    Boolean validatePublish(String promiseId, String eventType);

    /**
     * 验证事件分发
     */
    Boolean validateDistributeEvent(Long eventId, String consumerCode);


    /**
     * 验证延迟事件的发布
     * @param dispatchId
     * @param endDate
     * @return
     */
    Boolean validatePublishDelayEvent(String dispatchId, String endDate);
    Boolean selfTransportCompleteOrder(String orderId);
    Boolean updateMessageBoxIndex(String userPin, String startTime, String endTime);

    /**
     *
     * @return
     */
    Boolean dispatchRiskTest(List<Long> promiseIds);

}
