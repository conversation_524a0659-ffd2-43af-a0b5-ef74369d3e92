package com.jdh.o2oservice.export.promise;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.product.dto.AgencyAppointDateDto;
import com.jdh.o2oservice.export.product.dto.GroupUserAddressDTO;
import com.jdh.o2oservice.export.product.query.AgencyQueryDateRequest;
import com.jdh.o2oservice.export.product.query.OrderUserAddressQuery;
import com.jdh.o2oservice.export.promise.cmd.AgentModifyPromiseCmd;
import com.jdh.o2oservice.export.promise.cmd.CreateVoucherCmd;
import com.jdh.o2oservice.export.promise.cmd.InvalidVoucherCmd;
import com.jdh.o2oservice.export.promise.cmd.SubmitPromiseCmd;
import com.jdh.o2oservice.export.promise.dto.CompletePromiseDto;
import com.jdh.o2oservice.export.promise.dto.PromiseDto;
import com.jdh.o2oservice.export.promise.query.CompletePromiseRequest;
import com.jdh.o2oservice.export.promise.query.PromiseIdRequest;
import com.jdh.o2oservice.export.promise.query.PromiseListRequest;
import com.jdh.o2oservice.export.trade.dto.AvaiableAppointmentTimeDTO;
import com.jdh.o2oservice.export.trade.query.AvaiableAppointmentTimeParam;
import com.jdh.o2oservice.export.via.dto.ViaCompletePromiseDto;
import com.jdh.o2oservice.export.via.query.ViaCompletePromiseRequest;

import java.util.List;
import java.util.Map;

/**
 * JDH Promise JSF服务
 *
 * <AUTHOR>
 * @data 2024/9/18
 */
public interface PromiseJsfExport {
    /**
     * 创建服务单,并根据 syncCreatePromise 字段判断是否同步创建履约单
     *
     * @param cmd cmd
     * @return {@link Response}<{@link List}<{@link PromiseDto}>>
     */
    Response<List<PromiseDto>> createVoucher(CreateVoucherCmd cmd);

    /**
     * 作废服务单
     *
     * @param cmd CMD
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> invalidVoucher(InvalidVoucherCmd cmd);

    /**
     * 查询排期列表
     *
     * @param param param
     * @return {@link Response}<{@link AvaiableAppointmentTimeDTO}>
     */
    Response<AvaiableAppointmentTimeDTO> queryAvaiableAppointmentTime(AvaiableAppointmentTimeParam param);

    /**
     * 分组获取地址列表
     *
     * @param request request
     * @return {@link Response}<{@link Map}<{@link String}，{@link GroupUserAddressDTO}>>
     */
    Response<Map<String, GroupUserAddressDTO>> queryListGroupAddress(OrderUserAddressQuery request);

    /**
     * 提交预约
     *
     * @param cmd cmd
     * @return {@link Response}<{@link Boolean}>
     */
    Response<Boolean> submit(SubmitPromiseCmd cmd);

    /**
     * 预约详情
     *
     * @param request request
     * @return {@link Response}<{@link CompletePromiseDto}>
     */
    Response<CompletePromiseDto> queryCompletePromise(CompletePromiseRequest request);

    /**
     * 预约详情
     *
     * @param request request
     * @return {@link Response}<{@link CompletePromiseDto}>
     */
    Response<List<CompletePromiseDto>> queryCompletePromiseList(CompletePromiseRequest request);

    Response<PromiseDto> findPromise(PromiseIdRequest request);


    /**
     * queryPromise
     *
     * @param request 请求
     * @return {@link Response }<{@link PromiseDto }>
     */
    Response<PromiseDto> queryPromise(PromiseIdRequest request);

    /**
     * 查询预约时间
     * @param request
     * @return
     */
    Response<List<AgencyAppointDateDto>> queryAvailableTime(AgencyQueryDateRequest request);

    /**
     * 修改预约时间
     * @param cmd
     * @return
     */
    Response<Boolean> modifyAppointmentTime(AgentModifyPromiseCmd cmd);

    /**
     * 查询履约单列表
     * @param request
     * @return
     */
    Response<List<PromiseDto>> findByPromiseList(PromiseListRequest request);

}
