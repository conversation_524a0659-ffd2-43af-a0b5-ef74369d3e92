package com.jdh.o2oservice.export.product.dto;

import lombok.Data;

import java.io.Serializable;
@Data
public class ProductPurchasePriceDto implements Serializable {
    /**
     * skuId
     */
    private String skuId;

    /**
     * 到手价
     */
    private String purchasePrice;

    /**
     * 值为2
     * 计算了plus优惠（plus95折、plus专享立减、plus券）, 叠加上plus专属优惠（包括券和促销）
     */
    private Integer secondPriceType;
}
