package com.jdh.o2oservice.export.promise.cmd;

import com.jdh.o2oservice.common.result.request.AbstractBusinessIdentity;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CreateVoucherCmd extends AbstractBusinessIdentity {

    /**
     * 用户pin
     */
    private String userPin;

    /**
     * 服务兑换的来源id，兑换id/orderItemId/outerOrderId
     */
    private String sourceVoucherId;

    /**
     * 来源类型：京东订单、外部订单、兑换
     */
    private Integer sourceType;

    /**
     * 履约次数
     */
    private Integer promiseNum;

    /**
     * 服务过期时间
     */
    private Date expireDate;

    /**
     * 扩展字段
     */
    private VoucherExtend extend;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * JDH 服务单明细
     */
    private List<VoucherItem> voucherItemList;

    /**
     * 是否同步产码
     */
    private Boolean syncCreatePromise = Boolean.FALSE;
}
