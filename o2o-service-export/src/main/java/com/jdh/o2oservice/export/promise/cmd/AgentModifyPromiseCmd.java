package com.jdh.o2oservice.export.promise.cmd;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

;
;

/**
 * 提交预约单操作类
 *
 * @author: yang<PERSON>yu
 * @date: 2022/8/30 10:11 下午
 * @version: 1.0
 */
@Data
public class AgentModifyPromiseCmd implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /** pin */
    @NotNull(message = "请选择代预约客户")
    private String userPin;

    /**
     * 服务端
     */
    @NotNull(message = "请选择代预约服务单")
    private Long medicalPromiseId;

    /**
     * 起始时间
     */
    @NotNull(message = "请选择预约起始时间")
    private String appointmentStartTime;

    /**
     * 结束时间
     */
    @NotNull(message = "请选择预约结束时间")
    private String appointmentEndTime;

    /**
     * 是否立即预约
     */
    private Boolean isImmediately;

    /**
     * 备注
     */
    @Size(max = 150, message = "备注信息不能超过150个字符")
    private String remark;

    /**
     * 日期类型
     */
    private Integer dateType = 2;

    /**
     * promiseId
     */
    private Long promiseId;

    /**
     * 操作人角色
     */
    private Integer operatorRoleType;

    /**
     * 操作人
     */
    private String operator;

}
