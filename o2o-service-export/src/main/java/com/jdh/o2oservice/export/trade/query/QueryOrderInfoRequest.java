package com.jdh.o2oservice.export.trade.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 完整订单请求
 *
 * <AUTHOR>
 * @date 2024/05/07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrderInfoRequest implements Serializable {

    /**
     * 三方订单号
     */
    private String partnerSourceOrderId;

    /**
     * 三方渠道号
     */
    private Integer partnerSource;

    /**
     * 子渠道号
     */
    private String saleChannelId;
}
