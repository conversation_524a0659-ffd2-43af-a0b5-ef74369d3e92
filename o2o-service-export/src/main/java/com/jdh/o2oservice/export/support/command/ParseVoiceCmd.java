package com.jdh.o2oservice.export.support.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/30 21:51
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ParseVoiceCmd {

    private String domianCode;
    private String aggregateCode;
    private String aggregateId;
    private List<Long> fileIds;
    private Integer voiceType;
}
