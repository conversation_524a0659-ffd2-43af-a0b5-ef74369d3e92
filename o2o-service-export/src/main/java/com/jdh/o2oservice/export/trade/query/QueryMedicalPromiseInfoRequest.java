package com.jdh.o2oservice.export.trade.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * 完整订单请求
 *
 * <AUTHOR>
 * @date 2024/05/07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryMedicalPromiseInfoRequest implements Serializable {

    /**
     * 样品码集合
     */
    private Set<String> specimenCodes;

    /**
     * 三方渠道号
     */
    private Integer partnerSource;

    /**
     * 子渠道号
     */
    private String saleChannelId;
}
