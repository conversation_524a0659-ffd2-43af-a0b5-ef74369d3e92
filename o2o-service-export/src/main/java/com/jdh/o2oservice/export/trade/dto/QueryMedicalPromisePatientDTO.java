package com.jdh.o2oservice.export.trade.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/4/25 15:02
 **/
@Data
public class QueryMedicalPromisePatientDTO {

    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 证件类型 @CredentialTypeEnum
     */
    private Integer credentialType;

    /**
     * 证件号码
     */
    private String credentialNo;

    /**
     * 性别【1男，2女】
     */
    private Integer gender;

    /**
     * 生日;格式yyyy-MM-dd
     */
    private String birthday;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 三方患者ID
     */
    private String outerPatientId;
}
