package com.jdh.o2oservice.export.report.query;

import com.jdh.o2oservice.common.result.request.AbstractRequest;
import lombok.Data;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-04-26 16:58
 * @Desc :
 */
@Data
public class MedicalReportRequest  extends AbstractRequest {
    /**
     * 报告单id
     */
    private Long reportId;
    /**
     * 患者pin
     */
    private String userPin;
    /**
     * 跳转页面-来源
     */
    private String source;

    /**
     * 履约检测单ID
     */
    private Long medicalPromiseId;
    /**
     * 履约单ID
     */
    private Long promiseId;
}
