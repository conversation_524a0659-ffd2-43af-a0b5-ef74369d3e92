package com.jdh.o2oservice.export.promise.cmd;

import com.jdh.o2oservice.common.result.request.AbstractCmd;
import lombok.Data;

import java.io.Serializable;


/**
 * 修改预约
 *
 * <AUTHOR>
 * @date 2023/12/25
 */
@Data
public class ModifyAppointCmd extends AbstractCmd implements Serializable {

    /**
     * 履约单ID
     */
    private String promiseId;

    /**
     * 提交基础信息
     */
    private AppointmentTime appointmentTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作角色
     * @see com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum
     */
    private Integer operatorRoleType;

    /**
     * 收货地址
     */
    private String storeId;

}
