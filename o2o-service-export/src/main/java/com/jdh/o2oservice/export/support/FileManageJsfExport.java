package com.jdh.o2oservice.export.support;

import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.support.command.GenerateGetUrlCommand;
import com.jdh.o2oservice.export.support.command.GeneratePutUrlCommand;
import com.jdh.o2oservice.export.support.dto.FilePreSignedUrlDto;
import com.jdh.o2oservice.export.support.dto.FileTaskDto;
import com.jdh.o2oservice.export.support.dto.FileUrlDto;
import com.jdh.o2oservice.export.support.dto.PutFileResultDto;
import com.jdh.o2oservice.export.support.query.FileInputStreamRequest;
import com.jdh.o2oservice.export.support.query.GetFileUrlRequest;
import com.jdh.o2oservice.export.support.query.PageFileTaskRequest;
import com.jdh.o2oservice.export.support.query.PutFileRequest;

import java.io.InputStream;
import java.util.List;


/**
 * 文件管理jsf
 *
 * <AUTHOR>
 * @date 2024/07/17
 */
public interface FileManageJsfExport {


    /**
     * 生成文件上传的预签名链接
     *
     * @param cmd cmd
     * @return {@link Response}<{@link FilePreSignedUrlDto}>
     */
    Response<FilePreSignedUrlDto> generatePutUrl(GeneratePutUrlCommand cmd);

    /**
     * 生成获取文件的地址
     *
     *
     * @param cmd cmd
     * @return {@link Response}<{@link List}<{@link FilePreSignedUrlDto}> >
     */
    Response<List<FilePreSignedUrlDto>> generateGetUrl(GenerateGetUrlCommand cmd);


    /**
     * 文件解析任务分页查询
     *
     * @param request 请求
     * @return {@link Response}<{@link PageDto}<{@link FileTaskDto}>>
     */
    Response<PageDto<FileTaskDto>> pageFileTask(PageFileTaskRequest request);

    /**
     * 获取文件网址
     *
     * @param request 请求
     * @return {@link Response}<{@link String}>
     */
    Response<String> getFileUrl(GetFileUrlRequest request);

    /**
     * 获取文件网址
     *
     * @param ossFilePath 请求
     * @return {@link Response}<{@link String}>
     */
    Response<String> getDownLoadUrlByFilePath(String ossFilePath);

    /**
     * 获取多个文件网址
     *
     * @param request 请求
     * @return {@link Response}<{@link String}>
     */
    Response<List<FileUrlDto>> getMultiFileUrl(GetFileUrlRequest request);

    /**
     * 查询文件流
     * @param fileInputStreamRequest
     * @return
     */
    Response<InputStream> getFileInputStream(FileInputStreamRequest fileInputStreamRequest);

    /**
     * 上传文件
     * @param putFileRequest
     * @return
     */
    Response<PutFileResultDto> putFile(PutFileRequest putFileRequest);
}
