package com.jdh.o2oservice.export.medicalpromise.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Description: 检测单查询入参
 * @Author: wangpengfei144
 * @Date: 2024/4/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalPromiseListRequest extends AbstractPageQuery implements Serializable {

    /**
     * 序列化号
     */
    private static final long serialVersionUID = 7916718519689852395L;
    /**
     * 检测单ID
     */
    private Long medicalPromiseId;
    /**
     * 履约单ID
     */
    private Long promiseId;
    /**
     * 检测单状态
     */
    private Integer status;
    /**
     * 样本条码
     */
    private String specimenCode;
    /**
     * 检测项目ID
     */
    private String serviceItemId;
    /**
     * 耗材类型
     */
    private String materialType;

    /**
     * 履约单IDList
     */
    private List<Long> promiseIdList;

    /**
     * 受检者ID
     */
    private List<Long> promisePatientIdList;

    /**
     * 是否查询项目详情
     */
    private Boolean itemDetail = Boolean.FALSE;

    /**
     * 是否查报告详情
     */
    private Boolean reportDetail = Boolean.FALSE;

    /**
     * 是否查受检人详情
     */
    private Boolean patientDetail = Boolean.FALSE;

    /**
     * 是否查作废的单子,默认查询
     */
    private Boolean invalid = Boolean.TRUE;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 报告开始时间
     */
    private Date reportStartTime;

    /**
     * 报告结束时间
     */
    private Date reportEndTime;
    /**
     * 报告状态 0.未出报告，1.已出报告
     */
    private Integer reportStatus;
    /**
     * verticalCodeSet
     */
    private Set<String> verticalCodeSet;
    /**
     * 是否查询全部(包括yn=0)
     */
    private Boolean allQuery = Boolean.FALSE;
    /**
     * flag
     */
    private String flag;

    /**
     * 是否查询项目详情（非实验室关联配置）
     */
    private Boolean itemSummaryDetail = Boolean.FALSE;

    /**
     * 样本条码列表
     */
    private List<String> specimenCodeList;

}
