package com.jdh.o2oservice.export.medicalpromise.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 查询单个检测单入参
 * @Author: wangpengfei144
 * @Date: 2024/4/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalPromiseRequest implements Serializable {

    /**
     * 序列化号
     */
    private static final long serialVersionUID = 4730640511019777443L;

    /**
     * 条码信息
     */
    private String specimenCode;

    /**
     * 检测单ID
     */
    private Long medicalPromiseId;

    /**
     * 供应商ID
     */
    private Long providerId;

    /**
     * 是否查询项目详情
     */
    private Boolean itemDetail = Boolean.FALSE;

    /**
     * 是否查报告详情
     */
    private Boolean reportDetail = Boolean.FALSE;

    /**
     * 是否查受检人详情
     */
    private Boolean patientDetail = Boolean.FALSE;
    /**
     * promiseId
     */
    private Long promiseId;
    /** 用户唯一ID */
    private Long promisePatientId;
    /** 服务ID */
    private Long serviceId;
    /**
     * 实验室ID
     */
    private String stationId;
    /**
     * 是否报告eta数据
     */
    private Boolean etaDetail = Boolean.FALSE;


}
