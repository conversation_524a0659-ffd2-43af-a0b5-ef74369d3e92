package com.jdh.o2oservice.export.angel.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @program: jdh-o2o-service
 * @description:服务者 我的 C端
 * @author: luxingchen3
 * @create: 2024-05-06 09:56
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AngelMineDto implements Serializable {
    /**
     * 服务者id
     */
    private Long angelId;

    /**
     * 服务者pin
     */
    private String angelPin;

    /**
     * 服务者姓名
     */
    private String angelName;

    /**
     * 服务者头图
     */
    private String headImg;

    /**
     * 接单状态 0关闭 1开启
     */
    private Integer takeOrderStatus;

    /**
     * 审核状态
     */
    private Integer auditProcessStatus;

    /**
     * 审核链接
     */
    private String auditLink;

    /**
     * 客服电话
     */
    private String serviceTel;

    /**
     * 耗材购买链接
     */
    private String materialPackageLink;

    /**
     * 已结算工单
     */
    private Integer settledWorkCount;

    /**
     * 未服务工单数量
     */
    private Integer unServedWorkCount;

    /**
     * 已服务工单数量
     */
    private Integer servedWorkCount;

    /**
     * 是否属于黑名单，1表示在黑名单，0表示不在黑名单
     */
    private Integer blackListFlag;

    /**
     * 今日收入
     */
    private BigDecimal toDaySettleAmount = BigDecimal.ZERO;


    /**
     * 累计收入
     */
    private BigDecimal totSettleAmount = BigDecimal.ZERO;

    /**
     * 人员标签 0兼职 1全职
     */
    private Integer jobNature;


    private String walletLink;

    private Boolean userServiceInfo;//是否维护了用户服务信息

    private String alertMessage;//如果没有维护用户服务信息 弹框提示内容;

    private List<MenuItem> menuItemList;//入口配置

    private Integer cpsInviteCode; // cps邀请码

    private Long angelUnionId; // 护士联盟ID

    @Data
    public static class MenuItem{

        private String key; //唯一值

        private String icon;//图标

        private String text;//文案

        private String url;//跳转地址

        private String alertMessage;//弹框内容

        private String promptImageUrl;//提示图片地址
    }


}
