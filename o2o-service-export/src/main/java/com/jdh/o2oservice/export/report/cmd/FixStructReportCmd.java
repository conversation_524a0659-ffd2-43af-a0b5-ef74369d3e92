package com.jdh.o2oservice.export.report.cmd;

import lombok.Data;
import org.aspectj.apache.bcel.generic.IINC;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/16
 */
@Data
public class FixStructReportCmd {

    /**
     * 存储医疗承诺的ID列表
     */
    private List<Long> medicalPromiseIds;

    /**
     * 开始时间，用于指定报告的起始日期。
     */
    private Date startTime;

    /**
     * 报告的结束时间。
     */
    private Date endTime;

    /**
     * 实验室ID
     */
    private String stationId;

    private Integer pageNum;

    private Integer pageSize;

    private Boolean needFlushJpg;
    /**
     * 偏移量
     */
    private Long offset;

    /**
     * 偏移量截止
     */
    private Long offsetEnd;


}
