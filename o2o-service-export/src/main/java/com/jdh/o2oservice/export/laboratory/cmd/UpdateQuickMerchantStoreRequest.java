package com.jdh.o2oservice.export.laboratory.cmd;

import com.jdh.o2oservice.export.laboratory.enums.DockingTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * @ClassName addQuickMerchantStore
 * @Description
 * <AUTHOR>
 * @Date 2025/1/7 6:40 PM
 * @Version 1.0
 **/
@Data
public class UpdateQuickMerchantStoreRequest {

    private Integer businessType =16;//业务类型16到家快检业务

    private String storeName;//实验室名称

    private String storeAddr;//实验室地址

    private String storePhone;//门店电话

    private String lng;//经度

    private String lat;//纬度

    private Integer status;//1上架 2下架

    private String channelRuleCode;//天算渠道

    private String contactName;//联系人姓名

    private String contactPhone;//联系人电话

    private String storeHours;//营业时间

    /**
     * @see DockingTypeEnum
     */
    private Integer dockingType;//接入类型

    private String erp;//操作人账号

    private Integer operateType;//2更新

    private String jdStoreId;//实验室id

    private String channelNo;//新的渠道编号

    private Integer limitBuyStatus;//爆单状态 1 关闭  0开启

    /**
     * 资质图片列表
     */
    private List<Long> licenseFileIdList;

    /**
     * 实验室支持报告类型
     */
    private List<Integer> reportFormatList;

    /**
     * 使用的系统版本
     */
    private String useSystemVersion;

}
