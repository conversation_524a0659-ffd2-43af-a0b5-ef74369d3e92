package com.jdh.o2oservice.export.product.cmd;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <pre>
 *  京东耗材包管理
 * </pre>
 *
 */
@Data
public class CreateJdhMaterialPackageCmd implements Serializable {

    /**
     * <pre>
     * 耗材包名称
     * </pre>
     */
    @NotBlank(message = "耗材名称不允许为空")
    @Size(max = 10, message = "耗材名称最大10个字符")
    private String materialPackageName;

    /**
     * <pre>
     * 耗材包详细清单
     * </pre>
     */
    @NotBlank(message = "耗材物品清单不允许为空")
    private String materialPackageDetail;

    /**
     * <pre>
     * 耗材包商品id
     * </pre>
     */
    @NotNull(message = "耗材商品sku不允许为空")
    private Long skuId;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;

    /**
     * <pre>
     * erp
     * </pre>
     */
    private String erp;
}