package com.jdh.o2oservice.export.trade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/4/25 15:02
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrderDTO {

    /**
     * 三方订单号
     */
    private String partnerSourceOrderId;

    /**
     * 京东订单号
     */
    private Long orderId;

    /**
     * 履约单号
     */
    private Long promiseId;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 跳转链接
     */
    private String redirectUrl;
}
