package com.jdh.o2oservice.export.trade;

import com.jdh.o2oservice.common.result.response.Response;
import com.jdh.o2oservice.export.product.dto.InspectProjectChildDetailDTO;
import com.jdh.o2oservice.export.trade.dto.InspectionSheetButtonDTO;
import com.jdh.o2oservice.export.trade.dto.QueryMedicalPromiseDTO;
import com.jdh.o2oservice.export.trade.dto.QueryOrderDTO;
import com.jdh.o2oservice.export.trade.query.InspectionSheetButtonParam;
import com.jdh.o2oservice.export.trade.query.QueryMedicalPromiseInfoRequest;
import com.jdh.o2oservice.export.trade.query.QueryOrderInfoRequest;

import java.util.List;

public interface InspectionSheetJsfExport {
    /**
     * 查询是否可以下检验单
     */
    Response<InspectionSheetButtonDTO> queryInspectionSheetButton(InspectionSheetButtonParam param);

    List<InspectProjectChildDetailDTO> test(InspectionSheetButtonParam param);

    /**
     * 供渠道方调用的查询订单信息
     * @param request
     * @return
     */
    Response<QueryOrderDTO> queryOrderInfo(QueryOrderInfoRequest request);


    /**
     * 供渠道方调用的查询检测单信息
     * @param request
     * @return
     */
    Response<List<QueryMedicalPromiseDTO>> queryMedicalPromiseInfoBatch(QueryMedicalPromiseInfoRequest request);
}
