package com.jdh.o2oservice.export.product.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 商品售卖渠道号
 *
 * <AUTHOR>
 * @date 2024/05/02
 */
@Getter
public enum ProductSaleChannelEnum {

    /**
     * 消医
     */
    XFYL(1010645803L, "消医"),

    /**
     * 互医
     */
    NET_HP(1020410783L, "互医"),

    /**
     * 平安好医生
     */
    SAFE_GOOD_DOCTOR(7010350132L, "平安好医生"),

    /**
     * B 端
     */
    B_EXTREMITY(7029685567L, "B 端"),

    /**
     * 自费导诊
     */
    HOSPITAL_PAID_GUIDANCE(7035124123L, "自费导诊"),
    ;

    /**
     * TYPE_MAP
     */
    private static final Map<Long, ProductSaleChannelEnum> TYPE_MAP = new HashMap<>();

    /**
     * 初始化
     */
    static {
        for (ProductSaleChannelEnum value : values()) {
            TYPE_MAP.put(value.channelId, value);
        }
    }

    /**
     * @param channelId channelId
     * @param channelName channelName
     */
    ProductSaleChannelEnum(Long channelId, String channelName) {
        this.channelId = channelId;
        this.channelName = channelName;
    }

    /**
     * 渠道id
     */
    private final Long channelId;

    /**
     * 渠道名称
     */
    private final String channelName;

    /**
     *
     * @param channelId channelId
     * @return enum
     */
    public static ProductSaleChannelEnum valuesOf(Long channelId){
        if(channelId == null){
            return null;
        }
        return TYPE_MAP.get(channelId);
    }
}