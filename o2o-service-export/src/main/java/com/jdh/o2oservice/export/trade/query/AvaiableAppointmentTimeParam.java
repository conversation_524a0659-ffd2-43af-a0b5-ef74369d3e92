package com.jdh.o2oservice.export.trade.query;

import com.jdh.o2oservice.common.result.request.AbstractQuery;
import lombok.Data;

import java.util.List;

/**
 * AppointmentTimeParam 预约时间入参
 *
 * <AUTHOR>
 * @version 2024/3/7 14:44
 **/
@Data
public class AvaiableAppointmentTimeParam extends AbstractQuery {
    /**
     * sku列表
     */
    private List<Long> skuIds;
    /**
     * 地址id
     */
    private String addressId;

    /**
     * 全地址
     */
    private String fullAddress;

    private Long promiseId;//用户履约单id

    private Integer showTimeType;// 1 展示可约时间 2展示不可约时间 3或null展示全部

}
