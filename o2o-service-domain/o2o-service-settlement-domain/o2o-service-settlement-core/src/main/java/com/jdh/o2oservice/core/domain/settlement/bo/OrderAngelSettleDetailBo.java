package com.jdh.o2oservice.core.domain.settlement.bo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单收入与支出：护士结算
 * @Description
 * <AUTHOR>
 * @Date 2024/4/18 21:31
 * @Doc
 **/
@Data
public class OrderAngelSettleDetailBo implements Serializable {
    /**
     * 记收入：sku 金额
     */
    private List<OrderAngelSettleFeeBo> itemInComeAmountList = new ArrayList<>();
    /**
     * 预约单：ext 收入金额
     */
    private List<OrderAngelSettleFeeBo> feeInComeAmountList = new ArrayList<>();
    /**
     * 记支出：护士服务费金额
     */
    private List<BigDecimal> itemOutComeAmountList = new ArrayList<>();
    /**
     * 预约单支出：护士fee费用金额
     */
    private BigDecimal feeOutComeAmount;
    /**
     * 预约单支出：冲收入金额
     */
    private BigDecimal refundBackSkuAmount;
    /**
     * 预约单支出：冲收入金额
     */
    private BigDecimal refundBackFeeAmount;
    /**
     * toB账户信息
     */
    private ThirdOrderEnterpriseBo thirdOrderEnterpriseBo;

}
