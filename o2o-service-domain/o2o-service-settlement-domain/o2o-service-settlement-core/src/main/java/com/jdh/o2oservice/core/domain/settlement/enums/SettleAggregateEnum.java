package com.jdh.o2oservice.core.domain.settlement.enums;

import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;

/**
 * 业务域内聚合枚举
 * @author: liwenming
 * @date: 2024/05/10 11:13
 * @version: 1.0
 */
public enum SettleAggregateEnum implements AggregateCode {

    /**
     * 服务者
     */
    SETTLE_ANGEL(DomainEnum.SETTLE_MENT, "settleAngel"),

    SETTLE_ADJUST(DomainEnum.SETTLE_MENT, "settleAdjust"),
    ;


    /** */
    private DomainCode domain;
    /** */
    private String code;

    SettleAggregateEnum(DomainCode domain, String code) {
        this.domain = domain;
        this.code = code;
    }

    /**
     * 聚合编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return domain;
    }

    @Override
    public String getCode() {
        return code;
    }
}
