package com.jdh.o2oservice.core.domain.settlement.service.ability;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.RefundStatusRatioMapping;
import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.convert.SettlementDomainConvert;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import com.jdh.o2oservice.core.domain.support.rpc.B2bEnterpriseServiceRpc;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseAccountContractDto;
import com.jdh.o2oservice.export.support.query.JdhEnterpriseRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 退款结算-护士服务费
 *
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class InvalidThirdPromiseAngelSettleAmountAbility implements SettleAbility {

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;
    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     * b2bEnterpriseServiceRpc
     */
    @Resource
    private B2bEnterpriseServiceRpc b2bEnterpriseServiceRpc;

    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SettleAbilityCode getAbilityCode() {
        return SettleAbilityCode.INVALID_THIRD_SERVICE_AMOUNT;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(AngelServiceFinishSettlementContext context) {
        log.info("[InvalidThirdPromiseAngelSettleAmountAbility.execute] ,context={}",context);
        // 订单信息
        String serviceFeeSnapshot = findJdOrderExtContext(context);
        if(StringUtil.isBlank(serviceFeeSnapshot)){
            log.error("[InvalidThirdPromiseAngelSettleAmountAbility.execute] serviceFeeSnapshot is null,promiseId={}",context.getPromiseId());
            serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(context.getAngelId(),null,context.getPromiseId());
        }

        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);
        Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
        if(CollUtil.isEmpty(angelSkuServiceAmountMap)){
            log.info("[InvalidThirdPromiseAngelSettleAmountAbility.execute] angelSkuServiceAmountMap is null,orderId={}",context.getOrderId());
            return;
        }

        context.setOrderId(Long.valueOf(context.getSourceVoucherId()));
        context.setServiceInComeFlag(Boolean.FALSE);
        context.setFeeInComeFlag(Boolean.FALSE);

        JdOrderDetailBo jdOrderDetailBo = new JdOrderDetailBo();
        jdOrderDetailBo.setOrderId(Long.valueOf(context.getSourceVoucherId()));
        jdOrderDetailBo.setPaymentTime(context.getPaymentTime());
        context.setJdOrderDetailBo(jdOrderDetailBo);

        OrderAngelSettleDetailBo orderAngelSettleDetailBo = context.getOrderAngelSettleDetailBo();

        RefundStatusRatioMapping refundStatusRatioMapping = calcOrderRefundSettleFeeAmount(context.getFreezeStatus());
        if(Objects.isNull(refundStatusRatioMapping)){
            orderAngelSettleDetailBo.setFeeOutComeAmount(BigDecimal.ZERO);
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(BigDecimal.ZERO);
        }else{
            BigDecimal settleFeeRatio = new BigDecimal(refundStatusRatioMapping.getSettleFeeRatio());
            if(Objects.nonNull(settleFeeRatio) && settleFeeRatio.compareTo(BigDecimal.ZERO) > 0){
                BigDecimal angelFeeAmount = serverSettleAmountBo.getAngelFeeAmount();
                angelFeeAmount = angelFeeAmount.multiply(settleFeeRatio).setScale(2, RoundingMode.HALF_UP);
                if(context.getVoucherLastService()){
                    orderAngelSettleDetailBo.setFeeOutComeAmount(angelFeeAmount);
                }
            }
            BigDecimal settleServiceRatio = new BigDecimal(refundStatusRatioMapping.getSettleServiceRatio());
            if(Objects.nonNull(settleServiceRatio) && settleServiceRatio.compareTo(BigDecimal.ZERO) > 0){
                BigDecimal angelSkuServiceAmount = angelSkuServiceAmountMap.get(Long.parseLong(context.getServiceId()));
                orderAngelSettleDetailBo.getItemOutComeAmountList().add(angelSkuServiceAmount);
            }
        }

        if(context.getVoucherLastService()){
            context.setFeeInComeFlag(Boolean.TRUE);
            context.setServiceInComeFlag(Boolean.TRUE);
            this.getEnterpriseAccountContract(orderAngelSettleDetailBo,context.getPromiseId(),context.getServiceId());
        }

        context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
        context.setAngelId(serverSettleAmountBo.getAngelId());
        context.setJdhAngelInfoBo(getJdhAngelDetail(context));
        log.info("[InvalidThirdPromiseAngelSettleAmountAbility.execute] context={}",context);
    }

    /**
     *
     * @param orderAngelSettleDetailBo
     * @param promiseId
     * @return
     */
    private void getEnterpriseAccountContract(OrderAngelSettleDetailBo orderAngelSettleDetailBo,Long promiseId,String serviceId){
        JdhEnterpriseRequest jdhEnterpriseRequest = new JdhEnterpriseRequest();
        jdhEnterpriseRequest.setPromiseId(promiseId);
        jdhEnterpriseRequest.setSourceReceiptType(1);
        JdhEnterpriseAccountContractDto jdhEnterpriseAccountContractDto = b2bEnterpriseServiceRpc.queryEnterpriseAccountAndContract(jdhEnterpriseRequest);
        if(Objects.nonNull(jdhEnterpriseAccountContractDto)){
            ThirdOrderEnterpriseBo thirdOrderEnterpriseBo = SettlementDomainConvert.instance.convertToThirdOrderEnterpriseBo(jdhEnterpriseAccountContractDto);
            orderAngelSettleDetailBo.setThirdOrderEnterpriseBo(thirdOrderEnterpriseBo);
            OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
            orderAngelSettleFeeBo.setOrderId(promiseId);
            orderAngelSettleFeeBo.setItemInComeAmount(thirdOrderEnterpriseBo.getServiceAmount());
            orderAngelSettleFeeBo.setServiceId(serviceId);
            List<OrderAngelSettleFeeBo> itemInComeAmountList = new ArrayList<>();
            itemInComeAmountList.add(orderAngelSettleFeeBo);
            orderAngelSettleDetailBo.setItemInComeAmountList(itemInComeAmountList);
        }
    }

    /**
     * 计算整单退款数据
     * @param freezeStatus
     */
    private RefundStatusRatioMapping calcOrderRefundSettleFeeAmount(Integer freezeStatus){
        // 退款比例
        Map<String, RefundStatusRatioMapping> refundStatusAmoutRatioMap = duccConfig.getRefundStatusAmoutRatio();
        RefundStatusRatioMapping refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusAmoutRatioMap.get(String.valueOf(freezeStatus))), RefundStatusRatioMapping.class);
        return refundStatusRatioMapping;
    }

    /**
     *
     * @param context
     * @return
     */
    private String findJdOrderExtContext(AngelServiceFinishSettlementContext context){
        return settleOrderInfoRpc.findJdOrderExtContext(context.getPromiseId());
    }


    /**
     * 查询护士
     * @param context
     * @return
     */
    private JdhAngelInfoBo getJdhAngelDetail(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            jdhAngelInfoBo = settleOrderInfoRpc.queryJdhAngelInfo(context.getAngelId());
        }
        return jdhAngelInfoBo;
    }

}
