package com.jdh.o2oservice.core.domain.settlement.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @ClassName:服务费结算比例
 * @Description:
 * @Author: liwenming
 * @Date: 2024/5/13 10:35
 * @Vserion: 1.0
 **/
@Data
public class ServerSettleAmountBo {

    /**
     * 订单号
     */
    private Long orderId;
    /**
     * angelId
     */
    private Long angelId;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 履约单号
     */
    private Long promiseId;

//////////////////////////////////////派单展示//////////////////////////////////////////////////////////
    /**
     * 护士结算：结算总金额
     */
    private BigDecimal settleTotalAmount = BigDecimal.ZERO;
    /**
     * 服务费 + 时段费+距离费+动态调整费
     */
    private Map<String, BigDecimal> settleTypeAmountMap;

///////////////////////////////////////服务完成结算快照/////////////////////////////////////////////////////////
    /**
     * 护士结算：ext---时段费+距离费+动态调整费
     */
    private BigDecimal angelFeeAmount;

    /**
     * 护士结算：sku--服务费
     */
    private Map<Long,BigDecimal> angelSkuServiceAmountMap;
    /**
     * 派单加价费用
     */
    private BigDecimal dispatchMarkupPrice;

}
