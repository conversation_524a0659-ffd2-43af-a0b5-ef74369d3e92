package com.jdh.o2oservice.core.domain.settlement.service.ability;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.RefundStatusRatioMapping;
import com.jdh.o2oservice.core.domain.settlement.bo.JdOrderDetailBo;
import com.jdh.o2oservice.core.domain.settlement.bo.JdhAngelInfoBo;
import com.jdh.o2oservice.core.domain.settlement.bo.OrderAngelSettleDetailBo;
import com.jdh.o2oservice.core.domain.settlement.bo.ServerSettleAmountBo;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


/**
 * 退款结算-护士服务费
 *
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class RefundSettleAngelServiceAmountAbility implements SettleAbility {

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;
    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SettleAbilityCode getAbilityCode() {
        return SettleAbilityCode.REFUND_ANGEL_SERVICE_AMOUNT;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(AngelServiceFinishSettlementContext context) {
        log.info("[RefundSettleAngelServiceAmountAbility.execute] ,context={}",context);
        // 订单信息
        JdOrderDetailBo jdOrderDetailBo = getJdOrderDetailBo(context);
        String serviceFeeSnapshot = jdOrderDetailBo.getServiceFeeSnapshot();
        if(StringUtil.isBlank(serviceFeeSnapshot)){
            Long orderId = context.getOrderId();
            log.error("[RefundSettleAngelServiceAmountAbility.execute] serviceFeeSnapshot is null,orderId={}",orderId);
            if(Objects.nonNull(jdOrderDetailBo.getParentId()) && jdOrderDetailBo.getParentId() > 0){
                orderId = jdOrderDetailBo.getParentId();
            }
            serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(context.getAngelId(),orderId,context.getPromiseId());
        }
        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);
        if(Objects.isNull(serverSettleAmountBo)){
            log.info("[RefundSettleAngelServiceAmountAbility.execute] serverSettleAmountBo is null,orderId={}",context.getOrderId());
            return;
        }
        Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
        if(CollUtil.isEmpty(angelSkuServiceAmountMap)){
            log.info("[RefundSettleAngelServiceAmountAbility.execute] angelSkuServiceAmountMap is null,orderId={}",context.getOrderId());
            return;
        }
        BigDecimal angelSkuServiceAmount = angelSkuServiceAmountMap.get(Long.parseLong(context.getServiceId()));

        OrderAngelSettleDetailBo orderAngelSettleDetailBo = context.getOrderAngelSettleDetailBo();
        if(!context.getAngelSettleService()){
            log.info("[RefundSettleAngelServiceAmountAbility.execute] 护士不结算:服务费,orderId={}",context.getOrderId());
            context.setAngelSkuServiceAmount(Optional.ofNullable(angelSkuServiceAmount).orElse(BigDecimal.ZERO));
            context.setAngelFeeServiceAmount(serverSettleAmountBo.getAngelFeeAmount());
        }else{
            // 护士服务费
            if(Objects.nonNull(angelSkuServiceAmount)){
                orderAngelSettleDetailBo.getItemOutComeAmountList().add(angelSkuServiceAmount);
            }
        }

        // 护士ext费用
        if(context.getAngelSettleFee() && context.getVoucherLastService()){
            BigDecimal angelFeeAmount = serverSettleAmountBo.getAngelFeeAmount();
            BigDecimal settleFeeRatio = calcOrderRefundSettleFeeAmount(context.getFreezeStatus());
            angelFeeAmount = angelFeeAmount.multiply(settleFeeRatio).setScale(2, RoundingMode.HALF_UP);
            orderAngelSettleDetailBo.setFeeOutComeAmount(angelFeeAmount);
        }

        context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
        context.setAngelId(serverSettleAmountBo.getAngelId());
        context.setJdhAngelInfoBo(getJdhAngelDetail(context));
        log.info("[RefundSettleAngelServiceAmountAbility.execute] context={}",context);
    }

    /**
     * 计算整单退款数据
     * @param freezeStatus
     */
    private BigDecimal calcOrderRefundSettleFeeAmount(Integer freezeStatus){
        if(Objects.isNull(freezeStatus)){
            return BigDecimal.ONE;
        }
        // 退款比例
        Map<String, RefundStatusRatioMapping> refundStatusAmoutRatioMap = duccConfig.getRefundStatusAmoutRatio();
        RefundStatusRatioMapping refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusAmoutRatioMap.get(String.valueOf(freezeStatus))), RefundStatusRatioMapping.class);
        if(Objects.isNull(refundStatusRatioMapping)){
            log.error("[RefundSettleAngelServiceAmountAbility.execute],calcOrderRefundSettleFeeAmount is null");
            return BigDecimal.ZERO;
        }else{
            BigDecimal settleFeeRatio = new BigDecimal(refundStatusRatioMapping.getSettleFeeRatio());
            if(Objects.nonNull(settleFeeRatio) && settleFeeRatio.compareTo(BigDecimal.ZERO) > 0){
                return settleFeeRatio;
            }
        }
        return BigDecimal.ZERO;
    }


    /**
     *
     * @param context
     * @return
     */
    private JdOrderDetailBo getJdOrderDetailBo(AngelServiceFinishSettlementContext context){
        JdOrderDetailBo jdOrderDetailBo = context.getJdOrderDetailBo();
        if(Objects.isNull(jdOrderDetailBo)){
            jdOrderDetailBo = settleOrderInfoRpc.getSplitOrderSettleDetail(context.getOrderId(),context.getServiceId(),context.getPromiseId());
        }
        return jdOrderDetailBo;
    }


    /**
     * 查询护士
     * @param context
     * @return
     */
    private JdhAngelInfoBo getJdhAngelDetail(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            jdhAngelInfoBo = settleOrderInfoRpc.queryJdhAngelInfo(context.getAngelId());
        }
        return jdhAngelInfoBo;
    }

}
