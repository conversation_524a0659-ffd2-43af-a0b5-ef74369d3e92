package com.jdh.o2oservice.core.domain.settlement.context;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: duanqiaona1
 * @Date: 2024/5/14 9:30 上午
 * @Description:
 */
@Data
public class AngelSettlementQueryContext extends AbstractPageQuery implements Serializable {
    private static final long serialVersionUID = -2406981332933100852L;

    /**
     * orderId
     */
    private Long orderId;
    /**
     * 服务者id
     */
    private Long angelId;
    /**
     * pin
     */
    private String userPin;

    /**
     * 结算id
     */
    private Long settleId;

    /**
     * 结算业务id
     */
    private String settlementBusinessId;
    /**
     * 结算类型 1 收入2 支出
     */
    private Integer settlementType;
    /**
     * 人员标签 0兼职 1全职
     */
    private Integer jobNature;
    /**
     * 结算状态0 初始化 1 冻结中 2 已结算
     */
    private Integer settleStatus;

    /**
     * 提现状态 1提现中 2提现成功 3提现失败
     */
    private Integer cashStatus;
    /**
     * 创建时间开始
     */
    private Date createTimeStart;


    /**
     * 创建时间结尾
     */
    private Date createTimeEnd;


    /**
     * 结算时间开始
     */
    private Date settleTimeStart;


    /**
     * 结算时间结束
     */
    private Date settleTimeEnd;

    /**
     * 提现流水号
     */
    private Long settlementNo;
    /**
     * 预计结算时间
     */
    private Date expectSettleTime;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 结算状态查询list 用于聚合明细
     */
    private List<Integer> settleStatusList;
    /**
     * 费项枚举 1上门检测服务 2上门护理服务 3调整项 4 激励 5 其他费项
     */
    private List<Integer> itemTypeList;
    /**
     * angelIdList
     */
    private List<Long> angelIdList;

    /**
     * 收入类型 1-上门检测服务 2-上门护理服务 3-调整项(保底) 4-激励 5-其他费项 6-手工调账
     */
    private Integer itemType;

    /**
     * 可提现时间开始
     */
    private Date cashTimeStart;

    /**
     * 可提现时间结尾
     */
    private Date cashTimeEnd;

    /**
     * 收入来源id
     */
    private Long itemSourceId;
}
