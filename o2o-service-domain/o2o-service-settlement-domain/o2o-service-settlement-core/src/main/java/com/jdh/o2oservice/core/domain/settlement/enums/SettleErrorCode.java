package com.jdh.o2oservice.core.domain.settlement.enums;

import com.jdh.o2oservice.base.exception.errorcode.AbstractErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.ToString;

/**
 * 订单域错误码
 *
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2024/01/23 12:11 下午
 * @version: 1.0
 */
@ToString
public enum SettleErrorCode implements AbstractErrorCode {

    /**
     * 交易 结算
     */
    CITY_ID_NULL(DomainEnum.TRADE, "35000", "上门地址城市id为空"),
    CITY_INFO_NULL(DomainEnum.TRADE, "35001", "城市未配置，不能估算结算价"),
    ANGEL_ID_NULL(DomainEnum.TRADE, "35002", "护士id为空"),
    ANGEL_INFO_NULL(DomainEnum.TRADE, "35003", "查不到护士信息"),
    ANGEL_PROFESSION_INFO_NULL(DomainEnum.TRADE, "35004", "护士职级为空"),
    ANGEL_PROFESSION_AMOUNT_NULL(DomainEnum.TRADE, "35005", "护士职级对应时薪未维护，请运营维护"),
    ORDER_ITEM_PROFESSION_AMOUNT_NULL(DomainEnum.TRADE, "35006", "护士职级对应时薪未维护，请运营维护"),
    ORDER_STATUS_NO_SAME(DomainEnum.TRADE, "35007", "订单结算，履约单未完成"),
    ORDER_SETTLE_NOT_VERTICAL_CODE(DomainEnum.TRADE, "35008", "结算场景不支持"),
    ANGEL_STATION_NULL(DomainEnum.TRADE, "35009", "护士站不存在"),
    ORDER_CALC_SERVICE_FEE_USER_ADDRESS_MISS(DomainEnum.TRADE, "35010", "计算服务费项信息，用户地址信息缺失"),
    ANGEL_JOBNATURE_INFO_NULL(DomainEnum.TRADE, "35011", "护士人员标签为空"),

    ANGEL_SUBMIT_CASH_REPEAT(DomainEnum.TRADE, "35012", "操作太频繁了，请稍后再试"),
    ANGEL_SUBMIT_CASH_NOT_EXISTS(DomainEnum.TRADE, "35013", "提现记录不存在，请确认"),
    ANGEL_SUBMIT_CASH_FAILED(DomainEnum.TRADE, "35014", "提现失败"),
    ANGEL_SUBMIT_CASH_NOT(DomainEnum.TRADE, "35015", "非提现失败状态，不允许重新提现"),
    ANGEL_SUBMIT_CASH_RESULT_UPDATE_FAILED(DomainEnum.TRADE, "35016", "护士提现结果更新失败"),
    QUERY_ANGEL_SETTLE_FAILED(DomainEnum.TRADE, "35017", "查询护士账户失败"),
    CHECK_ANGEL_NAME_FAILED(DomainEnum.TRADE, "35018", "护士姓名已变更，重新检验"),
    ANGEL_ASJUST_DATA_OUT(DomainEnum.TRADE, "35019", "护士调账数据一次不超过99条"),
    ANGEL_ASJUST_FILE_UPLOAD_ERROR(DomainEnum.TRADE, "35020", "护士调账文件上传失败，请重试"),

    EXPORT_SETTLE_DATA_ZERO(DomainEnum.TRADE, "35021", "导出无符合数据，请重新选择导出条件"),
    EXPORT_SETTLE_DATA_OUT(DomainEnum.TRADE, "35022", "导出数据一次最多10000条,请重新选择导出条件"),

    CASH_DATE_T_ADD_TWO(DomainEnum.TRADE, "35023", "提现日期T+2之后,请重新填写提现日期"),
    ADJUST_AMOUNT_OUT(DomainEnum.TRADE, "35024", "调账金额需要在-99999.99至99999.99之间"),
    ADJUST_AMOUNT_IS_NULL(DomainEnum.TRADE, "35025", "调账金额不能为空"),

    ;


    /**
     * ProviderErrorCode
     *
     * @param domainEnum  域枚举
     * @param code        代码
     * @param description 描述
     */
    SettleErrorCode(DomainEnum domainEnum, String code, String description) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
    }

    /**
     *
     */
    private DomainEnum domainEnum;
    /**
     *
     */
    private String code;
    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * 获取代码
     *
     * @return {@link String}
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 获取描述
     *
     * @return {@link String}
     */
    @Override
    public String getDescription() {
        return description;
    }

}
