package com.jdh.o2oservice.core.domain.settlement.service.ability;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.convert.SettlementDomainConvert;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import com.jdh.o2oservice.core.domain.support.rpc.B2bEnterpriseServiceRpc;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseAccountContractDto;
import com.jdh.o2oservice.export.support.query.JdhEnterpriseRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 第三方订单收入结算
 *
 * @author: liwenming
 * @date: 2025/3/7 9:54 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class ThirdOrderSettleInComeAmountAbility implements SettleAbility {

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;
    /**
     * b2bEnterpriseServiceRpc
     */
    @Resource
    private B2bEnterpriseServiceRpc b2bEnterpriseServiceRpc;

    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SettleAbilityCode getAbilityCode() {
        return SettleAbilityCode.THIRD_ORDER_INCOME_AMOUNT;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(AngelServiceFinishSettlementContext context) {
        log.info("[ThirdOrderSettleInComeAmountAbility.execute] ,context={}",context);
        if(context.getAngelSettleBack()){
            log.info("[ThirdOrderSettleInComeAmountAbility.execute] 冲收入,settlementBusinessId={}",context.getSettlementBusinessId());
            return;
        }
        String serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(context.getAngelId(),null,context.getPromiseId());
        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);
        Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
        if(CollUtil.isEmpty(angelSkuServiceAmountMap)){
            log.info("[ThirdOrderSettleInComeAmountAbility.execute] angelSkuServiceAmountMap is null,promiseId={}",context.getPromiseId());
        }

        context.setOrderLastService(Boolean.FALSE);
        context.setFeeInComeFlag(Boolean.FALSE);
        context.setOrderId(context.getPromiseId());
        JdOrderDetailBo jdOrderDetailBo = new JdOrderDetailBo();
        jdOrderDetailBo.setOrderId(context.getPromiseId());
        jdOrderDetailBo.setPaymentTime(context.getPaymentTime());
        context.setJdOrderDetailBo(jdOrderDetailBo);
        BigDecimal angelSkuServiceAmount = angelSkuServiceAmountMap.get(Long.parseLong(context.getServiceId()));
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = Objects.nonNull(context.getOrderAngelSettleDetailBo()) ?
                context.getOrderAngelSettleDetailBo() : new OrderAngelSettleDetailBo();
        orderAngelSettleDetailBo.getItemOutComeAmountList().add(angelSkuServiceAmount);
        if(context.getVoucherLastService()){
            orderAngelSettleDetailBo.setFeeOutComeAmount(serverSettleAmountBo.getAngelFeeAmount());
            this.getEnterpriseAccountContract(orderAngelSettleDetailBo,context.getPromiseId(),context.getServiceId());
        }
        context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
        context.setAngelId(serverSettleAmountBo.getAngelId());
        context.setJdhAngelInfoBo(getJdhAngelDetail(context));
    }

    /**
     *
     * @param orderAngelSettleDetailBo
     * @param promiseId
     * @return
     */
    private void getEnterpriseAccountContract(OrderAngelSettleDetailBo orderAngelSettleDetailBo,Long promiseId,String serviceId){
        JdhEnterpriseRequest jdhEnterpriseRequest = new JdhEnterpriseRequest();
        jdhEnterpriseRequest.setPromiseId(promiseId);
        jdhEnterpriseRequest.setSourceReceiptType(1);
        jdhEnterpriseRequest.setFreezeType(1);
        JdhEnterpriseAccountContractDto jdhEnterpriseAccountContractDto = b2bEnterpriseServiceRpc.queryEnterpriseAccountAndContract(jdhEnterpriseRequest);
        if(Objects.nonNull(jdhEnterpriseAccountContractDto)){
            ThirdOrderEnterpriseBo thirdOrderEnterpriseBo = SettlementDomainConvert.instance.convertToThirdOrderEnterpriseBo(jdhEnterpriseAccountContractDto);
            orderAngelSettleDetailBo.setThirdOrderEnterpriseBo(thirdOrderEnterpriseBo);
            OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
            orderAngelSettleFeeBo.setOrderId(promiseId);
            orderAngelSettleFeeBo.setItemInComeAmount(thirdOrderEnterpriseBo.getServiceAmount());
            orderAngelSettleFeeBo.setServiceId(serviceId);
            List<OrderAngelSettleFeeBo> itemInComeAmountList = new ArrayList<>();
            itemInComeAmountList.add(orderAngelSettleFeeBo);
            orderAngelSettleDetailBo.setItemInComeAmountList(itemInComeAmountList);
        }
    }

    /**
     *
     * @param context
     * @return
     */
    private String findJdOrderExtContext(AngelServiceFinishSettlementContext context){
        return settleOrderInfoRpc.findJdOrderExtContext(context.getPromiseId());
    }

    /**
     * 查询护士
     * @param context
     * @return
     */
    private JdhAngelInfoBo getJdhAngelDetail(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            jdhAngelInfoBo = settleOrderInfoRpc.queryJdhAngelInfo(context.getAngelId());
        }
        return jdhAngelInfoBo;
    }

}
