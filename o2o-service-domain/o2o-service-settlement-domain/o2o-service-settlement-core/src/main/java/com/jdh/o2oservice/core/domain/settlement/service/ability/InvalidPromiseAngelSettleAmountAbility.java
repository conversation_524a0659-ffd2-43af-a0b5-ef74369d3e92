package com.jdh.o2oservice.core.domain.settlement.service.ability;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.RefundStatusRatioMapping;
import com.jdh.o2oservice.core.domain.settlement.bo.JdOrderDetailBo;
import com.jdh.o2oservice.core.domain.settlement.bo.JdhAngelInfoBo;
import com.jdh.o2oservice.core.domain.settlement.bo.OrderAngelSettleDetailBo;
import com.jdh.o2oservice.core.domain.settlement.bo.ServerSettleAmountBo;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.Objects;


/**
 * 退款结算-护士服务费
 *
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class InvalidPromiseAngelSettleAmountAbility implements SettleAbility {

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;
    /**
     * duccConfig
     */
    @Resource
    private DuccConfig duccConfig;
    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SettleAbilityCode getAbilityCode() {
        return SettleAbilityCode.INVALID_ANGEL_SERVICE_AMOUNT;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(AngelServiceFinishSettlementContext context) {
        log.info("[InvalidPromiseAngelSettleAmountAbility.execute] ,context={}",context);
        // 订单信息
        String serviceFeeSnapshot = findJdOrderExtContext(context);
        if(StringUtil.isBlank(serviceFeeSnapshot)){
            log.error("[InvalidPromiseAngelSettleAmountAbility.execute] serviceFeeSnapshot is null,promiseId={}",context.getPromiseId());
            serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(context.getAngelId(),null,context.getPromiseId());
        }

        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);
        Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
        if(CollUtil.isEmpty(angelSkuServiceAmountMap)){
            log.info("[InvalidPromiseAngelSettleAmountAbility.execute] angelSkuServiceAmountMap is null,orderId={}",context.getOrderId());
            return;
        }
        context.setOrderLastService(Boolean.FALSE);
        context.setServiceInComeFlag(Boolean.FALSE);
        context.setFeeInComeFlag(Boolean.FALSE);
        context.setVoucherLastService(Boolean.TRUE);
        context.setOrderId(Long.valueOf(context.getSourceVoucherId()));

        JdOrderDetailBo jdOrderDetailBo = new JdOrderDetailBo();
        jdOrderDetailBo.setOrderId(Long.valueOf(context.getSourceVoucherId()));
        jdOrderDetailBo.setPaymentTime(context.getPaymentTime());
        context.setJdOrderDetailBo(jdOrderDetailBo);

        OrderAngelSettleDetailBo orderAngelSettleDetailBo = context.getOrderAngelSettleDetailBo();

        RefundStatusRatioMapping refundStatusRatioMapping = calcOrderRefundSettleFeeAmount(context.getFreezeStatus());
        if(Objects.isNull(refundStatusRatioMapping)){
            orderAngelSettleDetailBo.setFeeOutComeAmount(BigDecimal.ZERO);
            orderAngelSettleDetailBo.getItemOutComeAmountList().add(BigDecimal.ZERO);
        }else{
            BigDecimal settleFeeRatio = new BigDecimal(refundStatusRatioMapping.getSettleFeeRatio());
            if(Objects.nonNull(settleFeeRatio) && settleFeeRatio.compareTo(BigDecimal.ZERO) > 0){
                BigDecimal angelFeeAmount = serverSettleAmountBo.getAngelFeeAmount();
                angelFeeAmount = angelFeeAmount.multiply(settleFeeRatio).setScale(2, RoundingMode.HALF_UP);
                orderAngelSettleDetailBo.setFeeOutComeAmount(angelFeeAmount);
            }
            BigDecimal settleServiceRatio = new BigDecimal(refundStatusRatioMapping.getSettleServiceRatio());
            if(Objects.nonNull(settleServiceRatio) && settleServiceRatio.compareTo(BigDecimal.ZERO) > 0){
                BigDecimal angelSkuServiceAmount = angelSkuServiceAmountMap.get(Long.parseLong(context.getServiceId()));
                orderAngelSettleDetailBo.getItemOutComeAmountList().add(angelSkuServiceAmount);
            }
        }
        context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
        context.setAngelId(serverSettleAmountBo.getAngelId());
        context.setJdhAngelInfoBo(getJdhAngelDetail(context));
        log.info("[InvalidPromiseAngelSettleAmountAbility.execute] context={}",context);
    }

    /**
     * 计算整单退款数据
     * @param freezeStatus
     */
    private RefundStatusRatioMapping calcOrderRefundSettleFeeAmount(Integer freezeStatus){
        // 退款比例
        Map<String, RefundStatusRatioMapping> refundStatusAmoutRatioMap = duccConfig.getRefundStatusAmoutRatio();
        RefundStatusRatioMapping refundStatusRatioMapping = JSONUtil.toBean(
                JSONUtil.toJsonStr(refundStatusAmoutRatioMap.get(String.valueOf(freezeStatus))), RefundStatusRatioMapping.class);
        return refundStatusRatioMapping;
    }

    /**
     *
     * @param context
     * @return
     */
    private String findJdOrderExtContext(AngelServiceFinishSettlementContext context){
        return settleOrderInfoRpc.findJdOrderExtContext(context.getPromiseId());
    }


    /**
     * 查询护士
     * @param context
     * @return
     */
    private JdhAngelInfoBo getJdhAngelDetail(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            jdhAngelInfoBo = settleOrderInfoRpc.queryJdhAngelInfo(context.getAngelId());
        }
        return jdhAngelInfoBo;
    }

}
