package com.jdh.o2oservice.core.domain.settlement.convert;


import com.jdh.o2oservice.core.domain.settlement.bo.ThirdOrderEnterpriseBo;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleItemTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleStatusEnum;
import com.jdh.o2oservice.core.domain.settlement.enums.SettleTypeEnum;
import com.jdh.o2oservice.core.domain.settlement.model.*;
import com.jdh.o2oservice.export.support.dto.JdhEnterpriseAccountContractDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

/**
 * 结算转换类工具
 */
@Mapper
public interface SettlementDomainConvert {
    SettlementDomainConvert instance = Mappers.getMapper(SettlementDomainConvert.class);

    /**
     * 转换成mq消息对象
     *
     * @param jdhSettlementEbs
     * @return
     */
    @Mapping(source = "businessLine", target = "bussinessLine")
    @Mapping(source = "firstCategoryCode", target = "categoryCode")
    @Mapping(source = "newOrgId", target = "nwOrgId")
    @Mapping(target = "docCreateTime", source = "docCreateTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "appliedDate", source = "appliedDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    JdEbsMessageInfo modelToMqMessage(JdhSettlementEbs jdhSettlementEbs);

    /**
     * 护士服务费收入
     * @param context
     * @return
     */
    @Named("packAngelServiceSettlement")
    default AngelSettlement packAngelServiceSettlement(AngelSettlementAndEbsDetail context, BigDecimal settleAmount, Integer itemType) {
        AngelSettlement angelSettlement = new AngelSettlement();
        angelSettlement.setOrderId(context.getOrderId());
        angelSettlement.setAngelId(context.getAngelId());
        angelSettlement.setJobNature(context.getJdhAngelInfoBo() != null ? context.getJdhAngelInfoBo().getJobNature() : context.getJobNature());
        angelSettlement.setSettlementBusinessId(context.getSettlementBusinessId());
        angelSettlement.setPromiseId(context.getPromiseId());
        angelSettlement.setPatientId(context.getPromisePatientId());
        angelSettlement.setSkuId(Long.parseLong(context.getServiceId()));
        angelSettlement.setSettlementType(SettleTypeEnum.INCOME.getType());
        angelSettlement.setItemType(itemType);
        angelSettlement.setSettleAmount(settleAmount);
        angelSettlement.setSettleTime(context.getSettleTime());
        angelSettlement.setExpectSettleTime(context.getExpectSettleTime());
        angelSettlement.setSettleStatus(SettleStatusEnum.INIT.getType());
        return angelSettlement;
    }

    /**
     * 护士收入明细
     * @param settleId
     * @param itemType
     * @param settleAmount
     * @return
     */
    @Named("packAngelSettlementDetail")
    default AngelSettlementDetail packAngelSettlementDetail(Long settleId,Integer itemType,BigDecimal settleAmount) {
        AngelSettlementDetail angelSettlementDetail = new AngelSettlementDetail();
        angelSettlementDetail.setSettleId(settleId);
        angelSettlementDetail.setFeeName(SettleItemTypeEnum.getSettleTypeEnumByType(itemType).getDesc());
        angelSettlementDetail.setSettleAmount(settleAmount);
        return angelSettlementDetail;
    }

    /**
     *
     * @param jdhEnterpriseAccountContractDto
     * @return
     */
    ThirdOrderEnterpriseBo convertToThirdOrderEnterpriseBo(JdhEnterpriseAccountContractDto jdhEnterpriseAccountContractDto);

}
