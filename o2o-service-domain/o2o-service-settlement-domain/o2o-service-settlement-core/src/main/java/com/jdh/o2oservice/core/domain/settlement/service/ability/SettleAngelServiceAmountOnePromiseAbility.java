package com.jdh.o2oservice.core.domain.settlement.service.ability;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhVoucherStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 结算-护士服务费
 *
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class SettleAngelServiceAmountOnePromiseAbility implements SettleAbility {

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;


    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SettleAbilityCode getAbilityCode() {
        return SettleAbilityCode.PROMISE_ANGEL_SERVICE_AMOUNT;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(AngelServiceFinishSettlementContext context) {
        log.info("[SettleAngelServiceAmountAbility.execute] ,context={}",context);
        if(context.getAngelSettleBack()){
            log.info("[SettleAngelServiceAmountAbility.execute] 冲收入,settlementBusinessId={}",context.getSettlementBusinessId());
            return;
        }
        // 订单信息
        JdOrderDetailBo jdOrderDetailBo = getJdOrderDetailBo(context);
        String serviceFeeSnapshot = jdOrderDetailBo.getServiceFeeSnapshot();
        Long orderId = context.getOrderId();
        if(StringUtil.isBlank(serviceFeeSnapshot)){
            log.error("[SettleAngelServiceAmountAbility.execute] serviceFeeSnapshot is null,orderId={}",orderId);
            if(Objects.nonNull(jdOrderDetailBo.getParentId()) && jdOrderDetailBo.getParentId() > 0){
                orderId = jdOrderDetailBo.getParentId();
                context.setParentId(orderId);
            }
            serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(context.getAngelId(),orderId,context.getPromiseId());
        }
        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);
        Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
        if(CollUtil.isEmpty(angelSkuServiceAmountMap)){
            log.info("[SettleAngelServiceAmountAbility.execute] angelSkuServiceAmountMap is null,orderId={}",context.getOrderId());
        }
        context.setVoucherLastService(Boolean.TRUE);
        BigDecimal angelSkuServiceAmount = angelSkuServiceAmountMap.get(Long.parseLong(context.getServiceId()));
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = bulidOrderAngelSettleDetail(context,jdOrderDetailBo,serverSettleAmountBo.getAngelFeeAmount(),orderId);
        orderAngelSettleDetailBo.getItemOutComeAmountList().add(angelSkuServiceAmount);
        context.setOrderId(jdOrderDetailBo.getOrderId());
        context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
        context.setAngelId(serverSettleAmountBo.getAngelId());
        context.setJdhAngelInfoBo(getJdhAngelDetail(context));
    }

    /**
     *
     * @param context
     * @param jdOrderDetailBo
     * @return
     */
    private OrderAngelSettleDetailBo bulidOrderAngelSettleDetail(AngelServiceFinishSettlementContext context,JdOrderDetailBo jdOrderDetailBo,
                                                                 BigDecimal angelFeeAmount,Long orderId){
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = Objects.nonNull(context.getOrderAngelSettleDetailBo()) ?
                context.getOrderAngelSettleDetailBo() : new OrderAngelSettleDetailBo();
        VoucherBo voucherBo = settleOrderInfoRpc.getVoucherDto(context.getVoucherId());
        if(!(Objects.nonNull(voucherBo) && JdhVoucherStatusEnum.EXPIRED.getStatus().equals(voucherBo.getStatus()))){
            List<JdOrderItemBo> jdOrderItemList = jdOrderDetailBo.getJdOrderItemList();
            if(CollUtil.isNotEmpty(jdOrderItemList)){
                JdOrderItemBo jdOrderItemBo = jdOrderItemList.get(0);
                BigDecimal itemInComeAmount = this.calcSkuRefundAmount(context.getVoucherLastService(),jdOrderDetailBo,jdOrderItemBo);
                OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
                orderAngelSettleFeeBo.setOrderId(jdOrderItemBo.getOrderId());
                orderAngelSettleFeeBo.setServiceId(String.valueOf(jdOrderItemBo.getSkuId()));
                orderAngelSettleFeeBo.setItemInComeAmount(itemInComeAmount);
                List<OrderAngelSettleFeeBo> itemInComeAmountList = new ArrayList<>();
                itemInComeAmountList.add(orderAngelSettleFeeBo);
                orderAngelSettleDetailBo.setItemInComeAmountList(itemInComeAmountList);
            }
        }

        // 工单完成，费项支出
        if(context.getVoucherLastService()){
            List<JdOrderMoneyBo> jdOrderMoneyList = settleOrderInfoRpc.getJdOrderMoneyList(orderId);
            orderAngelSettleDetailBo.setFeeInComeAmountList(bulidOrderAngelSettleFeeBoList(jdOrderMoneyList));
            orderAngelSettleDetailBo.setFeeOutComeAmount(angelFeeAmount);
        }
        return orderAngelSettleDetailBo;
    }

    /**
     *
     * @param jdOrderDetailBo
     * @param jdOrderItemBo
     * @return
     */
    private BigDecimal calcSkuRefundAmount(Boolean voucherLastService,JdOrderDetailBo jdOrderDetailBo,JdOrderItemBo jdOrderItemBo){
        // 订单实付款
        BigDecimal orderRefundAmount = jdOrderDetailBo.getOrderAmount();
        if(Objects.isNull(orderRefundAmount) || orderRefundAmount.compareTo(BigDecimal.ZERO) <= 0){
            return BigDecimal.ZERO;
        }
        // ext金额
        BigDecimal feeAmount = bulidOrderAngelSettleFeeAmount(jdOrderDetailBo.getJdOrderMoneyList(),jdOrderDetailBo.getOrderId());
        // sku金额
        BigDecimal orderSkuTotalAmount = orderRefundAmount.subtract(feeAmount);
        // 单个sku金额
        BigDecimal orderSkuAvgAmount = orderSkuTotalAmount.divide(BigDecimal.valueOf(jdOrderItemBo.getSkuNum()),2,BigDecimal.ROUND_DOWN);
        // 最后一笔
        if(voucherLastService){
            BigDecimal lastSkuAmount = orderSkuTotalAmount.subtract(orderSkuAvgAmount.multiply(BigDecimal.valueOf(jdOrderItemBo.getSkuNum() - 1)));
            return lastSkuAmount;
        }
        return orderSkuAvgAmount;
    }

    /**
     *
     * @param context
     * @return
     */
    private JdOrderDetailBo getJdOrderDetailBo(AngelServiceFinishSettlementContext context){
        JdOrderDetailBo jdOrderDetailBo = context.getJdOrderDetailBo();
        if(Objects.isNull(jdOrderDetailBo)){
            jdOrderDetailBo = settleOrderInfoRpc.getSplitOrderSettleDetail(context.getOrderId(),context.getServiceId(),context.getPromiseId());
        }
        return jdOrderDetailBo;
    }

    /**
     *
     * @param jdOrderMoneyList
     * @return
     */
    private List<OrderAngelSettleFeeBo> bulidOrderAngelSettleFeeBoList(List<JdOrderMoneyBo> jdOrderMoneyList){
        Integer feeMoneyType = 507;
        List<OrderAngelSettleFeeBo> orderAngelSettleFeeBoList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
            for(JdOrderMoneyBo jdOrderMoney : jdOrderMoneyList){
                if(feeMoneyType.equals(jdOrderMoney.getMoneyType())){
                    OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
                    orderAngelSettleFeeBo.setOrderId(jdOrderMoney.getOrderId());
                    orderAngelSettleFeeBo.setFeeInComeAmount(jdOrderMoney.getAmount());
                    orderAngelSettleFeeBoList.add(orderAngelSettleFeeBo);
                }
            }
        }
        return orderAngelSettleFeeBoList;
    }

    /**
     *
     * @param jdOrderMoneyList
     * @return
     */
    private BigDecimal bulidOrderAngelSettleFeeAmount(List<JdOrderMoneyBo> jdOrderMoneyList,Long orderId){
        Integer feeMoneyType = 507;
        BigDecimal feeInComeAmount = BigDecimal.ZERO;
        if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
            for(JdOrderMoneyBo jdOrderMoney : jdOrderMoneyList){
                if(orderId.equals(jdOrderMoney.getOrderId())){
                    if(feeMoneyType.equals(jdOrderMoney.getMoneyType())){
                        feeInComeAmount = feeInComeAmount.add(jdOrderMoney.getAmount());
                    }
                }

            }
        }
        return feeInComeAmount;
    }

    /**
     * 查询护士
     * @param context
     * @return
     */
    private JdhAngelInfoBo getJdhAngelDetail(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            jdhAngelInfoBo = settleOrderInfoRpc.queryJdhAngelInfo(context.getAngelId());
        }
        return jdhAngelInfoBo;
    }

}
