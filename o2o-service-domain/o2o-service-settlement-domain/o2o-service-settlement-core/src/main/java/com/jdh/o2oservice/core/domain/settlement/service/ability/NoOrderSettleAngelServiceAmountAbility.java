package com.jdh.o2oservice.core.domain.settlement.service.ability;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import com.jdh.o2oservice.core.domain.settlement.bo.*;
import com.jdh.o2oservice.core.domain.settlement.context.AngelServiceFinishSettlementContext;
import com.jdh.o2oservice.core.domain.settlement.rpc.SettleOrderInfoRpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 结算-护士服务费
 *
 * @author: liwenming
 * @date: 2024/5/17 9:54 上午
 * @version: 1.0
 */
@Component
@Slf4j
public class NoOrderSettleAngelServiceAmountAbility implements SettleAbility {

    /**
     * settleOrderInfoRpc
     */
    @Resource
    private SettleOrderInfoRpc settleOrderInfoRpc;


    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @Override
    public SettleAbilityCode getAbilityCode() {
        return SettleAbilityCode.NO_ORDER_ANGEL_SERVICE_AMOUNT;
    }

    /**
     * 处理
     *
     * @param context
     */
    @Override
    public void execute(AngelServiceFinishSettlementContext context) {
        log.info("[NoOrderSettleAngelServiceAmountAbility.execute] ,context={}",context);
        if(context.getAngelSettleBack()){
            log.info("[NoOrderNoOrderSettleAngelServiceAmountAbility.execute] 冲收入,settlementBusinessId={}",context.getSettlementBusinessId());
            return;
        }
        // 订单信息
        String serviceFeeSnapshot = findJdOrderExtContext(context);
        if(StringUtil.isBlank(serviceFeeSnapshot)){
            log.error("[NoOrderNoOrderSettleAngelServiceAmountAbility.execute] serviceFeeSnapshot is null,promiseId={}",context.getPromiseId());
            serviceFeeSnapshot = settleOrderInfoRpc.getOrderSettleSnapshotAmount(context.getAngelId(),null,context.getPromiseId());
        }
        ServerSettleAmountBo serverSettleAmountBo = JSONObject.parseObject(serviceFeeSnapshot,ServerSettleAmountBo.class);
        Map<Long,BigDecimal> angelSkuServiceAmountMap = serverSettleAmountBo.getAngelSkuServiceAmountMap();
        if(CollUtil.isEmpty(angelSkuServiceAmountMap)){
            log.info("[NoOrderSettleAngelServiceAmountAbility.execute] angelSkuServiceAmountMap is null,promiseId={}",context.getPromiseId());
        }

        context.setOrderLastService(Boolean.FALSE);
//        context.setVoucherLastService(Boolean.FALSE);
        context.setServiceInComeFlag(Boolean.FALSE);
        context.setFeeInComeFlag(Boolean.FALSE);
        context.setOrderId(Long.valueOf(context.getSourceVoucherId()));
        JdOrderDetailBo jdOrderDetailBo = new JdOrderDetailBo();
        jdOrderDetailBo.setOrderId(Long.valueOf(context.getSourceVoucherId()));
        jdOrderDetailBo.setPaymentTime(context.getPaymentTime());
        context.setJdOrderDetailBo(jdOrderDetailBo);
        BigDecimal angelSkuServiceAmount = angelSkuServiceAmountMap.get(Long.parseLong(context.getServiceId()));
        OrderAngelSettleDetailBo orderAngelSettleDetailBo = Objects.nonNull(context.getOrderAngelSettleDetailBo()) ?
                context.getOrderAngelSettleDetailBo() : new OrderAngelSettleDetailBo();
        orderAngelSettleDetailBo.getItemOutComeAmountList().add(angelSkuServiceAmount);
        orderAngelSettleDetailBo.setFeeOutComeAmount(serverSettleAmountBo.getAngelFeeAmount());

        context.setOrderAngelSettleDetailBo(orderAngelSettleDetailBo);
        context.setAngelId(serverSettleAmountBo.getAngelId());
        context.setJdhAngelInfoBo(getJdhAngelDetail(context));
    }

    /**
     *
     * @param jdOrderDetailBo
     * @param jdOrderItemBo
     * @return
     */
    private BigDecimal calcSkuRefundAmount(Boolean voucherLastService,JdOrderDetailBo jdOrderDetailBo,JdOrderItemBo jdOrderItemBo){
        // 订单实付款
        BigDecimal orderRefundAmount = jdOrderDetailBo.getOrderAmount();
        if(Objects.isNull(orderRefundAmount) || orderRefundAmount.compareTo(BigDecimal.ZERO) <= 0){
            return BigDecimal.ZERO;
        }
        // ext金额
        BigDecimal feeAmount = bulidOrderAngelSettleFeeAmount(jdOrderDetailBo.getJdOrderMoneyList(),jdOrderDetailBo.getOrderId());
        // sku金额
        BigDecimal orderSkuTotalAmount = orderRefundAmount.subtract(feeAmount);
        // 单个sku金额
        BigDecimal orderSkuAvgAmount = orderSkuTotalAmount.divide(BigDecimal.valueOf(jdOrderItemBo.getSkuNum()),2,BigDecimal.ROUND_DOWN);
        // 最后一笔
        if(voucherLastService){
            BigDecimal lastSkuAmount = orderSkuTotalAmount.subtract(orderSkuAvgAmount.multiply(BigDecimal.valueOf(jdOrderItemBo.getSkuNum() - 1)));
            return lastSkuAmount;
        }
        return orderSkuAvgAmount;
    }

    /**
     *
     * @param context
     * @return
     */
    private String findJdOrderExtContext(AngelServiceFinishSettlementContext context){
        return settleOrderInfoRpc.findJdOrderExtContext(context.getPromiseId());
    }

    /**
     *
     * @param jdOrderMoneyList
     * @return
     */
    private List<OrderAngelSettleFeeBo> bulidOrderAngelSettleFeeBoList(List<JdOrderMoneyBo> jdOrderMoneyList){
        Integer feeMoneyType = 507;
        List<OrderAngelSettleFeeBo> orderAngelSettleFeeBoList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
            for(JdOrderMoneyBo jdOrderMoney : jdOrderMoneyList){
                if(feeMoneyType.equals(jdOrderMoney.getMoneyType())){
                    OrderAngelSettleFeeBo orderAngelSettleFeeBo = new OrderAngelSettleFeeBo();
                    orderAngelSettleFeeBo.setOrderId(jdOrderMoney.getOrderId());
                    orderAngelSettleFeeBo.setFeeInComeAmount(jdOrderMoney.getAmount());
                    orderAngelSettleFeeBoList.add(orderAngelSettleFeeBo);
                }
            }
        }
        return orderAngelSettleFeeBoList;
    }

    /**
     *
     * @param jdOrderMoneyList
     * @return
     */
    private BigDecimal bulidOrderAngelSettleFeeAmount(List<JdOrderMoneyBo> jdOrderMoneyList,Long orderId){
        Integer feeMoneyType = 507;
        BigDecimal feeInComeAmount = BigDecimal.ZERO;
        if(CollectionUtil.isNotEmpty(jdOrderMoneyList)){
            for(JdOrderMoneyBo jdOrderMoney : jdOrderMoneyList){
                if(orderId.equals(jdOrderMoney.getOrderId())){
                    if(feeMoneyType.equals(jdOrderMoney.getMoneyType())){
                        feeInComeAmount = feeInComeAmount.add(jdOrderMoney.getAmount());
                    }
                }

            }
        }
        return feeInComeAmount;
    }

    /**
     * 查询护士
     * @param context
     * @return
     */
    private JdhAngelInfoBo getJdhAngelDetail(AngelServiceFinishSettlementContext context){
        JdhAngelInfoBo jdhAngelInfoBo = context.getJdhAngelInfoBo();
        if(Objects.isNull(jdhAngelInfoBo)){
            jdhAngelInfoBo = settleOrderInfoRpc.queryJdhAngelInfo(context.getAngelId());
        }
        return jdhAngelInfoBo;
    }

}
