package com.jdh.o2oservice.core.domain.product.model;

import lombok.*;

import java.util.Date;

/**
 * <pre>
 *  商品清单关联库
 * </pre>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhSkuItemRel {
    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageNum = 1;

    /**
     * <pre>
     * 分页
     * </pre>
     */
    private int pageSize = 10;

    /**
     * <pre>
     * 主键
     * </pre>
     */
    private Long id;
    
    /**
     * <pre>
     * 商品id
     * </pre>
     */
    private Long skuId;
    
    /**
     * <pre>
     * 清单类型 1-项目（单项目） 2-服务（多项目套餐） 3-服务组（多服务套餐）
     * </pre>
     */
    private Integer skuItemType;
    
    /**
     * <pre>
     * 清单内容id,实际业务主键id,比如服务id或者项目id
     * </pre>
     */
    private String skuItemId;

    /**
     * <pre>
     * 渠道id
     * </pre>
     */
    private Long channelId;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;
    
    /**
     * <pre>
     * 版本号
     * </pre>
     */
    private Integer version;
    
    /**
     * <pre>
     * 是否有效 0-无效 1-有效
     * </pre>
     */
    private Integer yn;
    
    /**
     * <pre>
     * 创建人
     * </pre>
     */
    private String createUser;
    
    /**
     * <pre>
     * 更新人
     * </pre>
     */
    private String updateUser;
    
    /**
     * <pre>
     * 创建时间
     * </pre>
     */
    private Date createTime;
    
    /**
     * <pre>
     * 更新时间
     * </pre>
     */
    private Date updateTime;
}