package com.jdh.o2oservice.core.domain.product.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.product.context.ServiceIndicatorExactQueryContext;
import com.jdh.o2oservice.core.domain.product.context.ServiceIndicatorQueryContext;
import com.jdh.o2oservice.core.domain.product.model.Indicator;
import com.jdh.o2oservice.core.domain.product.model.IndicatorIdentifier;
import com.jdh.o2oservice.core.domain.product.model.JdhStationIndicatorRel;

import java.util.List;

/**
 * @InterfaceName:JdhServiceIndicatorRepository
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/20 14:56
 * @Vserion: 1.0
 **/
public interface JdhServiceIndicatorRepository extends Repository<Indicator, IndicatorIdentifier> {

    /**
     * 分页查询指标数据
     *
     * @param serviceIndicatorQueryContext
     * @return
     */
    Page<Indicator> queryIndicatorPageInfo(ServiceIndicatorQueryContext serviceIndicatorQueryContext);

    /**
     * 查询指标集合
     * @param indicatorQueryContext
     * @return
     */
    List<Indicator> queryIndicatorList(ServiceIndicatorQueryContext indicatorQueryContext);

    /**
     * 精确查询服务指标
     *
     * @param exactQueryContext
     * @return
     */
    List<Indicator> queryIndicatorExactList(ServiceIndicatorExactQueryContext exactQueryContext);

    /**
     * 查询指标下个性化配置实验室列表
     *
     * @param jdhStationIndicatorRel
     * @return
     */
    List<JdhStationIndicatorRel> queryStationIndicatorList(JdhStationIndicatorRel  jdhStationIndicatorRel);

    /**
     * 查询指标下个性化实验室配置
     *
     * @param jdhStationIndicatorRel
     * @return
     */
    JdhStationIndicatorRel queryStationIndicator(JdhStationIndicatorRel  jdhStationIndicatorRel);
}
