package com.jdh.o2oservice.core.domain.product.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.medicine.base.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <pre>
 *  商品关系表
 * </pre>
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhSkuRel {
    
    /**
     * <pre>
     * 主键
     * </pre>
     */
    private Long id;
    
    /**
     * <pre>
     * 父商品id（主站商品id）
     * </pre>
     */
    private Long parentSkuId;

    /**
     * <pre>
     * 父商品短名称
     * </pre>
     */
    private String parentShortName;
    
    /**
     * <pre>
     * 子商品id（主站商品id）
     * </pre>
     */
    private Long skuId;
    
    /**
     * <pre>
     * 子商品短名称
     * </pre>
     */
    private String skuShortName;
    
    /**
     * <pre>
     * 商品关系类型 1-可同时购买（如加项套餐） 2-不可同时购买（如升级套餐）
     * </pre>
     */
    private Integer skuRelType;
    
    /**
     * <pre>
     * 备注
     * </pre>
     */
    private String remark;
    
    /**
     * <pre>
     * 版本号
     * </pre>
     */
    private Integer version;
    
    /**
     * <pre>
     * 是否有效 0-无效 1-有效
     * </pre>
     */
    private Integer yn;
    
    /**
     * <pre>
     * 创建人
     * </pre>
     */
    private String createUser;
    
    /**
     * <pre>
     * 更新人
     * </pre>
     */
    private String updateUser;
    
    /**
     * <pre>
     * 创建时间
     * </pre>
     */
    private Date createTime;
    
    /**
     * <pre>
     * 更新时间
     * </pre>
     */
    private Date updateTime;
    
    /**
     * <pre>
     * 服务项目列表
     * </pre>
     */
    List<ServiceItem> serviceItemList;

    /**
     * <pre>
     * 扩展信息
     * </pre>
     */
    String extend;

    /**
     * SKU短介绍
     * @return
     */
    public String getSkuShortDesc(){
        try {
            if(StringUtil.isEmpty(extend)){
                return null;
            }
            JSONObject extendJson = JSON.parseObject(extend);
            String skuShortDesc = extendJson.getString("skuShortDesc");
            if(StringUtil.isEmpty(skuShortDesc)){
                return null;
            }
            return skuShortDesc;
        } catch (Exception e){
            return null;
        }
    }

    /**
     * SKU长介绍
     * @return
     */
    public String getSkuFullDesc(){
        try {
            if(StringUtil.isEmpty(extend)){
                return null;
            }
            JSONObject extendJson = JSON.parseObject(extend);
            String skuFullDesc = extendJson.getString("skuFullDesc");
            if(StringUtil.isEmpty(skuFullDesc)){
                return null;
            }
            return skuFullDesc;
        } catch (Exception e){
            return null;
        }
    }

    /**
     * SKU标签
     * @return
     */
    public String getSkuTagDesc(){
        try {
            if(StringUtil.isEmpty(extend)){
                return null;
            }
            JSONObject extendJson = JSON.parseObject(extend);
            String skuTagDesc = extendJson.getString("skuTagDesc");
            if(StringUtil.isEmpty(skuTagDesc)){
                return null;
            }
            return skuTagDesc;
        } catch (Exception e){
            return null;
        }
    }
}