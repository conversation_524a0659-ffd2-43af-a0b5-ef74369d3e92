package com.jdh.o2oservice.core.domain.product.context;

import com.jdh.o2oservice.core.domain.support.vertical.context.BusinessContext;
import lombok.Builder;
import lombok.Data;

import java.util.Set;

/**
 * @ClassName:ServiceItemQueryContext
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/22 11:24
 * @Vserion: 1.0
 **/
@Data
@Builder
public class ServiceItemQueryContext extends BusinessContext {

    /**
     * 项目编码
     */
    private Set<Long> itemIds;

    /**
     * 指标编码集合
     */
    private Set<Long> indicatorIds;

    /**
     * 指标名称
     */
    private String itemName;

    /**
     * 商家类型 1-自营 2-POP
     */
    private Integer vendorType;

    /**
     * 商品一级类目
     */
    private Long firstSkuCategory;

    /**
     * 商品二级类目
     */
    private Long secondSkuCategory;

    /**
     * 商品三级类目
     */
    private Long thirdSkuCategory;

    /**
     * 一级分类编码
     */
    private Set<Long> firstIndicatorCategory;

    /**
     * 二级分类编码
     */
    private Set<Long> secondIndicatorCategory;

    /**
     * 三级分类编码
     */
    private Set<Long> thirdIndicatorCategory;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 项目类型1:供应商体检项目
     */
    private Integer itemType;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 供应商（渠道）编码
     */
    private String channelNo;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页长
     */
    private Integer pageSize;


    /**
     * 项目来源 1-消费医疗自定义,2-全国医疗服务技术规范
     */
    private Integer itemSource;

    /**
     * 项目名称-英文
     */
    private String itemNameEn;

    /**
     * 项目编码
     */
    private Long itemId;


    /**
     * 项目类型1:供应商体检项目 2 检测类 3 护理类
     */
    private Set<Integer> itemTypes;
}
