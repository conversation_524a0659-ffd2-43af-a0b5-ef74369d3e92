package com.jdh.o2oservice.core.domain.product.model;

import com.jdh.o2oservice.base.model.Entity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @ClassName:ServiceItem
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/3/20 11:14
 * @Vserion: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceItem implements Entity<ServiceItemIdentifier> {

    /**
     * 主键
     */
    private Long id;

    /**
     * 项目编码
     */
    private Long itemId;

    /**
     * 商品一级类目
     */
    private Long firstSkuCategory;

    /**
     * 商品二级类目
     */
    private Long secondSkuCategory;

    /**
     * 商品三级类目
     */
    private Long thirdSkuCategory;

    /**
     * 一级分类编码
     */
    private Long firstIndicatorCategory;

    /**
     * 一级分类名称
     */
    private String firstIndicatorCategoryName;

    /**
     * 二级分类编码
     */
    private Long secondIndicatorCategory;

    /**
     * 二级分类名称
     */
    private String secondIndicatorCategoryName;

    /**
     * 三级分类编码
     */
    private Long thirdIndicatorCategory;

    /**
     * 三级分类编名称
     */
    private String thirdIndicatorCategoryName;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目意义
     */
    private String itemMean;

    /**
     * 项目类型
     */
    private Integer itemType;

    /**
     * 项目价格
     */
    private BigDecimal itemPrice;

    /**
     * 适用人群
     */
    private String itemSuitable;

    /**
     * 服务时长
     */
    private Integer serviceDuration;

    /**
     * 检测时长
     */
    private Integer inspectDuration;

    /**
     * 检查扩展信息
     */
    private String inspectExt;

    /**
     * 供应商（渠道）编码
     */
    private String channelNo;

    /**
     * 商家类型
     */
    private Integer vendorType;

    /**
     * 体检项目可换项
     */
    private String replaceIndicatorCategory;

    /**
     * 备注
     */
    private String remark;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否有效 0:无效 1有效
     */
    private Integer yn;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 服务项目关系依赖关系信息
     */
    private List<ServiceItemIndicatorRel> indicatorRelList;

    /**
     * 服务项目下的指标列表
     */
    private List<Indicator> indicatorList;

    /**
     * 服务项目下的指标列表
     */
    private String indicatorString;

    /**
     * 服务项目下的指标列表
     */
    private Set<Long> indicatorIdList;
    /**
     * 项目来源 1-消费医疗自定义,2-全国医疗服务技术规范
     */
    private Integer itemSource;

    /**
     * 引用外部项目编码,如行业标准或规范
     */
    private String referenceItemCode;

    /**
     * 项目名称-英文
     */
    private String itemNameEn;

    /**
     * 低值耗材分档,分档由低到高,按1-9分为9个档次
     */
    private Integer lowValueMaterialLevel;

    /**
     * 服务资源类型 1-骑手 2-护士 3-护工 4-康复师
     */
    private Integer serviceResourceType;

    /**
     * 服务资源数量
     */
    private Integer serviceResourceNum;

    /**
     * 技术难度按1—100分赋值,分值越大,难度越高
     */
    private Integer technicalLevel;

    /**
     * 风险程度按1—100分赋值,分值越大,风险越高
     */
    private Integer riskLevel;

    /**
     * 人力资源消耗相对值 1—100分赋值,分值越大,人力资源消耗越多
     */
    private Integer resourceUseRelativeValue;

    /**
     * 计量单位 次、日
     */
    private String itemMeasuringUnit;

    /**
     * 特殊情况资源消耗调整系数,备注形式,如双侧增加不超过0.5倍
     */
    private String resourceUseAdjustRemark;

    /**
     * 收费票据分类 1-床位费 2-诊察费 3-检查费 4-化验费 5-治疗费 6-手术费 7-护理费 8-药事服务费 9-其他收费
     */
    private Integer collectNotesType;

    /**
     * 会计科目分类 1-挂号收入 2-诊察收入 3-检查收入 4-化验收入 5-治疗收入 6-手术收入 7-床位收入 8-护理收入 9-其他收入
     */
    private Integer accountingSubjectType;

    /**
     * 病案首页费用分类 1-综合医疗服务类 2-诊断类 3-治疗类 4-康复类 5-中医类 6-西药类 7-中药类 8-血液和血液制品类 9-耗材类 10-其他类
     */
    private Integer medicalRecordCostType;

    /**
     * 样本类型 1-鼻咽拭子采样 2-唾液 3-痰液 4-粪便 5-肛周拭子 6-尿液 7-指尖血 8-干血斑 9-阴道/宫颈采样拭子 10-C13吹气袋（幽门螺旋杆菌检测） 11-头发（带毛囊）12-静脉血
     */
    private Integer sampleType;

    /**
     * 检测方法学 1-实时荧光定量PCR法 2-免疫荧光法 3-PCR法 4-PCR-流式荧光杂交法,更多详见系统词典配置
     */
    private Integer testWay;

    /**
     * 年龄范围 逗号隔开0,20
     */
    private String ageRange;

    /**
     * 性别多选 1-男 2-女,逗号隔开
     */
    private String sex;

    /**
     * 检测项目内涵
     */
    private String testRemark;

    /**
     * 通天塔链接
     */
    private String tongUrl;


    /**
     * 说明
     */
    private String desc;

    /**
     * 耗材列表
     */
    private List<ServiceItemMaterialPackageRel> materialList;


    /**
     * 技能列表
     */
    private Set<String> angelSkillCodeList;

    /**
     * 必须耗材列表
     */
    private Set<Long> materialIdNeedList;

    /**
     * 可选耗材列表
     */
    private Set<Long> materialIdList;

    /**
     * 允许导入项目id
     */
    private Boolean allowInsertItemId;

    /**
     * 报告展示类型，1.结构化，2.PDF
     */
    private Integer reportShowType;

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        this.version++;
    }

    /**
     * 获取标识符
     *
     * @return {@link ID}
     */
    @Override
    public ServiceItemIdentifier getIdentifier() {
        return new ServiceItemIdentifier(this.itemId);
    }
}
