package com.jdh.o2oservice.core.domain.product.enums;


import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;

/**
 * 商品聚合枚举
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
public enum ProductAggregateEnum implements AggregateCode {

    /** 商品聚合 */
    PRODUCT_SKU(DomainEnum.PRODUCT, "sku"),
    /** 商品服务聚合 */
    PRODUCT_PROGRAM(DomainEnum.PRODUCT, "program"),
    /** 商品业务项目聚合 */
    PRODUCT_BIZ_ITEM(DomainEnum.PRODUCT, "bizItem"),
    /** 商品标准项目聚合 */
    PRODUCT_STANDARD_ITEM(DomainEnum.PRODUCT, "standardItem"),
    /** 商品标准指标聚合 */
    PRODUCT_STANDARD_INDICATOR(DomainEnum.PRODUCT, "standardIndicator"),
    /** 商品Pop项目聚合 */
    PRODUCT_BIZ_POP_ITEM(DomainEnum.PRODUCT, "bizPopItem"),

    /** 商品自营项目聚合 */
    PRODUCT_BIZ_SELF_ITEM(DomainEnum.PRODUCT, "bizSelfItem"),

    PRODUCT_PROGRAM_ITEM(DomainEnum.PRODUCT, "program"),

    PRODUCT_REGION_FEE_CONFIG(DomainEnum.PRODUCT, "regionFeeConfig"),
    ;
    
    /**
     * domain
     */
    private final DomainCode domain;
    
    /**
     * code
     */
    private final String code;
    
    /**
     *
     * @param domain domain
     * @param code code
     */
    ProductAggregateEnum(DomainCode domain, String code) {
        this.domain = domain;
        this.code = code;
    }
    
    /**
     * 获取domain
     *
     * @return domain
     */
    @Override
    public DomainCode getDomainCode() {
        return domain;
    }
    
    /**
     * 获取code
     *
     * @return code
     */
    @Override
    public String getCode() {
        return code;
    }


}
