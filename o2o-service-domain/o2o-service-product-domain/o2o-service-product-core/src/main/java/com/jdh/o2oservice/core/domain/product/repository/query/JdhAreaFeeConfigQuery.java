package com.jdh.o2oservice.core.domain.product.repository.query;

import lombok.*;

/**
 * <pre>
 *  套餐（服务）之间关系
 * </pre>
 *
 * <AUTHOR>
 * @date 2024-07-15 21:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhAreaFeeConfigQuery{
    /**
     * '地区费项配置id'
     */
    private Long areaFeeConfigId;

    /**
     * '费项配置文件id'
     */
    private String feeConfigId;

    /**
     * <pre>
     * 业务身份: 1 骑手检测 2 护士检测 3 护士护理
     * </pre>
     */
    private Integer serviceType;

    /**
     * 渠道id 比如C端、互医 其对应的channelId为1010645803等
     */
    private String channelId;

    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;

    /**
     * '县地区code'
     */
    private String countyCode;
    /**
     * '乡镇code'
     */
    private String townCode;
}