package com.jdh.o2oservice.core.domain.product.repository.db;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.product.model.*;
import com.jdh.o2oservice.export.product.query.JdhSkuListRequest;

import java.util.List;

/**
 * 京东健康商品扩展主数据数据仓
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
public interface JdhSkuRepository extends Repository<JdhSku, JdhSkuIdentifier> {

    /**
     * 保存
     *
     * @param jdhSku jdhSku
     * @return count
     */
    @Override
    int save(JdhSku jdhSku);
    
    /**
     * 更新
     *
     * @param jdhSku jdhSku
     * @return count
     */
    int update(JdhSku jdhSku);

    /**
     * 更新
     *
     * @param jdhSku jdhSku
     * @return count
     */
    int updateSaleStatus(JdhSku jdhSku);
    
    /**
     * 通过Identify 查询
     *
     */
    @Override
    JdhSku find(JdhSkuIdentifier identifier);
    
    /**
     * 查询多个sku
     *
     */
    List<JdhSku> queryMultiSku(List<JdhSku> list);

    /**
     * 查询多个SKU
     * @param request
     * @return
     */
    List<JdhSku> queryMultiSku(JdhSkuListRequest request);
    
    /**
     * 查询单个sku项目列表
     *
     */
    List<JdhSkuItemRel> queryJdhSkuItemRelList(JdhSkuItemRel rel);
    
    /**
     * 查询多个sku项目列表
     *
     */
    List<JdhSkuItemRel> queryJdhSkuItemRelList(List<JdhSkuItemRel> list, Long channelId, Integer skuItemType, Boolean limit);
    
    /**
     * 分页查询sku列表
     *
     */
    Page<JdhSku> queryPageJdkSku(JdhSku jdhSku);
    
    /**
     * 保存sku加项信息
     *
     */
    int saveSkuRelInfo(JdhSkuRel jdhSkuRel);

    /**
     * 批量保存sku加项信息
     *
     */
    int batchSaveSkuRelInfo(JdhSkuRel parentSku, List<JdhSkuRel> jdhSkuRelList);
    
    /**
     * 删除sku加项信息
     *
     */
    int deleteSkuRelInfo(JdhSkuRel jdhSkuRel);
    
    /**
     * 查询sku加项信息
     *
     */
    List<JdhSkuRel> queryJdhSkuRelInfo(JdhSkuRel jdhSkuRel);

    /**
     * 查询多个sku加购商品信息
     *
     */
    List<JdhSkuRel> queryMultiJdhSkuRelInfo(List<JdhSkuRel> list, Integer skuRelType);

    /**
     * 分页查询商品项目关系
     *
     */
    Page<JdhSkuItemRel> queryJdhSkuItemRelPage(JdhSkuItemRel rel);

    /**
     * 查询商品扩展属性
     * @param jdhSkuExtend
     * @return
     */
    List<JdhSkuExtend> queryJdhSkuExtendList(JdhSkuExtend jdhSkuExtend);

    /**
     * 查询商品扩展属性
     * @param jdhSkuExtend
     * @return
     */
    Boolean saveJdhSkuExtend(JdhSkuExtend jdhSkuExtend);
}
