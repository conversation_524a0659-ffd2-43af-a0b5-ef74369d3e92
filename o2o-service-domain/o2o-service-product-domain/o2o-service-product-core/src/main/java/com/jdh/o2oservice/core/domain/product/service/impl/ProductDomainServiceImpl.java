package com.jdh.o2oservice.core.domain.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.googlecode.aviator.AviatorEvaluator;
import com.jd.jim.cli.Cluster;
import com.jd.medicine.base.common.annotation.LogAndUmp;
import com.jd.medicine.base.common.util.CollectionUtil;
import com.jd.medicine.base.common.util.DateUtil;
import com.jd.medicine.base.common.util.JsonUtil;
import com.jd.ump.profiler.util.StringUtil;
import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.ducc.model.SkuDetailRouteConfig;
import com.jdh.o2oservice.base.enums.*;
import com.jdh.o2oservice.base.util.IdCardUtils;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.product.bo.*;
import com.jdh.o2oservice.core.domain.product.bo.vo.*;
import com.jdh.o2oservice.core.domain.product.model.JdhSku;
import com.jdh.o2oservice.core.domain.product.model.JdhSkuRel;
import com.jdh.o2oservice.core.domain.product.model.ProductServiceGoods;
import com.jdh.o2oservice.core.domain.product.model.ServiceItem;
import com.jdh.o2oservice.core.domain.product.repository.db.JdhSkuRepository;
import com.jdh.o2oservice.core.domain.product.repository.query.ProductServiceGoodsQuery;
import com.jdh.o2oservice.core.domain.product.service.ProductDomainService;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.SkuInfoRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.*;
import com.jdh.o2oservice.core.domain.support.basic.rpc.param.CommentPageParam;
import com.jdh.o2oservice.export.angel.dto.JdhStationDto;
import com.jdh.o2oservice.export.product.dto.ProductPurchasePriceDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @ClassName ProductDomainServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/5 15:57
 **/
@Service
@Slf4j
public class ProductDomainServiceImpl implements ProductDomainService {

    private String customIconConfigStr = "[{\"isClick\":true,\"icon\":\"https://jkimg10.360buyimg.com/pop/jfs/t1/219909/18/33013/2470/6561a1bdF5e25832d/6c0c102fe952df2c.png\",\"text\":\"店铺\",\"jumpLink\":\"https://mall.jd.com/index-1000477238.html\",\"open\":true}]";

    /**
     * 商品sku仓库
     */
    @Resource
    private DuccConfig duccConfig;

    /**
     * 健康地址rpc
     */
    @Resource
    private JdhAddressRpc jdhAddressRpc;
    /**
     * jimClient
     */
    @Resource
    private Cluster jimClient;

    @Resource
    @Autowired
    private SkuInfoRpc skuInfoRpc;

    /**
     * addressRpc
     */
    @Autowired
    private AddressRpc addressRpc;

    @Resource
    private JdhSkuRepository jdhSkuRepository;

    /**
     * 查询服务套餐列表
     *
     * @param query
     * @return
     */
    @Override
    public List<ProductServiceGoods> queryServiceGoodsList(ProductServiceGoodsQuery query) {
        List<ProductServiceGoods> result = new ArrayList<>();
        //pop
        if (Objects.equals(query.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.POP_LOC.getCode())) {
            log.info("ProductDomainServiceImpl -> queryServiceGoodsList pop查服务套餐列表流程");
            //京麦后台对接的商家，返回默认套餐列表
            if (Objects.equals(query.getDockingType(), 5)) {
                log.info("ProductDomainServiceImpl -> queryServiceGoodsList 京麦对接的pop商家查服务套餐列表");
                ProductServiceGoods serviceGoods = ProductServiceGoods.builder().outServiceName(query.getCrsSku().getSkuName()).channelType(query.getChannelType()).serviceId(query.getServiceId()).outServiceId(query.getServiceId()).outServiceName(query.getCrsSku().getSkuName()).userGender(GenderEnum.COMMON.getType()).userMarriage(MarryEnum.COMMON.getType()).build();
                result = Lists.newArrayList(serviceGoods);
            }
            return result;
        }
        //一卡万店
        else if (Objects.equals(query.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.YK.getCode())) {
            log.info("ProductDomainServiceImpl -> queryServiceGoodsList 一卡万店查服务套餐列表流程");
            return result;
        }
        return result;
    }

    /**
     * 获取用户最近一次使用的收获地址(O2O项目中结算页用户行为接口也会更新缓存)
     * 1、优先使用入参addressId，并更新缓存
     * 2、如入参addressId为空，优先使用缓存中的addressId
     * 3、如缓存中也没有addressId，使用地址列表的默认地址
     * 4、如果地址列表没有默认，取第一个
     * 5、如没有没有地址列表，返回null
     *
     * @param userPin
     * @param addressId
     * @return
     */
    @Override
    public UserAddressDetailBO getUserLastAddress(String userPin, Long addressId) {
        log.info("ProductDomainServiceImpl getUserLastAddress userPin={}, addressId={}", userPin, addressId);
        //未登录无地址信息
        if (StringUtil.isBlank(userPin)) {
            return null;
        }
        List<AddressDetailBO> list = jdhAddressRpc.queryAddressList(userPin);
        if (Objects.isNull(list) || list.isEmpty()) {
            return null;
        }
        // 本次是否需要更新缓存，当且仅当入参addressId不为空且有效地址对象时
        boolean isUpdateRedis = false;
        // 入参addressId为空，使用缓存中的addressId
        if (addressId == null) {
            String cacheAddressIdString = jimClient.get(RedisKeyEnum.getRedisKey(RedisKeyEnum.USER_LAST_ADDRESS_ID_KEY, userPin));
            if (cacheAddressIdString != null && cacheAddressIdString.matches("\\d+")) {
                addressId = Long.parseLong(cacheAddressIdString);
            }
        } else {
            isUpdateRedis = true;
        }
        AddressDetailBO addressDetailBO = null;
        if (addressId != null) {
            long finalAddressId = addressId.longValue();
            // 取指定ID地址
            addressDetailBO = list.stream().filter(item -> item.getAddressId().longValue() == finalAddressId).findFirst().orElse(null);
            if (isUpdateRedis && addressDetailBO != null) {
                jimClient.set(RedisKeyEnum.getRedisKey(RedisKeyEnum.USER_LAST_ADDRESS_ID_KEY, userPin), String.valueOf(finalAddressId));
            }
        } else {
            // 取默认地址
            addressDetailBO = list.stream().filter(AddressDetailBO::isAddressDefault).findFirst().orElse(null);
            // 没有默认地址，取第一个
            if (addressDetailBO == null) {
                addressDetailBO = list.get(0);
            }
        }

        return getAddressDetailDTO(userPin, addressDetailBO);
    }

    @Override
    public ProductDefaultSkuBO getProductDefaultSkuDTO(RpcSkuBO crsSkuBO, PriceInfoResponseBO priceInfoResponse,
                                                       boolean selfFlag, JdhSku jdhSku , Map<String, ProductPurchasePriceDto> productPurchasePriceMap, Set<String> skuFreeFeeList, boolean spuFlag) {
        String skuId = crsSkuBO.getSkuId();
        ProductDefaultSkuBO defaultSku = new ProductDefaultSkuBO();
        defaultSku.setSkuId(skuId);
        defaultSku.setSkuName(crsSkuBO.getSkuName());
        defaultSku.setSelfFlag(!selfFlag);
        defaultSku.setTitle(crsSkuBO.getSkuName());
        defaultSku.setImgDfsUrl(crsSkuBO.getImgDfsUrl());
        defaultSku.setServiceType(jdhSku.getServiceType());
        defaultSku.setSpuId(crsSkuBO.getSpuId());
        //defaultSku.setQuantity();
        //defaultSku.setSpecification();
        //划线价格
        Map<String, PriceResultBO> skuPrice = new HashMap<>();
        if (priceInfoResponse != null && priceInfoResponse.getPriceMap() != null) {
            skuPrice = priceInfoResponse.getPriceMap();
            // 促销信息不为空
            if (priceInfoResponse.getInfoResponse() != null && priceInfoResponse.getInfoResponse().containsKey(skuId)) {
                List<PromoInfoFieldBO> promotionInfoFieldList = priceInfoResponse.getInfoResponse().get(skuId).getPromoInfoFieldList();
                if (promotionInfoFieldList != null) {
                    promotionInfoFieldList.stream().forEach(item -> {
                        defaultSku.setPromotionTags(item.getTags());
                    });
                }
            }
        }
        PriceResultBO priceResult = skuPrice.get(skuId);
        if (priceResult != null) {
            if (StringUtils.isNotBlank(priceResult.getMarketPrice())) {
                defaultSku.setJdPrice(new BigDecimal(priceResult.getMarketPrice()));
                defaultSku.setJdPriceDesc(priceResult.getMarketPrice());
            }
            // 如果原价不为空且原价比最终价大，用原价替换划线价
            if (StringUtils.isNotBlank(priceResult.getOriginalPrice()) && new BigDecimal(priceResult.getOriginalPrice()).compareTo(new BigDecimal(priceResult.getJdPrice())) == 1) {
                defaultSku.setJdPrice(new BigDecimal(priceResult.getOriginalPrice()));
                defaultSku.setJdPriceDesc(priceResult.getOriginalPrice());
            }

            //到手价格
            //2023-11-28 到手价不展示，变为展示京东价
            defaultSku.setFinalPrice(new BigDecimal(priceResult.getJdPrice()));
            defaultSku.setFinalPriceDesc(priceResult.getJdPrice());

            // 双价标记
            if(priceResult.getUp() != null){
                defaultSku.setDoublePriceTag(priceResult.getUp());
            }
        }

        if (jdhSku != null) {
            //标签
            if (StringUtils.isNotBlank(jdhSku.getTags())) {
                List<String> tags = JSONArray.parseArray(jdhSku.getTags(), String.class);
                List<ProductSkuLabelBO> productSkuLabelDTOS = new ArrayList<>();
                for (String tag : tags) {
                    ProductSkuLabelBO productSkuLabelDTO = new ProductSkuLabelBO();
                    productSkuLabelDTO.setTitle(tag);
                    //目前只有一个
                    productSkuLabelDTO.setType("1");
                    productSkuLabelDTOS.add(productSkuLabelDTO);
                }
                defaultSku.setLabel(productSkuLabelDTOS);
            }
            // 后端不用这个字段处理逻辑，前端需要
            defaultSku.setHomeBizCode(String.valueOf(jdhSku.getServiceType()));
        }

        // 查询到手价
        if (MapUtils.isNotEmpty(productPurchasePriceMap) && Objects.nonNull(productPurchasePriceMap.get(skuId))){
            ProductPurchasePriceDto productPurchasePrice = productPurchasePriceMap.get(skuId);
            String purchasePrice =productPurchasePrice.getPurchasePrice();
            if (StringUtils.isNotBlank(purchasePrice)){
                defaultSku.setFinalPrice(new BigDecimal(purchasePrice));
                defaultSku.setFinalPriceDesc(purchasePrice);
            }
            if (spuFlag){
                defaultSku.setMatchPlusFlag(NumConstant.NUM_2.equals(productPurchasePrice.getSecondPriceType()));
            }
        }

        // 没有配置一口价，才能展示此文案
        if (CollectionUtils.isEmpty(skuFreeFeeList) || !skuFreeFeeList.contains(skuId)) {
            defaultSku.setFinalPriceDesc(defaultSku.getFinalPriceDesc() + "起");
        }
        if (spuFlag){
            defaultSku.setPriceDescTip(duccConfig.getProductDetailPriceDescTip());
        }else {
            defaultSku.setPriceDesc(duccConfig.getProductDetailPriceDescTip());
        }
        log.info("ProductDomainServiceImpl getProductDefaultSkuDTO defaultSku={}", JSON.toJSONString(defaultSku));
        return defaultSku;
    }

    @Override
    public List<ProductDefaultSkuBO> getProductDefaultSkuDTOS(List<JdhSkuRel> jdhSkuRels, Map<String, RpcSkuBO> skuInfo, Boolean selfFlag, PriceInfoResponseBO priceInfoResponseBO, Set<String> skuFreeFeeList
            , Map<String, ProductPurchasePriceDto> productPurchasePriceMap, Map<String, List<JdhStationDto>> skuStationMap, Boolean spuFlag) {
        List<ProductDefaultSkuBO> productDefaultSkuDTOS = new ArrayList<>();
        try {
            if (CollectionUtils.isEmpty(jdhSkuRels)) {
                log.info("ProductDomainServiceImpl getProductDefaultSkuDTOS jdhSkuRels empty");
                return productDefaultSkuDTOS;
            }
            List<JdhSku> jdhSkus = jdhSkuRepository.queryMultiSku(jdhSkuRels.stream().map(s -> JdhSku.builder().skuId(s.getSkuId()).build())
                    .filter(Objects::nonNull).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(jdhSkus)){
                log.info("ProductDomainServiceImpl getProductDefaultSkuDTOS jdhSkus empty");
                return productDefaultSkuDTOS;
            }
            Map<Long, JdhSku> jdhSkuMap = jdhSkus.stream().collect(Collectors.toMap(JdhSku::getSkuId, vo -> vo));
            log.info("升级加购配置全部信息jdhSku:{},jdhSkuRels:{},skuInfo:{}", JSON.toJSONString(jdhSkus), JSON.toJSONString(jdhSkuRels), JSON.toJSONString(skuInfo));

            for (JdhSkuRel jdhSkuRel : jdhSkuRels) {
                Long skuId = jdhSkuRel.getSkuId();
                JdhSku jdhSku = jdhSkuMap.get(skuId);
                if (Objects.isNull(jdhSku)){
                    log.info("ProductDomainServiceImpl getProductDefaultSkuDTOS jdhSku empty jdhSkuRel={}", JSON.toJSONString(jdhSkuRel));
                    continue;
                }
                RpcSkuBO skuBO = skuInfo.get(String.valueOf(skuId));
                if (Objects.isNull(skuBO)){
                    log.info("ProductDomainServiceImpl getProductDefaultSkuDTOS skuBO empty jdhSkuRel={}", JSON.toJSONString(jdhSkuRel));
                    continue;
                }
                ProductDefaultSkuBO productDefaultSkuBO = this.getProductDefaultSkuDTO(skuInfo.get(String.valueOf(skuId)), priceInfoResponseBO, selfFlag, jdhSku, productPurchasePriceMap, skuFreeFeeList, spuFlag);
                log.info("升级加购配置单品信息 sku：{},result:{}", skuId, JSON.toJSONString(productDefaultSkuBO));
                List<ServiceItem> serviceItemList = jdhSkuRel.getServiceItemList();
                if (CollectionUtils.isEmpty(serviceItemList)) {
                    log.info("ProductDomainServiceImpl getProductDefaultSkuDTOS serviceItemList empty jdhSkuRel={}", JSON.toJSONString(jdhSkuRel));
                    continue;
                }
                Map<String,String> shortNameTemp = duccConfig.getSkuShortNameTempMap();
                if (CollUtil.isNotEmpty(shortNameTemp) && shortNameTemp.containsKey(String.valueOf(skuId))) {
                    productDefaultSkuBO.setTitle(shortNameTemp.get(String.valueOf(skuId)));
                } else {
                    StringBuilder title = new StringBuilder();
                    for (int i = 0; i < serviceItemList.size(); i++) {
                        ServiceItem serviceItemDto = serviceItemList.get(i);
                        title.append(serviceItemDto.getItemName());
                        if (i < serviceItemList.size() - 1) {
                            title.append("+");
                        }
                    }
                    productDefaultSkuBO.setTitle(title.toString());
                }

                SkuDetailRouteConfig skuDetailRouteConfig = duccConfig.getSkuDetailRouteConfig();
                Map<String, Object> param = new HashMap<>(1);
                param.put("jdhSku", jdhSku);
                if (skuDetailRouteConfig != null && (boolean) AviatorEvaluator.compile(skuDetailRouteConfig.getExpression(), Boolean.TRUE).execute(param)) {
                    // 具体的业务数据，需要替换占位符
                    Map<String, String> dataMap = new HashMap<>(1);
                    dataMap.put("skuId", String.valueOf(jdhSku.getSkuId()));
                    // 创建StringSubstitutor，入参是要替换的业务数据map
                    StringSubstitutor sub = new StringSubstitutor(dataMap);
                    // 占位符字段替换为具体业务数据，入参为模板字符串
                    productDefaultSkuBO.setSkuJumpUrl(sub.replace(skuDetailRouteConfig.getUrl()));
                }
                productDefaultSkuBO.setSkuShortName(jdhSkuRel.getSkuShortName());
                ProductSkuLabelBO productSkuLabelBO = new ProductSkuLabelBO();
                productSkuLabelBO.setType("1");
                productSkuLabelBO.setTitle(jdhSkuRel.getSkuShortName());
                productDefaultSkuBO.setLabel(Collections.singletonList(productSkuLabelBO));
                // sku是否能服务
                if (MapUtils.isNotEmpty(skuStationMap)){
                    productDefaultSkuBO.setMatchStationFlag(CollectionUtils.isNotEmpty(skuStationMap.get(String.valueOf(jdhSku.getSkuId()))));
                }
                productDefaultSkuBO.setSkuAbbreviation(StringUtils.isNotBlank(skuBO.getSkuAbbreviation()) ? skuBO.getSkuAbbreviation() : skuBO.getSkuName());
                productDefaultSkuBO.setSkuGroupInnerOrder(Objects.nonNull(skuBO.getSkuGroupInnerOrder())? skuBO.getSkuGroupInnerOrder() : 1);
                productDefaultSkuDTOS.add(productDefaultSkuBO);
            }
            if (CollectionUtils.isNotEmpty(productDefaultSkuDTOS) && spuFlag){
                productDefaultSkuDTOS.sort(Comparator.comparing(ProductDefaultSkuBO::getSkuGroupInnerOrder));
            }
            log.info("ProductDomainServiceImpl getProductDefaultSkuDTOS productDefaultSkuDTOS={}", JSON.toJSONString(productDefaultSkuDTOS));
        } catch (Exception e) {
            log.error("ProductDomainServiceImpl getProductDefaultSkuDTOS error e", e);
        }
        return productDefaultSkuDTOS;
    }

    private UserAddressDetailBO getAddressDetailDTO(String userPin, AddressDetailBO defaultAddressDetail) {
        //参数检查
        if(Objects.isNull(defaultAddressDetail)) {
            log.error("[ProductDomainServiceImpl -> getAddressDetailDTO],地址信息为空");
            return null;
        }

        //上面用户收货地址接口，返回的经纬度数据不准确，这里使用地址换经纬度逻辑进行纠正
        GisPointBo gisPointBo = null;
        try {
            gisPointBo = addressRpc.getLngLatByAddress(defaultAddressDetail.getFullAddress());
        } catch (Exception e) {
            log.error("getAddressDetailDTO 通过全地址换经纬度exception",e);
        }
        UserAddressDetailBO dto = new UserAddressDetailBO();
        dto.setAddressDetail(defaultAddressDetail.getAddressDetail());
        dto.setMobile(defaultAddressDetail.getMobile());
        dto.setName(IdCardUtils.maskLastName(defaultAddressDetail.getName()));
        dto.setUserPin(userPin);
        dto.setFullAddress(defaultAddressDetail.getFullAddress());
        dto.setProvinceId(defaultAddressDetail.getProvinceId());
        dto.setCityId(defaultAddressDetail.getCityId());
        dto.setCountyId(defaultAddressDetail.getCountyId());
        dto.setTownId(Objects.isNull(defaultAddressDetail.getTownId()) || defaultAddressDetail.getTownId() <= NumConstant.NUM_0  ? null : defaultAddressDetail.getTownId());
        dto.setAddressId(defaultAddressDetail.getAddressId());
        dto.setAddressId(defaultAddressDetail.getAddressId());
        dto.setProvinceName(defaultAddressDetail.getProvinceName());
        dto.setCityName(defaultAddressDetail.getCityName());
        dto.setCountyName(defaultAddressDetail.getCountyName());
        dto.setTownName(defaultAddressDetail.getTownName());
        dto.setAddressDefault(defaultAddressDetail.isAddressDefault());
        dto.setLatitude(Objects.isNull(gisPointBo) || Objects.isNull(gisPointBo.getLatitude()) ? defaultAddressDetail.getLatitude() : gisPointBo.getLatitude().doubleValue());
        dto.setLongitude(Objects.isNull(gisPointBo) || Objects.isNull(gisPointBo.getLongitude()) ? defaultAddressDetail.getLongitude() : gisPointBo.getLongitude().doubleValue());
        dto.setCoordType(defaultAddressDetail.getCoordType());
        log.info("ProductDomainServiceImpl getAddressDetailDTO dto={}", JSON.toJSONString(dto));
        return dto;
    }


    @Override
    public List<ProductDetailCustomIconConfigBO> getCustomConfigButtons(String envType, Map<String, String> properties) {
        List<ProductDetailCustomIconConfigBO> customIconConfigs = JSONObject.parseArray(properties.getOrDefault("customIconConfigs", customIconConfigStr), ProductDetailCustomIconConfigBO.class);
        JSONObject envBuySwitch = JSON.parseObject(properties.getOrDefault("envBuySwitch", "{\"jdapp\":true,\"jdhapp\":true,\"jdheapp\":true,\"jdmeapp\":true,\"miniprogram\":true,\"wxwork\":true,\"wexin\":true,\"qq\":true,\"h5\":true}"));
        boolean canBuy = Objects.isNull(envBuySwitch) || envBuySwitch.getBoolean(envType);
        for (ProductDetailCustomIconConfigBO config : customIconConfigs) {
            switch (config.getButtonType()) {
                case 1:
                    // 店铺 现在只会有店铺，todo madong 迁移老项目时再重构此部分的按钮数据

                    break;
                case 2:
                    // 咚咚
//                    config.setJumpLink(this.getCustomerServiceLink(envType, detailFloorBO.getSkuNo(), duccConfig));
                    break;
                case 3:
                    break;
                default:
                    // 处理未知类型或者不做任何事情
            }
        }

        return customIconConfigs;
    }


    /**
     * 转换为sku信息
     *
     * @param materialDTO
     * @return {@link ProductCarouselFileBO}
     */
    @Override
    public List<ProductCarouselFileBO> skuMaterialBO2Dto(SkuMaterialBO materialDTO, VideoMarkResultBO videoMarkResultBO) {
        //是否有视频标记位置
        Boolean hasVideo = Objects.nonNull(videoMarkResultBO) && StringUtils.isNotBlank(videoMarkResultBO.getPlayUrl());
        List<SkuMaterialImageBO> images = materialDTO.getImages();
        AtomicReference<String> primaryImage = new AtomicReference<>();
        List<ProductCarouselFileBO> carouselMapDetailList = images.stream().map(skuMaterialImageDTO -> {
            log.info("ProductDomainServiceImpl.skuMaterialBO2Dto.skuMaterialImageDTO={}", JSON.toJSONString(skuMaterialImageDTO));
            ProductCarouselFileBO carouselFileDTO = new ProductCarouselFileBO();
            //记录主图
            if (Objects.equals(skuMaterialImageDTO.getIsPrimary(), CommonConstant.ONE)) {
                primaryImage.set(skuMaterialImageDTO.getPath());
            }
            //如果有视频 图片位置递增1 否则维持原状
            log.info("ProductDomainServiceImpl.skuMaterialBO2Dto.carouselFileDTO={},hasVideo={},skuMaterialImageDTO={}", JSON.toJSONString(carouselFileDTO),hasVideo,JSON.toJSONString(skuMaterialImageDTO));
            carouselFileDTO.setFileSort(hasVideo ?   Objects.equals(skuMaterialImageDTO.getIsPrimary(), CommonConstant.ONE) ? 1 : skuMaterialImageDTO.getOrderSort()+1:
                    Objects.equals(skuMaterialImageDTO.getIsPrimary(), CommonConstant.ONE) ? 0 : skuMaterialImageDTO.getOrderSort());
            carouselFileDTO.setFileType(CommonConstant.ONE); // 文件类型：1-图片，2-视频
            carouselFileDTO.setFileUrl(skuMaterialImageDTO.getPath());
            return carouselFileDTO;
        }).collect(Collectors.toList());
        /**如果当前sku有主图视频*/
        if (hasVideo) {
            ProductCarouselFileBO carouselFileVideoDTO = new ProductCarouselFileBO();
            carouselFileVideoDTO.setFileType(CommonConstant.TWO); // 文件类型：1-图片，2-视频
            carouselFileVideoDTO.setFileUrl(videoMarkResultBO.getPlayUrl());
            //将主图设置为视频封面图
            carouselFileVideoDTO.setJumpUrl(StringUtils.isNotBlank(primaryImage.get()) ? primaryImage.get() : videoMarkResultBO.getImageUrl());
            carouselFileVideoDTO.setFileSort(CommonConstant.ZERO);
            carouselMapDetailList.add(carouselFileVideoDTO);
        }
        Collections.sort(carouselMapDetailList, Comparator.comparing(ProductCarouselFileBO::getFileSort));
        log.info("ProductDomainServiceImpl.skuMaterialBO2Dto.carouselMapDetailList={}",JSON.toJSONString(carouselMapDetailList));
        return carouselMapDetailList;
    }

    @Override
    public Map<String, Map<String, String>> getShare(String skuNo, List<ProductCarouselFileBO> carouselMapDetailList, ProductDefaultSkuBO productDefaultSkuBO, Map<String, String> properties) {
        Map<String, Map<String, String>> shareInfo = com.jd.medicine.base.common.util.JsonUtil.parseObject(properties.getOrDefault("shareInfo", ""), new TypeReference<Map<String, Map<String, String>>>() {
        });
        if (MapUtils.isNotEmpty(shareInfo)) {
            String title = StringUtils.isNotBlank(productDefaultSkuBO.getTitle()) ? productDefaultSkuBO.getTitle() : "京东居家快检";
            shareInfo.forEach((key, channelMap) -> {
                if (ShareChannelEnum.JDH_MINI_PROGRAM.getChannel().equals(key)) {
                    //主图不存在的情况下兜底图片
                    String mainImgUrl = "";
                    if (CollectionUtils.isNotEmpty(carouselMapDetailList)) {
                        ProductCarouselFileBO productCarouselFileBO = carouselMapDetailList.get(0);
                        mainImgUrl = productCarouselFileBO.getFileUrl();
                        /**视频格式兼容*/
                        if (Integer.valueOf(CommonConstant.TWO).equals(productCarouselFileBO.getFileType())){
                            mainImgUrl=productCarouselFileBO.getJumpUrl();
                        }
                    }

                    String mainImgUrlLink = MessageFormat.format(channelMap.get("imgUrl"), mainImgUrl);

                    if(StringUtils.isNotBlank(mainImgUrlLink) && mainImgUrlLink.contains("/jfs/")){
                        log.info("[ProductDomainServiceImpl.skuMaterialBO2Dto.carouselMapDetailList],mainImgUrlLink={}", mainImgUrlLink);
                        mainImgUrlLink = mainImgUrlLink.replaceFirst("/jfs/", "/s300x300_jfs/");
                    }
                    channelMap.put("mpIconUrl", MessageFormat.format(channelMap.get("mpIconUrl"), mainImgUrl));
                    channelMap.put("mpPath", MessageFormat.format(channelMap.get("mpPath"), skuNo));
                    channelMap.put("imgUrl", mainImgUrlLink);
                    channelMap.put("title", MessageFormat.format(channelMap.get("title"), title));
                }
            });
        }
        return shareInfo;
    }

    @Override
    public ProductDetailBottomBannerBO getProductDetailBottomBannerDTO(Integer buyLimitType) {
        ProductDetailBottomBannerBO bottomBanner = new ProductDetailBottomBannerBO();
        if (buyLimitType == null) {
            bottomBanner.setFlag(false);
        } else {
            ProductDetailBottomBannerEnum byBuyLimitType = ProductDetailBottomBannerEnum.getByBuyLimitType(buyLimitType);
            bottomBanner.setFlag(true);
            bottomBanner.setText(byBuyLimitType.getText());
            bottomBanner.setBtnText(byBuyLimitType.getBtnText());
            bottomBanner.setBuyLimitType(byBuyLimitType.getBuyLimitType());
        }

        bottomBanner.setBuyLimitType(buyLimitType);
        return bottomBanner;
    }

    @Override
    public ProductInfoBO getProductInfoBO(boolean noStation, boolean isLogin, UserAddressDetailBO userAddressDetail, JdhSku jdhSkuBo) {
        ProductInfoBO productInfo = new ProductInfoBO();

        ProductServiceIntroduceBO productServiceIntroduceDTO = new ProductServiceIntroduceBO();
        productServiceIntroduceDTO.setTitle("服务须知");
        if (jdhSkuBo != null) {
            productServiceIntroduceDTO.setImageUrl(jdhSkuBo.getServiceProcessImg());
            String tagStr = jdhSkuBo.getServiceNotice();
            if (StringUtils.isNotBlank(tagStr)) {
                List<JdhSkuServiceNoticeBO> tags = JSONArray.parseArray(tagStr, JdhSkuServiceNoticeBO.class);
                // 如果标签列表不为空
                if (tags.size() >= 2) {
                    // 如果有两个或两个以上的标签，取前两个标签并用逗号分隔
                    productServiceIntroduceDTO.setText(tags.get(0).getContent() + "·" + tags.get(1).getContent());
                } else {
                    // 如果只有一个标签，直接取第一个
                    productServiceIntroduceDTO.setText(tags.get(0).getContent());
                }
            }

            String serviceNotice = jdhSkuBo.getServiceNotice();
            if (StringUtils.isNotBlank(serviceNotice)) {
                List<JdhSkuServiceNoticeBO> jdhSkuServiceNoticeBOS = JSONArray.parseArray(serviceNotice, JdhSkuServiceNoticeBO.class);
                List<ProductServiceIntroduceContentBO> productServiceIntroduceContentDTOS = new ArrayList<>();
                for (JdhSkuServiceNoticeBO jdhSkuServiceNoticeDto : jdhSkuServiceNoticeBOS) {
                    ProductServiceIntroduceContentBO productServiceIntroduceContentDTO = new ProductServiceIntroduceContentBO();
                    productServiceIntroduceContentDTO.setKey(jdhSkuServiceNoticeDto.getTitle());
                    productServiceIntroduceContentDTO.setContent(jdhSkuServiceNoticeDto.getContent());
                    productServiceIntroduceContentDTOS.add(productServiceIntroduceContentDTO);
                }
                productServiceIntroduceDTO.setIntroduceContent(productServiceIntroduceContentDTOS);
                //服务须知信息
                productInfo.setServiceIntroduce(productServiceIntroduceDTO);
            }
        }


        //服务范围信息地址信息
        ProductServiceAreaBO productServiceAreaDTO = new ProductServiceAreaBO();
        productServiceAreaDTO.setLabel("上门地址");
        productServiceAreaDTO.setIsLogin(isLogin);
        productInfo.setMainContent(Lists.newArrayList(productServiceAreaDTO));
        productInfo.setUserAddress(userAddressDetail);
        productInfo.setIsLogin(isLogin);
        //未登录直接返回
        if (!isLogin) {
            productServiceAreaDTO.setContent(ProductServiceAreaContentBO.builder().desc("请选择上门地址").build());
            productServiceAreaDTO.setFlag(false);
            productInfo.setBuyLimitType(ProductDetailBottomBannerEnum.NO_LOGIN.getBuyLimitType());
            return productInfo;
        }
        //没有地址直接返回
        if (Objects.isNull(userAddressDetail)) {
            productServiceAreaDTO.setContent(ProductServiceAreaContentBO.builder().desc("请选择上门地址").build());
            productServiceAreaDTO.setFlag(false);
            productInfo.setBuyLimitType(ProductDetailBottomBannerEnum.ADDRESS_NULL.getBuyLimitType());
            return productInfo;
        }
        productServiceAreaDTO.setAddressId(userAddressDetail.getAddressId());
        productServiceAreaDTO.setProvinceCode(Objects.nonNull(userAddressDetail.getProvinceId()) ? userAddressDetail.getProvinceId().toString() : null);
        productServiceAreaDTO.setCityCode(Objects.nonNull(userAddressDetail.getCityId()) ? userAddressDetail.getCityId().toString() : null);
        productServiceAreaDTO.setAreaCode(Objects.nonNull(userAddressDetail.getCountyId()) ? userAddressDetail.getCountyId().toString() : null);
        productServiceAreaDTO.setContent(ProductServiceAreaContentBO.builder().desc(userAddressDetail.getFullAddress()).addressId(userAddressDetail.getAddressId()).build());
        //没有找到对应的服务站直接返回
        if (noStation) {
//            productServiceAreaDTO.setContent(ProductServiceAreaContentBO.builder().desc("请选择上门地址").build());
            productServiceAreaDTO.setFlag(false);
            productInfo.setBuyLimitType(ProductDetailBottomBannerEnum.NO_TESTING_SERVICE.getBuyLimitType());
            return productInfo;
        }
        // 禁用 该商品已经爆单，暂不支持购买
        if (jdhSkuBo.getSaleStatus() == 0){
            productServiceAreaDTO.setFlag(false);
            productInfo.setBuyLimitType(ProductDetailBottomBannerEnum.TUBE.getBuyLimitType());
            return productInfo;
        }
        productServiceAreaDTO.setFlag(true);
        return productInfo;
    }

    /**
     * 获取商品评价信息
     * @param skuNo
     * @return
     */
    @Override
    @LogAndUmp(jKey = "com.jdh.o2oservice.core.domain.product.service.impl.ProductDomainServiceImpl.getEvaluateInfo", errorReturnJsfResult = false)
    public EvaluateInfoBO getEvaluateInfo(String skuNo) {
        //如果是护士上门屏蔽评价楼层
       /* if (duccConfig.getNursesHomeSkuList().contains(skuNo)) {
            log.info("QuickCheckServiceImpl getCommentPageList 护士上门流程,屏蔽评价楼层");
            return null;
        }*/
        //非呼吸道12联，屏蔽评价楼层
      /*  if (!Objects.equals(skuNo, "100077601060")) {
            log.info("QuickCheckServiceImpl getCommentPageList 非呼吸道12联，屏蔽评价楼层");
            return null;
        }*/
        CommentPageParam commentPageParam=new CommentPageParam();
        commentPageParam.setPageSize(1);
        commentPageParam.setSkuNo(skuNo);
        String commentListResult = skuInfoRpc.getCommentPageList(commentPageParam);
        if(!StringUtil.isBlank(commentListResult)){
            CommentUgcListResult commentUgcListResult = JsonUtil.parseObject(commentListResult, CommentUgcListResult.class);
            log.info("ProductDomainServiceImpl.getEvaluateInfo.commentUgcListResult={}",JsonUtil.toJSONString(commentUgcListResult));
            if(Objects.nonNull(commentUgcListResult)&&Objects.nonNull(commentUgcListResult.getSummary())
                    &&StringUtils.isNotBlank(commentUgcListResult.getSummary().getCommentCountStr())&& !commentUgcListResult.getSummary().getCommentCountStr().equals("0")&&
                    CollectionUtils.isNotEmpty(commentUgcListResult.getComments())){
                EvaluateInfoBO evaluateInfoBO = buildEvaluateInfoDTO(commentUgcListResult);
                evaluateInfoBO.setPageNum(commentPageParam.getPageNum());
                return evaluateInfoBO;
            }
        }
        return null;
    }

    @Override
    public SkuQuesttion4Result getSkuQuestionList(String uuid,String userPin,String skuNo) {
        QueryQuesttionParamBO queryQuesttionParamBO = new QueryQuesttionParamBO();
        SiteBo siteBo = new SiteBo();
        queryQuesttionParamBO.setSite(siteBo);
        queryQuesttionParamBO.setProductId(skuNo);
        queryQuesttionParamBO.setLandPin(userPin);
        queryQuesttionParamBO.setUuid(uuid);
        String questionResult = skuInfoRpc.getPageQuestionList(JsonUtil.toJSONString(queryQuesttionParamBO));
        if(StringUtils.isNotBlank(questionResult)){
            SkuQuesttionResultBO skuQuesttionResultBO = JsonUtil.parseObject(questionResult, SkuQuesttionResultBO.class);
            if(Objects.nonNull(skuQuesttionResultBO) && CommonConstant.ZERO_STR.equals(skuQuesttionResultBO.getResultCode())){
                return dealAnswer(skuQuesttionResultBO);
            }
        }
        return null;
    }

    /**
     *
     * @param skuQuesttionResultBO
     * @return
     */
    private SkuQuesttion4Result dealAnswer(SkuQuesttionResultBO skuQuesttionResultBO){
        SkuQuesttionBO result = skuQuesttionResultBO.getResult();
        List<QuesttionBO> questionList = result.getQuestionList();
        SkuQuesttion4Result skuQuesttion4Result = new SkuQuesttion4Result();
        int answerQuestion = 0;
        skuQuesttion4Result.setShowQuestionBox(Boolean.TRUE);
        if(CollectionUtils.isNotEmpty(questionList)){
            for(QuesttionBO questtionBO : questionList){
                List<AnswerBO> answerList = questtionBO.getAnswerList();
                if(CollectionUtils.isNotEmpty(answerList)){
                    answerQuestion ++;
                    questtionBO.setAnswerNum(answerList.size());
                }
            }
            questionList = questionList.stream().filter(item -> item.getAnswerNum() > CommonConstant.ZERO).collect(Collectors.toList());
            questionList = questionList.stream().sorted(Comparator.comparing(QuesttionBO::getAnswerNum).reversed()).collect(Collectors.toList());
            if(questionList.size() > CommonConstant.SIX){
                questionList = questionList.subList(0,6);
            }
            skuQuesttion4Result.setQuestionDetailList(questionList);
            if(answerQuestion >= CommonConstant.SIX){
                skuQuesttion4Result.setShowQuestionBox(Boolean.FALSE);
            }
        }
        return skuQuesttion4Result;
    }

    /**
     *
     * @param commentUgcListResult
     * @return
     */
    private EvaluateInfoBO buildEvaluateInfoDTO(CommentUgcListResult commentUgcListResult){
        EvaluateInfoBO evaluateInfoBO = new EvaluateInfoBO();
        CommentSummaryVO summary = commentUgcListResult.getSummary();
        evaluateInfoBO.setCount(summary.getCommentCountStr());
        evaluateInfoBO.setPositiveComment(summary.getGoodRateShow());
        evaluateInfoBO.setMaxPage(commentUgcListResult.getMaxPage());

        List<TagStatisticsBO> commentSummaryList = new ArrayList<>();
        TagStatisticsBO allTag = new TagStatisticsBO("全部",summary.getCommentCountStr(),CommonConstant.ZERO);
        commentSummaryList.add(allTag);
        if(  Objects.equals(CommonConstant.ONE,duccConfig.getQuickCheckCommentTag())){
            TagStatisticsBO goodTag = new TagStatisticsBO("好评",summary.getGoodCountStr(),CommonConstant.THREE);
            TagStatisticsBO generalTag = new TagStatisticsBO("中评",summary.getGeneralCountStr(),CommonConstant.TWO);
            TagStatisticsBO poorTag = new TagStatisticsBO("差评",summary.getPoorCountStr(),CommonConstant.ONE);
            commentSummaryList.add(goodTag);
            commentSummaryList.add(generalTag);
            commentSummaryList.add(poorTag);
        }
        evaluateInfoBO.setCommentSummaryList(commentSummaryList);

        List<TagStatisticsUgcVO> tagStatistics = commentUgcListResult.getTagStatistics();
        CommentExt ext = commentUgcListResult.getExt();
        List<TagStatisticsBO> commentTags = new ArrayList<>();
        if(Objects.nonNull(ext)){
            evaluateInfoBO.setSoType(ext.getSoType());
            Integer imageListCount = ext.getImageListCount();
            if(imageListCount > 0){
                TagStatisticsBO tagStatisticsBO = new TagStatisticsBO();
                tagStatisticsBO.setScore(CommonConstant.FOUR);
                tagStatisticsBO.setLabel("有图评价");
                tagStatisticsBO.setCount(String.valueOf(imageListCount));
                commentTags.add(tagStatisticsBO);
            }
        }
        if(CollectionUtil.isNotEmpty(tagStatistics)){
            tagStatistics.forEach(tag -> {
                TagStatisticsBO tagStatisticsBO = new TagStatisticsBO();
                tagStatisticsBO.setLabelId(tag.getId());
                tagStatisticsBO.setLabel(tag.getName());
                tagStatisticsBO.setCount(String.valueOf(tag.getCount()));
                commentTags.add(tagStatisticsBO);
            });
            evaluateInfoBO.setCommentTags(commentTags);
        }
        List<CommentUgcVO> comments = commentUgcListResult.getComments();
        if(CollectionUtil.isNotEmpty(comments)){
            String commentGuidList = duccConfig.getCommentGuidList();
            List<CommentUgcBO> list = new ArrayList<>();
            comments.forEach(commentUgcVO -> {
                if(com.jd.medicine.base.common.util.StringUtil.isBlank(commentGuidList) || !commentGuidList.contains(commentUgcVO.getGuid())){
                    CommentUgcBO commentUgcBO = new CommentUgcBO();
                    commentUgcBO.setGuid(commentUgcVO.getGuid());
                    commentUgcBO.setContent(commentUgcVO.getContent());
                    commentUgcBO.setDate(DateUtil.formatDate(commentUgcVO.getCreationTime(),CommonConstant.YMD));
                    commentUgcBO.setPortrait("https://" + commentUgcVO.getUserImageUrl());
                    commentUgcBO.setStar(commentUgcVO.getScore());
                    commentUgcBO.setUserName(commentUgcVO.getNickName());
                    List<ImageVO> images = commentUgcVO.getImages();
                    if(CollectionUtil.isNotEmpty(images)){
                        List<String> imageList = images.stream().map(ImageVO::getImgUrl).collect(Collectors.toList());
                        commentUgcBO.setPicList(imageList);
                    }
                    list.add(commentUgcBO);
                }
            });
            evaluateInfoBO.setList(list);
        }
        return evaluateInfoBO;
    }
}