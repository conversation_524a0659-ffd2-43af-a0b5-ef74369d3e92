<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>o2o-service-product-domain</artifactId>
        <groupId>com.jdh.o2oservice</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>o2o-service-product-core</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-base</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-product-core-ext</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.jdh.o2oservice</groupId>
            <artifactId>o2o-service-support-core</artifactId>
            <version>${revision}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--        GIS-->
        <dependency>
            <groupId>com.jd.lbs.geofencing</groupId>
            <artifactId>geofencing-api</artifactId>
            <version>2.6.6-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!-- 限购中台 -->
        <dependency>
            <groupId>com.jd.limitbuy</groupId>
            <artifactId>limitbuy.soa.order-client</artifactId>
            <version>0.1.74</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>13.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
