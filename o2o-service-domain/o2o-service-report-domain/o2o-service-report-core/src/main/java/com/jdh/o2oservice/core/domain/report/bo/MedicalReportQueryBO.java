package com.jdh.o2oservice.core.domain.report.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * @Description: 报告查询BO
 * @Author: wangpengfei144
 * @Date: 2024/10/9
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MedicalReportQueryBO {
    /**
     * 履约单
     */
    private Long promiseId;
    /**
     * 报告ID
     */
    private Long id;

    /**
     * 包含要查询的报告ID集合。
     */
    private Set<Long> ids;

    /**
     * 患者ID，用于关联报告和患者信息。
     */
    private Long patientId;
    /**
     * 用户的个人识别码，用于验证用户身份。
     */
    private String userPin;
    /**
     * 检测单ID
     */
    private Set<Long> medicalPromiseIds;

    /**
     * 报告中心ID
     */
    private String reportCenterId;
    /**
     * 文件的MD5值，用于验证文件完整性。
     */
    private String fileMd5;

    private Boolean orderByAsc;
    /**
     *
     */
    private Long offset ;
    /**
     *
     */
    private Long offsetEnd;
}

