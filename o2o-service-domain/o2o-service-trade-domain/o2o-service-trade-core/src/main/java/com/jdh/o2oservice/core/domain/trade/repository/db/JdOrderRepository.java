package com.jdh.o2oservice.core.domain.trade.repository.db;

import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.core.domain.trade.model.JdOrder;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderExt;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderIdentifier;
import com.jdh.o2oservice.core.domain.trade.model.JdOrderItem;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @InterfaceName:JdOrderRepository
 * @Description: 京东订单仓储
 * @Author: yaoqinghai
 * @Date: 2024/1/9 14:50
 * @Vserion: 1.0
 **/
public interface JdOrderRepository extends Repository<JdOrder, JdOrderIdentifier> {


     JdOrder findFull(Long orderId, String userPin);

        /**
         * 写入订单信息
         *
         * @param jdOrder
         * @return
         */
    @Override
    int save(JdOrder jdOrder);

    /**
     * 查询订单详情
     *
     * @param jdOrder
     * @return
     */
    JdOrder findOrderDetail(JdOrder jdOrder);

    /**
     * 查询订单详情
     *
     * @param jdOrder
     * @return
     */
    List<JdOrder> findOrderListByParentId(JdOrder jdOrder);

    /**
     * 根据订单号查询父子单清单
     *
     * @param id
     * @return
     */
    List<JdOrder> findOrderListByOrderId(@NotNull JdOrderIdentifier id);

    /**
     * 更新订单的身份信息
     *
     * @param jdOrder
     * @return
     */
    int updateOrderByOrderId(JdOrder jdOrder);

    /**
     * @param jdOrder
     * @return
     */
    int updateNoSplitOrder(JdOrder jdOrder);

    /**
     * @param jdOrder
     * @return
     */
    int saveSplitOrder(JdOrder jdOrder, JdOrder parentIdOrder, JdOrderItem jdOrderItem);

    /**
     * 取消订单支付
     *
     * @param jdOrder
     * @return
     */
    int cancalOrderByOrderId(JdOrder jdOrder);

    /**
     * 根据售卖渠道的外部订单ID，查询对应的唯一父订单
     *
     * @return
     */
    Long queryParentOrderIdByPartnerSourceOrderId(String partnerSourceOrderId, String UserPin);

    /**
     * 更新订单状态信息
     *
     * @param jdOrder
     * @return
     */
    int updateOrderStatusByOrderId(JdOrder jdOrder);

    /**
     * 更新订单扩展信息
     *
     * @param jdOrderExtList
     * @return
     */
    int updateOrderExt(List<JdOrderExt> jdOrderExtList);

    /**
     * 根据订单号查询父子单清单
     * @param orderIds
     * @return
     */
    List<JdOrder> findOrdersByList(List<Long> orderIds);

    /**
     * 根据外部订单号查询订单
     * @param jdOrder
     * @return
     */
    List<JdOrder> findOrderListByPartnerSource(JdOrder jdOrder);
}
