package com.jdh.o2oservice.core.domain.trade.enums;

import lombok.Getter;

/**
 * JD订单费用类型列举
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Getter
public enum JdOrderFeeTypeEnum {

    /**
     * 费项
     */
    TIME_PERIOD_APPOINTMENT_TYPE_IMMEDIATELY(11,FeeAggregateTypeEnum.TIME_PERIOD_FEE,"立即上门"),
    TIME_PERIOD_TIME_TYPE_HOLIDAY(12,FeeAggregateTypeEnum.TIME_PERIOD_FEE,"节假日"),

    HOME_VISIT(31,FeeAggregateTypeEnum.HOME_VISIT_FEE,"上门费"),

    UPGRADE_ANGEL(40,FeeAggregateTypeEnum.DYNAMIC_FEE,"升级护士费"),

    DYNAMIC(42,FeeAggregateTypeEnum.DYNAMIC_FEE,"动态调整费"),

    NIGHT_SERVICE_FEE(43,FeeAggregateTypeEnum.TIME_PERIOD_FEE,"夜间服务费"),

    PEAK_SERVICE_FEE(44,FeeAggregateTypeEnum.TIME_PERIOD_FEE,"高峰时段服务费"),

    ;

    /** */
    JdOrderFeeTypeEnum(Integer type,FeeAggregateTypeEnum aggregateTypeEnum,String code) {
        this.type = type;
        this.aggregateTypeEnum = aggregateTypeEnum;
        this.code = code;
    }

    /** */
    private Integer type;
    /** */
    private FeeAggregateTypeEnum aggregateTypeEnum;
    /** */
    private String code;

}
