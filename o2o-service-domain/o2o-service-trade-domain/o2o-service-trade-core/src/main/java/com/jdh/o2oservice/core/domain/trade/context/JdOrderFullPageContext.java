package com.jdh.o2oservice.core.domain.trade.context;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> limeng363
 * @Date : 2024-05-09 15:38
 * @Desc :
 */
@Data
public class JdOrderFullPageContext extends AbstractPageQuery {
    /**
     * 订单来源
     */
    private String partnerSource;

    /** 垂直业务身份编码 */
    private String verticalCode;

    /** 服务类型 */
    private String serviceType;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 服务单号（检测单id）
     */
    private String medicalPromiseId;
    /**
     * 样本编号
     */
    private String specimenCode;
    /**
     * 是否上传服务记录
     */
    private String taskBizExtStatus;
    /**
     * 用户pin
     */
    private String userPin;
    /**
     * 服务状态
     */
    private String commonStatus;
    /**
     * 护士姓名
     */
    private String angelName;
    /**
     * 检测人电话
     */
    private String userPhone;
    /**
     * 实验室名称
     */
    private String laboratoryStationName;
    /**
     * 订单创建时间-开始
     */
    private Date orderCreateTimeStart;
    /**
     * 订单创建时间-结束
     */
    private Date orderCreateTimeEnd;
    /**
     * 预约时间-开始
     */
    private Date appointmentStartTime;
    /**
     * 预约时间-结束
     */
    private Date appointmentEndTime;

    /**
     * 收样时间-开始
     */
    private Date checkStartTime;
    /**
     * 收样时间-结束
     */
    private Date checkEndTime;

    /**
     * 履约单哈
     */
    private String promiseId;

    /**
     * 是否包含加项商品
     */
    private Integer includeAddSku;

    /**
     * 商品类型
     */
    private Integer wareType;

    /**
     * 订单类型
     */
    private String orderType;

    private String stationId;//实验室id

    private List<Integer> medicalPromiseStatus;//检测单状态

    /**
     * 导出场景值
     */
    private String exportScene;

    private String outShipId;//
}
