package com.jdh.o2oservice.core.domain.trade.vo;

import lombok.Data;

import java.util.List;

/**
 * @ClassName:JdOrderExtendVo
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/1/27 19:02
 * @Vserion: 1.0
 **/
@Data
public class JdOrderExtendVo {

    /**
     * 草稿id
     */
    private String draftId;

    /**
     * 消费医疗标
     */
    private String xfyl;

    /**
     * 预售标识 0-非预售 1-预售
     */
    private String presale;

    /**
     * 订单商品退款明细
     */
    private List<SkuRefundVo> skuRefundVoList;
    /**
     * 下单人手机号
     */
    private String orderPhone;

    /**
     * 下单备注
     */
    private String orderRemark;
    /**
     * 商品服务类型
     */
    private Integer skuServiceType;

    /**
     * 商品类型（"0":一般商品 "1":延保商品 "2":赠品结构（可能是赠品或者附件））
     */
    private Integer wareType;

    /**
     * 原始订单id，满赠订单查询原始订单
     * 由于VTP历史原因，订单表中父订单id设置为了0，这里将父订单id写入扩展字段
     */
    private Long originParentOrderId;

    /**
     * 订单下商品关系
     */
    private List<JdSkuRelationInfoVo> jdSkuRelationInfoVoList;

    /**
     * 渠道
     */
    private String channelName;

    /**
     * 服务人员升级是否选中 true-选中 false-未选中
     */
    private Boolean serviceUpgradeSelected;

    /**
     * partnerSource下的子渠道号
     */
    private String saleChannelId;

}
