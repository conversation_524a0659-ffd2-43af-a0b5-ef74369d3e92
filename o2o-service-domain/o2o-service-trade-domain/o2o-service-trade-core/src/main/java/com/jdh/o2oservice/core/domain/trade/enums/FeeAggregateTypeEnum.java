package com.jdh.o2oservice.core.domain.trade.enums;

import lombok.Getter;

/**
 * JD订单费用类型列举
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
@Getter
public enum FeeAggregateTypeEnum {

    /**
     * 算费类型
     */
    TIME_PERIOD_FEE(3901,39011,1,"时段费"),
    HOME_VISIT_FEE(3901,39012,2,"上门费"),
    DYNAMIC_FEE(3901,39013,3,"动态调整费"),
//    UPGRADE_ANGEL_FEE(3901,39014,4,"升级护士费"),
    ;

    /** */
    FeeAggregateTypeEnum(Integer type, Integer subType, Integer showIndex,String code) {
        this.type = type;
        this.subType = subType;
        this.code = code;
        this.showIndex = showIndex;
    }

    /** */
    private Integer type;
    /** */
    private Integer subType;
    /** */
    private String code;
    /** */
    private Integer showIndex;

    /**
     * findBySubType
     *
     * @param subType 子类型
     * @return {@link FeeAggregateTypeEnum}
     */
    public static FeeAggregateTypeEnum findBySubType(Integer subType){
        for (FeeAggregateTypeEnum value : FeeAggregateTypeEnum.values()) {
            if(value.getSubType().equals(subType)){
                return value;
            }
        }
        return null;
    }

}
