package com.jdh.o2oservice.core.domain.angel.repository.query;

import com.jdh.o2oservice.common.result.request.AbstractPageQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @ClassName:AngelStationPageQuery
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/27 23:06
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AngelStationPageQuery extends AbstractPageQuery {

    /**
     * 服务站id
     */
    private Long angelStationId;

    /**
     * 围栏id
     */
    private String fenceId;

    /**
     * 服务站名称
     */
    private String angelStationName;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     *区编码
     */
    private String districtCode;

    /**
     * 服务站状态
     */
    private Integer angelStationStatus;

    /**
     * 服务站长
     */
    private String angelStationMaster;

    /**
     * 业务模式 1护士-全职 2护士-兼职
     */
    private Integer stationModeType;

    /**
     * 实验室id
     */
    private String stationId;

    /**
     * 查询updateTime的开始时间
     */
    private Date queryUpdateTimeStart;

    /**
     * 查询updateTime的结束时间
     */
    private Date queryUpdateTimeEnd;

    /**
     * 服务站ID list
     */
    List<Long> angelStationIdList;
}
