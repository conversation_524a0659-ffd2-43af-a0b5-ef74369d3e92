package com.jdh.o2oservice.core.domain.medpromise.repository.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName DispatchRepQuery
 * @Description
 * <AUTHOR>
 * @Date 2024/4/21 11:46
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalPromiseRepQuery {

    /**
     * 检测单ID
     */
    private Long medicalPromiseId;

    /**
     * 履约单ID
     */
    private Long promiseId;
    /**
     * 条码信息
     */
    private String specimenCode;

    /**
     * 供应商ID
     */
    private Long providerId;
    /** 用户唯一ID */
    private Long promisePatientId;
    /** 服务ID */
    private Long serviceId;

    /** 服务地点id */
    private String stationId;

}