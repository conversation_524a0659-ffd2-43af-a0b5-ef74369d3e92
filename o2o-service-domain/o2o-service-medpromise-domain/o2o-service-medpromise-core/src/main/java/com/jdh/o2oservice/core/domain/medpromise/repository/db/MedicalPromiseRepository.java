package com.jdh.o2oservice.core.domain.medpromise.repository.db;

import com.jdh.o2oservice.base.model.Repository;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromise;
import com.jdh.o2oservice.core.domain.medpromise.model.MedicalPromiseIdentifier;
import com.jdh.o2oservice.core.domain.medpromise.model.UpdateMedicalPromiseStation;
import com.jdh.o2oservice.core.domain.medpromise.query.MedicalPromiseListQuery;
import com.jdh.o2oservice.core.domain.medpromise.repository.query.MedicalPromiseRepQuery;

import java.util.Date;
import java.util.List;

/**
 * @Description: 检测单仓储层
 * @Interface: MedicalPromiseRepository
 * @Author: wangpengfei144
 * @Date: 2024/4/16
 */
public interface MedicalPromiseRepository extends Repository<MedicalPromise, MedicalPromiseIdentifier> {

    /**
     * findMedicalPromise
     *
     * @param query 查询
     * @return {@link MedicalPromise}
     */
    MedicalPromise findMedicalPromise(MedicalPromiseRepQuery query);

    /**
     * 查询检测单 分页
     * @param medicalPromiseListQuery
     * @return
     */
    PageDto<MedicalPromise> queryMedicalPromisePage(MedicalPromiseListQuery medicalPromiseListQuery);

    /**
     * 查询检测单 不分页
     * @param medicalPromiseListQuery
     * @return
     */
    List<MedicalPromise> queryMedicalPromiseList(MedicalPromiseListQuery medicalPromiseListQuery);

    /**
     * 批量更新
     * @param medicalPromises
     * @return
     */
    Integer updateFreezeBatch(List<MedicalPromise> medicalPromises);

    /**
     * 更新结算状态
     * @param medicalPromise
     * @return
     */
    Integer updateSettleSatus(MedicalPromise medicalPromise);
    /**
     * 批量更新
     * @param medicalPromises
     * @return
     */
    Integer updateBatch(List<MedicalPromise> medicalPromises);


    /**
     *
     * @param medicalPromises
     * @return
     */
    Integer updateStoreDispatch(List<MedicalPromise> medicalPromises);

    /**
     * 根据患者和服务查询检测单
     * @param medicalPromiseListQuery
     * @return
     */
    List<MedicalPromise> listByPatientAndService(MedicalPromiseListQuery medicalPromiseListQuery);

    /**
     * 更新序号
     * @param medicalPromise
     * @return
     */
    Boolean updateSerialNum(MedicalPromise medicalPromise);

    Boolean updateInit(MedicalPromise medicalPromise);


    String generalSerialNum(Long medicalPromiseId, Date checkDate);




    /**
     * 根据promisePatientId和sergiceItemId来新增或者更新MedicalPromise信息，返回medicalPromiseId
     * @param aggregate
     * @return
     */
    Long insertOrUpdateByPromisePatientAndServiceItem(MedicalPromise aggregate);

    /**
     * 根据ID删除检测单
     * @param medicalPromiseIds
     * @return
     */
    Integer deleteByMedicalPromiseIds(List<Long> medicalPromiseIds,Long mergeId);

    /**
     * 更新检测单实验室信息
     * @param updateMedicalPromiseStation
     * @return
     */
    Boolean updateMedicalPromiseStation(UpdateMedicalPromiseStation updateMedicalPromiseStation);

    /**
     * 查询检测单 不分页
     * @param medicalPromiseListQuery
     * @return
     */
    List<MedicalPromise> queryMedicalPromiseNoSettleList(MedicalPromiseListQuery medicalPromiseListQuery);

    /**
     *
     * @param specimenCodes
     * @return
     */
    Boolean existSpecimenCode(List<String> specimenCodes);

    /**
     * 查询总量
     * @param medicalPromiseListQuery
     * @return
     */
    Integer count(MedicalPromiseListQuery medicalPromiseListQuery);
}
