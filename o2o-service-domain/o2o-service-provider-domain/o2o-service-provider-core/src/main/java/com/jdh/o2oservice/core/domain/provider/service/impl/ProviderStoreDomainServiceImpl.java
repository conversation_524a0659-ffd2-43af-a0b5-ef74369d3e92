package com.jdh.o2oservice.core.domain.provider.service.impl;

import com.github.pagehelper.PageInfo;
import com.jd.fastjson.JSON;
import com.jd.health.xfyl.merchant.export.dto.store.StoreInfoDTO;
import com.jd.health.xfyl.merchant.export.param.supplier.SupplierMerchantStoreInfoParam;
import com.jd.health.xfyl.merchant.export.param.supplier.store.StoreInfoQueryParam;
import com.jd.health.xfyl.merchant.export.service.XfylMerchantAppointApiExportService;
import com.jd.health.xfyl.open.export.param.InStoreThirdStoreParam;
import com.jd.jim.cli.Cluster;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.enums.DockingTypeEnum;
import com.jdh.o2oservice.base.event.EventCoordinator;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.DynamicErrorCode;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.common.enums.MedicalPromiseStatusEnum;
import com.jdh.o2oservice.common.result.response.PageDto;
import com.jdh.o2oservice.core.domain.provider.bo.ListStoreScheduleBO;
import com.jdh.o2oservice.core.domain.provider.bo.StoreInfoBo;
import com.jdh.o2oservice.core.domain.provider.bo.XfylAppointDateBO;
import com.jdh.o2oservice.core.domain.provider.context.AppointmentMigrationContext;
import com.jdh.o2oservice.core.domain.provider.context.ProviderStoreContext;
import com.jdh.o2oservice.core.domain.provider.context.UpdateMerchantStoreBySelectContext;
import com.jdh.o2oservice.core.domain.provider.converter.JdhProviderStoreConverter;
import com.jdh.o2oservice.core.domain.provider.model.JdhStationServiceItemRel;
import com.jdh.o2oservice.core.domain.provider.model.Provider;
import com.jdh.o2oservice.core.domain.provider.query.ProviderStoreDetailQuery;
import com.jdh.o2oservice.core.domain.provider.query.QueryMerchantStoreListByParamQuery;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderRepository;
import com.jdh.o2oservice.core.domain.provider.repository.db.ProviderStoreRepository;
import com.jdh.o2oservice.core.domain.provider.repository.query.StoreDateScheduleQuery;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderMedicalPromiseRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreExportServiceRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.ProviderStoreScheduleRpc;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.AppointmentApiMerchantParam;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.MedicalPromiseFromEsBo;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.MedicalPromiseListFromEsParam;
import com.jdh.o2oservice.core.domain.provider.rpc.bo.UpdateMedicalPromiseStationParam;
import com.jdh.o2oservice.core.domain.provider.service.ProviderStoreDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName ProviderStoreDomainServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/1/2 16:21
 **/
@Service
@Slf4j
public class ProviderStoreDomainServiceImpl implements ProviderStoreDomainService {

    /**
     * providerStoreDateScheduleRpc
     */
    @Autowired
    private ProviderStoreScheduleRpc providerStoreScheduleRpc;

    @Resource
    private ProviderStoreExportServiceRpc providerStoreExportServiceRpc;

    @Resource
    private ProviderStoreRepository providerStoreRepository;

    @Resource
    private ProviderRepository providerRepository;

    @Resource
    private ProviderMedicalPromiseRpc providerMedicalPromiseRpc;

    @Resource
    private XfylMerchantAppointApiExportService xfylMerchantAppointApiExportService;

    @Resource
    private Cluster jimClient;

    @Resource
    private EventCoordinator eventCoordinator;





    /**
     * 查询门店排期列表
     * @param query
     * @return
     */
    @Override
    public List<XfylAppointDateBO> queryStoreDateScheduleList(StoreDateScheduleQuery query) {
        if (Objects.equals(query.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.POP_LOC.getCode())){
            log.info("ProviderStoreDomainServiceImpl -> queryStoreDateScheduleList pop查排期流程");
            ListStoreScheduleBO storeScheduleBO = ListStoreScheduleBO.builder()
                    .skuNo(query.getServiceId())
                    .storeId(query.getStoreId())
                    .build();
            return providerStoreScheduleRpc.queryPopStoreSchedule(storeScheduleBO);
        }
        else if (Objects.equals(query.getVerticalBusiness().getBusinessModeCode(), BusinessModeEnum.YK.getCode())){
            log.info("ProviderStoreDomainServiceImpl -> queryStoreDateScheduleList 一卡万店查排期流程");
            return null;
        }
        return null;
    }

    /**
     * 创建实验室
     * @param providerStoreContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean addQuickMerchantStore(ProviderStoreContext providerStoreContext) {
        InStoreThirdStoreParam inStoreThirdStoreParam = JdhProviderStoreConverter.INSTANCE.toPushStoreInfo(providerStoreContext);
        return providerStoreExportServiceRpc.pushStoreInfo(inStoreThirdStoreParam);
    }

    /**
     * 查询实验室详情
     * @param providerStoreDetailQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public StoreInfoBo queryMerchantStoreDetailByParam(ProviderStoreDetailQuery providerStoreDetailQuery) {
        return providerStoreExportServiceRpc.queryByStoreId(providerStoreDetailQuery.getJdStoreId());
    }

    /**
     * 编辑实验室
     * @param providerStoreContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean updateQuickMerchantStore(ProviderStoreContext providerStoreContext) {
        InStoreThirdStoreParam inStoreThirdStoreParam = JdhProviderStoreConverter.INSTANCE.toPushStoreInfo(providerStoreContext);
        return providerStoreExportServiceRpc.updateMerchantStore(inStoreThirdStoreParam);
    }

    /**
     * 查询实验室列表分页
     * @param queryMerchantStoreListByParamQuery
     * @return
     */
    @Override
    @LogAndAlarm
    public PageDto<StoreInfoBo> queryMerchantStoreListByParam(QueryMerchantStoreListByParamQuery queryMerchantStoreListByParamQuery) {
        StoreInfoQueryParam storeInfoQueryParam = JdhProviderStoreConverter.INSTANCE.toStoreInfoQueryParam(queryMerchantStoreListByParamQuery);
        PageInfo<StoreInfoDTO> pageInfo = providerStoreExportServiceRpc.queryMerchantStoreListByParam(storeInfoQueryParam);
        PageDto<StoreInfoBo> pageDto = new PageDto<>();
        pageDto.setPageSize(pageInfo.getPageSize());
        pageDto.setPageNum(pageInfo.getPageNum());
        pageDto.setList(JdhProviderStoreConverter.INSTANCE.merchantDtoToBo(pageInfo.getList()));
        pageDto.setTotalCount(pageInfo.getTotal());
        return pageDto;
    }

    /**
     * 更新实验室迁移配置
     * @param updateMerchantStoreBySelectContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean updateMerchantStoreBySelect(UpdateMerchantStoreBySelectContext updateMerchantStoreBySelectContext) {
        SupplierMerchantStoreInfoParam supplierMerchantStoreInfoParam = JdhProviderStoreConverter.INSTANCE.toSupplierMerchantStoreInfoParam(updateMerchantStoreBySelectContext);
        return providerStoreExportServiceRpc.updateMerchantStoreBySelect(supplierMerchantStoreInfoParam);
    }

    /**
     * 实验室迁移
     * @param appointmentMigrationContext
     * @return
     */
    @Override
    @LogAndAlarm
    public Boolean appointmentMigration(AppointmentMigrationContext appointmentMigrationContext) {

        //查询原实验室检测项目
        JdhStationServiceItemRel jdhStationServiceItemRel = new JdhStationServiceItemRel();
        jdhStationServiceItemRel.setStationId(appointmentMigrationContext.getFromJdStoreId());
        List<JdhStationServiceItemRel> fromJdhStationServiceItemRels = providerStoreRepository.queryStationServiceItemList(jdhStationServiceItemRel);
        if(CollectionUtils.isEmpty(fromJdhStationServiceItemRels)){
            log.error("原实验室检测项目为空,逻辑终止");
            throw new BusinessException(new DynamicErrorCode("-1","原实验室检测项目为空"));
        }

        //查询目标实验室检测项目
        JdhStationServiceItemRel targetJdhStationServiceItemRel = new JdhStationServiceItemRel();
        targetJdhStationServiceItemRel.setStationId(appointmentMigrationContext.getTargetJdStoreId());
        List<JdhStationServiceItemRel> targetJdhStationServiceItemRels = providerStoreRepository.queryStationServiceItemList(targetJdhStationServiceItemRel);
        if(CollectionUtils.isEmpty(targetJdhStationServiceItemRels)){
            log.error("目标实验室检测项目为空,逻辑终止");
            throw new BusinessException(new DynamicErrorCode("-1","目标实验室检测项目为空"));
        }


        Set<Long> targetServiceItemIds = targetJdhStationServiceItemRels.stream().map(JdhStationServiceItemRel::getServiceItemId).collect(Collectors.toSet());
        Set<Long> fromServiceItemIds = fromJdhStationServiceItemRels.stream().map(JdhStationServiceItemRel::getServiceItemId).collect(Collectors.toSet());

        //判断新实验室技能包含原实验室技能
        if(!targetServiceItemIds.containsAll(fromServiceItemIds)){
            log.error("目标实验室不支持原实验室所有检测项目,逻辑终止");
            throw new BusinessException(new DynamicErrorCode("-1","目标实验室检测项目为空"));
        }

        //保存实验室迁移配置
        SupplierMerchantStoreInfoParam supplierMerchantStoreInfoParam = JdhProviderStoreConverter.INSTANCE.toSupplierMerchantStoreInfoParam(appointmentMigrationContext);
        Boolean result = providerStoreExportServiceRpc.updateMerchantStoreBySelect(supplierMerchantStoreInfoParam);
        if(!result){
            log.error("实验室迁移配置保存失败");
            throw new BusinessException(new DynamicErrorCode("-1","实验室迁移配置保存失败"));
        }

        return true;
    }
}