package com.jdh.o2oservice.core.domain.provider.context;

import lombok.Data;

import java.util.List;

/**
 * @ClassName ProviderStoreContext
 * @Description
 * <AUTHOR>
 * @Date 2025/1/8 3:06 PM
 * @Version 1.0
 **/
@Data
public class ProviderStoreContext {

    private Integer businessType;

    private String channelNo;//渠道id

    private String storeName;//实验室名称

    private String storeAddr;//实验室地址

    private String storePhone;//门店电话

    private Integer status;//1上架 2下架

    private String channelRuleCode;//天算渠道code

    private String contactName;//联系人姓名

    private String contactPhone;//联系人电话

    private String storeLng;//经度

    private String storeLat;//维度

    private Integer operateType;//1新增 2编辑

    private Integer dockingType;//1api接入 2端接入

    private String storeId;//门店主键id

    private String storeHours;//营业时间

    private String firstChannelNo;//端接入方式,绑定的虚拟商家

    private Integer reportSupport=1;//电子报告是否支持回传 1支持 0不支持

    private String jdStoreId;//京东实验室id

    private String changeChannelNo;//改成新的京东实验室id

    private Integer limitBuyStatus;//爆单状态 1 关闭  0开启

    /**
     * 资质图片列表
     */
    private List<Long> licenseFileIdList;

    /**
     * 实验室支持报告类型
     */
    private List<Integer> reportFormatList;

    /**
     * 使用的系统版本
     */
    private String useSystemVersion;
}
