package com.jdh.o2oservice.core.domain.support.basic.enums;

import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;

/**
 * 支撑域聚合类型，每个子域使用单独维护
 * <AUTHOR>
 * @date 2023-12-25-7:34 下午
 */
public enum SupportAggregateEnum implements AggregateCode {

    /** 依附服务聚合 */
    VIA_CONFIG(DomainEnum.BASE, "viaConfig"),
    VIA_TAB(DomainEnum.BASE, "viaTab"),

    /**
     * 患者聚合
     */
    PATIENT(DomainEnum.BASE, "patient"),

    /**
     * 字典
     */
    DICT(DomainEnum.BASE, "dict"),
    CATEGORY(DomainEnum.BASE, "category"),
    FILE(DomainEnum.BASE, "file"),
    FILE_EXPORT_CONFIG(DomainEnum.BASE, "fileExportConfig"),

    /**
     * 触达域
     */
    REACH(DomainEnum.BASE, "reach"),
    //触达模板
    REACH_TEMPLATE(DomainEnum.BASE, "reachTemplate"),
    //触达触发器
    REACH_TRIGGER(DomainEnum.BASE, "reachTrigger"),

    /**
     *
     */
    VERTICAL_BUSINESS(DomainEnum.BASE, "verticalBusiness"),

    /**
     * 用户反馈
     */
    USER_FEEDBACK(DomainEnum.BASE, "userFeedback"),

    /**
     * 数据字段变更
     */
    DB_FIELD_CHANGE(DomainEnum.BASE, "dbFieldChange"),
    ;

    /** */
    private DomainCode domain;
    /** */
    private String code;
    /** */
    SupportAggregateEnum(DomainCode domain, String code) {
        this.domain = domain;
        this.code = code;
    }

    /** */
    @Override
    public DomainCode getDomainCode() {
        return domain;
    }

    @Override
    public String getCode() {
        return code;
    }


}
