package com.jdh.o2oservice.core.domain.support.reach.bo;

import com.jdh.o2oservice.core.domain.support.basic.model.PhoneNumber;
import com.jdh.o2oservice.core.domain.support.reach.ext.dto.ReachUser;
import lombok.Data;

import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6 15:57
 */
@Data
public class ReachUserBO {
    /** */
    private String phone;
    /** */
    private String pin;
    private String userType;
    /**
     * 扩展参数
     */
    private Object extend;

    public String encryptPhone() {
        return PhoneNumber.encrypt(phone);
    }

    public String encryptPhoneUrlCode() {

        try {
            String str = PhoneNumber.encrypt(phone);
            return URLEncoder.encode(str, "UTF-8");
        }catch (Exception e){
            return null;
        }
    }

    public ReachUserBO(ReachUser user){
        this.userType = user.getUserType();
        this.phone = user.getPhone();
        this.pin = user.getPin();
        this.extend = user.getExtend();
    }

}
