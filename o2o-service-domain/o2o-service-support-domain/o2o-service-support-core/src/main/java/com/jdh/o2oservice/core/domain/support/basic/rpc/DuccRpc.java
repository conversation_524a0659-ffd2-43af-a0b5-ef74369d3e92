package com.jdh.o2oservice.core.domain.support.basic.rpc;


/**
 * @ClassName DuccRpc
 * @Description
 * <AUTHOR>
 * @Date 2024/1/16 21:55
 **/
public interface DuccRpc {

    /**
     *
     * @param configKey
     * @return
     */
    void updateDuccConfig(String configKey, String configContent);

    /**
     *
     * @param configKey
     * @return
     */
    String getDuccConfig(String configKey);

    /**
     *
     * @return
     */
    boolean releaseDuccConfig();
}