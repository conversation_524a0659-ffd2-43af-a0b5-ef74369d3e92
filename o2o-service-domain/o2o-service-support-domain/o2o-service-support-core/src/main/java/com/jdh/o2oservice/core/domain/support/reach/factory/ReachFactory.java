package com.jdh.o2oservice.core.domain.support.reach.factory;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.exception.ExceptionFactory;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportErrorCode;
import com.jdh.o2oservice.core.domain.support.basic.valueobject.DelayTime;
import com.jdh.o2oservice.core.domain.support.reach.bo.ReachUserBO;
import com.jdh.o2oservice.core.domain.support.reach.context.CreateReachTemplateContext;
import com.jdh.o2oservice.core.domain.support.reach.context.CreateReachTriggerContext;
import com.jdh.o2oservice.core.domain.support.reach.context.ExecuteReachTaskContext;
import com.jdh.o2oservice.core.domain.support.reach.context.ReachTemplateParamBO;
import com.jdh.o2oservice.core.domain.support.reach.enums.ReachTypeEnum;
import com.jdh.o2oservice.core.domain.support.reach.model.*;
import com.jdh.o2oservice.core.domain.support.reach.repository.db.JdhReachConfigRepository;
import com.jdh.o2oservice.core.domain.support.reach.repository.db.JdhReachTemplateRepository;
import com.jdh.o2oservice.core.domain.support.reach.valueobject.ReachMessageBizType;
import com.jdh.o2oservice.core.domain.support.reach.valueobject.ReachTriggerDelayStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;

/**
 * 触发领域工厂
 * <AUTHOR>
 * @date 2023-12-20-8:54 下午
 */
@Component
@Lazy
@Slf4j
public class ReachFactory{


    private static final String MESSAGE_BOX_APP_ID = "angel";


    /**
     *
     * @param event
     * @param trigger
     * @return
     */
    public static JdhReachTask createReachTask(Event event, JdhReachTrigger trigger,  JdhReachTemplate template){

        Long taskId = SpringUtil.getBean(GenerateIdFactory.class).getId();
        JdhReachTask task = new JdhReachTask();
        task.setDomainCode(event.getDomainCode());
        task.setAggregateCode(event.getAggregateCode());
        task.setEventCode(event.getEventCode());
        task.setAggregateId(event.getAggregateId());
        task.setStatus(JdhReachTask.INIT_STATUS);
        task.setTemplateId(trigger.getTemplateId());
        task.setType(template.getType());
        task.setTaskId(taskId);
        task.setEventId(event.getEventId());
        task.setSelectUserFunctionId(trigger.getSelectUserFunctionId());
        task.setTriggerId(trigger.getTriggerId());
        String eventBody = event.getBody();
        if (StringUtils.isNotBlank(eventBody)){
            JSONObject map = JSON.parseObject(eventBody);
            task.setEventBody(map);
        }
        task.setSelectUserFunctionId(trigger.getSelectUserFunctionId());

         // 如果配置了延迟策略，需要根据策略解析出需要延迟的时间，并赋值给triggerTime
        ReachTriggerDelayStrategy strategy = trigger.getStrategy();
        LocalDateTime cur = LocalDateTime.now().plusSeconds(3);
        if (Objects.nonNull(strategy)){
            LocalDateTime now = LocalDateTime.now();
            boolean isHit = false;
            /**
             * 当前时间属于策略配置的起止时间段，则命中了策略，命中策略需要根据hitStrategy配置的延迟策略计算延迟时间；未命中策略需要根据notStrategy
             * 配置的策略计算延迟时间
             */

            if (now.isAfter(strategy.buildStartLocalDateTime()) && now.isBefore(strategy.buildCloseLocalDateTime())){
                isHit = true;
            }

            // 命中时间范围
            if (isHit){
                DelayTime delayTime = strategy.getHitStrategy();
                // 命中后的延迟策略不为空，需要计算延迟时间
                if (Objects.nonNull(delayTime)){
                    long timestamp = now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    LocalDateTime time = delayTime.calcDelayTime(timestamp);
                    task.setTriggerTime(time);
                }
            }else{
                DelayTime delayTime = strategy.getNotStrategy();
                // 未命中时间范围，延迟策略为空，则无需触达
                if (Objects.nonNull(delayTime)){
                    long timestamp = now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    LocalDateTime time = delayTime.calcDelayTime(timestamp);
                    task.setTriggerTime(time);
                }else{
                    log.warn("ReachFactory->createReachTask 不在触达时间范围，并且未配置延迟策略，终止任务 eventId={}", event.getEventId());
                    return null;
                }
            }
            // 如果事件的发布时间晚于当前时间，则是延迟触达的任务
        }else {
            if (Objects.nonNull(event.getPublishTime()) && cur.isBefore(event.getPublishTime())){
                task.setTriggerTime(event.getPublishTime());
            }
        }
        // 是否延迟
        if (Objects.nonNull(task.getTriggerTime()) && cur.isBefore(task.getTriggerTime())){
            task.setDelay(Boolean.TRUE);
        }else{
            task.setDelay(Boolean.FALSE);
        }
        return task;
    }

    /**
     *
     * @param context
     * @return
     */
    public static List<JdhReachMessage> createReachMessages(ExecuteReachTaskContext context){

        List<ReachUserBO> users = context.getUsers();
        JdhReachTask reachTask = context.getReachTask();
        JdhReachTemplate template = context.getTemplate();
        ReachMessageBizType messageType = context.getMessageType();
        Map<ReachUserBO, String> urlMap =  context.getReachUrl();
        Queue<Long> queue = SpringUtil.getBean(GenerateIdFactory.class).getBatchId(users.size());

        List<JdhReachMessage> msg = Lists.newArrayListWithCapacity(users.size());
        LocalDateTime cur = LocalDateTime.now();



        for (ReachUserBO user : users) {
            JdhReachMessage message = new JdhReachMessage();
            message.setTaskId(reachTask.getTaskId());
            message.setEventId(reachTask.getEventId());
            message.setMessageId(queue.poll());
            message.setDomainCode(reachTask.getDomainCode());
            message.setAggregateCode(reachTask.getAggregateCode());
            message.setEventCode(reachTask.getEventCode());
            message.setAggregateId(reachTask.getAggregateId());
            message.setUserPin(user.getPin());
            message.setMessageBizType(messageType.getMessageBizType());
            message.setMessageTitle(template.getTemplateName());
            message.setTimestamp(System.currentTimeMillis());
            if (MapUtils.isNotEmpty(context.getUserParams())){
                ReachTemplateParamBO[] params = context.getUserParams().get(user);
                message.setMessagePayload(initPayload(template, params));
            }else{
                message.setMessagePayload(template.getContent());

            }
            if (MapUtils.isNotEmpty(urlMap)){
                String url = urlMap.get(user);
                message.setUrl(url);
            }
            message.setSendTime(cur);
            message.setIsRead(YnStatusEnum.NO.getCode());
            message.setAppId(messageType.getAppId());
            message.setReachType(reachTask.getType());
            message.setUserPhone(user.getPhone());
            // 如果是消息盒子的消息,
            if (Objects.equals(template.getType(), ReachTypeEnum.APP_NOTIFY.getCode())
                    && StringUtils.equals(MESSAGE_BOX_APP_ID, messageType.getAppId())){

                JdhReachConfigRepository configRepository = SpringUtil.getBean(JdhReachConfigRepository.class);
                Integer integer = configRepository.findBoxGroupByMsgType(messageType.getMessageBizType());
                String index = MessageFormat.format(JdhReachMessage.MESSAGE_BOX_INDEX_TEMPLATE, user.getPin(), integer);
                message.setBoxMessageGroupIndex(index);
            }


            msg.add(message);
        }
        log.info("ReachFactory->createReachMessages msg={}", JSON.toJSONString(msg));
        return msg;
    }

    /**
     * 初始化消息内容，短信不需要初始化，消息需要
     * @param template
     * @param params
     * @return
     */
    private static String initPayload(JdhReachTemplate template, ReachTemplateParamBO[] params){
        if (params != null && params.length > 0){
            Object[] values = new Object[params.length];
            for (int i = 0; i < params.length; i++) {
                values[i] = params[i].getValue();
            }
            return MessageFormat.format(template.getContent(), values);
        }else{
            return template.getContent();
        }
    }

    /**
     * 创建jdh reach模板
     *
     * @param ctx ctx
     * @return {@link JdhReachTemplate}
     */
    public static JdhReachTemplate createJdhReachTemplate(CreateReachTemplateContext ctx){
        //1、基本信息校验。后面补充
        JdhReachTemplateRepository templateRepository = SpringUtil.getBean(JdhReachTemplateRepository.class);
        JdhReachTemplate jdhReachTemplate = templateRepository.find(ctx.getTemplateId());
        if(Objects.nonNull(jdhReachTemplate)){
            throw ExceptionFactory.getArgumentsException(SupportErrorCode.CREATE_REACH_TEMPLATE_ERROR,"【templateId】重复");
        }

        //2、开始创建实体
        JdhReachTemplate result = new JdhReachTemplate();
        result.setTemplateName(ctx.getTemplateName());
        result.setType(ctx.getType());
        result.setMessageBizType(ctx.getMessageBizType());
        result.setContent(ctx.getContent());
        result.setChannelTemplateId(ctx.getChannelTemplateId());
        result.setAccountId(ctx.getAccountId());
        result.setBell(Objects.isNull(ctx.getBell()) ? Boolean.FALSE : Boolean.TRUE);
        result.setParamParse(StrUtil.isEmpty(ctx.getParamParse()) ? null : JSON.parseArray(ctx.getParamParse(), ReachParamParse.class));
        result.setUrlParse(StrUtil.isEmpty(ctx.getUrlParse()) ? null : JSON.parseObject(ctx.getUrlParse(), ReachParamParse.class));
        result.setTemplateId(ctx.getTemplateId());
        result.setDesc(ctx.getDesc());
        return result;
    }

    /**
     * createJdhReachTrigger
     *
     * @param ctx ctx
     * @return {@link JdhReachTrigger}
     */
    public static JdhReachTrigger createJdhReachTrigger(CreateReachTriggerContext ctx){
        JdhReachTrigger trigger = new JdhReachTrigger();
        trigger.setTriggerId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        trigger.setTriggerName(ctx.getTriggerName());
        trigger.setDomainCode(ctx.getDomainCode());
        trigger.setAggregateCode(ctx.getAggregateCode());
        trigger.setEventCode(ctx.getEventCode());
        trigger.setExpressionFillFunction(ctx.getExpressionFillFunction());
        trigger.setExpressionStr(ctx.getExpressionStr());
        trigger.setTemplateId(ctx.getTemplateId());
        trigger.setSelectUserFunctionId(ctx.getSelectUserFunctionId());
        trigger.setStrategy(StrUtil.isBlank(ctx.getStrategy()) ? null : JSON.parseObject(ctx.getStrategy(), ReachTriggerDelayStrategy.class));
        trigger.setYn(YnStatusEnum.YES.getCode());
        return trigger;
    }
}
