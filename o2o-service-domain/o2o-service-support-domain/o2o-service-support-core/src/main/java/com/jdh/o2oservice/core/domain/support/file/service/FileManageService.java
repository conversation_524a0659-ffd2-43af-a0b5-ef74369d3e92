package com.jdh.o2oservice.core.domain.support.file.service;

import com.amazonaws.services.s3.model.ObjectMetadata;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.model.FileBizType;
import com.jdh.o2oservice.core.domain.support.file.context.PutFileResult;
import com.jdh.o2oservice.core.domain.support.file.model.JdhFile;
import com.jdh.o2oservice.core.domain.support.file.service.impl.FileManageServiceImpl;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 文件管理领域服务
 * @author: yangxiyu
 * @date: 2024/4/28 3:38 下午
 * @version: 1.0
 */
public interface FileManageService {

    /**
     * 文件上传
     * @param key
     * @param inputStream
     * @param filePath
     * @return
     */
    PutFileResult put(String key, InputStream inputStream, FileManageServiceImpl.FolderPathEnum filePath, String contentType,Boolean isPublic);


    /**
     * 获取文件
     * @param key
     * @param filePath
     * @return
     */
    InputStream get(String key, FileManageServiceImpl.FolderPathEnum filePath);

    /**
     * 获取文件
     * @param allPathFileName 全路径文件名称 如 fileManage/import.xlsx
     * @return
     */
    InputStream get(String allPathFileName);

    /**
     * 获取元数据
     * @param allPathFileName
     * @return
     */
    ObjectMetadata getObjectMetadata(String allPathFileName);

    /**
     * 获取公网访问链接
     * @param fileKey
     * @param isPublic
     * @param expiration
     * @return
     */
    String getPublicUrl(String fileKey, Boolean isPublic, Date expiration);

    /**
     * 生产查询的预签名链接
     * @param jdhFiles
     * @param isPublic
     * @param expiration
     * @return
     */
    Map<Long, String> generateGetUrl(List<JdhFile> jdhFiles, Boolean isPublic, Date expiration);

    /**
     *
     * @param jdhFile
     * @param isPublic
     * @param expiration
     * @return
     */
    URL generateGetUrl(JdhFile jdhFile, Boolean isPublic, Date expiration);

    /**
     * 生产上传的预签名链接
     * @param file
     * @param isPublic
     * @param expiration
     * @return
     */
    URL generatePutUrl(JdhFile file, Boolean isPublic, Date expiration, String contentType);


    /**
     * 公网链接转化京东云链接文件上传
     * @param key 文件名称
     * @param outUrl 公网链接
     * @param filePath 文件路径
     * @return PutFileResult
     */
    PutFileResult transferPut(String key, String outUrl, FileManageServiceImpl.FolderPathEnum filePath,String contentType);

    /**
     * 公网链接转化京东云链接文件上传
     * @param key 文件名称
     * @param outUrl 公网链接
     * @param filePath 文件路径
     * @return PutFileResult
     */
    PutFileResult transferPutV2(String key, String outUrl, FileManageServiceImpl.FolderPathEnum filePath, String contentType);

    /**
     * PDF文件追加签名信息，签名信息追加后将文件上传到OSS，返回文件ID
     * @param
     * @return
     */
    JdhFile pdfAddSignature(JdhFile signatureImage, String domainEnum, String fileBizType);

    /**
     * 获取签名的合同
     * @param domainEnum
     * @param fileBizType
     * @return
     */
    String getPdfSignatureContract(String domainEnum, String fileBizType);


    /**
     * 获取元数据（指定环境）
     * @param allPathFileName
     * @return
     */
    ObjectMetadata getObjectMetadataWithBucket(String allPathFileName,String bucket);


}
