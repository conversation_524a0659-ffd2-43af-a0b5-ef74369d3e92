package com.jdh.o2oservice.core.domain.support.file.enums;

import com.google.common.collect.Maps;
import com.jdh.o2oservice.core.domain.support.file.service.FileOperationType;

import java.util.Map;

/**
 * 文件导出类型
 * @author: yang<PERSON>yu
 * @date: 2024/3/20 5:15 下午
 * @version: 1.0
 */
public enum FileExportTypeEnum implements FileOperationType {

    /** 京麦端文件导出 */
    JM_PROMISE_EXPORT("jmPromiseExport", "京麦端预约单列表导出", 5000),

    STATION_EXPORT("stationExport","实验室端导出",5000),
    /**
     * 运营端标准项目导出
     */
    PRODUCT_STANDARD_ITEM_EXPORT("productStandardItemExport", "运营端标准项目导出", 10000),
    /**
     * 运营端标准项目导出
     */
    PRODUCT_STANDARD_INDICATOR_EXPORT("productStandardIndicatorExport", "运营端标准指标导出", 10000),
    /**
     * 运营端历史指标与标准指标映射导出
     */
    PRODUCT_OLD_STANDARD_INDICATOR_MAPPING_EXPORT("productOldStandardIndicatorMappingExport", "运营端历史指标与标准指标映射导出", 200000),

    /**
     * 运营端Pop商家项目导出
     */
    PRODUCT_BIZ_POP_ITEM_EXPORT("productBizPopItemExport", "运营端Pop商家项目导出", 20000),

    /**
     * 运营端自营项目导出
     */
    PRODUCT_BIZ_SELF_ITEM_EXPORT("productBizSelfItemExport", "运营端自营业务项目导出", 10000),

    /**
     * 套餐导出
     */
    PRODUCT_PROGRAM_EXPORT("productProgramExport", "运营端自营套餐导出", 10000),

    /**
     * 京东服务项目指标清洗新老数据对比导出
     */
    SERVICE_OLD_ITEM_MAPPING_NEW_ITEM_EXPORT("serviceOldItemMappingNewItemExport", "京东服务项目指标清洗新老数据对比导出", 200000),

    /**
     * 商品项目指标清洗新老数据对比导出
     */
    PRODUCT_OLD_ITEM_MAPPING_NEW_ITEM_EXPORT("productOldItemMappingNewItemExport", "商品项目指标清洗新老数据对比导出", 200000),

    /**
     * 一卡万店老项目指标映射导出
     */
    YKWD_OLD_ITEM_INDICATOR_MAPPING_EXPORT("ykwdOldItemIndicatorMappingExport", "一卡万店老项目指标映射导出", 1000000),

    STATION_APPOINT_LIST_EXPORT("stationAppointListExport","实验室端订单列表导出",10000),

    STATION_BILL_LIST_EXPORT("billAppointListExport","实验室端账单列表导出",10000),

    ANGEL_SETTLE_ADJUST_UPLOAD("angelSettleAdjustUpload", "护士调账批量新建任务", 1000),

    ANGEL_SETTLE_ADJUST_EXPORT("angelSettleAdjustExport", "护士调账列表导出任务", 10000),

    ANGEL_SETTLE_EXPORT("angelSettleExport", "护士结算列表导出", 10000),

    ACTIVITY_ANGEL_RECRUITMENT_EXPORT("angelRecruitmentActivityExport", "邀请护士列表导出", 10000),
    STATION_COMPOSITE_PROMISE_ALL_EXPORT("stationCompositePromiseAllExport", "实验室端全部检测单导出", 10000),
    MEDICAL_PROMISE_EXPORT("medicalPromiseExport","检验单导出",1000000),

    ENTERPRISE_BILL_DETAIL_EXPORT("enterpriseBillDetailExport", "企业账单明细导出", 5000),

    REGION_FEE_CONFIG_EXPORT("regionFeeConfigExport","地区费项配置导出",1000000),

    ENTERPRISE_VOUCHER_EXPORT("enterpriseVoucherExport","企业服务单导出",1000000),
    ;

    FileExportTypeEnum(String type, String desc, Integer maxCount) {
        this.type = type;
        this.desc = desc;
        this.maxCount = maxCount;
    }

    private String type;
    private String desc;
    private Integer maxCount;

    @Override
    public String getType() {
        return type;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getMaxCount() {
        return maxCount;
    }

    private static final Map<String, FileExportTypeEnum> EXPORT_MAP = Maps.newHashMap();
    static {
        for (FileExportTypeEnum value : values()) {
            EXPORT_MAP.put(value.type, value);
        }
    }
    public static FileExportTypeEnum findType(String type){
        return  EXPORT_MAP.get(type);
    }
}
