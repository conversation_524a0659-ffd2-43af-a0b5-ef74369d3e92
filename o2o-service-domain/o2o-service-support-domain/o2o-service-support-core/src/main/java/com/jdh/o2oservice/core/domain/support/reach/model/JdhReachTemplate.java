package com.jdh.o2oservice.core.domain.support.reach.model;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.SupportAggregateEnum;
import com.jdh.o2oservice.core.domain.support.reach.context.UpdateReachTemplateContext;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 触达任务配置
 * @author: yang<PERSON><PERSON>
 * @date: 2024/4/15 2:12 下午
 * @version: 1.0
 */
@Data
public class JdhReachTemplate implements Aggregate<JdhReachTemplateIdentifier> {

    /**
     * id
     */
    private Long id;
    /**
     * 任务ID
     */
    private String templateName;
    /**
     *
     */
    private Long templateId;
    /**
     * 模版绑定的触达任务类型
     */
    private Integer type;
    /**
     * 消息编码类型
     */
    private String messageBizType;
    /**
     * 模版内容
     */
    private String content;
    /**
     * 参数填充规则，按照content中占位符的顺序持久化和查询
     */
    private List<ReachParamParse> paramParse;
    /**
     * 模版
     */
    private String channelTemplateId;
    /**
     *
     */
    private String accountId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 是否需要铃声
     */
    private Boolean bell;
    /**
     * push跳转链接解析
     */
    private ReachParamParse urlParse;

    /**
     * 数据有效标识
     */
    private Integer yn;

    /**
     * 描述
     */
    private String desc;

    /**
     * 判断触达类型是否重复，同一个事件对于同一种触达类型仅能触达一次，如果配置错误可能导致多次触达。
     * 比如APP站内信在hitReachTypes已经存在，则返回true，说明重复，不再发送当前消息。
     * hitReachTypes中已经存
     * @param hitReachTypes
     * @return
     */
    public Boolean isRepetition(Set<Integer> hitReachTypes){
        if (CollectionUtils.isEmpty(hitReachTypes)){
            return Boolean.FALSE;
        }
        // 重复
        if (hitReachTypes.contains(type)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.BASE;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return SupportAggregateEnum.REACH_TEMPLATE;
    }

    /**
     * 版本
     *
     * @return {@link Integer}
     */
    @Override
    public Integer version() {
        return this.version;
    }

    /**
     * 版本增加
     */
    @Override
    public void versionIncrease() {
        this.version++;
    }

    /**
     * 获取标识符
     *
     * @return {@link JdhReachTemplateIdentifier}
     */
    @Override
    public JdhReachTemplateIdentifier getIdentifier() {
        return JdhReachTemplateIdentifier.builder().templateId(this.templateId).build();
    }

    /**
     * 更新
     *
     * @param ctx ctx
     */
    public void update(UpdateReachTemplateContext ctx) {
        //1、参数校验

        //2、字段更新 参数转换收到cmd -> ctx中
        this.setTemplateName(ctx.getTemplateName());
        this.setType(ctx.getType());
        this.setMessageBizType(ctx.getMessageBizType());
        this.setContent(ctx.getContent());
        this.setParamParse(StrUtil.isBlank(ctx.getParamParse()) ? null : JSON.parseArray(ctx.getParamParse(), ReachParamParse.class));
        this.setChannelTemplateId(ctx.getChannelTemplateId());
        this.setAccountId(ctx.getAccountId());
        this.setBell(Objects.isNull(ctx.getBell()) ? Boolean.FALSE : BooleanUtils.toBoolean(ctx.getBell()));
        this.setUrlParse(StrUtil.isBlank(ctx.getUrlParse()) ? null : JSON.parseObject(ctx.getUrlParse(), ReachParamParse.class));
        this.setDesc(ctx.getDesc());
    }

    /**
     * 删除
     */
    public void delete() {
        //1、必要信息校验

        //2、删除
        this.setYn(YnStatusEnum.NO.getCode());
    }
}
