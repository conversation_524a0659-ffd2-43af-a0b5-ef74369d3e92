package com.jdh.o2oservice.core.domain.promise.enums;

import com.jdh.o2oservice.base.exception.errorcode.AbstractErrorCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import lombok.ToString;

/**
 * 履约领域错误码
 * @author: yang<PERSON><PERSON>
 * @date: 2023/12/18 8:11 下午
 * @version: 1.0
 */
@ToString
public enum PromiseErrorCode implements AbstractErrorCode {
    /**
     * 履约域错误码
     */
    CREATE_CODE_BUSY(DomainEnum.PROMISE, "10001", "创建履约码繁忙"),
    CREATE_CODE_ERROR(DomainEnum.PROMISE, "10002", "创建履约码失败"),
    VOUCHER_CREATE_PARAM_MISS_ERROR(DomainEnum.PROMISE, "10003", "创建服务单参数{0}缺失"),
    VOUCHER_REPEAT_CREATE_ERROR(DomainEnum.PROMISE, "10004", "重复创建服务单异常"),
    VOUCHER_FREEZE_ERROR(DomainEnum.PROMISE, "10005", "服务单冻结异常，非正常状态不允许冻结"),
    VOUCHER_UN_FREEZE_ERROR(DomainEnum.PROMISE, "10006", "服务单解冻异常，非冻结状态不允许解冻"),
    VOUCHER_WAIT_SERVICE_ERROR(DomainEnum.PROMISE, "10007", "服务单启用失败"),
    VOUCHER_EXPIRE_ERROR(DomainEnum.PROMISE, "10008", "服务单过期操作失败"),
    SMS_CODE_ILLEGAL(DomainEnum.PROMISE, "10010", "短信验证码错误"),
    STORE_NOT_EXIST(DomainEnum.PROMISE, "10011", "门店不存在"),
    VOUCHER_COMPLETE_ERROR(DomainEnum.PROMISE, "10012", "服务单完成操作失败"),
    VOUCHER_INVALID_ERROR(DomainEnum.PROMISE, "10013", "服务单作废操作失败"),
    VOUCHER_DELAY_ERROR(DomainEnum.PROMISE, "10014", "服务单延期操作失败"),
    PROMISE_SUBMIT_MODIFY_ERROR(DomainEnum.PROMISE, "10014", "履约单提交修改预约失败"),
    PROMISE_SUBMIT_CANCEL_ERROR(DomainEnum.PROMISE, "10015", "履约单提交取消预约失败"),
    USER_MARRIED_NOT_SUITABLE_SERVICE_ERROR(DomainEnum.PROMISE, "10016", "预约人婚姻状况不符合，适用{}"),
    USER_AGE_NOT_SUITABLE_SERVICE_ERROR(DomainEnum.PROMISE, "10017", "预约人年龄不符合，适用年龄{}-{}"),
    PROMISE_CHANNEL_NO_ILLEGAL(DomainEnum.PROMISE, "10020", "渠道编码非法{}"),
    PROMISE_CHANNEL_APPOINTMENT_NO_ILLEGAL(DomainEnum.PROMISE, "10021", "渠道预约单号为空"),
    PROMISE_USER_MODIFY_DATE_TO_LONG_ERROR(DomainEnum.PROMISE, "10022", "修改预约时间超过服务周期"),
    PROMISE_CHANNEL_APPOINTMENT_ID_ILLEGAL(DomainEnum.PROMISE, "10023", "非法的渠道预约单号"),
    PROMISE_APPOINTMENT_ID_ILLEGAL(DomainEnum.PROMISE, "10024", "预约单号非法"),
    PROMISE_APPOINTMENT_THE_SAME_DAY_CANT_CANCEL(DomainEnum.PROMISE, "10025", "预约当天不能取消"),
    PROMISE_WRITE_OFF_CODE_ERROR(DomainEnum.PROMISE, "10026", "卡号或者卡密错误"),
    QUERY_VOUCHER_LIST_SOURCE_TYPE_ERROR(DomainEnum.PROMISE, "10027", "查询服务单集合，服务单类型为空"),
    QUERY_VOUCHER_LIST_SOURCE_ID_LIST_ERROR(DomainEnum.PROMISE, "10028", "查询服务单集合外部id为空"),
    PROMISE_STATUS_NO_SUPPORT(DomainEnum.PROMISE, "10029", "履约状态支持当前操作"),
    PROMISE_STATUS_NOT_ALLOW_OPERATION(DomainEnum.PROMISE, "10030", "当前单据状态不允许此操作"),
    VOUCHER_STATUS_NOT_ALLOW_OPERATION(DomainEnum.PROMISE, "10031", "当前单据状态不允许此操作"),
    PROMISE_FREEZE_NO_OPERATION(DomainEnum.PROMISE, "10032", "已冻结，无法操作"),
    PROMISE_WRITE_OFF_REPEAT_ERROR(DomainEnum.PROMISE, "10033", "该券码已核销，请勿重复操作"),
    PROMISE_WRITE_OFF_DATE_BEFORE_ERROR(DomainEnum.PROMISE, "10034", "核销日期早于预约日期，暂不能核销"),
    PROMISE_WRITE_OFF_SKU_ERROR(DomainEnum.PROMISE, "10035", "非正确商品不能核销"),
    PROMISE_WRITE_OFF_STORE_NOT_EXIST(DomainEnum.PROMISE, "10036", "核销门店数据为空"),

    PROMISE_SUBMIT_DISPATCH_ERROR(DomainEnum.PROMISE, "10037", "履约单发起派单失败"),
    PROMISE_FREEZE_STATUS_NOT_SUPPORT_ERROR(DomainEnum.PROMISE, "10038", "履约单当前状态不支持冻结"),
    PROMISE_FREEZE_PATIENT_IS_EMPTY(DomainEnum.PROMISE, "10039", "冻结的用户信息为空"),
    PROMISE_FREEZE_SERVICE_IS_EMPTY(DomainEnum.PROMISE, "10040", "冻结的服务信息为空"),
    PROMISE_INVALID_PATIENT_IS_EMPTY(DomainEnum.PROMISE, "10041", "作废的用户信息为空"),
    PROMISE_INVALID_SERVICE_IS_EMPTY(DomainEnum.PROMISE, "10042", "作废的服务信息为空"),
    PROMISE_FREEZE_MEDICAL_FAIL(DomainEnum.PROMISE, "10043", "冻结履约明细失败，请稍后再试"),
    PROMISE_FREEZE_TYPE_NOT_SUPPORT(DomainEnum.PROMISE, "10044", "不支持的冻结类型"),
    SERVICE_GENDER_NOT_SUPPORT(DomainEnum.PROMISE, "10045", "服务和性别不匹配"),
    SERVICE_MARRY_NOT_SUPPORT(DomainEnum.PROMISE, "10046", "服务和婚姻状态不匹配"),


    PROMISE_SUBMIT_APPOINTMENT_TIME_ERROR(DomainEnum.PROMISE, "10047", "请重新选择上门时间"),
    PROMISE_SUBMIT_APPOINTMENT_ADDRESS_ERROR(DomainEnum.PROMISE, "10048", "请重新选择上门地址"),
    PROMISE_SUBMIT_APPOINTMENT_ADDRESS_MISS_ERROR(DomainEnum.PROMISE, "10049", "请选择期望上门地址"),
    PROMISE_SUBMIT_APPOINTMENT_USER_VERIFY_ERROR(DomainEnum.PROMISE, "10050", "本服务仅适用实名认证用户"),
    PROMISE_SUBMIT_APPOINTMENT_AGE_ERROR(DomainEnum.PROMISE, "10051", "本服务适用年龄{}岁至{}岁"),
    PROMISE_SUBMIT_APPOINTMENT_GENDER_ERROR(DomainEnum.PROMISE, "10052", "本服务适用使用性别为{}"),
    PROMISE_SUBMIT_SERVICE_NOT_FOUND_ERROR(DomainEnum.PROMISE, "10053", "预约服务配置信息未找到"),
    PROMISE_SUBMIT_SERVICE_SALE_STATUS_ERROR(DomainEnum.PROMISE, "10054", "该服务已经爆单，暂不支持预约"),
    PROMISE_SUBMIT_STATION_NOT_FOUND_ERROR(DomainEnum.PROMISE, "10055", "预约上门区域无检测服务，请重新选择后提交"),
    PROMISE_SUBMIT_APPOINTMENT_REMARK_LENGTH_ERROR(DomainEnum.PROMISE, "10056", "预约备注信息超长，请重新输入"),
    PROMISE_SUBMIT_APPOINTMENT_USER_MISS_ERROR(DomainEnum.PROMISE, "10057", "请选择被服务人"),
    PROMISE_CANCEL_ERROR(DomainEnum.PROMISE, "10058", "取消失败:{}"),
    PROMISE_VOUCHER_EXPIRE_ERROR(DomainEnum.PROMISE, "10059", "卡券已过期 无法继续使用"),
    PROMISE_VOUCHER_INVALID_ERROR(DomainEnum.PROMISE, "10060", "卡券已退款 无法继续使用"),
    PROMISE_SUBMIT_APPOINTMENT_ADDRESS_NO_SERVICE(DomainEnum.PROMISE, "10061", "该地址不在履约范围内"),
    PROMISE_SUBMIT_APPOINTMENT_USER_CHANGE_ERROR(DomainEnum.PROMISE, "10062", "请重新选择被服务人"),

    PROMISE_MODIFY_DATE_DISPATCH_ERROR(DomainEnum.PROMISE, "10063", "履约单修改时间发起派单失败"),
    PROMISE_NOT_EXISTS(DomainEnum.PROMISE, "10064", "履约单无效"),
    PROMISE_MODIFY_TIME_IS_SAME(DomainEnum.PROMISE, "10065", "已预约了该时段,请选择其他时间"),
    PROMISE_USER_APPOINTMENT_DATE_TO_LONG_ERROR(DomainEnum.PROMISE, "10066", "预约时间超过服务周期"),
    SKU_ITEM_ILLEGAL(DomainEnum.PROMISE, "10067", "商品不可用，请练习客服"),
    IMMEDIATELY_ERROR(DomainEnum.PROMISE, "10067", "非尽快上门的预约单不能修改为尽快上门"),
    ORDER_LACK_ERROR(DomainEnum.PROMISE, "10068", "订单内容不完整"),
    VOUCHER_NUM_LACK(DomainEnum.PROMISE, "10069", "服务剩余可用次数不足"),
    SPECIMEN_CODE_REPEAT_FAIL(DomainEnum.PROMISE, "10070", "条码重复，请绑定新样本"),
    MODIFY_PROMISE_ERROR(DomainEnum.PROMISE,"10071", "修改太频繁，请稍后重试"),

    MODIFY_PROMISE_WORK_STATUS_ERROR(DomainEnum.PROMISE,"10072", "修改失败,该状态下不允许修改"),

    PROMISE_SUBMIT_APPOINTMENT_FULLADDRESS_PARSER_ERROR(DomainEnum.PROMISE, "10073", "地址解析失败"),
    ;



    /**
     * PromiseErrorCode
     *
     * @param domainEnum  域枚举
     * @param code        代码
     * @param description 描述
     */
    PromiseErrorCode(DomainEnum domainEnum, String code, String description) {
        this.domainEnum = domainEnum;
        this.code = code;
        this.description = description;
    }

    /** */
    private DomainEnum domainEnum;

    /** */
    private String code;
    /**
     * 展示给客户的错误提示，因为有的场景把系统异常传递给客户展示，体验不好
     */
    private String description;

    /**
     * getCode
     *
     * @return {@link String}
     */
    @Override
    public String getCode() {
        return this.code;
    }

    /**
     * getDescription
     *
     * @return {@link String}
     */
    @Override
    public String getDescription() {
        return this.description;
    }

}
