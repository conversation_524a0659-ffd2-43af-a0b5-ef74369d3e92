package com.jdh.o2oservice.core.domain.promise.model;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.base.statemachine.StateContext;
import com.jdh.o2oservice.base.statemachine.StateMachine;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.base.util.TimeFormat;
import com.jdh.o2oservice.base.util.TimeUtils;
import com.jdh.o2oservice.core.domain.promise.context.PromiseCancelSubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.context.PromiseDelayContext;
import com.jdh.o2oservice.core.domain.promise.context.PromiseModifySubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseEventTypeEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.DateTypeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhFreezeEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.enums.ServiceTagEnum;
import com.jdh.o2oservice.core.domain.support.promisego.rpc.bo.UserPromisegoBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * JdhPromise
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhPromise implements Aggregate<JdhPromiseIdentifier> {

    /**
     * id
     */
    private Long id;

    /** */
    private String userPin;
    /**
     * 垂直业务身份编码
     */
    private String verticalCode;
    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 履约单ID
     */
    private Long promiseId;

    /**
     * 服务单ID
     */
    private Long voucherId;

    /**
     * 冻结状态 0 正常 1 冻结
     */
    private Integer freeze;

    /**
     * 履约单序号，对于一个服务单需要多次履约的场景
     */
    private Integer serialNum;

    /**
     * 履约单状态：待履约/已履约/预约中/预约失败/预约成功/修改预约中/修改预约失败/取消中/取消失败/取消成功/
     */
    private Integer promiseStatus;

    /**
     * 免预约、在线预约、商家预约
     */
    private Integer promiseType;

    /**
     * 过期时间
     */
    private LocalDateTime expireDate;

    /**
     * 卡号ID
     */
    private String codeId;

    /**
     * 卡号
     */
    private String code;

    /**
     * 卡密
     */
    private String codePwd;

    /**
     * appointmentInfo
     */
    /**
     * 履约渠道编号
     */
    private Long channelNo;
    /**
     * 预约单号
     */
    private Long appointmentId;

    /**
     * 渠道方的履约单号
     */
    private String channelAppointmentId;

    /**
     * 服务兑换来源id
     */
    private String sourceVoucherId;

    /**
     *
     */
    private PromiseAppointmentTime appointmentTime;

    /**
     * 受检人（使用服务的患者信息）
     */
    private List<JdhPromisePatient> patients;
    /**
     * 预约门店
     * DUCC配置有引用，禁止修改属性名称
     */
    private PromiseStation store;

    /**
     * jdhPromiseServiceDetailList
     */
    private List<PromiseService> services;

    /**
     * 扩展信息
     */
    private List<JdhPromiseExtend> promiseExtends;
    /**
     * 预约人手机号，预约人是指当前操作预约的用户，被服务人和预约人可能不是同一个人
     */
    private String appointmentPhone;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否有效 0：无效；1：有效
     */
    private Integer yn;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作角色
     * @see com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum
     */
    private Integer operatorRoleType;

    /**
     * 预测信息
     */
    private UserPromisegoBo userPromisegoBo;

    /**
     * 聚合所属领域编码
     *
     * @return
     */
    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.PROMISE;
    }

    /**
     * 获取聚合编码
     *
     * @return {@link AggregateCode}
     */
    @Override
    public AggregateCode getAggregateCode() {
        return PromiseAggregateEnum.PROMISE;
    }

    /**
     * 获取标识符
     *
     * @return {@link JdhPromiseIdentifier}
     */
    @Override
    public JdhPromiseIdentifier getIdentifier() {
        return JdhPromiseIdentifier.builder().promiseId(this.getPromiseId()).build();
    }

    /**
     * 修改预约
     *
     * @param ctx CTX
     */
    public void modify(PromiseModifySubmitAbilityContext ctx) {
        StateMachine<JdhPromiseStatusEnum, PromiseEventTypeEnum, StateContext> stateMachine = SpringUtil.getBean("promiseStatemachine", StateMachine.class);
        JdhPromiseStatusEnum source = JdhPromiseStatusEnum.convert(ctx.getSourceStatus());
        JdhPromiseStatusEnum target = stateMachine.fireEvent(source, PromiseEventTypeEnum.PROMISE_USER_SUBMIT_MODIFY, ctx);
        if(JdhPromiseStatusEnum.MODIFY_ING != target){
            throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_MODIFY_ERROR);
        }
    }


    /**
     * 取消预约
     *
     * @param ctx CTX
     */
    public void cancel(PromiseCancelSubmitAbilityContext ctx) {
        StateMachine<JdhPromiseStatusEnum, PromiseEventTypeEnum, StateContext> stateMachine = SpringUtil.getBean("promiseStatemachine", StateMachine.class);
        JdhPromiseStatusEnum source = JdhPromiseStatusEnum.convert(ctx.getSourceStatus());
        JdhPromiseStatusEnum target = stateMachine.fireEvent(source, PromiseEventTypeEnum.PROMISE_USER_SUBMIT_CANCEL, ctx);
        if(JdhPromiseStatusEnum.CANCEL_ING != target){
            throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_CANCEL_ERROR);
        }
    }

    /**
     * 验证核销码
     * @param code
     * @param codePwd
     * @return
     */
    public boolean verifyCode(String code, String codePwd){
        // 如果当前卡号不为空，则进行校验
        if (StringUtils.isNotBlank(this.code) && !StringUtils.equals(this.code, code)){
            return Boolean.FALSE;
        }
        // 如果当前卡密不为空则进行校验
        if (StringUtils.isNotBlank(this.codePwd) && !StringUtils.equals(this.codePwd, codePwd)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 核销状态同步
     */
    public boolean writeOffSync(){
        if (Objects.equals(this.promiseStatus, JdhPromiseStatusEnum.COMPLETE.getStatus())){
            return Boolean.FALSE;
        }
        this.promiseStatus = JdhPromiseStatusEnum.COMPLETE.getStatus();
        return Boolean.TRUE;
    }


    /**
     * 作废（作废的同时需要解冻）
     */
    public void invalid(){
        this.promiseStatus = JdhPromiseStatusEnum.INVALID.getStatus();
        if (Objects.equals(freeze, JdhFreezeEnum.FREEZE.getStatus())){
            unFreeze();
        }
    }

    /**
     * 作废（作废的同时需要解冻）
     */
    public Boolean isInvalid(){
        return Objects.equals(this.promiseStatus, JdhPromiseStatusEnum.INVALID.getStatus());
    }

    /**
     * 冻结
     */
    public void freeze(){
        this.setFreeze(JdhFreezeEnum.FREEZE.getStatus());
    }


    /**
     * 冻结
     */
    public void unFreeze(){
        this.setFreeze(JdhFreezeEnum.UN_FREEZE.getStatus());
    }

    /**
     * 是否逆向
     *
     * @return {@link Boolean}
     */
    public Boolean isReversal(){
        return JdhFreezeEnum.FREEZE.getStatus().equals(this.getFreeze());
    }

    /**
     *
     * @return
     */
    public PromiseService findBasicService(){
        if (CollectionUtil.isEmpty(services)){
            return null;
        }
        if (services.size() == 1){
            return services.get(0);
        }
        Optional<PromiseService> optional = services.stream().filter(e -> Objects.equals(e.getTags(), ServiceTagEnum.BASIC.getType())).findFirst();
        return optional.orElse(null);

    }

    /**
     *
     * @param key
     * @return
     */
    public JdhPromiseExtend findExtend(String key){
        if (CollectionUtils.isEmpty(promiseExtends)){
            return null;
        }
        for (JdhPromiseExtend promiseExtend : promiseExtends) {
            if (StringUtils.equals(key, promiseExtend.getAttribute())){
                return promiseExtend;
            }
        }
        return null;

    }

    /**
     * copy一个promise
     * @return
     */
    public JdhPromise copyInstance(){
            return JdhPromiseConvert.INSTANCE.copy(this);
    }

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {
        version++;
    }

    /**
     * 查询唯一的患者信息，兼容老逻辑，老逻辑到店业务只有一个患者
     * @return
     */
    public JdhPromisePatient findOnlyPatient(){
        if (CollectionUtil.isEmpty(patients)){
            return null;
        }
        return patients.get(0);
    }
    /**
     * delay
     *
     * @param delayContext 延期
     * @return {@link Boolean}
     */
    public Boolean delay(PromiseDelayContext delayContext) {
        Date vExpireDate = delayContext.getJdhVoucher().getExpireDate();
        if(JdhPromiseStatusEnum.COMPLETE.getStatus().equals(this.getPromiseStatus())){
            return Boolean.FALSE;
        }
        this.setExpireDate(DateUtil.toLocalDateTime(vExpireDate));
        return Boolean.TRUE;
    }

    /**
     * 履约单是否已经是终态
     * @return
     */
    public Boolean isFail(){

        return Objects.equals(promiseStatus, JdhPromiseStatusEnum.APPOINTMENT_FAIL.getStatus())
                || Objects.equals(promiseStatus, JdhPromiseStatusEnum.MODIFY_FAIL.getStatus())
                || Objects.equals(promiseStatus, JdhPromiseStatusEnum.CANCEL_FAIL.getStatus());
    }

    /**
     *
     */
    @Mapper
    public interface JdhPromiseConvert{
        /** */
        JdhPromiseConvert INSTANCE = Mappers.getMapper(JdhPromiseConvert.class);
        /** */
        JdhPromise copy(JdhPromise promise);
    }

    /**
     *
     * @param extendKeyEnum
     * @param value
     */
    public void refreshExtend(PromiseExtendKeyEnum extendKeyEnum, String value){

        if (StringUtils.isBlank(value)){
            return;
        }
        if (CollectionUtils.isEmpty(promiseExtends)){
            this.promiseExtends = Lists.newArrayList();
        }

        Optional<JdhPromiseExtend> optional = promiseExtends.stream().filter(e-> StringUtils.equals(e.getAttribute(), extendKeyEnum.getFiledKey()))
                .findFirst();
        if (optional.isPresent()){
            optional.get().setValue(value);
        }else {
            JdhPromiseExtend extend = new JdhPromiseExtend();
            extend.setPromiseId(promiseId);
            extend.setAttribute(extendKeyEnum.getFiledKey());
            extend.setValue(value);
            promiseExtends.add(extend);
        }
    }

    public void refreshIntendedNurseExtend(PromiseExtendKeyEnum extendKeyEnum, PromiseIntendedNurse intendedNurse){
        if (intendedNurse == null){
            return;
        }
        if (CollectionUtils.isEmpty(promiseExtends)){
            this.promiseExtends = Lists.newArrayList();
        }
        Optional<JdhPromiseExtend> optional = promiseExtends.stream().filter(e-> StringUtils.equals(e.getAttribute(), extendKeyEnum.getFiledKey()))
                .findFirst();
        if (optional.isPresent()){
            optional.get().setValue(JSON.toJSONString(intendedNurse));
        }else {
            JdhPromiseExtend extend = new JdhPromiseExtend();
            extend.setPromiseId(promiseId);
            extend.setAttribute(extendKeyEnum.getFiledKey());
            extend.setValue(JSON.toJSONString(intendedNurse));
            promiseExtends.add(extend);
        }
    }

    /**
     * 获取扩展的orderId属性
     * @return
     */
    public String findExtendOrderId(){
        JdhPromiseExtend extend = findExtend(PromiseExtendKeyEnum.ORDER_ID.getFiledKey());
        if(Objects.nonNull(extend)){
            return extend.getValue();
        }
        return null;
    }

    /**
     * 格式化时间描述
     * @return
     */
    public String formatAppointTimeDesc(){
        try {
            //按照时间段
            LocalDateTime start = appointmentTime.getAppointmentStartTime();
            LocalDateTime end = appointmentTime.getAppointmentEndTime();

            String day = TimeUtils.localDateTimeToStr(start, TimeFormat.SHORT_PATTERN_LINE);
            String startTime = TimeUtils.localDateTimeToStr(start, TimeFormat.DATE_PATTERN_HM_SIMPLE);
            String endTime = TimeUtils.localDateTimeToStr(end, TimeFormat.DATE_PATTERN_HM_SIMPLE);

            return MessageFormat.format("{0} {1}-{2}", day, startTime, endTime);
        } catch (Exception e) {
            //log.error("OrderAppointmentTimeValueObject formatAppointTimeDesc error e", e);
            return StrUtil.EMPTY;
        }
    }
}
