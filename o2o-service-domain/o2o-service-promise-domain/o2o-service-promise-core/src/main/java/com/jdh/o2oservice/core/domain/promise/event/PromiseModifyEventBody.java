package com.jdh.o2oservice.core.domain.promise.event;

import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.PromiseAppointmentTime;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PromiseUserModifyEventBody
 */
@Data
@NoArgsConstructor
public class PromiseModifyEventBody extends PromiseEventBaseBody {


    private PromiseAppointmentTime beforeTime;

    private PromiseAppointmentTime afterTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作角色
     * @see com.jdh.o2oservice.common.enums.OperatorRoleTypeEnum
     */
    private Integer operatorRoleType;

    /**
     * PromiseSubmitEventBody
     *
     * @param
     */
    public PromiseModifyEventBody(JdhPromise snapshot, JdhPromise jdhPromise){
        super(snapshot, jdhPromise);

        this.beforeTime = snapshot.getAppointmentTime();
        this.afterTime = jdhPromise.getAppointmentTime();
        this.operator = jdhPromise.getOperator();
        this.operatorRoleType = jdhPromise.getOperatorRoleType();
    }
}
