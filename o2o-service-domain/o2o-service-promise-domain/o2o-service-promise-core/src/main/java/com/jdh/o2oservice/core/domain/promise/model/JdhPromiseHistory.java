package com.jdh.o2oservice.core.domain.promise.model;

import com.alibaba.fastjson.JSON;
import com.jdh.o2oservice.base.event.Event;
import com.jdh.o2oservice.base.model.Aggregate;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.base.model.DomainCode;
import com.jdh.o2oservice.base.model.DomainEnum;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseAggregateEnum;
import com.jdh.o2oservice.core.domain.promise.event.PromiseCallbackEventBody;
import com.jdh.o2oservice.core.domain.promise.event.PromiseModifyEventBody;
import com.jdh.o2oservice.core.domain.promise.event.PromiseSubmitEventBody;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * JdhPromise
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JdhPromiseHistory implements Aggregate<JdhPromiseHistoryIdentifier> {

    /**
     * 履约单号
     */
    private Long promiseId;

    /**
     * 事件编号
     */
    private String eventCode;

    /**
     * 操作之前状态
     */
    private Integer beforeStatus;

    /**
     * 操作之后状态
     */
    private Integer afterStatus;

    /**
     * 操作的说明
     */
    private String reason;

    /**
     * 扩展信息
     */
    private String extend;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 数据来源分支
     */
    private String branch;

    /**
     * 正向操作记录保存
     * @param event
     * @param body
     */
    public JdhPromiseHistory(Event event, PromiseSubmitEventBody body) {
        this.promiseId = Long.valueOf(event.getAggregateId());
        this.eventCode = event.getEventCode();
        this.beforeStatus = body.getBeforeStatus();
        this.afterStatus = body.getAfterStatus();
        this.version = event.getVersion();
        this.extend = JSON.toJSONString(body);
    }

    /**
     * 回调记录保存
     * @param event
     * @param body
     */
    public JdhPromiseHistory(Event event, PromiseCallbackEventBody body) {
        this.promiseId = Long.valueOf(event.getAggregateId());
        this.eventCode = event.getEventCode();
        this.beforeStatus = body.getBeforeStatus();
        this.afterStatus = body.getAfterStatus();
        this.version = event.getVersion();
        this.extend = JSON.toJSONString(body);
        this.reason = body.getMsg();
    }

    /**
     * 修改预约记录
     * @param event
     * @param body
     */
    public JdhPromiseHistory(Event event, PromiseModifyEventBody body) {
        this.promiseId = Long.valueOf(event.getAggregateId());
        this.eventCode = event.getEventCode();
        this.beforeStatus = body.getBeforeStatus();
        this.afterStatus = body.getAfterStatus();
        this.version = event.getVersion();
        this.extend = JSON.toJSONString(body);
    }

    public JdhPromiseHistory(Event event, Integer beforeStatus, Integer afterStatus, String reason) {
        this.promiseId = Long.valueOf(event.getAggregateId());
        this.eventCode = event.getEventCode();
        this.beforeStatus = beforeStatus;
        this.afterStatus = afterStatus;
        this.version = event.getVersion();
    }

    @Override
    public DomainCode getDomainCode() {
        return DomainEnum.PROMISE;
    }

    @Override
    public AggregateCode getAggregateCode() {
        return PromiseAggregateEnum.PROMISE_HISTORY;
    }

    @Override
    public Integer version() {
        return version;
    }

    @Override
    public void versionIncrease() {

    }

    @Override
    public JdhPromiseHistoryIdentifier getIdentifier() {
        return new JdhPromiseHistoryIdentifier(promiseId, version);
    }
}
