package com.jdh.o2oservice.core.domain.promise.service.ability.promise.modify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.util.TdeClientUtil;
import com.jdh.o2oservice.common.enums.BusinessModeEnum;
import com.jdh.o2oservice.core.domain.promise.context.PromiseModifySubmitAbilityContext;
import com.jdh.o2oservice.core.domain.promise.context.PromiseTime;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseErrorCode;
import com.jdh.o2oservice.core.domain.promise.enums.PromiseExtendKeyEnum;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromise;
import com.jdh.o2oservice.core.domain.promise.model.JdhPromiseExtend;
import com.jdh.o2oservice.core.domain.promise.model.PromiseAppointmentTime;
import com.jdh.o2oservice.core.domain.promise.model.PromiseStation;
import com.jdh.o2oservice.core.domain.support.basic.enums.JdhPromiseStatusEnum;
import com.jdh.o2oservice.core.domain.support.basic.rpc.AddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.JdhAddressRpc;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.AddressDetailBO;
import com.jdh.o2oservice.core.domain.support.basic.rpc.bo.BaseAddressBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 修改履约单数据
 *
 * <AUTHOR>
 * @date 2023/12/25
 */
@Component
@Slf4j
public class ModifyPromiseAbility implements ModifyAbility{

    @Autowired
    private JdhAddressRpc jdhAddressRpc;

    @Autowired
    private AddressRpc addressRpc;

    @Resource
    private TdeClientUtil tdeClientUtil;

    /**
     * 当前能力点匹配的能力编码
     *
     * @return
     */
    @SuppressWarnings("JdAvoidMethodReturnEnum")
    @Override
    public ModifyAbilityCode getAbilityCode() {
        return ModifyAbilityCode.MODIFY_PROMISE;
    }

    /**
     * 修改
     *
     * @param ctx CTX
     */
    @Override
    public void execute(PromiseModifySubmitAbilityContext ctx) {
        JdhPromise jdhPromise = ctx.getPromise();
        PromiseTime timeBo = ctx.getAppointmentTime();
        PromiseAppointmentTime appointmentTime = new PromiseAppointmentTime(timeBo.getDateType(), timeBo.getAppointmentStartTime(), timeBo.getAppointmentEndTime(), timeBo.getIsImmediately());
        //修改预约时间
        jdhPromise.setAppointmentTime(appointmentTime);
        jdhPromise.setPromiseStatus(JdhPromiseStatusEnum.MODIFY_ING.getStatus());

        PromiseStation promiseStation  = ctx.getPromise().getStore();
        if(BusinessModeEnum.SELF_TEST_TRANSPORT.getCode().equals(ctx.getVerticalBusiness().getBusinessModeCode())){
            //非快检模式,支持地址修改
            //storeId & store地址都为空，报错
            if(StrUtil.isBlank(ctx.getStoreId())){
                throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_MISS_ERROR);
            }
            //如果传了storeId 并且 storeAddr没有传，补充storeAddr信息
            AddressDetailBO addressDetailBO = new AddressDetailBO();
            if(StrUtil.isNotBlank(ctx.getStoreId())){
                List<AddressDetailBO> addressDetailBOList = jdhAddressRpc.queryAddressList(ctx.getUserPin());
                if(CollUtil.isEmpty(addressDetailBOList)){
                    throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_ERROR);
                }
                Optional<AddressDetailBO> first = addressDetailBOList.stream().filter(ele -> ele.getAddressId().equals(Long.parseLong(ctx.getStoreId()))).findFirst();
                if(first.isPresent()){
                    addressDetailBO = first.get();
                    promiseStation.setStoreAddr(first.get().getFullAddress());
                }else{
                    throw new BusinessException(PromiseErrorCode.PROMISE_SUBMIT_APPOINTMENT_ADDRESS_ERROR);
                }
            }
            promiseStation.setStoreId(ctx.getStoreId());
            // 修改用户预约地址
            fillJdAddressInfo(promiseStation);
            //修改用户预约人手机号
            jdhPromise.setAppointmentPhone(tdeClientUtil.decrypt(addressDetailBO.getEncryptMobile()));
            //修改预约人名称
            List<JdhPromiseExtend> jdhPromiseExtends = new ArrayList<>();
            if(CollectionUtils.isEmpty(jdhPromise.getPromiseExtends())){
                jdhPromise.setPromiseExtends(jdhPromiseExtends);
            }else{
                jdhPromiseExtends = jdhPromise.getPromiseExtends();
            }
            List<JdhPromiseExtend> tempJdhPromiseExtends = jdhPromiseExtends.stream().filter(f->PromiseExtendKeyEnum.APPOINTMENT_USER_NAME.getFiledKey().equals(f.getAttribute())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(tempJdhPromiseExtends)){
                //新增
                JdhPromiseExtend jdhPromiseExtend = new JdhPromiseExtend();
                jdhPromiseExtend.setAttribute(PromiseExtendKeyEnum.APPOINTMENT_USER_NAME.getFiledKey());
                jdhPromiseExtend.setValue(addressDetailBO.getName());
                jdhPromiseExtend.setPromiseId(jdhPromise.getPromiseId());
                jdhPromiseExtends.add(jdhPromiseExtend);
            }else{
                //更新操作
                AddressDetailBO finalAddressDetailBO = addressDetailBO;
                tempJdhPromiseExtends.forEach(t->{
                    t.setValue(finalAddressDetailBO.getName());
                });
            }

        }

    }



    /**
     * 补充jd 地址信息
     *
     */
    private void fillJdAddressInfo(PromiseStation promiseStation){
        try {
            BaseAddressBo jdAddressFromAddress = addressRpc.getJDAddressFromAddress(promiseStation.getStoreAddr());
            promiseStation.setProvinceCode(jdAddressFromAddress.getProvinceCode());
            promiseStation.setProvinceName(jdAddressFromAddress.getProvinceName());
            promiseStation.setCityCode(jdAddressFromAddress.getCityCode());
            promiseStation.setCityName(jdAddressFromAddress.getCityName());
            promiseStation.setDistrictCode(jdAddressFromAddress.getDistrictCode());
            promiseStation.setDistrictName(jdAddressFromAddress.getDistrictName());
            promiseStation.setTownCode(jdAddressFromAddress.getTownCode());
            promiseStation.setTownName(jdAddressFromAddress.getTownName());
        } catch (Exception e) {
            log.error("ModifyPromiseAbility -> fillJdAddressInfo", e);
        }
    }

}
