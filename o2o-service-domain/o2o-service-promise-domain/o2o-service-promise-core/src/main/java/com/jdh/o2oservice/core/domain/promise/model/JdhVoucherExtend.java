package com.jdh.o2oservice.core.domain.promise.model;
import lombok.Data;
import java.io.Serializable;

/**
 * JdhVoucherExtend
 *
 * <AUTHOR>
 * @date 2024/01/18
 */
@Data
public class JdhVoucherExtend implements Serializable {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * autoPromise
     */
    private Boolean autoPromise;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商家id
     */
    private String venderId;

    /**
     * 草稿id
     */
    private String draftId;

    /**
     * 下单手机号
     */
    private String orderPhone;

    /**
     * 下单备注
     */
    private String orderRemark;

    /**
     * 履约人数
     */
    private Integer promisePatientNum;
    /**
     * 是否含有加项 0不包含 1包含
     */
    private Integer hasAdded;

    /**
     * 赠品对应主商品sku
     */
    private Long mainSkuId;

    /**
     * 赠品对应主商品名称
     */
    private String mainSkuName;

    /**
     * 意向护士
     */
    private PromiseIntendedNurse intendedNurse;
    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 是否已有采样盒
     */
    private Boolean preSampleFlag;

    /**
     * 履约已被冻结次数
     */
    private Integer freezeNum;

}
