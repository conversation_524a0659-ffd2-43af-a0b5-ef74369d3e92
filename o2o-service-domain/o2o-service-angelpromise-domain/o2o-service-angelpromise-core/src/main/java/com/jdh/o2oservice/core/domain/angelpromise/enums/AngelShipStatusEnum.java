package com.jdh.o2oservice.core.domain.angelpromise.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 达达运单状态枚举
 * @ClassName:AngelShipStatusEnum
 * @Description: 运单状态
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2023/11/25 15:04
 * @Vserion: 1.0
 **/
@Getter
public enum AngelShipStatusEnum {
    /**
     *
     */
    SHIP_ORDER_INIT(0, "待骑手接单", "正在呼叫骑手", Sets.newHashSet(1, 2, 3, 4, 5, 8, 9, 10, 100, 1000), null),

    WAITING_RECEIVE_ORDER(1, "待骑手接单", "正在呼叫骑手", Sets.newHashSet(1, 2, 3, 4, 5, 8, 9, 10, 100, 1000), AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_WAIT_RECEIVED),

    WAITING_RECEIVE_GOODS(2, "骑手正在赶来", "骑手正在赶来", Sets.newHashSet(2, 3, 4, 5, 8, 9, 10, 100), AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_RECEIVED),

    DELIVERING_GOODS(3, "配送中", "骑手配送中", Sets.newHashSet(3, 4, 5, 8, 9, 10), AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_IN_DELIVERY),

    ORDER_SHIP_FINISH(4, "已送到实验室", "骑手已到达实验室", Sets.newHashSet(4), AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_FINISH),

    ORDER_SHIP_CANCEL(5, "已取消", "已取消", Sets.newHashSet(5), AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CANCEL),

    DISPATCH_SHIP_ORDER(8, "派单", "派单", Sets.newHashSet(2, 3, 4, 5, 8, 9, 10, 100), null),

    DELIVERED_BACK(9, "妥投异常之物品返回中", "妥投异常之物品返回中", Sets.newHashSet(4, 9, 10), AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CANCEL),

    DELIVERED_BACK_FINISH(10, "妥投异常之物品返回完成", "妥投异常之物品返回完成", Sets.newHashSet(), AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_CANCEL),

    WAITING_TRANSFER(11, "转单中", "转单中", Sets.newHashSet(1, 2, 3, 4, 5, 9, 10, 100), null),

    KNIGHT_REACH_STORE(100, "骑手已上门", "骑手已上门", Sets.newHashSet(3, 4, 5, 8, 9, 10, 100), AngelShipEventTypeEnum.ANGEL_WORK_SHIP_EVENT_IN_STORE),

    CREATE_ORDER_FAIL(1000, "创建达达运单失败", "创建达达运单失败", Sets.newHashSet(), null),

    CANCEL_SHIP_FAIL(400, "主动取消运单失败", "主动取消运单失败", Sets.newHashSet(2, 3, 4, 5, 8, 9, 10, 100), null),

    CANCEL_APPOINTMENT_FAIL(401, "取消运单成功取消预约单失败", "取消运单成功取消预约单失败", Sets.newHashSet(2, 3, 4, 5, 8, 9, 10, 100), null),

    ;


    /**
     * 运单状态
     */
    private Integer shipStatus;

    /**
     * 运单状态描述
     */
    private String shipStatusDesc;

    /**
     * 运单状态描述
     */
    private String shipStatusShortDesc;

    /**
     * 下一状态集合
     */
    private Set<Integer> nextStatusSet;

    /**
     * 运单状态变更事件
     */
    private AngelShipEventTypeEnum angelShipEventTypeEnum;

    AngelShipStatusEnum(Integer shipStatus, String shipStatusDesc, String shipStatusShortDesc, Set<Integer> nextStatusSet, AngelShipEventTypeEnum angelShipEventTypeEnum) {
        this.shipStatus = shipStatus;
        this.shipStatusDesc = shipStatusDesc;
        this.shipStatusShortDesc = shipStatusShortDesc;
        this.nextStatusSet = nextStatusSet;
        this.angelShipEventTypeEnum = angelShipEventTypeEnum;
    }

    private static List<Integer> refundShipStatusSet = Lists.newArrayList(AngelShipStatusEnum.ORDER_SHIP_CANCEL.getShipStatus(), AngelShipStatusEnum.DELIVERED_BACK.getShipStatus(), AngelShipStatusEnum.DELIVERED_BACK_FINISH.getShipStatus());
    private static List<Integer> illegalShipStatusSet = Lists.newArrayList(AngelShipStatusEnum.DELIVERED_BACK.getShipStatus(), AngelShipStatusEnum.DELIVERED_BACK_FINISH.getShipStatus());
    public static List<Integer> canRefundShipStatusList = Lists.newArrayList(AngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus(), AngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus(), AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus(),
            AngelShipStatusEnum.KNIGHT_REACH_STORE.getShipStatus());
    public static List<Integer> showSendCodeStatus = Lists.newArrayList(AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus(), AngelShipStatusEnum.KNIGHT_REACH_STORE.getShipStatus());

    /**
     * 获取状态描述
     *
     * @param status
     * @return
     */
    public static String getStatusDesc(Integer status) {
        if(Objects.isNull(status)){
            return null;
        }

        for (AngelShipStatusEnum value : AngelShipStatusEnum.values()) {
            if(value.getShipStatus().equals(status)){
                return value.getShipStatusDesc();
            }
        }
        return null;
    }

    /**
     * 获取状态描述
     *
     * @param status
     * @return
     */
    public static String getStatusShortDesc(Integer status) {
        if(Objects.isNull(status)){
            return null;
        }

        for (AngelShipStatusEnum value : AngelShipStatusEnum.values()) {
            if(value.getShipStatus().equals(status)){
                return value.getShipStatusShortDesc();
            }
        }
        return null;
    }

    /**
     * 匹配是否为逆向状态
     *
     * @param status
     * @return
     */
    public static Boolean matchRefundStatus(Integer status){
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return refundShipStatusSet.contains(status);
    }

    /**
     * 匹配是否为逆向状态
     *
     * @param status
     * @return
     */
    public static Boolean matchShipIllegalStatus(Integer status){
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return illegalShipStatusSet.contains(status);
    }

    /**
     * 骑手接单状态匹配
     *
     * @param status
     * @return
     */
    public static Boolean matchReceiveOrderStatus(Integer status) {
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus().equals(status);
    }

    /**
     * 派单状态匹配
     *
     * @param status
     * @return
     */
    public static Boolean matchCallOrderStatus(Integer status) {
        if(Objects.isNull(status)){
            return Boolean.FALSE;
        }
        return AngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus().equals(status);
    }

    /**
     * 匹配运单id
     *
     * @param shipStatus
     * @return
     */
    public static AngelShipStatusEnum matchShipStatusEnum(Integer shipStatus){
        for (AngelShipStatusEnum value : AngelShipStatusEnum.values()) {
            if(value.getShipStatus().equals(shipStatus)){
                return value;
            }
        }
        return null;
    }

    /**
     * 判断当前运单状态是否可以取消
     *
     * @param shipStatus
     * @return
     */
    public static boolean matchCanCancelStatus(Integer shipStatus) {
        if(canRefundShipStatusList.contains(shipStatus)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 获取运单状态事件
     *
     * @param shipStatus
     * @return
     */
    public static AngelShipEventTypeEnum matchAngelShipEventTypeEnum(Integer shipStatus){
        if(Objects.isNull(shipStatus)){
            return null;
        }
        for (AngelShipStatusEnum value : AngelShipStatusEnum.values()) {
            if(value.getShipStatus().equals(shipStatus)){
                return value.getAngelShipEventTypeEnum();
            }
        }
        return null;
    }

    /**
     * 判断正向状态
     * @param shipStatus
     * @return
     */
    public static Boolean checkForwardStatus(Integer shipStatus){
        return AngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus().equals(shipStatus) ||  AngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus().equals(shipStatus)
                ||  AngelShipStatusEnum.WAITING_RECEIVE_GOODS.getShipStatus().equals(shipStatus) ||  AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus().equals(shipStatus)
                ||  AngelShipStatusEnum.ORDER_SHIP_FINISH.getShipStatus().equals(shipStatus) ||  AngelShipStatusEnum.KNIGHT_REACH_STORE.getShipStatus().equals(shipStatus);
    }

    /**
     * 匹配不需要取消的运单
     *
     * @param shipStatus
     * @return
     */
    public static Boolean matchNoNeedCancel(Integer shipStatus) {
        if(Objects.isNull(shipStatus)){
            return Boolean.FALSE;
        }
        return DELIVERING_GOODS.getShipStatus().equals(shipStatus) || ORDER_SHIP_FINISH.getShipStatus().equals(shipStatus);
    }

    /**
     * 获取有效状态
     */
    public static List<Integer> getValidStatus() {
        return Lists.newArrayList(SHIP_ORDER_INIT.getShipStatus(), WAITING_RECEIVE_ORDER.getShipStatus(), WAITING_RECEIVE_GOODS.getShipStatus(),
                DELIVERING_GOODS.getShipStatus(), ORDER_SHIP_FINISH.getShipStatus(), DISPATCH_SHIP_ORDER.getShipStatus(), DELIVERED_BACK.getShipStatus(),
                DELIVERED_BACK_FINISH.getShipStatus(), WAITING_TRANSFER.getShipStatus(), KNIGHT_REACH_STORE.getShipStatus());
    }

    /**
     * 送达之前的运单状态
     */
    public static List<Integer> finishBeforeStatus() {
        return Lists.newArrayList(SHIP_ORDER_INIT.getShipStatus(), WAITING_RECEIVE_ORDER.getShipStatus(), WAITING_RECEIVE_GOODS.getShipStatus(),
                DELIVERING_GOODS.getShipStatus(), KNIGHT_REACH_STORE.getShipStatus());
    }

    /**
     * 获取有效状态
     */
    public static List<Integer> getSendingStatus() {
        return Lists.newArrayList(
                DELIVERING_GOODS.getShipStatus(), ORDER_SHIP_FINISH.getShipStatus());
    }
}
