package com.jdh.o2oservice.core.domain.angelpromise.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 取消运单状态枚举
 * @ClassName:AngelShipStatusEnum
 * @Description: 运单状态
 * @Vserion: 1.0
 **/
@Getter
public enum AngelShipCancelCodeStatusEnum {

    /**
     * 待服务
     */
    INIT(0, "默认值", false),

    /**
     * 配送员取消 退
     */
    DELIVERY_CANCEL(1, "配送员取消运单", true),

    /**
     * 运营取消 不退
     */
    OPERATE_CANCEL(2, "运营取消运单", false),

    /**
     * 系统取消 不退
     */
    SYSTEM_CANCEL(3, "系统job取消运单", false),

    /**
     * 运营调整用户预约时间取消 不退
     */
    ADJUST_APPOINT_TIME_CANCEL(4, "运营调整用户预约时间取消运单", false),

    /**
     * 实验室履约单取消 不退
     */
    MEDICAL_PROMISE_CANCEL(5, "用户申请取消订单进而取消运单", false),

    /**
     * 护士取消运单 不退
     */
    NURSE_CANCEL(6, "护士取消运单", false),
    /**
     * 运营端指派实验室 不退
     */
    TARGET_STATION(7, "运营端指派实验室",false),
    /**
     *
     */
    USER_ADJUST_APPOINT_TIME_CANCEL(8, "用户调整预约时间和地址取消运单", false),
    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;

    /**
     * 是否退款
     */
    private Boolean isRefund;

    /**
     * 获取取消原因码
     *
     * @param type
     * @return
     */
    public static AngelShipCancelCodeStatusEnum findCancelStatusEnum(Integer type) {
        if(Objects.isNull(type)) {
            return AngelShipCancelCodeStatusEnum.INIT;
        }
        for (AngelShipCancelCodeStatusEnum value : values()) {
            if(value.getType().equals(type)) {
                return value;
            }
        }
        return AngelShipCancelCodeStatusEnum.INIT;
    }

    AngelShipCancelCodeStatusEnum(Integer type, String desc, Boolean isRefund) {
        this.type = type;
        this.desc = desc;
        this.isRefund = isRefund;
    }
}
