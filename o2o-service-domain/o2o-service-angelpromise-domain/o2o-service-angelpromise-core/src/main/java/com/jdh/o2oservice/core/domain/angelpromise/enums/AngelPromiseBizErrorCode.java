package com.jdh.o2oservice.core.domain.angelpromise.enums;

import com.jdh.o2oservice.base.exception.errorcode.AbstractErrorCode;

/**
 * @InterfaceName:AngelPromiseBizErrorCode
 * @Description: 服务者履约错误码
 *
 * @Author: ya<PERSON>qing<PERSON>
 * @Date: 2024/4/18 16:54
 * @Vserion: 1.0
 **/
public enum AngelPromiseBizErrorCode implements AbstractErrorCode {

    /**
     *
     */
    REPEAT_CREATE("90001", "正在创建工单,请勿重复创建"),

    ANGEL_WORK_NOT_EXIST("90002", "服务者工单不存在"),

    ANGEL_WORK_EXECUTE_ARGUMENT_ILLEGAL("90003", "参数异常"),

    ANGEL_WORK_STATE_EXECUTE_ILLEGAL("90004", "服务者工单的状态不允许执行该事件"),

    ANGEL_TASK_NOT_EXIST("90005", "任务单不存在"),

    ANGEL_TASK_STATE_EXECUTE_ILLEGAL("90005", "服务者任务单的状态不允许执行该事件"),

    CREATE_DADA_SHIP_ERROR("90006", "创建达达运单失败"),

    RECEIVER_ADDRESS_INFO_ERROR("90007", "收货人地址错误"),

    SUPPLIER_ADDRESS_INFO_ERROR("90008", "发件人地址错误"),

    DADA_CALL_BACK_HANDLE_ERROR("90009", "达达回调状态通知异常"),

    REPEAT_DO("90010", "重复操作,稍后重试!"),

    EVENT_INFO_ERROR("90011", "事件缺失内容"),

    SHIP_TASK_NOT_EXIST("90012", "运单信息不存在"),

    VERIFICATION_CODE_IN_ERROR_STATUS("90015", "工单状态错误，请勿重复核销兑换码"),

    BIND_SPECIMEN_CODE_IN_ERROR_STATUS("90016", "工单状态错误，请勿重复绑定条码"),

    BIND_SPECIMEN_CODE_PROMISE_QUERY_ERROR("90017", "检验单查询失败，请重新绑定条码"),

    BIND_SPECIMEN_CODE_ERROR("90018", "检验单绑定条码失败"),

    WORK_STOP("90019", "工单暂停中"),

    GIS_ERR("90020", "地图交互失败"),

    ANGEL_NOT_EXIST("90021", "服务者信息不存在"),
    SIGNATURE_IMG_IS_NULL("90022", "签名图片不能为空"),
    MEDICAL_CERTIFICATE_IS_NULL("90023", "就诊记录图片不能为空"),


    PROMISE_NOT_EXIST("90024", "履约单信息不存在"),

    ORDER_NOT_EXIST("90025", "订单信息不存在"),

    TRUNCATED_GREATER_THAN_YEARS("90026", "查询时间跨度大于一年"),

    PROMISE_ID_NULL("90027", "履约单ID为空"),

    ANGEL_STATION_ID_ERROR("90028", "服务站id格式不正确"),

    SKU_ID_ERROR("90029", "skuId格式不正确"),

    ANGEL_ID_ERROR("90030", "服务者id格式不正确"),

    ANGEL_WORK_HANDLE_ERROR("90031", "服务者工单状态处理失败"),

    MEDICAL_PROMISE_NOT_EXIST("90032", "检测单信息不存在"),

    VERTICAL_EXTENSION_NOT_EXIST("90033", "垂直业务身份实现不存在"),

    MEDICAL_PROMISE_STATION_NOT_EXIST("90034", "检测点信息不存在"),

    EXIST_NOT_DELIVER_STATION("90035", "请确认全部样本已配送"),

    LABORATORY_GEO_NOT_EXIST("90036", "没有查询到实验室的经纬度信息"),

    DISTANCE_NOT_LESS_THAN_CONFIG("90037", "您距离实验室超过100米,请确认您已到达实验室"),

    SHIP_STATUS_CAN_NOT_CANCEL("90038", "运单状态不允许退款"),

    REPEAT_SEETLE("90039", "服务单结算中,请勿重复"),

    ANGEL_WORK_SOURCE_REPEAT("90040", "该服务者已经派过该工单不能继续派单"),

    SHARED_LOCK_EXECUTE_ERROR("90041", "乐观锁执行失败"),

    DISPATCH_STATION_ERROR("90042", "派实验室失败"),

    CREATE_ANGEL_WORK_ERROR("90043", "创建服务者工单异常!"),

    SHIP_TYPE_ERROR("90044", "运单类型错误!"),

    CANCEL_SHIP_ERROR("90045", "取消运单失败!"),

    ANGEL_ID_FORMAT_ERROR("90046", "人员ID格式不正确!"),

    MEDICAL_STATION_NOT_DISPATCH_ERROR("90047", "存在未派实验室的项目,请联系客服!"),

    WORK_CANCEL_SMALL_THREE_HOUR_ERROR("90048", "距服务开始还有不到2小时，不能取消工单!"),

    ANGEL_POSITION_INFO_NOT_FOUND("90049", "无法获取您的位置，无法完成本次配送!"),

    ANGEL_WORK_INFO_DELETION_ERROR("90050", "服务单确实信息，请联系客服"),

    ANGEL_WORK_STATUS_FINISH_ERROR("90051", "不允许完成送检，请先确认配送方式"),

    ANGEL_EXECUTE_TASK_ERROR("90052", "执行任务失败"),

    DISTRIBUTE_LOCK_ERROR("90053", "获取分布式锁失败"),

    ANGEL_LETTER_OF_CONSENT_IS_MUST("90054", "知情同意书必传"),

    SUPPLIER_ADDRESS("90055", "寄件地址不能为空"),

    WORK_STATUS_INVALID_ERROR("90056", "工单状态处于失效状态,不能进行操作"),

    SPECIMEN_CODE_HAS_EMPTY_ERROR("90057", "存在没有绑定条码的项目,请您核对"),

    DEDUCT_AMOUNT_ERROR("90058", "扣减金额错误,请核查"),

    RECEIVER_IS_NOT_EXIST_ERROR("90059", "接收点不存在"),

    TRADE_REFUND_ERROR("90060", "订单退款失败"),

    WORK_EXPORT_NUM_ERROR("90061", "导出的工单数量大于300条"),

    WORK_EXPORT_START_DATE_EMPTY_ERROR("90062", "导出工单的开始时间为空"),

    WORK_EXPORT_START_DATE_OVER_ERROR("90063", "开始和截止时间不能超过3天范围"),

    WORK_STATUS_CAN_NOT_SHOW_ANGEL_TRACK_ERROR("90064", "该状态不支持查看护士位置"),

    RIDER_PROVIDER_EMPTY_ERROR("90064", "无骑手供应商"),

    SHIP_STATUS_CAN_NOT_EXECUTE("90065", "运单状态不允许执行"),

    ANGEL_STATION_ERROR("90066", "服务者轨迹不存在"),

    SHIP_ETA_ERROR("90067", "没有查询到服务者的ETA信息"),

    PROVIDER_LAUNCH_CANCEL_SHIP("90068", "业务测发起的运单取消不需要再执行取消逻辑"),

    ANGEL_WORK_MODIFY_DATE_FAIL("90069", "修改工单服务时间失败"),

    ANGEL_TASK_MODIFY_DATE_FAIL("90070", "修改任务服务时间失败"),

    WORK_CANCEL_NO_WORK_ERROR("90071", "工单信息不存在"),

    RECEIVE_MSG_USER_ERP_NOT_NULL("90072", "导出结果接收人ERP不能为空"),

    MODIFY_PROMISE_ERROR("90073", "修改太频繁，请稍后重试")

    ;


    AngelPromiseBizErrorCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    private String code;

    private String description;

    /**
     * 错误码值
     *
     * @return
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 错误码描述信息
     *
     * @return
     */
    @Override
    public String getDescription() {
        return description;
    }
}
