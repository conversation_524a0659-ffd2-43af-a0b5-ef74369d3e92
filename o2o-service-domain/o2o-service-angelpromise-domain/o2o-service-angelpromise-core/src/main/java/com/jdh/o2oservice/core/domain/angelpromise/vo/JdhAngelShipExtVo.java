package com.jdh.o2oservice.core.domain.angelpromise.vo;

import com.jdh.o2oservice.export.angelpromise.dto.AngelWorkCreateShipExtCmd;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * @ClassName:JdhAngelShipExtVo
 * @Description: 扩展值对象
 * @Author: yaoqinghai
 * @Date: 2024/4/18 20:45
 * @Vserion: 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdhAngelShipExtVo {

    /**
     * 运单备注
     */
    private String shipRemark;

    /**
     * 被服务人id
     */
    private List<ShipTask> shipTaskList;

    /**
     * 扩展信息
     */
    private AngelWorkCreateShipExtCmd angelWorkCreateShipExt;

}
