package com.jdh.o2oservice.core.domain.angelpromise.repository.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @author:lichen55
 * @Description:
 * @date 2024-05-19 23:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AngelShipDBQuery {

    /**
     * 运单id
     */
    private List<Long> shipIds;

    /**
     * 服务工单Id
     */
    private Long workId;

    /**
     * 运单状态
     */
    private Set<Integer> status;

    /**
     * 接收点Id
     */
    private String receiverId;

    /**
     * 接收点Id集合
     */
    private Set<String> receiverIds;

    /**
     * 服务工单Id列表
     */
    private Set<Long> workIds;

    /**
     * 查询计划呼叫时间开始
     */
    private Date queryPlanCallTimeStart;

    private List<Integer> types;

    private String outShipId;//外部运单号

    public AngelShipDBQuery(Long workId){
        this.workId = workId;
    }

}
