package com.jdh.o2oservice.core.domain.angelpromise.enums;

import com.jdh.o2oservice.base.event.EventType;
import com.jdh.o2oservice.base.model.AggregateCode;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelShipCreateFailBody;
import com.jdh.o2oservice.core.domain.angelpromise.event.eventbody.AngelShipEventBody;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * AngelWorkEventTypeEnum
 * @author: yaoqinghai
 * @date: 2024/01/22 11:13
 * @version: 1.0
 */
@AllArgsConstructor
public enum AngelShipEventTypeEnum implements EventType {

    /**
     *
     */
    ANGEL_WORK_SHIP_EVENT_WAIT_RECEIVED(AngelWorkAggregateEnum.SHIP, "waitReceived", "待接单", null),

    ANGEL_WORK_SHIP_EVENT_RECEIVED(AngelWorkAggregateEnum.SHIP, "received", "已接单", null),

    ANGEL_WORK_SHIP_EVENT_IN_STORE(AngelWorkAggregateEnum.SHIP, "inStore", "已到店", null),

    ANGEL_WORK_SHIP_EVENT_IN_DELIVERY(AngelWorkAggregateEnum.SHIP, "inDelivery", "配送中", null),

    ANGEL_WORK_SHIP_EVENT_FINISH(AngelWorkAggregateEnum.SHIP, "finish", "完成", null),

    ANGEL_WORK_SHIP_EVENT_CANCEL(AngelWorkAggregateEnum.SHIP, "cancel", "取消", AngelShipEventBody.class),

    ANGEL_WORK_SHIP_EVENT_TIMEOUT_CANCEL(AngelWorkAggregateEnum.SHIP, "timeOutCancel", "超时取消", null),

    ANGEL_WORK_SHIP_EVENT_ABNORMAL_DELIVERY_RETURN(AngelWorkAggregateEnum.SHIP, "return", "异常返回", AngelShipEventBody.class),

    ANGEL_WORK_SHIP_EVENT_ABNORMAL_DELIVERY(AngelWorkAggregateEnum.SHIP, "abnormal", "妥投异常", AngelShipEventBody.class),

    //###################################创建运单失败############################################
    ANGEL_WORK_SHIP_EVENT_CREATE_FAIL(AngelWorkAggregateEnum.SHIP, "createFail", "创建失败", AngelShipCreateFailBody.class),

    ANGEL_WORK_SHIP_EVENT_SYNC(AngelWorkAggregateEnum.SHIP, "sync", "预约中心运单同步", AngelShipEventBody.class),

    ;

    private AngelWorkAggregateEnum aggregateCode;

    private String code;

    private String desc;

    private Class<?> bodyClass;

    public static AngelShipEventTypeEnum getEnumByEvent(String eventCode) {
        if (StringUtils.isBlank(eventCode)) {
            return null;
        }
        for (AngelShipEventTypeEnum value : values()) {
            if (value.getCode().equals(eventCode)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 事件所属的领域
     */
    @Override
    public AggregateCode getAggregateType() {
        return aggregateCode;
    }

    /**
     * 事件编码
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 事件描述
     */
    @Override
    public String getDesc() {
        return desc;
    }

    /**
     * @return
     */
    @Override
    public Class<?> bodyClass() {
        return bodyClass;
    }
}
