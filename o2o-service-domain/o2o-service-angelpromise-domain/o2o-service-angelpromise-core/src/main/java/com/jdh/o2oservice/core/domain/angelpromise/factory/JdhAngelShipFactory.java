package com.jdh.o2oservice.core.domain.angelpromise.factory;

import com.jdh.o2oservice.base.constatnt.CommonConstant;
import com.jdh.o2oservice.base.constatnt.NumConstant;
import com.jdh.o2oservice.base.enums.ShipRepeatReasonEnum;
import com.jdh.o2oservice.base.enums.YnStatusEnum;
import com.jdh.o2oservice.base.factory.GenerateIdFactory;
import com.jdh.o2oservice.base.util.CoordinateUtil;
import com.jdh.o2oservice.base.util.SpringUtil;
import com.jdh.o2oservice.common.enums.DeliveryTypeEnum;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelShipCallBackContext;
import com.jdh.o2oservice.core.domain.angelpromise.context.AngelWorkShipCreateContext;
import com.jdh.o2oservice.core.domain.angelpromise.enums.AngelShipStatusEnum;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShip;
import com.jdh.o2oservice.core.domain.angelpromise.model.AngelShipHistory;
import com.jdh.o2oservice.core.domain.angelpromise.model.JdhAngelShipFee;
import com.jdh.o2oservice.core.domain.angelpromise.vo.JdhAngelShipExtVo;
import com.jdh.o2oservice.ext.ship.reponse.CreateShipExtFeeResponse;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.util.Date;
import java.util.Objects;

/**
 * @ClassName:JdhAngelShipFactory
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/21 20:57
 * @Vserion: 1.0
 **/
public class JdhAngelShipFactory {

    public static AngelShip create(AngelWorkShipCreateContext shipCreateContext){
        AngelShip angelShip = AngelShip.builder()
                .workId(Long.valueOf(shipCreateContext.getWorkId()))
                .shipId(SpringUtil.getBean(GenerateIdFactory.class).getId())
                .shopNo(shipCreateContext.getShopNo())
                .creator(shipCreateContext.getAngelPin())
                .createTime(new Date())
                .receiverFullAddress(shipCreateContext.getReceiverAddress())
                .receiverId(shipCreateContext.getProviderShopNo())
                .receiverName(shipCreateContext.getReceiverName())
                .senderFullAddress(shipCreateContext.getSupplierAddress())
                .senderName(shipCreateContext.getSupplierName())
                .senderNameIndex(shipCreateContext.getSupplierName())
                .senderPhone(shipCreateContext.getSupplierPhone())
                .senderPhoneIndex(shipCreateContext.getSupplierPhone())
                .shipStatus(AngelShipStatusEnum.SHIP_ORDER_INIT.getShipStatus())
                .repeatType(ShipRepeatReasonEnum.PRIMARY.getReasonType())
                .type(shipCreateContext.getDeliveryType())
                .jdhAngelShipExtVo(JdhAngelShipExtVo.builder().shipTaskList(shipCreateContext.getShipTaskList()).angelWorkCreateShipExt(shipCreateContext.getAngelWorkCreateShipExt()).build())
                .updater(shipCreateContext.getAngelPin())
                .updateTime(new Date())
                .version(CommonConstant.ONE)
                .yn(YnStatusEnum.YES.getCode())
                .build();

        if (Objects.nonNull(shipCreateContext.getAngelWorkCreateShipExt())){
            angelShip.setOutShipId(shipCreateContext.getAngelWorkCreateShipExt().getThirdShipOrder());
        }


        //运单扩展信息
        angelShip.extendVoRemark(shipCreateContext.getInfo());

        AngelShipHistory shipHistory = AngelShipHistory.builder()
                .shipId(angelShip.getShipId())
                .workId(angelShip.getWorkId())
                .afterStatus(angelShip.getShipStatus())
                .operateTime(new Date())
                .yn(YnStatusEnum.YES.getCode())
                .createTime(new Date())
                .creator(shipCreateContext.getAngelPin())
                .build();

        angelShip.setShipHistoryList(Lists.newArrayList(shipHistory));
        return angelShip;
    }

    public static JdhAngelShipFee createShipFee(AngelShip angelShip, CreateShipExtFeeResponse extFeeResponse) {
        JdhAngelShipFee angelShipFee = new JdhAngelShipFee();
        angelShipFee.setShipFeeId(SpringUtil.getBean(GenerateIdFactory.class).getId());
        angelShipFee.setShipId(angelShip.getShipId());
        angelShipFee.setAmount(extFeeResponse.getAmount());
        angelShipFee.setTotalAmount(extFeeResponse.getTotalAmount());
        angelShipFee.setDiscountAmount(extFeeResponse.getDiscountAmount());
        angelShipFee.setTotalAmountDetail(extFeeResponse.getTotalAmountDetail());
        angelShipFee.setInterestList(extFeeResponse.getInterestList());
        angelShipFee.setYn(YnStatusEnum.YES.getCode());
        angelShipFee.setVersion(NumConstant.NUM_1);
        angelShipFee.setCreateTime(new Date());
        angelShipFee.setUpdateTime(new Date());

        return angelShipFee;
    }

    /**
     * 组装ship
     *
     * @param angelShip
     * @param callBackContext
     */
    public static AngelShip swapShip(AngelShip angelShip, AngelShipCallBackContext callBackContext) {
        if(DeliveryTypeEnum.SHANSONG_DELIVERY.getType().equals(angelShip.getType())) {
            //百度坐标系转滕讯坐标系
            double[] riderPoint = CoordinateUtil.bd09ToGcj02(callBackContext.getLongitude(), callBackContext.getLatitude());
            angelShip.setTransferStartLng(riderPoint[0]);
            angelShip.setTransferStartLat(riderPoint[1]);
        }else {
            angelShip.setTransferStartLng(callBackContext.getLongitude());
            angelShip.setTransferStartLat(callBackContext.getLatitude());
        }
        //骑手配送中会生成收件码，更新运单中的收件码信息
        if(AngelShipStatusEnum.DELIVERING_GOODS.getShipStatus().equals(angelShip.getShipStatus())) {
            angelShip.setFinishCode(callBackContext.getFinishCode());
            angelShip.setFinishCodeIndex(callBackContext.getFinishCode());
        }

        if(StringUtils.isNotEmpty(callBackContext.getLogisticsMessage())){
            angelShip.setLogisticsMessage(callBackContext.getLogisticsMessage());
        }

        //待接单状态清空骑手信息
        if(AngelShipStatusEnum.WAITING_RECEIVE_ORDER.getShipStatus().equals(angelShip.getShipStatus())) {
            angelShip.setTransferPhone(null);
            angelShip.setTransferPhoneIndex(null);
            angelShip.setTransferName(null);
            angelShip.setTransferNameIndex(null);
            angelShip.setTransferId(null);
            angelShip.setTransferHeadImg(null);
            angelShip.setOutShipId(null);
            return angelShip;
        }

        if(StringUtils.isNotBlank(callBackContext.getDmMobile())) {
            angelShip.setTransferPhone(callBackContext.getDmMobile());
            angelShip.setTransferPhoneIndex(callBackContext.getDmMobile());
        }
        if(StringUtils.isNotBlank(callBackContext.getDmName())) {
            angelShip.setTransferName(callBackContext.getDmName());
            angelShip.setTransferNameIndex(callBackContext.getDmName());
        }
        if(StringUtils.isNotBlank(callBackContext.getDmId())) {
            angelShip.setTransferId(callBackContext.getDmId());
        }
        if(StringUtils.isNotBlank(callBackContext.getDmHeadIcon())) {
            angelShip.setTransferHeadImg(callBackContext.getDmHeadIcon());
        }
        if(StringUtils.isNotBlank(callBackContext.getSendCode())) {
            angelShip.setSendCode(callBackContext.getSendCode());
            angelShip.setSendCodeIndex(callBackContext.getSendCode());
        }
        if(StringUtils.isNotBlank(callBackContext.getClientId())){
            angelShip.setOutShipId(callBackContext.getClientId());
        }
        return angelShip;
    }
}
