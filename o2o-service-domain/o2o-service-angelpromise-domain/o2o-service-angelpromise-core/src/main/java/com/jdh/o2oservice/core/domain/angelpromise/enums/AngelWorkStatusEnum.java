package com.jdh.o2oservice.core.domain.angelpromise.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @author:lichen55
 * @createTime: 2024-04-18 18:32
 * @Description:
 * @date 2024-05-09 23:44
 */
@Getter
@AllArgsConstructor
public enum AngelWorkStatusEnum implements AngelPromiseStatus {

    /**
     * 初始状态
     */
    INIT(0, "INIT", "待服务"),


    /**
     * 待接单
     */
    WAIT_RECEIVE(1, "待接单", "待接单"),

    /**
     * 已接单
     */
    RECEIVED(2, "已接单", "待服务"),

    /**
     * 待服务
     */
    WAIT_SERVICE(3, "已出门", "正在前往"),

    /**
     * 服务中
     */
    SERVICING(4, "服务中", "服务中"),

    /**
     * 服务结束
     */
    SERVICED(5, "上门结束", "服务中"),

    /**
     * 送检中
     */
    DELIVERING(6, "送检中", "送检中"),

    /**
     * 服务完成
     */
    COMPLETED(7, "服务完成", "服务完成"),

    /**
     * 已退款
     */
    REFUNDED(8, "已退款", "已退款"),

    /**
     * 已取消
     */
    CANCEL(9, "已取消", "已取消"),

    /**
     * 已过期
     */
    EXPIRED(10, "已过期", "已过期"),
    ;

    /**
     * 类型
     */
    private Integer type;

    /**
     * desc
     */
    private String desc;

    /**
     * showDesc(端上的状态展示)
     */
    private String showDesc;

    /**
     * 根据类型查询枚举详情
     * @param type
     * @return
     */
    public static AngelWorkStatusEnum getEnumByCode(Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }
        for (AngelWorkStatusEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 获取有效状态
     */
    public static List<Integer> getValidStatus() {
        return Lists.newArrayList(AngelWorkStatusEnum.INIT.getType(), AngelWorkStatusEnum.WAIT_RECEIVE.getType(), AngelWorkStatusEnum.RECEIVED.getType(),
                AngelWorkStatusEnum.WAIT_SERVICE.getType(), AngelWorkStatusEnum.SERVICING.getType(), AngelWorkStatusEnum.SERVICED.getType(),
                AngelWorkStatusEnum.DELIVERING.getType(), AngelWorkStatusEnum.COMPLETED.getType());
    }

    /**
     * 获取待服务状态
     */
    public static List<Integer> getWaitServiceStatus() {
        return Lists.newArrayList(AngelWorkStatusEnum.INIT.getType(), AngelWorkStatusEnum.WAIT_RECEIVE.getType(), AngelWorkStatusEnum.RECEIVED.getType(),
                AngelWorkStatusEnum.WAIT_SERVICE.getType(), AngelWorkStatusEnum.SERVICING.getType(), AngelWorkStatusEnum.SERVICED.getType(),
                AngelWorkStatusEnum.DELIVERING.getType());
    }

    /**
     * 获取有效状态
     */
    public static Set<Integer> getWorkStatus() {
        return Sets.newHashSet(AngelWorkStatusEnum.INIT.getType(), AngelWorkStatusEnum.WAIT_RECEIVE.getType(), AngelWorkStatusEnum.RECEIVED.getType(),
                AngelWorkStatusEnum.WAIT_SERVICE.getType(), AngelWorkStatusEnum.SERVICING.getType(), AngelWorkStatusEnum.SERVICED.getType(),
                AngelWorkStatusEnum.DELIVERING.getType(), AngelWorkStatusEnum.COMPLETED.getType(), AngelWorkStatusEnum.REFUNDED.getType());
    }

    /**
     * 保单不显示状态
     */
    public static List<Integer> InvalidStatus() {
        return Lists.newArrayList(
                AngelWorkStatusEnum.REFUNDED.getType(),
                AngelWorkStatusEnum.CANCEL.getType());
    }


    /**
     * 检查工单状态是否可用
     *
     * @param workStatus
     */
    public static boolean workStatusNoAlive(Integer workStatus) {
        if(Objects.isNull(workStatus)){
            return false;
        }
        return AngelWorkStatusEnum.REFUNDED.getType().equals(workStatus)
                || AngelWorkStatusEnum.CANCEL.getType().equals(workStatus);
    }

    /**
     * 展示服务者实时位置
     *
     * @param workStatus
     * @return
     */
    public static  boolean showAngelRealTrackStatus(Integer workStatus) {
        if(Objects.isNull(workStatus)){
            return Boolean.FALSE;
        }
        return WAIT_SERVICE.getType().equals(workStatus);
    }

    /**
     * 展示服务者实时位置
     *
     * @return
     */
    public static  Set<Integer> deliveryBlankStatus() {
        return Sets.newHashSet(
                  AngelWorkStatusEnum.SERVICED.getType(),
                AngelWorkStatusEnum.DELIVERING.getType(), AngelWorkStatusEnum.COMPLETED.getType());
    }

    /**
     * 获取所有状态
     */
    public static Set<Integer> getWorkStatusAll() {
        return Sets.newHashSet(AngelWorkStatusEnum.INIT.getType(), AngelWorkStatusEnum.WAIT_RECEIVE.getType(), AngelWorkStatusEnum.RECEIVED.getType(),
                AngelWorkStatusEnum.WAIT_SERVICE.getType(), AngelWorkStatusEnum.SERVICING.getType(), AngelWorkStatusEnum.SERVICED.getType(),
                AngelWorkStatusEnum.DELIVERING.getType(), AngelWorkStatusEnum.COMPLETED.getType(), AngelWorkStatusEnum.REFUNDED.getType(),AngelWorkStatusEnum.CANCEL.getType(),AngelWorkStatusEnum.EXPIRED.getType());
    }
}
