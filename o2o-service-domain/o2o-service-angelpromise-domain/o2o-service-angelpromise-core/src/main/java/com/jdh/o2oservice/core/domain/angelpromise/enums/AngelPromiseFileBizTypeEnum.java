package com.jdh.o2oservice.core.domain.angelpromise.enums;

import com.jdh.o2oservice.base.model.FileBizType;

/**
 * @author: yang<PERSON>yu
 * @date: 2024/4/30 5:08 下午
 * @version: 1.0
 */
public enum AngelPromiseFileBizTypeEnum implements FileBizType {

    /**
     * 用户签名图片文件
     */
    SIGNATURE("signature"),

    /** 知情同意书模版 */
    LETTER_OF_CONSENT_TEMPLATE("letter_of_consent_template"),

    /** 知情同意书（已签名） */
    LETTER_OF_CONSENT("letter_of_consent"),

    /** 就诊记录 */
    MEDICAL_CERTIFICATE("medical_certificate"),

    /** 着装图片文件 */
    CLOTHING("clothing"),

    /** 医疗废弃物销毁图片文件 */
    MEDICAL_WASTE("medical_waste_destroy"),

    /** 服务记录 */
    SERVICE_RECORD("service_record"),

    /** 录音文件 */
    SOUND_RECORDING("sound_recording"),
    /**
     * 电话录音文件
     */
    CALL_SOUND("call_sound"),
    ;

    /**
     *
     * @param fileBizType
     */
    AngelPromiseFileBizTypeEnum(String fileBizType) {
        this.fileBizType = fileBizType;
    }

    /**
     * 文件类型
     */
    private String fileBizType;

    @Override
    public String getBizType() {
        return fileBizType;
    }
}
