package com.jdh.o2oservice.common.enums;


import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName:DeliveryTypeEnum
 * @Description:
 * @Author: yaoqinghai
 * @Date: 2024/4/23 11:32
 * @Vserion: 1.0
 **/
@Getter
public enum DeliveryTypeEnum {

    /**
     *
     */
    SELF_DELIVERY(1, "自配送", AngelDetailTypeEnum.SELF_SUPPLIER),

    RIDER_DELIVERY(2, "达达配送", AngelDetailTypeEnum.DADA_SUPPLIER),

    SHANSONG_DELIVERY(3, "闪送配送", AngelDetailTypeEnum.SHANSONG_SUPPLIER),

    SHUNFENG_DELIVERY(4, "顺丰配送", AngelDetailTypeEnum.SHUNFENG_SUPPLIER),

    JD_LOGISTICS_DELIVERY(100,"京东物流",AngelDetailTypeEnum.JD_LOGISTICS_SUPPLIER),

    THIRD_DELIVERY(5, "第三方配送", AngelDetailTypeEnum.THIRD_SUPPLIER),
    ;

    private Integer type;

    private String desc;

    private AngelDetailTypeEnum angelDetailTypeEnum;

    DeliveryTypeEnum(Integer type, String desc, AngelDetailTypeEnum angelDetailTypeEnum) {
        this.type = type;
        this.desc = desc;
        this.angelDetailTypeEnum = angelDetailTypeEnum;
    }

    /**
     * 按类型获取enum
     *
     * @param type 类型
     * @return {@link DeliveryTypeEnum}
     */
    public static DeliveryTypeEnum getEnumByType(Integer type){
        for (DeliveryTypeEnum value : DeliveryTypeEnum.values()) {
            if(value.getType().equals(type)){
                return value;
            }
        }
        return null;
    }

    /**
     * 按类型获取enum
     *
     * @param type 类型
     * @return {@link DeliveryTypeEnum}
     */
    public static String getEnumDescByType(Integer type){
        for (DeliveryTypeEnum value : DeliveryTypeEnum.values()) {
            if(value.getType().equals(type)){
                return value.getDesc();
            }
        }
        return "";
    }

    /**
     * 获取骑手供应商业务类型
     *
     * @param type
     * @return
     */
    public static Integer fetchAngelDetailType(Integer type) {
        if(Objects.isNull(type)) {
            return null;
        }
        for (DeliveryTypeEnum value : DeliveryTypeEnum.values()) {
            if(value.getType().equals(type)){
                return value.getAngelDetailTypeEnum().getType();
            }
        }
        return null;
    }

    /**
     * 获取骑手供应商业务类型
     *
     * @param detailType
     * @return
     */
    public static Integer fetchDeliveryTypeByDetailType(Integer detailType) {
        if(Objects.isNull(detailType)) {
            return null;
        }
        for (DeliveryTypeEnum value : DeliveryTypeEnum.values()) {
            if(value.getAngelDetailTypeEnum().getType().equals(detailType)){
                return value.getType();
            }
        }
        return null;
    }

    /**
     * 匹配需要处理库存的供应商类型
     *
     * @param type
     * @return
     */
    public static Boolean matchNeedInventoryType(Integer type) {
        if(Objects.isNull(type)) {
            return Boolean.FALSE;
        }
        return RIDER_DELIVERY.getType().equals(type);
    }
}
