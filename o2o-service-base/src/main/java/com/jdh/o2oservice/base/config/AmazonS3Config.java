package com.jdh.o2oservice.base.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 参考文档：https://docs.jdcloud.com/cn/object-storage-service/installation-s3
 * 申请地址：https://taishan.jd.com/oss/resourceMgr/oss/list
 * 备注：京东云存储公网访问需要收费，内网访问不会收费，所以会配置两个客户端，在使用的时候需要判断处理。
 * @author: yang<PERSON>yu
 * @date: 2024/3/21 3:11 下午
 * @version: 1.0
 */
@Configuration
public class AmazonS3Config {

    /** */
    @Value("${oss.accessKey}")
    private String accessKey;
    /** */
    @Value("${oss.secretKey}")
    private String secretKey;
    /** 内网Endpoint */
    @Value("${oss.internalEndPoint}")
    private String internalEndPoint;
    /** 公网Endpoint */
    @Value("${oss.publicEndPoint}")
    private String publicEndPoint;
    /** */
    @Value("${oss.region}")
    private String region;
    /** */
    @Value("${oss.connectionTimeout}")
    private String connectionTimeout;

    /**
     * 内网访问客户端
     * @return
     */
    @Bean
    public AmazonS3 internalAmazonS3() {
        ClientConfiguration config = new ClientConfiguration();
        config.setConnectionTimeout(Integer.parseInt(connectionTimeout));
        AwsClientBuilder.EndpointConfiguration endpointConfig = new AwsClientBuilder.EndpointConfiguration(internalEndPoint, region);
        AWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);
        AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
        AmazonS3 s3 = AmazonS3Client.builder()
                .withEndpointConfiguration(endpointConfig)
                .withClientConfiguration(config)
                .withCredentials(awsCredentialsProvider)
                .disableChunkedEncoding()
                .withPathStyleAccessEnabled(true)
                .build();
        return s3;
    }

    /**
     * 公网访问客户端
     * @return
     */
    @Bean
    public AmazonS3 publicAmazonS3() {
        ClientConfiguration config = new ClientConfiguration();
        config.setConnectionTimeout(Integer.parseInt(connectionTimeout));
        AwsClientBuilder.EndpointConfiguration endpointConfig = new AwsClientBuilder.EndpointConfiguration(publicEndPoint, region);
        AWSCredentials awsCredentials = new BasicAWSCredentials(accessKey, secretKey);
        AWSCredentialsProvider awsCredentialsProvider = new AWSStaticCredentialsProvider(awsCredentials);
        AmazonS3 s3 = AmazonS3Client.builder()
                .withEndpointConfiguration(endpointConfig)
                .withClientConfiguration(config)
                .withCredentials(awsCredentialsProvider)
                .disableChunkedEncoding()
                .withPathStyleAccessEnabled(true)
                .build();
        return s3;
    }

}
