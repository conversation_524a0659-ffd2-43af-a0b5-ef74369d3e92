package com.jdh.o2oservice.base.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/18 15:35
 */
@SuppressWarnings("ALL")
public class TextUtil {



    /**
     * 按照指定长度size，截取文本；char为英文字母或者符号时占一位长度，即在区间 [0,127]占一位，其他占两位
     * 在DynamicParse 配置中有引用
     * @return
     */
    public static String splitCN(String text, String sizeStr){
        if (StringUtils.isBlank(text) || StringUtils.isBlank(sizeStr)){
            return text;
        }
        Integer size = Integer.valueOf(sizeStr);
        char[] array = text.toCharArray();
        StringBuilder builder = new StringBuilder();
        int count = 0;
        int i = 0;
        for (; i < array.length && count < size; i++) {
            char cur = array[i];
            builder.append(cur);
            if (cur <= 127){
                count += 1;
            }else{
                count += 2;
            }
        }
        // 截取的文本小于原始文本，后面追加...
        if (count >= size && i < text.length()){
            builder.append("...");
        }
        return builder.toString();
    }

    /**
     * 替换空格和制表符
     *
     * @param content
     * @return
     */
    public static String replaceSpace(String content) {
        if(StringUtils.isBlank(content)) {
            return null;
        }
        content = content.replaceAll("\\\\n", "");
        content = content.replaceAll("\\\\r", "");
        content = content.replaceAll("\\\\t", "");
        content = content.replaceAll("\\s+", "");
        return content;
    }

    /**
     * 使用给定的值替换模板中的占位符。
     * @param template 包含占位符的模板字符串。
     * @param values 用于替换占位符的键值对映射。
     * @return 替换后的字符串。
     */
    public static String replacePlaceholders(String template, Map<String, String> values) {
        // 正则表达式匹配占位符，例如 {med}，{link}
        Pattern pattern = Pattern.compile("\\{(\\w+)}");
        Matcher matcher = pattern.matcher(template);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            // 获取占位符中的键
            String key = matcher.group(1);
            // 获取键对应的值
            String replacement = values.getOrDefault(key, matcher.group(0));
            // 替换并追加到结果中
            matcher.appendReplacement(result, replacement);
        }
        // 追加剩余部分
        matcher.appendTail(result);

        return result.toString();
    }

    public static void main(String[] args) {
        System.out.println(TextUtil.splitCN("京东消费医疗 护士到家服务", "20"));
    }
}
