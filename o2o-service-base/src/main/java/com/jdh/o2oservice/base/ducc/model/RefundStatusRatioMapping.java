package com.jdh.o2oservice.base.ducc.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @ClassName:RefundFreezeMapping
 * @Description:
 * @Author: liwenming
 * @Date: 2024/5/1 10:40
 * @Vserion: 1.0
 **/
@Data
public class RefundStatusRatioMapping {

    ///////////////////////////////////记收入比例////////////////////////////////////
    /**
     * 记收入:服务费比例
     */
    private String serviceAmountIncomeRatio;
    /**
     * 记收入:其他费项比例（最后一笔）
     */
    private String feeAmountIncomeRatio;
    ///////////////////////////////////退款金额比例////////////////////////////////////
    /**
     * 服务费退款比例
     */
    private String serviceAmountRefundRatio;
    /**
     * 其他费项退款比例（最后一笔）
     */
    private String feeAmountRefundRatio;

    ///////////////////////////////////结算金额比例////////////////////////////////////
    /**
     * 服务费比例
     */
    private String settleServiceRatio;
    /**
     * 时段费+距离费+动态调整费结算比例
     */
    private String settleFeeRatio;
    /**
     * 时段费+距离费+动态调整费结算比例
     */
    private String lastSettleFeeRatio;

}
