package com.jdh.o2oservice.base.groovy.core;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.groovy.config.GroovySourceConfig;
import com.jdh.o2oservice.base.util.AssertUtils;
import groovy.lang.GroovyClassLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName RuleEngineGroovyModuleRuleExecutor
 * @Description
 * <AUTHOR>
 * @Date 2024/10/22 16:06
 */
@Slf4j
@Component
public class GroovyModuleExecutor implements GroovyExecutor<GroovyScript> {

    /**
     * classCache
     */
    private static Table<String, String, Class> classTabCh = HashBasedTable.create();

    /**
     * 根据ducc key 获取可运行的class 方法
     * @param name
     * @return
     */
    @Override
    public GroovyScript getInstance(String name) {
        try {
            Long start = new Date().getTime();
            //从ducc获取 groovy脚本   根据duccKey  获取 script 内容
            String script = getScriptConf(name);
            log.info("[RuleEngineGroovyModuleRuleExecutor -> getInstance],获取脚本内容!script={}", script);
            //执行编译class 成功后放入本地class
            Class<GroovyScript> aClass = parseGroovyAndCache(name, script);
            if(aClass == null) {
                throw new BusinessException(BusinessErrorCode.GROOVY_INIT_ERROR);
            }
            log.info("[RuleEngineGroovyModuleRuleExecutor -> getInstance],groovyTest.timeLast:{}", (new Date().getTime() - start));
            return aClass.newInstance();
        } catch (Exception ex) {
            log.error("[RuleEngineGroovyModuleRuleExecutor -> getInstance],groovy获取实例对象失败!", ex);
            throw new BusinessException(BusinessErrorCode.GROOVY_INIT_ERROR);
        }
    }

    /**
     * 从ducc获取 groovy脚本   根据duccKey  获取 script 内容
     * 执行编译class 成功后放入本地class
     * @param name
     * @param script
     * @return
     */
    @Override
    public Class<GroovyScript> parseGroovyAndCache(String name, String script) {
        try {
            AssertUtils.hasText(name, "脚本名称不能为空");
            if(StringUtils.isBlank(script)) {
                classTabCh.row(name).clear();
                return null;
            }

            String md5Hex = DigestUtils.md5Hex(script);
            Class groovyCls = classTabCh.get(name, md5Hex);
            if(null == groovyCls) {
                log.info("[RuleEngineGroovyModuleRuleExecutor -> parseGroovyAndCache] parseClass new md5Hex={}", md5Hex);
                GroovyClassLoader classLoader = new GroovyClassLoader();
                Class aClass = classLoader.parseClass(script);
                classTabCh.put(name, md5Hex, aClass);
                return aClass;
            }

            return groovyCls;
        } catch (Exception ex) {
            log.error("[RuleEngineGroovyModuleRuleExecutor -> parseGroovyAndCache],groovy获取实例对象失败!", ex);
            throw new BusinessException(BusinessErrorCode.GROOVY_INIT_ERROR);
        }
    }

    /**
     * 从ducc获取 groovy脚本   根据duccKey  获取 script 内容
     * 执行编译class 成功后放入本地class
     * @param script
     * @return
     */
    public Class<GroovyScript> parseGroovyAndCache(String script) {
        try {
            AssertUtils.hasText(script, "脚本内容不能为空");
            GroovyClassLoader classLoader = new GroovyClassLoader();
            Class aClass = classLoader.parseClass(script);
            return aClass;
        } catch (Exception ex) {
            log.error("[RuleEngineGroovyModuleRuleExecutor -> getInstance],groovy获取实例对象失败!", ex);
            throw new BusinessException(BusinessErrorCode.GROOVY_INIT_ERROR);
        }
    }


    /**
     * 查询脚本
     *
     * @param key
     * @return
     */
    public String getScriptConf(String key) {
        return GroovySourceConfig.getGroovyScript(key);
    }
}
