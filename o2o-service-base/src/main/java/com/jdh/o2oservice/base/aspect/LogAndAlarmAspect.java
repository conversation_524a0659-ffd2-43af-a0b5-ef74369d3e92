package com.jdh.o2oservice.base.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.jd.ump.profiler.util.StringUtil;
import com.jdh.o2oservice.base.alarm.AlarmReachMessage;
import com.jdh.o2oservice.base.alarm.AlarmReachRpcFactory;
import com.jdh.o2oservice.base.annotation.AlarmPolicy;
import com.jdh.o2oservice.base.annotation.LogAndAlarm;
import com.jdh.o2oservice.base.ducc.DuccConfig;
import com.jdh.o2oservice.base.exception.ArgumentsException;
import com.jdh.o2oservice.base.exception.BusinessException;
import com.jdh.o2oservice.base.exception.SystemException;
import com.jdh.o2oservice.base.exception.errorcode.BusinessErrorCode;
import com.jdh.o2oservice.base.exception.errorcode.ErrorCode;
import com.jdh.o2oservice.base.util.ResponseUtil;
import com.jdh.o2oservice.common.result.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;


/**
 * 日志和UMP 切面
 *
 * <AUTHOR>
 * @date 2023/09/14
 */
@Slf4j
@Aspect
@Component
@Order(Integer.MIN_VALUE)
public class LogAndAlarmAspect {

    /**
     * 日志ID
     */
    private static final String LOG_ID = "logid";

    /**
     * commonDuccConfig
     */
    @Autowired
    private DuccConfig duccConfig;

    /**
     * alarmReachRpcFactory
     */
    @Autowired
    private AlarmReachRpcFactory alarmReachRpcFactory;

    /**
     * pointCut
     */
    @Pointcut("@annotation(com.jdh.o2oservice.base.annotation.LogAndAlarm)")
    public void pointCut() {
    }

    /**
     * 日志和UMP
     *
     * @param point point
     * @return {@link Object}
     */
    @Around("pointCut()")
    public Object logAndAlarmAspect(ProceedingJoinPoint point) throws Throwable {
        Object[] args = point.getArgs();
        Signature signature = point.getSignature();
        MethodSignature ms = null;
        if (signature instanceof MethodSignature) {
            ms = (MethodSignature) signature;
        }

        if (Objects.isNull(ms)) {
            return point.proceed();
        }

        String[] parameterNames = ms.getParameterNames();
        Method method = ms.getMethod();
        LogAndAlarm annotation = method.getAnnotation(LogAndAlarm.class);
        String umpKey = annotation.jKey();
        if (umpKey.length() <= 0) {
            umpKey = point.getTarget().getClass().getName() + "." + ms.getMethod().getName();
            if (umpKey.length() > 128) {
                int startIndex = umpKey.length() - 128;
                umpKey = umpKey.substring(startIndex);
            }
        }

        //boolean umpSwitch = annotation.umpSwitch();
        boolean logSwitch = annotation.logSwitch();
        boolean mdcSwitch = StringUtil.isBlank(MDC.get(LOG_ID));

        // 新版ump无需设置appName
        CallerInfo callerInfo = Profiler.registerInfo(umpKey);
//        if(umpSwitch){
//            callerInfo = Profiler.registerInfo(umpKey);
//        }

        StopWatch stopWatch = new StopWatch();
        try {
            // 补充logId
            if (mdcSwitch) {
                MDC.put(LOG_ID, UUID.fastUUID().toString());
            }

            // 打印日志
            if (logSwitch) {
                String paramStr = Objects.nonNull(parameterNames) ? JSON.toJSONString(args) : "";
                log.info("{},start,param:{}", umpKey, paramStr);
            }

            // 执行逻辑
            stopWatch.start();
            Object proceed = point.proceed();
            stopWatch.stop();

            if (logSwitch) {
                log.info("{},end,用时{}毫秒,result:{}", umpKey, stopWatch.getTotalTimeMillis(), JSON.toJSONString(proceed));
            }

            return proceed;
        } catch (SystemException se) {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            ErrorCode errorCode = se.getErrorCode();
            boolean needExcludeAlarm = false;
            if (Objects.nonNull(callerInfo)) {
                //如果包含需要排除预警的code码，不进行ump预警
                if (MapUtil.isNotEmpty(duccConfig.getExcludeAlarmCode())) {
                    List<String> clueErrorCodes = duccConfig.getExcludeAlarmCode().get(umpKey);
                    if (CollUtil.isNotEmpty(clueErrorCodes) && clueErrorCodes.contains(errorCode.getCode())) {
                        needExcludeAlarm = true;
                    }
                }
            }
            if (!needExcludeAlarm) {
                //执行配置策略报警
                executeAlarmPolicy(annotation.alarmPolicy(), callerInfo, point.getArgs(), se.getMessage());
            }
            log.error(umpKey + ",SystemException,", se);

            if (ms.getReturnType() == Response.class) {
                Response response = ResponseUtil.buildErrResponse(errorCode);
                if (logSwitch) {
                    log.info("{},end,用时{}毫秒,result:{}", umpKey, stopWatch.getTotalTimeMillis(), JSON.toJSONString(response));
                }
                return response;
            } else {
                log.info(umpKey + ",用时{}毫秒", stopWatch.getTotalTimeMillis());
                throw se;
            }
        } catch (ArgumentsException ae) {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            ErrorCode errorCode = ae.getErrorCode();
            boolean needExcludeAlarm = false;
            if (Objects.nonNull(callerInfo)) {
                //如果包含需要排除预警的code码，不进行ump预警
                if (MapUtil.isNotEmpty(duccConfig.getExcludeAlarmCode())) {
                    List<String> clueErrorCodes = duccConfig.getExcludeAlarmCode().get(umpKey);
                    if (CollUtil.isNotEmpty(clueErrorCodes) && clueErrorCodes.contains(errorCode.getCode())) {
                        needExcludeAlarm = true;
                    }
                }
            }
            if (!needExcludeAlarm) {
                executeAlarmPolicy(annotation.alarmPolicy(), callerInfo, point.getArgs(), ae.getMessage());
            }
            log.error(umpKey + ",ArgumentsException,", ae);

            if (ms.getReturnType() == Response.class) {
                Response response = ResponseUtil.buildErrResponse(errorCode);
                if (logSwitch) {
                    log.info("{},end,用时{}毫秒,result:{}", umpKey, stopWatch.getTotalTimeMillis(), JSON.toJSONString(response));
                }
                return response;
            } else {
                log.info(umpKey + ",用时{}毫秒", stopWatch.getTotalTimeMillis());
                throw ae;
            }
        } catch (BusinessException be) {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            ErrorCode errorCode = be.getErrorCode();
            boolean needExcludeAlarm = false;
            if (Objects.nonNull(callerInfo)) {
                //如果包含需要排除预警的code码，不进行ump预警
                if (MapUtil.isNotEmpty(duccConfig.getExcludeAlarmCode())) {
                    List<String> clueErrorCodes = duccConfig.getExcludeAlarmCode().get(umpKey);
                    if (CollUtil.isNotEmpty(clueErrorCodes) && clueErrorCodes.contains(errorCode.getCode())) {
                        needExcludeAlarm = true;
                    }
                }
            }
            if (!needExcludeAlarm) {
                executeAlarmPolicy(annotation.alarmPolicy(), callerInfo, point.getArgs(), be.getMessage());
            }
            log.error(umpKey + ",businessException,", be);

            if (ms.getReturnType() == Response.class) {
                Response response = ResponseUtil.buildErrResponse(errorCode);
                if (logSwitch) {
                    log.info("{},end,用时{}毫秒,result:{}", umpKey, stopWatch.getTotalTimeMillis(), JSON.toJSONString(response));
                }
                return response;
            } else {
                log.info(umpKey + ",用时{}毫秒", stopWatch.getTotalTimeMillis());
                throw be;
            }
        } catch (Exception exception) {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            if (Objects.nonNull(callerInfo)) {
                executeAlarmPolicy(annotation.alarmPolicy(), callerInfo, point.getArgs());
            }
            log.error(umpKey + ",exception,", exception);

            if (ms.getReturnType() == Response.class) {
                Response response = ResponseUtil.buildErrResponse(BusinessErrorCode.UNKNOWN_ERROR);
                if (logSwitch) {
                    log.info("{},end,用时{}毫秒,result:{}", umpKey, stopWatch.getTotalTimeMillis(), JSON.toJSONString(response));
                }
                return response;
            } else {
                log.info(umpKey + ",用时{}毫秒", stopWatch.getTotalTimeMillis());
                throw exception;
            }
        } catch (Throwable throwable) {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            log.error(umpKey + ",throwable,", throwable);
            if (Objects.nonNull(callerInfo)) {
                executeAlarmPolicy(annotation.alarmPolicy(), callerInfo, point.getArgs());
            }
            if (ms.getReturnType() == Response.class) {
                Response response = ResponseUtil.buildErrResponse(BusinessErrorCode.UNKNOWN_ERROR);
                if (logSwitch) {
                    log.info("{},end,用时{}毫秒,result:{}", umpKey, stopWatch.getTotalTimeMillis(), JSON.toJSONString(response));
                }
                return response;
            } else {
                log.info(umpKey + ",用时{}毫秒", stopWatch.getTotalTimeMillis());
                throw throwable;
            }
        } finally {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
            if (Objects.nonNull(callerInfo)) {
                Profiler.registerInfoEnd(callerInfo);
            }
            if (mdcSwitch) {
                MDC.remove(LOG_ID);
            }
        }
    }

    /**
     * 执行报警策略
     *
     * @param alarmPolicies
     * @param callerInfo
     */
    private void executeAlarmPolicy(AlarmPolicy[] alarmPolicies, CallerInfo callerInfo, Object[] args) {
        executeAlarmPolicy(alarmPolicies, callerInfo, args, BusinessErrorCode.UNKNOWN_ERROR.getDescription());
    }

    /**
     * 执行报警策略
     *
     * @param alarmPolicies
     * @param callerInfo
     */
    private void executeAlarmPolicy(AlarmPolicy[] alarmPolicies, CallerInfo callerInfo, Object[] args, String errorMsg) {
        //没有配置自定义报警策略，执行默认ump报警
        if (alarmPolicies == null || alarmPolicies.length == 0) {
            log.info("LogAndAlarmAspect -> executeAlarmPolicy, 执行默认ump报警");
            Profiler.functionError(callerInfo);
            return;
        }
        //配置了自定义策略，按配置依次执行
        log.info("LogAndAlarmAspect -> executeAlarmPolicy, 配置了自定义策略，按配置依次执行");
        for (int i = 0; i < alarmPolicies.length; i++) {
            AlarmPolicy alarmPolicy = alarmPolicies[i];
            alarmReachRpcFactory.createAlarmReachRpc(alarmPolicy.type()).sendAlarm(
                    AlarmReachMessage.builder()
                            .args(args)
                            .keyword(alarmPolicy.keyword())
                            .methodName(alarmPolicy.methodName())
                            .message(errorMsg)
                            .callerInfo(callerInfo)
                            .build());
        }
    }

}
