package com.jdh.o2oservice.base.util;

/**
 * 计算距离
 *
 * <AUTHOR>
 * @date 2024/04/17
 */
public class PositionUtil {

    /**
     * 赤道半径（单位：米）
     */
    private static final double EQUATOR_RADIUS = 6378137;

    /**
     * （反余弦计算方式）两个坐标之间的距离  返回距离，单位m
     *
     * @param longitude1 第一个点的经度
     * @param latitude1  第一个点的纬度
     * @param longitude2 第二个点的经度
     * @param latitude2  第二个点的纬度
     * @return 返回距离，单位m
     */
    public static double getDistance(double longitude1, double latitude1, double longitude2, double latitude2) {
        // 纬度
        double lat1 = Math.toRadians(latitude1);
        double lat2 = Math.toRadians(latitude2);
        // 经度
        double lon1 = Math.toRadians(longitude1);
        double lon2 = Math.toRadians(longitude2);
        // 纬度之差
        double a = lat1 - lat2;
        // 经度之差
        double b = lon1 - lon2;
        // 计算两点距离的公式
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        // 弧长乘赤道半径, 返回单位: 米
        s = s * EQUATOR_RADIUS;
        return s;
    }

    /**
     * （反余弦计算方式）两个坐标之间的距离  返回距离，单位km 并且向上取整
     *
     * @param longitude1 第一个点的经度
     * @param latitude1  第一个点的纬度
     * @param longitude2 第二个点的经度
     * @param latitude2  第二个点的纬度
     * @return 返回距离，单位km 并且向上取整
     */
    public static int getDistanceCeilKm(double longitude1, double latitude1, double longitude2, double latitude2){
        return (int) Math.ceil(getDistance(longitude1,latitude1,longitude2,latitude2)/1000);
    }


    /**
     * main
     *
     * @param args args
     */
    public static void main(String[] args) {
        System.out.println(getDistance(116.33956, 39.89841, 116.421263, 39.948625));
        System.out.println(getDistance(116.421263, 39.948625,116.33956, 39.89841 ));
        System.out.println(getDistanceCeilKm(116.33956, 39.89841, 116.421263, 39.948625));
        System.out.println(getDistanceCeilKm(116.421263, 39.948625,116.33956, 39.89841 ));
    }

}